# Public Regulatory Data Sources Integration

This PR adds integration with public regulatory data sources to RegulationGuru, allowing users to access and synchronize data from various government agencies and regulatory bodies.

## Features

- Integration with 5 major public regulatory data sources:
  1. **SEC EDGAR** - Securities and Exchange Commission's EDGAR database
  2. **CFPB Consumer Complaints** - Consumer Financial Protection Bureau's Consumer Complaint Database
  3. **FINRA Rules** - Financial Industry Regulatory Authority's rules and regulatory notices
  4. **Federal Reserve** - Federal Reserve's regulatory data and economic indicators
  5. **USAspending.gov** - Federal spending data and contract information

- New API endpoints for:
  - Listing available public data sources
  - Testing connections to public data sources
  - Fetching data from public data sources
  - Managing external API source configurations
  - Synchronizing data from external API sources

- Database models for:
  - External API sources
  - External API synchronization logs
  - Extension of the RegulationURL model to include external API source relationship

- Background tasks for data synchronization

## Implementation Details

### Directory Structure

```
app/api/data_sources/
├── __init__.py
├── base_client.py
├── router.py
└── public/
    ├── __init__.py
    ├── manager.py
    ├── sec_edgar.py
    ├── cfpb_complaints.py
    ├── finra_rules.py
    ├── federal_reserve.py
    └── usa_spending.py
```

### Database Changes

- Added new tables:
  - `external_api_sources` - Stores configurations for external API sources
  - `external_api_sync_logs` - Logs synchronization events for external API sources

- Updated existing tables:
  - `regulation_urls` - Added `external_api_source_id` column to link regulations to external API sources

### API Endpoints

- `/api/v1/data-sources/public/available` - List available public data sources
- `/api/v1/data-sources/public/test-connections` - Test connections to public data sources
- `/api/v1/data-sources/public/{source_name}/data` - Fetch data from a public data source
- `/api/v1/data-sources/external` - CRUD operations for external API sources
- `/api/v1/data-sources/external/{source_id}/sync` - Synchronize data from an external API source
- `/api/v1/data-sources/external/{source_id}/logs` - Get synchronization logs for an external API source

## How to Use

### Running the Migration

Before using the new features, run the migration script to update the database schema:

```bash
python migrations/add_external_api_sources.py
```

### Creating an External API Source

```bash
curl -X POST "http://localhost:8000/api/v1/data-sources/external" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "SEC EDGAR Source",
    "source_type": "sec_edgar",
    "base_url": "https://www.sec.gov/",
    "description": "SEC EDGAR data source for regulatory filings",
    "is_active": true,
    "sync_frequency": 24
  }'
```

### Fetching Data from a Public Source

```bash
curl -X GET "http://localhost:8000/api/v1/data-sources/public/sec_edgar/data?data_type=regulatory_data&limit=5"
```

### Synchronizing Data from an External API Source

```bash
curl -X POST "http://localhost:8000/api/v1/data-sources/external/1/sync"
```

## Documentation

See the [Public Data Sources Documentation](docs/public_data_sources.md) for detailed information on the available data sources and API endpoints.

## Future Improvements

- Add more public data sources
- Implement more sophisticated data mapping and normalization
- Add support for authentication with API keys for sources that require it
- Implement more advanced filtering and search capabilities
- Add UI components for managing external API sources
