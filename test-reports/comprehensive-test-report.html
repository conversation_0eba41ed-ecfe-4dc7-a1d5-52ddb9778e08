
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>RegulationGuru - Comprehensive Test Report</title>
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
                .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                .header { background: linear-gradient(135deg, #1a365d, #2d5a87); color: white; padding: 30px; border-radius: 8px 8px 0 0; }
                .header h1 { margin: 0; font-size: 2.5em; }
                .header p { margin: 10px 0 0; opacity: 0.9; }
                .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; padding: 30px; }
                .stat-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border-left: 4px solid #28a745; }
                .stat-card.warning { border-left-color: #ffc107; }
                .stat-card.danger { border-left-color: #dc3545; }
                .stat-number { font-size: 2em; font-weight: bold; color: #1a365d; }
                .stat-label { color: #6c757d; margin-top: 5px; }
                .results { padding: 0 30px 30px; }
                .result-item { background: #f8f9fa; margin: 10px 0; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745; }
                .result-item.failed { border-left-color: #dc3545; background: #fff5f5; }
                .result-title { font-weight: bold; font-size: 1.2em; color: #1a365d; }
                .result-description { color: #6c757d; margin-top: 5px; }
                .footer { background: #f8f9fa; padding: 20px 30px; border-radius: 0 0 8px 8px; text-align: center; color: #6c757d; }
                .success { color: #28a745; }
                .failed { color: #dc3545; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🛡️ RegulationGuru Test Report</h1>
                    <p>Comprehensive test suite results - 2025-06-09 09:27:19</p>
                </div>
                
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number">7</div>
                        <div class="stat-label">Total Test Suites</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number success">0</div>
                        <div class="stat-label">Passed Suites</div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-number failed">7</div>
                        <div class="stat-label">Failed Suites</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">0.0%</div>
                        <div class="stat-label">Success Rate</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">7.8s</div>
                        <div class="stat-label">Total Duration</div>
                    </div>
                </div>
                
                <div class="results">
                    <h2>Test Suite Results</h2>
        
                    <div class="result-item failed">
                        <div class="result-title">❌ Smoke Tests</div>
                        <div class="result-description">Basic functionality validation tests</div>
                        
                    </div>
            
                    <div class="result-item failed">
                        <div class="result-title">❌ Unit Tests</div>
                        <div class="result-description">Core application logic and component tests</div>
                        
                    </div>
            
                    <div class="result-item failed">
                        <div class="result-title">❌ Integration Tests</div>
                        <div class="result-description">API integration and database interaction tests</div>
                        
                    </div>
            
                    <div class="result-item failed">
                        <div class="result-title">❌ Security Tests</div>
                        <div class="result-description">Security vulnerability and penetration tests</div>
                        
                    </div>
            
                    <div class="result-item failed">
                        <div class="result-title">❌ Performance Tests</div>
                        <div class="result-description">API response time and load testing</div>
                        
                    </div>
            
                    <div class="result-item failed">
                        <div class="result-title">❌ Accessibility Tests</div>
                        <div class="result-description">WCAG compliance and screen reader compatibility tests</div>
                        
                    </div>
            
                    <div class="result-item failed">
                        <div class="result-title">❌ E2E Tests</div>
                        <div class="result-description">End-to-end browser automation tests</div>
                        
                    </div>
            
                </div>
                
                <div class="footer">
                    <p>Generated on 2025-06-09 09:27:27 | RegulationGuru Test Suite</p>
                    <p>View detailed reports: 
                        <a href="unit-report.html">Unit</a> | 
                        <a href="integration-report.html">Integration</a> | 
                        <a href="e2e-report.html">E2E</a> | 
                        <a href="performance-report.html">Performance</a> | 
                        <a href="security-report.html">Security</a>
                    </p>
                </div>
            </div>
        </body>
        </html>
        