[{"keyword": "Feature", "name": "Regulatory Management System", "tags": [], "location": "features/regulatory_management.feature:1", "status": "failed", "description": ["As a compliance officer", "I want to manage regulatory information", "So that I can ensure organizational compliance"], "elements": [{"type": "background", "keyword": "Background", "name": "", "location": "features/regulatory_management.feature:6", "steps": [{"keyword": "Given", "step_type": "given", "name": "the RegulationGuru application is running", "location": "features/regulatory_management.feature:7"}, {"keyword": "And", "step_type": "given", "name": "I have access to the regulatory management system", "location": "features/regulatory_management.feature:8"}]}, {"type": "scenario", "keyword": "<PERSON><PERSON><PERSON>", "name": "View regulatory dashboard", "tags": [], "location": "features/regulatory_management.feature:10", "steps": [{"keyword": "Given", "step_type": "given", "name": "the RegulationGuru application is running", "location": "features/regulatory_management.feature:7", "match": {"location": "features/steps/regulatory_steps.py:12", "arguments": []}, "result": {"status": "passed", "duration": 0.004275321960449219}}, {"keyword": "And", "step_type": "given", "name": "I have access to the regulatory management system", "location": "features/regulatory_management.feature:8", "match": {"location": "features/steps/regulatory_steps.py:23", "arguments": []}, "result": {"status": "passed", "duration": 0.011655092239379883}}, {"keyword": "Given", "step_type": "given", "name": "I am on the main dashboard", "location": "features/regulatory_management.feature:11", "match": {"location": "features/steps/regulatory_steps.py:34", "arguments": []}, "result": {"status": "passed", "duration": 0.012363195419311523}}, {"keyword": "When", "step_type": "when", "name": "I navigate to the regulatory section", "location": "features/regulatory_management.feature:12", "match": {"location": "features/steps/regulatory_steps.py:44", "arguments": []}, "result": {"status": "passed", "duration": 0.0072476863861083984}}, {"keyword": "Then", "step_type": "then", "name": "I should see the regulatory dashboard", "location": "features/regulatory_management.feature:13", "match": {"location": "features/steps/regulatory_steps.py:54", "arguments": []}, "result": {"status": "passed", "duration": 8.344650268554688e-05}}, {"keyword": "And", "step_type": "then", "name": "I should see a list of regulations", "location": "features/regulatory_management.feature:14", "match": {"location": "features/steps/regulatory_steps.py:60", "arguments": []}, "result": {"status": "passed", "duration": 6.4849853515625e-05}}, {"keyword": "And", "step_type": "then", "name": "I should see regulatory statistics", "location": "features/regulatory_management.feature:15", "match": {"location": "features/steps/regulatory_steps.py:73", "arguments": []}, "result": {"status": "passed", "duration": 3.24249267578125e-05}}], "status": "passed"}, {"type": "scenario", "keyword": "<PERSON><PERSON><PERSON>", "name": "Search for regulations", "tags": [], "location": "features/regulatory_management.feature:17", "steps": [{"keyword": "Given", "step_type": "given", "name": "the RegulationGuru application is running", "location": "features/regulatory_management.feature:7", "match": {"location": "features/steps/regulatory_steps.py:12", "arguments": []}, "result": {"status": "passed", "duration": 0.0036458969116210938}}, {"keyword": "And", "step_type": "given", "name": "I have access to the regulatory management system", "location": "features/regulatory_management.feature:8", "match": {"location": "features/steps/regulatory_steps.py:23", "arguments": []}, "result": {"status": "passed", "duration": 0.0068836212158203125}}, {"keyword": "Given", "step_type": "given", "name": "I am on the regulatory dashboard", "location": "features/regulatory_management.feature:18", "match": {"location": "features/steps/regulatory_steps.py:81", "arguments": []}, "result": {"status": "passed", "duration": 0.006829738616943359}}, {"keyword": "When", "step_type": "when", "name": "I search for \"financial\" regulations", "location": "features/regulatory_management.feature:19", "match": {"location": "features/steps/regulatory_steps.py:91", "arguments": [{"value": "financial", "name": "search_term"}]}, "result": {"status": "passed", "duration": 0.008761882781982422}}, {"keyword": "Then", "step_type": "then", "name": "I should see regulations containing \"financial\"", "location": "features/regulatory_management.feature:20", "match": {"location": "features/steps/regulatory_steps.py:102", "arguments": [{"value": "financial", "name": "search_term"}]}, "result": {"status": "passed", "duration": 5.507469177246094e-05}}, {"keyword": "And", "step_type": "then", "name": "the results should be filtered appropriately", "location": "features/regulatory_management.feature:21", "match": {"location": "features/steps/regulatory_steps.py:119", "arguments": []}, "result": {"status": "passed", "duration": 3.218650817871094e-05}}], "status": "passed"}, {"type": "scenario", "keyword": "<PERSON><PERSON><PERSON>", "name": "Add a new regulation", "tags": [], "location": "features/regulatory_management.feature:23", "steps": [{"keyword": "Given", "step_type": "given", "name": "the RegulationGuru application is running", "location": "features/regulatory_management.feature:7", "match": {"location": "features/steps/regulatory_steps.py:12", "arguments": []}, "result": {"status": "passed", "duration": 0.0038220882415771484}}, {"keyword": "And", "step_type": "given", "name": "I have access to the regulatory management system", "location": "features/regulatory_management.feature:8", "match": {"location": "features/steps/regulatory_steps.py:23", "arguments": []}, "result": {"status": "passed", "duration": 0.007210969924926758}}, {"keyword": "Given", "step_type": "given", "name": "I am on the regulatory dashboard", "location": "features/regulatory_management.feature:24", "match": {"location": "features/steps/regulatory_steps.py:81", "arguments": []}, "result": {"status": "passed", "duration": 0.009533882141113281}}, {"keyword": "When", "step_type": "when", "name": "I click on \"Add New Regulation\"", "location": "features/regulatory_management.feature:25", "match": {"location": "features/steps/regulatory_steps.py:125", "arguments": []}, "result": {"status": "passed", "duration": 4.7206878662109375e-05}}, {"keyword": "And", "step_type": "when", "name": "I fill in the regulation details", "location": "features/regulatory_management.feature:26", "table": {"headings": ["Field", "Value"], "rows": [["Title", "Test Financial Regulation"], ["Category", "Financial Services"], ["Description", "Test regulation for BDD"]]}, "match": {"location": "features/steps/regulatory_steps.py:132", "arguments": []}, "result": {"status": "passed", "duration": 6.103515625e-05}}, {"keyword": "And", "step_type": "when", "name": "I submit the form", "location": "features/regulatory_management.feature:31", "match": {"location": "features/steps/regulatory_steps.py:140", "arguments": []}, "result": {"status": "passed", "duration": 0.012592315673828125}}, {"keyword": "Then", "step_type": "then", "name": "I should see a success message", "location": "features/regulatory_management.feature:32", "match": {"location": "features/steps/regulatory_steps.py:157", "arguments": []}, "result": {"status": "failed", "duration": 0.0001728534698486328, "error_message": ["Assertion Failed: Submission failed: 500", "Captured stdout:", "❌ Step failed: I should see a success message"]}}, {"keyword": "And", "step_type": "then", "name": "the new regulation should appear in the list", "location": "features/regulatory_management.feature:33"}], "status": "failed"}, {"type": "scenario", "keyword": "<PERSON><PERSON><PERSON>", "name": "View regulation details", "tags": [], "location": "features/regulatory_management.feature:35", "steps": [{"keyword": "Given", "step_type": "given", "name": "the RegulationGuru application is running", "location": "features/regulatory_management.feature:7", "match": {"location": "features/steps/regulatory_steps.py:12", "arguments": []}, "result": {"status": "passed", "duration": 0.004465341567993164}}, {"keyword": "And", "step_type": "given", "name": "I have access to the regulatory management system", "location": "features/regulatory_management.feature:8", "match": {"location": "features/steps/regulatory_steps.py:23", "arguments": []}, "result": {"status": "passed", "duration": 0.007551431655883789}}, {"keyword": "Given", "step_type": "given", "name": "there is a regulation titled \"Test Financial Regulation\"", "location": "features/regulatory_management.feature:36", "match": {"location": "features/steps/regulatory_steps.py:188", "arguments": [{"value": "Test Financial Regulation", "name": "title"}]}, "result": {"status": "passed", "duration": 4.76837158203125e-05}}, {"keyword": "When", "step_type": "when", "name": "I click on the regulation", "location": "features/regulatory_management.feature:37", "match": {"location": "features/steps/regulatory_steps.py:195", "arguments": []}, "result": {"status": "passed", "duration": 4.267692565917969e-05}}, {"keyword": "Then", "step_type": "then", "name": "I should see the regulation details page", "location": "features/regulatory_management.feature:38", "match": {"location": "features/steps/regulatory_steps.py:201", "arguments": []}, "result": {"status": "passed", "duration": 3.24249267578125e-05}}, {"keyword": "And", "step_type": "then", "name": "I should see the regulation title", "location": "features/regulatory_management.feature:39", "match": {"location": "features/steps/regulatory_steps.py:206", "arguments": []}, "result": {"status": "passed", "duration": 3.075599670410156e-05}}, {"keyword": "And", "step_type": "then", "name": "I should see the regulation category", "location": "features/regulatory_management.feature:40", "match": {"location": "features/steps/regulatory_steps.py:211", "arguments": []}, "result": {"status": "passed", "duration": 2.8371810913085938e-05}}, {"keyword": "And", "step_type": "then", "name": "I should see the regulation description", "location": "features/regulatory_management.feature:41", "match": {"location": "features/steps/regulatory_steps.py:217", "arguments": []}, "result": {"status": "passed", "duration": 2.9802322387695312e-05}}], "status": "passed"}, {"type": "scenario", "keyword": "<PERSON><PERSON><PERSON>", "name": "Update regulation information", "tags": [], "location": "features/regulatory_management.feature:43", "steps": [{"keyword": "Given", "step_type": "given", "name": "the RegulationGuru application is running", "location": "features/regulatory_management.feature:7", "match": {"location": "features/steps/regulatory_steps.py:12", "arguments": []}, "result": {"status": "passed", "duration": 0.003998756408691406}}, {"keyword": "And", "step_type": "given", "name": "I have access to the regulatory management system", "location": "features/regulatory_management.feature:8", "match": {"location": "features/steps/regulatory_steps.py:23", "arguments": []}, "result": {"status": "passed", "duration": 0.007835865020751953}}, {"keyword": "Given", "step_type": "given", "name": "I am viewing a regulation details page", "location": "features/regulatory_management.feature:44", "result": {"status": "undefined", "duration": 0}}, {"keyword": "When", "step_type": "when", "name": "I click on \"Edit\"", "location": "features/regulatory_management.feature:45"}, {"keyword": "And", "step_type": "when", "name": "I update the description to \"Updated test regulation\"", "location": "features/regulatory_management.feature:46"}, {"keyword": "And", "step_type": "when", "name": "I save the changes", "location": "features/regulatory_management.feature:47"}, {"keyword": "Then", "step_type": "then", "name": "I should see a success message", "location": "features/regulatory_management.feature:48"}, {"keyword": "And", "step_type": "then", "name": "the updated description should be displayed", "location": "features/regulatory_management.feature:49"}], "status": "failed"}, {"type": "scenario", "keyword": "<PERSON><PERSON><PERSON>", "name": "Delete a regulation", "tags": [], "location": "features/regulatory_management.feature:51", "steps": [{"keyword": "Given", "step_type": "given", "name": "the RegulationGuru application is running", "location": "features/regulatory_management.feature:7", "match": {"location": "features/steps/regulatory_steps.py:12", "arguments": []}, "result": {"status": "passed", "duration": 0.0037610530853271484}}, {"keyword": "And", "step_type": "given", "name": "I have access to the regulatory management system", "location": "features/regulatory_management.feature:8", "match": {"location": "features/steps/regulatory_steps.py:23", "arguments": []}, "result": {"status": "passed", "duration": 0.007623195648193359}}, {"keyword": "Given", "step_type": "given", "name": "there is a regulation titled \"Test Financial Regulation\"", "location": "features/regulatory_management.feature:52", "match": {"location": "features/steps/regulatory_steps.py:188", "arguments": [{"value": "Test Financial Regulation", "name": "title"}]}, "result": {"status": "passed", "duration": 4.8160552978515625e-05}}, {"keyword": "When", "step_type": "when", "name": "I delete the regulation", "location": "features/regulatory_management.feature:53", "result": {"status": "undefined", "duration": 0}}, {"keyword": "And", "step_type": "when", "name": "I confirm the deletion", "location": "features/regulatory_management.feature:54"}, {"keyword": "Then", "step_type": "then", "name": "I should see a success message", "location": "features/regulatory_management.feature:55"}, {"keyword": "And", "step_type": "then", "name": "the regulation should no longer appear in the list", "location": "features/regulatory_management.feature:56"}], "status": "failed"}, {"type": "scenario", "keyword": "<PERSON><PERSON><PERSON>", "name": "Filter regulations by category", "tags": [], "location": "features/regulatory_management.feature:58", "steps": [{"keyword": "Given", "step_type": "given", "name": "the RegulationGuru application is running", "location": "features/regulatory_management.feature:7", "match": {"location": "features/steps/regulatory_steps.py:12", "arguments": []}, "result": {"status": "passed", "duration": 0.0038216114044189453}}, {"keyword": "And", "step_type": "given", "name": "I have access to the regulatory management system", "location": "features/regulatory_management.feature:8", "match": {"location": "features/steps/regulatory_steps.py:23", "arguments": []}, "result": {"status": "passed", "duration": 0.007035732269287109}}, {"keyword": "Given", "step_type": "given", "name": "there are regulations in multiple categories", "location": "features/regulatory_management.feature:59", "result": {"status": "undefined", "duration": 0}}, {"keyword": "When", "step_type": "when", "name": "I filter by \"Financial Services\" category", "location": "features/regulatory_management.feature:60"}, {"keyword": "Then", "step_type": "then", "name": "I should only see regulations in the \"Financial Services\" category", "location": "features/regulatory_management.feature:61"}], "status": "failed"}, {"type": "scenario", "keyword": "<PERSON><PERSON><PERSON>", "name": "Export regulations data", "tags": [], "location": "features/regulatory_management.feature:63", "steps": [{"keyword": "Given", "step_type": "given", "name": "the RegulationGuru application is running", "location": "features/regulatory_management.feature:7", "match": {"location": "features/steps/regulatory_steps.py:12", "arguments": []}, "result": {"status": "passed", "duration": 0.003968715667724609}}, {"keyword": "And", "step_type": "given", "name": "I have access to the regulatory management system", "location": "features/regulatory_management.feature:8", "match": {"location": "features/steps/regulatory_steps.py:23", "arguments": []}, "result": {"status": "passed", "duration": 0.0074503421783447266}}, {"keyword": "Given", "step_type": "given", "name": "I am on the regulatory dashboard", "location": "features/regulatory_management.feature:64", "match": {"location": "features/steps/regulatory_steps.py:81", "arguments": []}, "result": {"status": "passed", "duration": 0.0068874359130859375}}, {"keyword": "When", "step_type": "when", "name": "I click on \"Export Data\"", "location": "features/regulatory_management.feature:65", "result": {"status": "undefined", "duration": 0}}, {"keyword": "And", "step_type": "when", "name": "I select \"CSV\" format", "location": "features/regulatory_management.feature:66"}, {"keyword": "Then", "step_type": "then", "name": "I should receive a downloadable CSV file", "location": "features/regulatory_management.feature:67"}, {"keyword": "And", "step_type": "then", "name": "the file should contain regulation data", "location": "features/regulatory_management.feature:68"}], "status": "failed"}, {"type": "scenario", "keyword": "<PERSON><PERSON><PERSON>", "name": "View regulatory compliance status", "tags": [], "location": "features/regulatory_management.feature:70", "steps": [{"keyword": "Given", "step_type": "given", "name": "the RegulationGuru application is running", "location": "features/regulatory_management.feature:7", "match": {"location": "features/steps/regulatory_steps.py:12", "arguments": []}, "result": {"status": "passed", "duration": 0.003401041030883789}}, {"keyword": "And", "step_type": "given", "name": "I have access to the regulatory management system", "location": "features/regulatory_management.feature:8", "match": {"location": "features/steps/regulatory_steps.py:23", "arguments": []}, "result": {"status": "passed", "duration": 0.006944417953491211}}, {"keyword": "Given", "step_type": "given", "name": "I am on the regulatory dashboard", "location": "features/regulatory_management.feature:71", "match": {"location": "features/steps/regulatory_steps.py:81", "arguments": []}, "result": {"status": "passed", "duration": 0.006784915924072266}}, {"keyword": "When", "step_type": "when", "name": "I navigate to the compliance status section", "location": "features/regulatory_management.feature:72", "result": {"status": "undefined", "duration": 0}}, {"keyword": "Then", "step_type": "then", "name": "I should see compliance metrics", "location": "features/regulatory_management.feature:73"}, {"keyword": "And", "step_type": "then", "name": "I should see compliance percentages", "location": "features/regulatory_management.feature:74"}, {"keyword": "And", "step_type": "then", "name": "I should see any compliance gaps", "location": "features/regulatory_management.feature:75"}], "status": "failed"}, {"type": "scenario", "keyword": "<PERSON><PERSON><PERSON>", "name": "Manage regulatory alerts", "tags": [], "location": "features/regulatory_management.feature:77", "steps": [{"keyword": "Given", "step_type": "given", "name": "the RegulationGuru application is running", "location": "features/regulatory_management.feature:7", "match": {"location": "features/steps/regulatory_steps.py:12", "arguments": []}, "result": {"status": "passed", "duration": 0.003392457962036133}}, {"keyword": "And", "step_type": "given", "name": "I have access to the regulatory management system", "location": "features/regulatory_management.feature:8", "match": {"location": "features/steps/regulatory_steps.py:23", "arguments": []}, "result": {"status": "passed", "duration": 0.006951570510864258}}, {"keyword": "Given", "step_type": "given", "name": "I am on the regulatory dashboard", "location": "features/regulatory_management.feature:78", "match": {"location": "features/steps/regulatory_steps.py:81", "arguments": []}, "result": {"status": "passed", "duration": 0.0076220035552978516}}, {"keyword": "When", "step_type": "when", "name": "I navigate to the alerts section", "location": "features/regulatory_management.feature:79", "result": {"status": "undefined", "duration": 0}}, {"keyword": "Then", "step_type": "then", "name": "I should see active regulatory alerts", "location": "features/regulatory_management.feature:80"}, {"keyword": "And", "step_type": "then", "name": "I should be able to mark alerts as read", "location": "features/regulatory_management.feature:81"}, {"keyword": "And", "step_type": "then", "name": "I should be able to create new alerts", "location": "features/regulatory_management.feature:82"}], "status": "failed"}]}]