

+===========================================================================================================================================================================================+


DEPRECATED: this command (`check`) has been DEPRECATED, and will be unsupported beyond 01 June 2024.


We highly encourage switching to the new `scan` command which is easier to use, more powerful, and can be set up to mimic the deprecated command if required.


+===========================================================================================================================================================================================+


{
    "report_meta": {
        "scan_target": "environment",
        "scanned": [
            "/usr/lib/python310.zip",
            "/usr/lib/python3.10/lib-dynload",
            "/usr/local/lib/python3.10/dist-packages",
            "/usr/lib/python3.10",
            "/home/<USER>/.local/bin",
            "/usr/lib/python3/dist-packages",
            "/home/<USER>/.local/lib/python3.10/site-packages",
            "/home/<USER>/.local/lib/python3.10/site-packages/setuptools/_vendor"
        ],
        "scanned_full_path": [
            "/home/<USER>/.local/bin",
            "/usr/lib/python310.zip",
            "/usr/lib/python3.10",
            "/usr/lib/python3.10/lib-dynload",
            "/home/<USER>/.local/lib/python3.10/site-packages",
            "/usr/local/lib/python3.10/dist-packages",
            "/usr/lib/python3/dist-packages",
            "/home/<USER>/.local/lib/python3.10/site-packages/setuptools/_vendor"
        ],
        "target_languages": [
            "python"
        ],
        "policy_file": null,
        "policy_file_source": "local",
        "audit_and_monitor": false,
        "api_key": false,
        "account": "",
        "local_database_path": null,
        "safety_version": "3.5.2",
        "timestamp": "2025-06-10 16:04:59",
        "packages_found": 151,
        "vulnerabilities_found": 14,
        "vulnerabilities_ignored": 0,
        "remediations_recommended": 0,
        "telemetry": {
            "safety_options": {
                "json": {
                    "--json": 1
                }
            },
            "safety_version": "3.5.2",
            "safety_source": "cli",
            "os_type": "Linux",
            "os_release": "6.12.29-aug1",
            "os_description": "Linux-6.12.29-aug1-x86_64-with-glibc2.35",
            "python_version": "3.10.12",
            "safety_command": "check"
        },
        "git": {
            "branch": "main",
            "tag": "",
            "commit": "4a28f653802ad4a1d3b649cad0860771df5283b0",
            "dirty": "True",
            "origin": "https://github.com/forkrul/replit-10Baht-RegulationGuru"
        },
        "project": null,
        "json_version": "1.1",
        "remediations_attempted": 0,
        "remediations_completed": 0,
        "remediation_mode": "NON_INTERACTIVE"
    },
    "scanned_packages": {
        "markupsafe": {
            "name": "markupsafe",
            "version": "3.0.2",
            "requirements": [
                {
                    "raw": "markupsafe==3.0.2",
                    "extras": [],
                    "marker": null,
                    "name": "markupsafe",
                    "specifier": "==3.0.2",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "pyyaml": {
            "name": "pyyaml",
            "version": "6.0.2",
            "requirements": [
                {
                    "raw": "pyyaml==6.0.2",
                    "extras": [],
                    "marker": null,
                    "name": "pyyaml",
                    "specifier": "==6.0.2",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "annotated-types": {
            "name": "annotated-types",
            "version": "0.7.0",
            "requirements": [
                {
                    "raw": "annotated-types==0.7.0",
                    "extras": [],
                    "marker": null,
                    "name": "annotated-types",
                    "specifier": "==0.7.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "anyio": {
            "name": "anyio",
            "version": "4.9.0",
            "requirements": [
                {
                    "raw": "anyio==4.9.0",
                    "extras": [],
                    "marker": null,
                    "name": "anyio",
                    "specifier": "==4.9.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "async-timeout": {
            "name": "async-timeout",
            "version": "5.0.1",
            "requirements": [
                {
                    "raw": "async-timeout==5.0.1",
                    "extras": [],
                    "marker": null,
                    "name": "async-timeout",
                    "specifier": "==5.0.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "authlib": {
            "name": "authlib",
            "version": "1.6.0",
            "requirements": [
                {
                    "raw": "authlib==1.6.0",
                    "extras": [],
                    "marker": null,
                    "name": "authlib",
                    "specifier": "==1.6.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "babel": {
            "name": "babel",
            "version": "2.17.0",
            "requirements": [
                {
                    "raw": "babel==2.17.0",
                    "extras": [],
                    "marker": null,
                    "name": "babel",
                    "specifier": "==2.17.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "backports-datetime-fromisoformat": {
            "name": "backports-datetime-fromisoformat",
            "version": "2.0.3",
            "requirements": [
                {
                    "raw": "backports-datetime-fromisoformat==2.0.3",
                    "extras": [],
                    "marker": null,
                    "name": "backports-datetime-fromisoformat",
                    "specifier": "==2.0.3",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "bandit": {
            "name": "bandit",
            "version": "1.8.3",
            "requirements": [
                {
                    "raw": "bandit==1.8.3",
                    "extras": [],
                    "marker": null,
                    "name": "bandit",
                    "specifier": "==1.8.3",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "beautifulsoup4": {
            "name": "beautifulsoup4",
            "version": "4.13.4",
            "requirements": [
                {
                    "raw": "beautifulsoup4==4.13.4",
                    "extras": [],
                    "marker": null,
                    "name": "beautifulsoup4",
                    "specifier": "==4.13.4",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "behave": {
            "name": "behave",
            "version": "1.2.6",
            "requirements": [
                {
                    "raw": "behave==1.2.6",
                    "extras": [],
                    "marker": null,
                    "name": "behave",
                    "specifier": "==1.2.6",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "blis": {
            "name": "blis",
            "version": "1.3.0",
            "requirements": [
                {
                    "raw": "blis==1.3.0",
                    "extras": [],
                    "marker": null,
                    "name": "blis",
                    "specifier": "==1.3.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "branca": {
            "name": "branca",
            "version": "0.8.1",
            "requirements": [
                {
                    "raw": "branca==0.8.1",
                    "extras": [],
                    "marker": null,
                    "name": "branca",
                    "specifier": "==0.8.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "catalogue": {
            "name": "catalogue",
            "version": "2.0.10",
            "requirements": [
                {
                    "raw": "catalogue==2.0.10",
                    "extras": [],
                    "marker": null,
                    "name": "catalogue",
                    "specifier": "==2.0.10",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "certifi": {
            "name": "certifi",
            "version": "2025.4.26",
            "requirements": [
                {
                    "raw": "certifi==2025.4.26",
                    "extras": [],
                    "marker": null,
                    "name": "certifi",
                    "specifier": "==2025.4.26",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "charset-normalizer": {
            "name": "charset-normalizer",
            "version": "3.4.2",
            "requirements": [
                {
                    "raw": "charset-normalizer==3.4.2",
                    "extras": [],
                    "marker": null,
                    "name": "charset-normalizer",
                    "specifier": "==3.4.2",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "click": {
            "name": "click",
            "version": "8.1.8",
            "requirements": [
                {
                    "raw": "click==8.1.8",
                    "extras": [],
                    "marker": null,
                    "name": "click",
                    "specifier": "==8.1.8",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "cloudpathlib": {
            "name": "cloudpathlib",
            "version": "0.21.1",
            "requirements": [
                {
                    "raw": "cloudpathlib==0.21.1",
                    "extras": [],
                    "marker": null,
                    "name": "cloudpathlib",
                    "specifier": "==0.21.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "confection": {
            "name": "confection",
            "version": "0.1.5",
            "requirements": [
                {
                    "raw": "confection==0.1.5",
                    "extras": [],
                    "marker": null,
                    "name": "confection",
                    "specifier": "==0.1.5",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "coverage": {
            "name": "coverage",
            "version": "7.8.2",
            "requirements": [
                {
                    "raw": "coverage==7.8.2",
                    "extras": [],
                    "marker": null,
                    "name": "coverage",
                    "specifier": "==7.8.2",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "cymem": {
            "name": "cymem",
            "version": "2.0.11",
            "requirements": [
                {
                    "raw": "cymem==2.0.11",
                    "extras": [],
                    "marker": null,
                    "name": "cymem",
                    "specifier": "==2.0.11",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "dparse": {
            "name": "dparse",
            "version": "0.6.4",
            "requirements": [
                {
                    "raw": "dparse==0.6.4",
                    "extras": [],
                    "marker": null,
                    "name": "dparse",
                    "specifier": "==0.6.4",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "exceptiongroup": {
            "name": "exceptiongroup",
            "version": "1.3.0",
            "requirements": [
                {
                    "raw": "exceptiongroup==1.3.0",
                    "extras": [],
                    "marker": null,
                    "name": "exceptiongroup",
                    "specifier": "==1.3.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "execnet": {
            "name": "execnet",
            "version": "2.1.1",
            "requirements": [
                {
                    "raw": "execnet==2.1.1",
                    "extras": [],
                    "marker": null,
                    "name": "execnet",
                    "specifier": "==2.1.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "fastapi": {
            "name": "fastapi",
            "version": "0.115.12",
            "requirements": [
                {
                    "raw": "fastapi==0.115.12",
                    "extras": [],
                    "marker": null,
                    "name": "fastapi",
                    "specifier": "==0.115.12",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "filelock": {
            "name": "filelock",
            "version": "3.16.1",
            "requirements": [
                {
                    "raw": "filelock==3.16.1",
                    "extras": [],
                    "marker": null,
                    "name": "filelock",
                    "specifier": "==3.16.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "folium": {
            "name": "folium",
            "version": "0.19.7",
            "requirements": [
                {
                    "raw": "folium==0.19.7",
                    "extras": [],
                    "marker": null,
                    "name": "folium",
                    "specifier": "==0.19.7",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "gherkin-official": {
            "name": "gherkin-official",
            "version": "29.0.0",
            "requirements": [
                {
                    "raw": "gherkin-official==29.0.0",
                    "extras": [],
                    "marker": null,
                    "name": "gherkin-official",
                    "specifier": "==29.0.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "greenlet": {
            "name": "greenlet",
            "version": "3.2.3",
            "requirements": [
                {
                    "raw": "greenlet==3.2.3",
                    "extras": [],
                    "marker": null,
                    "name": "greenlet",
                    "specifier": "==3.2.3",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "h11": {
            "name": "h11",
            "version": "0.16.0",
            "requirements": [
                {
                    "raw": "h11==0.16.0",
                    "extras": [],
                    "marker": null,
                    "name": "h11",
                    "specifier": "==0.16.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "httpcore": {
            "name": "httpcore",
            "version": "1.0.9",
            "requirements": [
                {
                    "raw": "httpcore==1.0.9",
                    "extras": [],
                    "marker": null,
                    "name": "httpcore",
                    "specifier": "==1.0.9",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "httpx": {
            "name": "httpx",
            "version": "0.28.1",
            "requirements": [
                {
                    "raw": "httpx==0.28.1",
                    "extras": [],
                    "marker": null,
                    "name": "httpx",
                    "specifier": "==0.28.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "idna": {
            "name": "idna",
            "version": "3.10",
            "requirements": [
                {
                    "raw": "idna==3.10",
                    "extras": [],
                    "marker": null,
                    "name": "idna",
                    "specifier": "==3.10",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "itsdangerous": {
            "name": "itsdangerous",
            "version": "2.2.0",
            "requirements": [
                {
                    "raw": "itsdangerous==2.2.0",
                    "extras": [],
                    "marker": null,
                    "name": "itsdangerous",
                    "specifier": "==2.2.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "jinja2": {
            "name": "jinja2",
            "version": "3.1.6",
            "requirements": [
                {
                    "raw": "jinja2==3.1.6",
                    "extras": [],
                    "marker": null,
                    "name": "jinja2",
                    "specifier": "==3.1.6",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "joblib": {
            "name": "joblib",
            "version": "1.5.1",
            "requirements": [
                {
                    "raw": "joblib==1.5.1",
                    "extras": [],
                    "marker": null,
                    "name": "joblib",
                    "specifier": "==1.5.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "langcodes": {
            "name": "langcodes",
            "version": "3.5.0",
            "requirements": [
                {
                    "raw": "langcodes==3.5.0",
                    "extras": [],
                    "marker": null,
                    "name": "langcodes",
                    "specifier": "==3.5.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "language-data": {
            "name": "language-data",
            "version": "1.3.0",
            "requirements": [
                {
                    "raw": "language-data==1.3.0",
                    "extras": [],
                    "marker": null,
                    "name": "language-data",
                    "specifier": "==1.3.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "mako": {
            "name": "mako",
            "version": "1.3.10",
            "requirements": [
                {
                    "raw": "mako==1.3.10",
                    "extras": [],
                    "marker": null,
                    "name": "mako",
                    "specifier": "==1.3.10",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "marisa-trie": {
            "name": "marisa-trie",
            "version": "1.2.1",
            "requirements": [
                {
                    "raw": "marisa-trie==1.2.1",
                    "extras": [],
                    "marker": null,
                    "name": "marisa-trie",
                    "specifier": "==1.2.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "markdown-it-py": {
            "name": "markdown-it-py",
            "version": "3.0.0",
            "requirements": [
                {
                    "raw": "markdown-it-py==3.0.0",
                    "extras": [],
                    "marker": null,
                    "name": "markdown-it-py",
                    "specifier": "==3.0.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "marshmallow": {
            "name": "marshmallow",
            "version": "4.0.0",
            "requirements": [
                {
                    "raw": "marshmallow==4.0.0",
                    "extras": [],
                    "marker": null,
                    "name": "marshmallow",
                    "specifier": "==4.0.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "mdurl": {
            "name": "mdurl",
            "version": "0.1.2",
            "requirements": [
                {
                    "raw": "mdurl==0.1.2",
                    "extras": [],
                    "marker": null,
                    "name": "mdurl",
                    "specifier": "==0.1.2",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "murmurhash": {
            "name": "murmurhash",
            "version": "1.0.13",
            "requirements": [
                {
                    "raw": "murmurhash==1.0.13",
                    "extras": [],
                    "marker": null,
                    "name": "murmurhash",
                    "specifier": "==1.0.13",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "nltk": {
            "name": "nltk",
            "version": "3.9.1",
            "requirements": [
                {
                    "raw": "nltk==3.9.1",
                    "extras": [],
                    "marker": null,
                    "name": "nltk",
                    "specifier": "==3.9.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "numpy": {
            "name": "numpy",
            "version": "2.2.6",
            "requirements": [
                {
                    "raw": "numpy==2.2.6",
                    "extras": [],
                    "marker": null,
                    "name": "numpy",
                    "specifier": "==2.2.6",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "pandas": {
            "name": "pandas",
            "version": "2.3.0",
            "requirements": [
                {
                    "raw": "pandas==2.3.0",
                    "extras": [],
                    "marker": null,
                    "name": "pandas",
                    "specifier": "==2.3.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "parse": {
            "name": "parse",
            "version": "1.20.2",
            "requirements": [
                {
                    "raw": "parse==1.20.2",
                    "extras": [],
                    "marker": null,
                    "name": "parse",
                    "specifier": "==1.20.2",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "parse-type": {
            "name": "parse-type",
            "version": "0.6.4",
            "requirements": [
                {
                    "raw": "parse-type==0.6.4",
                    "extras": [],
                    "marker": null,
                    "name": "parse-type",
                    "specifier": "==0.6.4",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "pbr": {
            "name": "pbr",
            "version": "6.1.1",
            "requirements": [
                {
                    "raw": "pbr==6.1.1",
                    "extras": [],
                    "marker": null,
                    "name": "pbr",
                    "specifier": "==6.1.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "playwright": {
            "name": "playwright",
            "version": "1.52.0",
            "requirements": [
                {
                    "raw": "playwright==1.52.0",
                    "extras": [],
                    "marker": null,
                    "name": "playwright",
                    "specifier": "==1.52.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "pluggy": {
            "name": "pluggy",
            "version": "1.6.0",
            "requirements": [
                {
                    "raw": "pluggy==1.6.0",
                    "extras": [],
                    "marker": null,
                    "name": "pluggy",
                    "specifier": "==1.6.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "preshed": {
            "name": "preshed",
            "version": "3.0.10",
            "requirements": [
                {
                    "raw": "preshed==3.0.10",
                    "extras": [],
                    "marker": null,
                    "name": "preshed",
                    "specifier": "==3.0.10",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "psutil": {
            "name": "psutil",
            "version": "6.1.1",
            "requirements": [
                {
                    "raw": "psutil==6.1.1",
                    "extras": [],
                    "marker": null,
                    "name": "psutil",
                    "specifier": "==6.1.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "psycopg2-binary": {
            "name": "psycopg2-binary",
            "version": "2.9.10",
            "requirements": [
                {
                    "raw": "psycopg2-binary==2.9.10",
                    "extras": [],
                    "marker": null,
                    "name": "psycopg2-binary",
                    "specifier": "==2.9.10",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "pydantic": {
            "name": "pydantic",
            "version": "2.9.2",
            "requirements": [
                {
                    "raw": "pydantic==2.9.2",
                    "extras": [],
                    "marker": null,
                    "name": "pydantic",
                    "specifier": "==2.9.2",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "pydantic-core": {
            "name": "pydantic-core",
            "version": "2.23.4",
            "requirements": [
                {
                    "raw": "pydantic-core==2.23.4",
                    "extras": [],
                    "marker": null,
                    "name": "pydantic-core",
                    "specifier": "==2.23.4",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "pyee": {
            "name": "pyee",
            "version": "13.0.0",
            "requirements": [
                {
                    "raw": "pyee==13.0.0",
                    "extras": [],
                    "marker": null,
                    "name": "pyee",
                    "specifier": "==13.0.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "pygments": {
            "name": "pygments",
            "version": "2.19.1",
            "requirements": [
                {
                    "raw": "pygments==2.19.1",
                    "extras": [],
                    "marker": null,
                    "name": "pygments",
                    "specifier": "==2.19.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "pypdf": {
            "name": "pypdf",
            "version": "5.6.0",
            "requirements": [
                {
                    "raw": "pypdf==5.6.0",
                    "extras": [],
                    "marker": null,
                    "name": "pypdf",
                    "specifier": "==5.6.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "pypdf2": {
            "name": "pypdf2",
            "version": "3.0.1",
            "requirements": [
                {
                    "raw": "pypdf2==3.0.1",
                    "extras": [],
                    "marker": null,
                    "name": "pypdf2",
                    "specifier": "==3.0.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "pytest": {
            "name": "pytest",
            "version": "8.4.0",
            "requirements": [
                {
                    "raw": "pytest==8.4.0",
                    "extras": [],
                    "marker": null,
                    "name": "pytest",
                    "specifier": "==8.4.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "pytest-asyncio": {
            "name": "pytest-asyncio",
            "version": "1.0.0",
            "requirements": [
                {
                    "raw": "pytest-asyncio==1.0.0",
                    "extras": [],
                    "marker": null,
                    "name": "pytest-asyncio",
                    "specifier": "==1.0.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "pytest-base-url": {
            "name": "pytest-base-url",
            "version": "2.1.0",
            "requirements": [
                {
                    "raw": "pytest-base-url==2.1.0",
                    "extras": [],
                    "marker": null,
                    "name": "pytest-base-url",
                    "specifier": "==2.1.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "pytest-bdd": {
            "name": "pytest-bdd",
            "version": "8.1.0",
            "requirements": [
                {
                    "raw": "pytest-bdd==8.1.0",
                    "extras": [],
                    "marker": null,
                    "name": "pytest-bdd",
                    "specifier": "==8.1.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "pytest-cov": {
            "name": "pytest-cov",
            "version": "6.1.1",
            "requirements": [
                {
                    "raw": "pytest-cov==6.1.1",
                    "extras": [],
                    "marker": null,
                    "name": "pytest-cov",
                    "specifier": "==6.1.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "pytest-html": {
            "name": "pytest-html",
            "version": "4.1.1",
            "requirements": [
                {
                    "raw": "pytest-html==4.1.1",
                    "extras": [],
                    "marker": null,
                    "name": "pytest-html",
                    "specifier": "==4.1.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "pytest-metadata": {
            "name": "pytest-metadata",
            "version": "3.1.1",
            "requirements": [
                {
                    "raw": "pytest-metadata==3.1.1",
                    "extras": [],
                    "marker": null,
                    "name": "pytest-metadata",
                    "specifier": "==3.1.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "pytest-playwright": {
            "name": "pytest-playwright",
            "version": "0.7.0",
            "requirements": [
                {
                    "raw": "pytest-playwright==0.7.0",
                    "extras": [],
                    "marker": null,
                    "name": "pytest-playwright",
                    "specifier": "==0.7.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "pytest-xdist": {
            "name": "pytest-xdist",
            "version": "3.7.0",
            "requirements": [
                {
                    "raw": "pytest-xdist==3.7.0",
                    "extras": [],
                    "marker": null,
                    "name": "pytest-xdist",
                    "specifier": "==3.7.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "python-dateutil": {
            "name": "python-dateutil",
            "version": "2.9.0.post0",
            "requirements": [
                {
                    "raw": "python-dateutil==2.9.0.post0",
                    "extras": [],
                    "marker": null,
                    "name": "python-dateutil",
                    "specifier": "==2.9.0.post0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "python-multipart": {
            "name": "python-multipart",
            "version": "0.0.20",
            "requirements": [
                {
                    "raw": "python-multipart==0.0.20",
                    "extras": [],
                    "marker": null,
                    "name": "python-multipart",
                    "specifier": "==0.0.20",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "python-slugify": {
            "name": "python-slugify",
            "version": "8.0.4",
            "requirements": [
                {
                    "raw": "python-slugify==8.0.4",
                    "extras": [],
                    "marker": null,
                    "name": "python-slugify",
                    "specifier": "==8.0.4",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "pytz": {
            "name": "pytz",
            "version": "2025.2",
            "requirements": [
                {
                    "raw": "pytz==2025.2",
                    "extras": [],
                    "marker": null,
                    "name": "pytz",
                    "specifier": "==2025.2",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "redis": {
            "name": "redis",
            "version": "6.2.0",
            "requirements": [
                {
                    "raw": "redis==6.2.0",
                    "extras": [],
                    "marker": null,
                    "name": "redis",
                    "specifier": "==6.2.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "regex": {
            "name": "regex",
            "version": "2024.11.6",
            "requirements": [
                {
                    "raw": "regex==2024.11.6",
                    "extras": [],
                    "marker": null,
                    "name": "regex",
                    "specifier": "==2024.11.6",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "requests": {
            "name": "requests",
            "version": "2.32.4",
            "requirements": [
                {
                    "raw": "requests==2.32.4",
                    "extras": [],
                    "marker": null,
                    "name": "requests",
                    "specifier": "==2.32.4",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "rich": {
            "name": "rich",
            "version": "14.0.0",
            "requirements": [
                {
                    "raw": "rich==14.0.0",
                    "extras": [],
                    "marker": null,
                    "name": "rich",
                    "specifier": "==14.0.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "ruamel.yaml": {
            "name": "ruamel.yaml",
            "version": "0.18.14",
            "requirements": [
                {
                    "raw": "ruamel.yaml==0.18.14",
                    "extras": [],
                    "marker": null,
                    "name": "ruamel.yaml",
                    "specifier": "==0.18.14",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "ruamel.yaml.clib": {
            "name": "ruamel.yaml.clib",
            "version": "0.2.12",
            "requirements": [
                {
                    "raw": "ruamel.yaml.clib==0.2.12",
                    "extras": [],
                    "marker": null,
                    "name": "ruamel.yaml.clib",
                    "specifier": "==0.2.12",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "safety": {
            "name": "safety",
            "version": "3.5.2",
            "requirements": [
                {
                    "raw": "safety==3.5.2",
                    "extras": [],
                    "marker": null,
                    "name": "safety",
                    "specifier": "==3.5.2",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "safety-schemas": {
            "name": "safety-schemas",
            "version": "0.0.14",
            "requirements": [
                {
                    "raw": "safety-schemas==0.0.14",
                    "extras": [],
                    "marker": null,
                    "name": "safety-schemas",
                    "specifier": "==0.0.14",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "setuptools": {
            "name": "setuptools",
            "version": "80.9.0",
            "requirements": [
                {
                    "raw": "setuptools==80.9.0",
                    "extras": [],
                    "marker": null,
                    "name": "setuptools",
                    "specifier": "==80.9.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "shellingham": {
            "name": "shellingham",
            "version": "1.5.4",
            "requirements": [
                {
                    "raw": "shellingham==1.5.4",
                    "extras": [],
                    "marker": null,
                    "name": "shellingham",
                    "specifier": "==1.5.4",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "smart-open": {
            "name": "smart-open",
            "version": "7.1.0",
            "requirements": [
                {
                    "raw": "smart-open==7.1.0",
                    "extras": [],
                    "marker": null,
                    "name": "smart-open",
                    "specifier": "==7.1.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "sniffio": {
            "name": "sniffio",
            "version": "1.3.1",
            "requirements": [
                {
                    "raw": "sniffio==1.3.1",
                    "extras": [],
                    "marker": null,
                    "name": "sniffio",
                    "specifier": "==1.3.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "soupsieve": {
            "name": "soupsieve",
            "version": "2.7",
            "requirements": [
                {
                    "raw": "soupsieve==2.7",
                    "extras": [],
                    "marker": null,
                    "name": "soupsieve",
                    "specifier": "==2.7",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "spacy": {
            "name": "spacy",
            "version": "3.8.7",
            "requirements": [
                {
                    "raw": "spacy==3.8.7",
                    "extras": [],
                    "marker": null,
                    "name": "spacy",
                    "specifier": "==3.8.7",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "spacy-legacy": {
            "name": "spacy-legacy",
            "version": "3.0.12",
            "requirements": [
                {
                    "raw": "spacy-legacy==3.0.12",
                    "extras": [],
                    "marker": null,
                    "name": "spacy-legacy",
                    "specifier": "==3.0.12",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "spacy-loggers": {
            "name": "spacy-loggers",
            "version": "1.0.5",
            "requirements": [
                {
                    "raw": "spacy-loggers==1.0.5",
                    "extras": [],
                    "marker": null,
                    "name": "spacy-loggers",
                    "specifier": "==1.0.5",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "sqlalchemy": {
            "name": "sqlalchemy",
            "version": "2.0.41",
            "requirements": [
                {
                    "raw": "sqlalchemy==2.0.41",
                    "extras": [],
                    "marker": null,
                    "name": "sqlalchemy",
                    "specifier": "==2.0.41",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "srsly": {
            "name": "srsly",
            "version": "2.5.1",
            "requirements": [
                {
                    "raw": "srsly==2.5.1",
                    "extras": [],
                    "marker": null,
                    "name": "srsly",
                    "specifier": "==2.5.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "starlette": {
            "name": "starlette",
            "version": "0.46.2",
            "requirements": [
                {
                    "raw": "starlette==0.46.2",
                    "extras": [],
                    "marker": null,
                    "name": "starlette",
                    "specifier": "==0.46.2",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "starlette-admin": {
            "name": "starlette-admin",
            "version": "0.15.1",
            "requirements": [
                {
                    "raw": "starlette-admin==0.15.1",
                    "extras": [],
                    "marker": null,
                    "name": "starlette-admin",
                    "specifier": "==0.15.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "stevedore": {
            "name": "stevedore",
            "version": "5.4.1",
            "requirements": [
                {
                    "raw": "stevedore==5.4.1",
                    "extras": [],
                    "marker": null,
                    "name": "stevedore",
                    "specifier": "==5.4.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "tenacity": {
            "name": "tenacity",
            "version": "9.1.2",
            "requirements": [
                {
                    "raw": "tenacity==9.1.2",
                    "extras": [],
                    "marker": null,
                    "name": "tenacity",
                    "specifier": "==9.1.2",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "text-unidecode": {
            "name": "text-unidecode",
            "version": "1.3",
            "requirements": [
                {
                    "raw": "text-unidecode==1.3",
                    "extras": [],
                    "marker": null,
                    "name": "text-unidecode",
                    "specifier": "==1.3",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "thinc": {
            "name": "thinc",
            "version": "8.3.6",
            "requirements": [
                {
                    "raw": "thinc==8.3.6",
                    "extras": [],
                    "marker": null,
                    "name": "thinc",
                    "specifier": "==8.3.6",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "tomli": {
            "name": "tomli",
            "version": "2.2.1",
            "requirements": [
                {
                    "raw": "tomli==2.2.1",
                    "extras": [],
                    "marker": null,
                    "name": "tomli",
                    "specifier": "==2.2.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "tomlkit": {
            "name": "tomlkit",
            "version": "0.13.3",
            "requirements": [
                {
                    "raw": "tomlkit==0.13.3",
                    "extras": [],
                    "marker": null,
                    "name": "tomlkit",
                    "specifier": "==0.13.3",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "tqdm": {
            "name": "tqdm",
            "version": "4.67.1",
            "requirements": [
                {
                    "raw": "tqdm==4.67.1",
                    "extras": [],
                    "marker": null,
                    "name": "tqdm",
                    "specifier": "==4.67.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "typer": {
            "name": "typer",
            "version": "0.16.0",
            "requirements": [
                {
                    "raw": "typer==0.16.0",
                    "extras": [],
                    "marker": null,
                    "name": "typer",
                    "specifier": "==0.16.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "typing-extensions": {
            "name": "typing-extensions",
            "version": "4.14.0",
            "requirements": [
                {
                    "raw": "typing-extensions==4.14.0",
                    "extras": [],
                    "marker": null,
                    "name": "typing-extensions",
                    "specifier": "==4.14.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "typing-inspection": {
            "name": "typing-inspection",
            "version": "0.4.1",
            "requirements": [
                {
                    "raw": "typing-inspection==0.4.1",
                    "extras": [],
                    "marker": null,
                    "name": "typing-inspection",
                    "specifier": "==0.4.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "tzdata": {
            "name": "tzdata",
            "version": "2025.2",
            "requirements": [
                {
                    "raw": "tzdata==2025.2",
                    "extras": [],
                    "marker": null,
                    "name": "tzdata",
                    "specifier": "==2025.2",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "urllib3": {
            "name": "urllib3",
            "version": "2.4.0",
            "requirements": [
                {
                    "raw": "urllib3==2.4.0",
                    "extras": [],
                    "marker": null,
                    "name": "urllib3",
                    "specifier": "==2.4.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "uvicorn": {
            "name": "uvicorn",
            "version": "0.34.3",
            "requirements": [
                {
                    "raw": "uvicorn==0.34.3",
                    "extras": [],
                    "marker": null,
                    "name": "uvicorn",
                    "specifier": "==0.34.3",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "wasabi": {
            "name": "wasabi",
            "version": "1.1.3",
            "requirements": [
                {
                    "raw": "wasabi==1.1.3",
                    "extras": [],
                    "marker": null,
                    "name": "wasabi",
                    "specifier": "==1.1.3",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "weasel": {
            "name": "weasel",
            "version": "0.4.1",
            "requirements": [
                {
                    "raw": "weasel==0.4.1",
                    "extras": [],
                    "marker": null,
                    "name": "weasel",
                    "specifier": "==0.4.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "wrapt": {
            "name": "wrapt",
            "version": "1.17.2",
            "requirements": [
                {
                    "raw": "wrapt==1.17.2",
                    "extras": [],
                    "marker": null,
                    "name": "wrapt",
                    "specifier": "==1.17.2",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "xyzservices": {
            "name": "xyzservices",
            "version": "2025.4.0",
            "requirements": [
                {
                    "raw": "xyzservices==2025.4.0",
                    "extras": [],
                    "marker": null,
                    "name": "xyzservices",
                    "specifier": "==2025.4.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ]
        },
        "pygobject": {
            "name": "pygobject",
            "version": "3.42.1",
            "requirements": [
                {
                    "raw": "pygobject==3.42.1",
                    "extras": [],
                    "marker": null,
                    "name": "pygobject",
                    "specifier": "==3.42.1",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "pyjwt": {
            "name": "pyjwt",
            "version": "2.3.0",
            "requirements": [
                {
                    "raw": "pyjwt==2.3.0",
                    "extras": [],
                    "marker": null,
                    "name": "pyjwt",
                    "specifier": "==2.3.0",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "secretstorage": {
            "name": "secretstorage",
            "version": "3.3.1",
            "requirements": [
                {
                    "raw": "secretstorage==3.3.1",
                    "extras": [],
                    "marker": null,
                    "name": "secretstorage",
                    "specifier": "==3.3.1",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "attrs": {
            "name": "attrs",
            "version": "21.2.0",
            "requirements": [
                {
                    "raw": "attrs==21.2.0",
                    "extras": [],
                    "marker": null,
                    "name": "attrs",
                    "specifier": "==21.2.0",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "blinker": {
            "name": "blinker",
            "version": "1.4",
            "requirements": [
                {
                    "raw": "blinker==1.4",
                    "extras": [],
                    "marker": null,
                    "name": "blinker",
                    "specifier": "==1.4",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "cryptography": {
            "name": "cryptography",
            "version": "3.4.8",
            "requirements": [
                {
                    "raw": "cryptography==3.4.8",
                    "extras": [],
                    "marker": null,
                    "name": "cryptography",
                    "specifier": "==3.4.8",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "dbus-python": {
            "name": "dbus-python",
            "version": "1.2.18",
            "requirements": [
                {
                    "raw": "dbus-python==1.2.18",
                    "extras": [],
                    "marker": null,
                    "name": "dbus-python",
                    "specifier": "==1.2.18",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "distro": {
            "name": "distro",
            "version": "1.7.0",
            "requirements": [
                {
                    "raw": "distro==1.7.0",
                    "extras": [],
                    "marker": null,
                    "name": "distro",
                    "specifier": "==1.7.0",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "distro-info": {
            "name": "distro-info",
            "version": "1.1+ubuntu0.2",
            "requirements": [
                {
                    "raw": "distro-info==1.1+ubuntu0.2",
                    "extras": [],
                    "marker": null,
                    "name": "distro-info",
                    "specifier": "==1.1+ubuntu0.2",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "httplib2": {
            "name": "httplib2",
            "version": "0.20.2",
            "requirements": [
                {
                    "raw": "httplib2==0.20.2",
                    "extras": [],
                    "marker": null,
                    "name": "httplib2",
                    "specifier": "==0.20.2",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "importlib-metadata": {
            "name": "importlib-metadata",
            "version": "4.6.4",
            "requirements": [
                {
                    "raw": "importlib-metadata==4.6.4",
                    "extras": [],
                    "marker": null,
                    "name": "importlib-metadata",
                    "specifier": "==4.6.4",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "iniconfig": {
            "name": "iniconfig",
            "version": "1.1.1",
            "requirements": [
                {
                    "raw": "iniconfig==1.1.1",
                    "extras": [],
                    "marker": null,
                    "name": "iniconfig",
                    "specifier": "==1.1.1",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "jeepney": {
            "name": "jeepney",
            "version": "0.7.1",
            "requirements": [
                {
                    "raw": "jeepney==0.7.1",
                    "extras": [],
                    "marker": null,
                    "name": "jeepney",
                    "specifier": "==0.7.1",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "keyring": {
            "name": "keyring",
            "version": "23.5.0",
            "requirements": [
                {
                    "raw": "keyring==23.5.0",
                    "extras": [],
                    "marker": null,
                    "name": "keyring",
                    "specifier": "==23.5.0",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "launchpadlib": {
            "name": "launchpadlib",
            "version": "1.10.16",
            "requirements": [
                {
                    "raw": "launchpadlib==1.10.16",
                    "extras": [],
                    "marker": null,
                    "name": "launchpadlib",
                    "specifier": "==1.10.16",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "lazr.restfulclient": {
            "name": "lazr.restfulclient",
            "version": "0.14.4",
            "requirements": [
                {
                    "raw": "lazr.restfulclient==0.14.4",
                    "extras": [],
                    "marker": null,
                    "name": "lazr.restfulclient",
                    "specifier": "==0.14.4",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "lazr.uri": {
            "name": "lazr.uri",
            "version": "1.0.6",
            "requirements": [
                {
                    "raw": "lazr.uri==1.0.6",
                    "extras": [],
                    "marker": null,
                    "name": "lazr.uri",
                    "specifier": "==1.0.6",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "more-itertools": {
            "name": "more-itertools",
            "version": "8.10.0",
            "requirements": [
                {
                    "raw": "more-itertools==8.10.0",
                    "extras": [],
                    "marker": null,
                    "name": "more-itertools",
                    "specifier": "==8.10.0",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "oauthlib": {
            "name": "oauthlib",
            "version": "3.2.0",
            "requirements": [
                {
                    "raw": "oauthlib==3.2.0",
                    "extras": [],
                    "marker": null,
                    "name": "oauthlib",
                    "specifier": "==3.2.0",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "packaging": {
            "name": "packaging",
            "version": "21.3",
            "requirements": [
                {
                    "raw": "packaging==21.3",
                    "extras": [],
                    "marker": null,
                    "name": "packaging",
                    "specifier": "==21.3",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "pip": {
            "name": "pip",
            "version": "22.0.2",
            "requirements": [
                {
                    "raw": "pip==22.0.2",
                    "extras": [],
                    "marker": null,
                    "name": "pip",
                    "specifier": "==22.0.2",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "py": {
            "name": "py",
            "version": "1.10.0",
            "requirements": [
                {
                    "raw": "py==1.10.0",
                    "extras": [],
                    "marker": null,
                    "name": "py",
                    "specifier": "==1.10.0",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "pyparsing": {
            "name": "pyparsing",
            "version": "2.4.7",
            "requirements": [
                {
                    "raw": "pyparsing==2.4.7",
                    "extras": [],
                    "marker": null,
                    "name": "pyparsing",
                    "specifier": "==2.4.7",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "python-apt": {
            "name": "python-apt",
            "version": "2.4.0+ubuntu4",
            "requirements": [
                {
                    "raw": "python-apt==2.4.0+ubuntu4",
                    "extras": [],
                    "marker": null,
                    "name": "python-apt",
                    "specifier": "==2.4.0+ubuntu4",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "six": {
            "name": "six",
            "version": "1.16.0",
            "requirements": [
                {
                    "raw": "six==1.16.0",
                    "extras": [],
                    "marker": null,
                    "name": "six",
                    "specifier": "==1.16.0",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "ssh-import-id": {
            "name": "ssh-import-id",
            "version": "5.11",
            "requirements": [
                {
                    "raw": "ssh-import-id==5.11",
                    "extras": [],
                    "marker": null,
                    "name": "ssh-import-id",
                    "specifier": "==5.11",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "toml": {
            "name": "toml",
            "version": "0.10.2",
            "requirements": [
                {
                    "raw": "toml==0.10.2",
                    "extras": [],
                    "marker": null,
                    "name": "toml",
                    "specifier": "==0.10.2",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "unattended-upgrades": {
            "name": "unattended-upgrades",
            "version": "0.1",
            "requirements": [
                {
                    "raw": "unattended-upgrades==0.1",
                    "extras": [],
                    "marker": null,
                    "name": "unattended-upgrades",
                    "specifier": "==0.1",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "wadllib": {
            "name": "wadllib",
            "version": "1.3.6",
            "requirements": [
                {
                    "raw": "wadllib==1.3.6",
                    "extras": [],
                    "marker": null,
                    "name": "wadllib",
                    "specifier": "==1.3.6",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "wheel": {
            "name": "wheel",
            "version": "0.37.1",
            "requirements": [
                {
                    "raw": "wheel==0.37.1",
                    "extras": [],
                    "marker": null,
                    "name": "wheel",
                    "specifier": "==0.37.1",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "zipp": {
            "name": "zipp",
            "version": "1.0.0",
            "requirements": [
                {
                    "raw": "zipp==1.0.0",
                    "extras": [],
                    "marker": null,
                    "name": "zipp",
                    "specifier": "==1.0.0",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ]
        },
        "autocommand": {
            "name": "autocommand",
            "version": "2.2.2",
            "requirements": [
                {
                    "raw": "autocommand==2.2.2",
                    "extras": [],
                    "marker": null,
                    "name": "autocommand",
                    "specifier": "==2.2.2",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages/setuptools/_vendor"
                }
            ]
        },
        "backports.tarfile": {
            "name": "backports.tarfile",
            "version": "1.2.0",
            "requirements": [
                {
                    "raw": "backports.tarfile==1.2.0",
                    "extras": [],
                    "marker": null,
                    "name": "backports.tarfile",
                    "specifier": "==1.2.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages/setuptools/_vendor"
                }
            ]
        },
        "inflect": {
            "name": "inflect",
            "version": "7.3.1",
            "requirements": [
                {
                    "raw": "inflect==7.3.1",
                    "extras": [],
                    "marker": null,
                    "name": "inflect",
                    "specifier": "==7.3.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages/setuptools/_vendor"
                }
            ]
        },
        "jaraco.collections": {
            "name": "jaraco.collections",
            "version": "5.1.0",
            "requirements": [
                {
                    "raw": "jaraco.collections==5.1.0",
                    "extras": [],
                    "marker": null,
                    "name": "jaraco.collections",
                    "specifier": "==5.1.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages/setuptools/_vendor"
                }
            ]
        },
        "jaraco.context": {
            "name": "jaraco.context",
            "version": "5.3.0",
            "requirements": [
                {
                    "raw": "jaraco.context==5.3.0",
                    "extras": [],
                    "marker": null,
                    "name": "jaraco.context",
                    "specifier": "==5.3.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages/setuptools/_vendor"
                }
            ]
        },
        "jaraco.functools": {
            "name": "jaraco.functools",
            "version": "4.0.1",
            "requirements": [
                {
                    "raw": "jaraco.functools==4.0.1",
                    "extras": [],
                    "marker": null,
                    "name": "jaraco.functools",
                    "specifier": "==4.0.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages/setuptools/_vendor"
                }
            ]
        },
        "jaraco.text": {
            "name": "jaraco.text",
            "version": "3.12.1",
            "requirements": [
                {
                    "raw": "jaraco.text==3.12.1",
                    "extras": [],
                    "marker": null,
                    "name": "jaraco.text",
                    "specifier": "==3.12.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages/setuptools/_vendor"
                }
            ]
        },
        "platformdirs": {
            "name": "platformdirs",
            "version": "4.2.2",
            "requirements": [
                {
                    "raw": "platformdirs==4.2.2",
                    "extras": [],
                    "marker": null,
                    "name": "platformdirs",
                    "specifier": "==4.2.2",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages/setuptools/_vendor"
                }
            ]
        },
        "typeguard": {
            "name": "typeguard",
            "version": "4.3.0",
            "requirements": [
                {
                    "raw": "typeguard==4.3.0",
                    "extras": [],
                    "marker": null,
                    "name": "typeguard",
                    "specifier": "==4.3.0",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages/setuptools/_vendor"
                }
            ]
        }
    },
    "affected_packages": {
        "zipp": {
            "name": "zipp",
            "version": "1.0.0",
            "requirements": [
                {
                    "raw": "zipp==1.0.0",
                    "extras": [],
                    "marker": null,
                    "name": "zipp",
                    "specifier": "==1.0.0",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ],
            "found": null,
            "insecure_versions": [
                "0.1.0",
                "0.2.0",
                "0.2.1",
                "0.3.0",
                "0.3.1",
                "0.3.2",
                "0.3.3",
                "0.4.0",
                "0.5.0",
                "0.5.1",
                "0.5.2",
                "0.6.0",
                "1.0.0",
                "1.1.0",
                "1.1.1",
                "1.2.0",
                "2.0.0",
                "2.0.1",
                "2.1.0",
                "2.2.0",
                "2.2.1",
                "3.0.0",
                "3.1.0",
                "3.10.0",
                "3.11.0",
                "3.12.0",
                "3.12.1",
                "3.13.0",
                "3.14.0",
                "3.15.0",
                "3.16.0",
                "3.16.1",
                "3.16.2",
                "3.17.0",
                "3.18.0",
                "3.18.1",
                "3.18.2",
                "3.19.0",
                "3.2.0",
                "3.3.0",
                "3.3.1",
                "3.3.2",
                "3.4.0",
                "3.4.1",
                "3.4.2",
                "3.5.0",
                "3.5.1",
                "3.6.0",
                "3.7.0",
                "3.8.0",
                "3.8.1",
                "3.9.0",
                "3.9.1"
            ],
            "secure_versions": [
                "3.22.0",
                "3.21.0",
                "3.20.2",
                "3.20.1",
                "3.20.0",
                "3.19.3",
                "3.19.2",
                "3.19.1"
            ],
            "latest_version_without_known_vulnerabilities": "3.22.0",
            "latest_version": "3.22.0",
            "more_info_url": "https://data.safetycli.com/p/pypi/zipp/97c/"
        },
        "wheel": {
            "name": "wheel",
            "version": "0.37.1",
            "requirements": [
                {
                    "raw": "wheel==0.37.1",
                    "extras": [],
                    "marker": null,
                    "name": "wheel",
                    "specifier": "==0.37.1",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ],
            "found": null,
            "insecure_versions": [
                "0.1",
                "0.10.0",
                "0.10.1",
                "0.10.2",
                "0.10.3",
                "0.11.0",
                "0.12.0",
                "0.13.0",
                "0.14.0",
                "0.15.0",
                "0.16.0",
                "0.17.0",
                "0.18.0",
                "0.19.0",
                "0.2",
                "0.21.0",
                "0.22.0",
                "0.23.0",
                "0.24.0",
                "0.25.0",
                "0.26.0",
                "0.27.0",
                "0.28.0",
                "0.29.0",
                "0.3",
                "0.30.0",
                "0.30.0a0",
                "0.31.0",
                "0.31.1",
                "0.32.0",
                "0.32.1",
                "0.32.2",
                "0.32.3",
                "0.33.0",
                "0.33.1",
                "0.33.4",
                "0.33.5",
                "0.33.6",
                "0.34.0",
                "0.34.1",
                "0.34.2",
                "0.35.0",
                "0.35.1",
                "0.36.0",
                "0.36.1",
                "0.36.2",
                "0.37.0",
                "0.37.1",
                "0.38.0",
                "0.4",
                "0.4.1",
                "0.4.2",
                "0.5",
                "0.6",
                "0.7",
                "0.8",
                "0.9",
                "0.9.1",
                "0.9.2",
                "0.9.3",
                "0.9.4",
                "0.9.5",
                "0.9.6",
                "0.9.7"
            ],
            "secure_versions": [
                "0.46.1",
                "0.46.0",
                "0.45.1",
                "0.45.0",
                "0.44.0",
                "0.43.0",
                "0.42.0",
                "0.41.3",
                "0.41.2",
                "0.41.1",
                "0.41.0",
                "0.40.0",
                "0.38.4",
                "0.38.3",
                "0.38.2",
                "0.38.1"
            ],
            "latest_version_without_known_vulnerabilities": "0.46.1",
            "latest_version": "0.46.1",
            "more_info_url": "https://data.safetycli.com/p/pypi/wheel/97c/"
        },
        "py": {
            "name": "py",
            "version": "1.10.0",
            "requirements": [
                {
                    "raw": "py==1.10.0",
                    "extras": [],
                    "marker": null,
                    "name": "py",
                    "specifier": "==1.10.0",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ],
            "found": null,
            "insecure_versions": [
                "0.8.0a2",
                "0.9.0",
                "0.9.1",
                "0.9.2",
                "1.0.0",
                "1.0.1",
                "1.0.2",
                "1.1.0",
                "1.10.0",
                "1.1.1",
                "1.11.0",
                "1.2.0",
                "1.2.1",
                "1.3.0",
                "1.3.1",
                "1.3.2",
                "1.3.3",
                "1.3.4",
                "1.4.0",
                "1.4.1",
                "1.4.10",
                "1.4.11",
                "1.4.12",
                "1.4.13",
                "1.4.14",
                "1.4.15",
                "1.4.16",
                "1.4.17",
                "1.4.18",
                "1.4.19",
                "1.4.2",
                "1.4.20",
                "1.4.21",
                "1.4.22",
                "1.4.23",
                "1.4.24",
                "1.4.25",
                "1.4.26",
                "1.4.27",
                "1.4.28",
                "1.4.29",
                "1.4.3",
                "1.4.30",
                "1.4.31",
                "1.4.32",
                "1.4.32.dev1",
                "1.4.33",
                "1.4.34",
                "1.4.4",
                "1.4.5",
                "1.4.6",
                "1.4.7",
                "1.4.7.dev3",
                "1.4.8",
                "1.4.9",
                "1.5.1",
                "1.5.2",
                "1.5.3",
                "1.5.4",
                "1.6.0",
                "1.7.0",
                "1.8.0",
                "1.8.1",
                "1.8.2",
                "1.9.0"
            ],
            "secure_versions": [],
            "latest_version_without_known_vulnerabilities": "",
            "latest_version": "1.11.0",
            "more_info_url": "https://data.safetycli.com/p/pypi/py/97c/"
        },
        "pip": {
            "name": "pip",
            "version": "22.0.2",
            "requirements": [
                {
                    "raw": "pip==22.0.2",
                    "extras": [],
                    "marker": null,
                    "name": "pip",
                    "specifier": "==22.0.2",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ],
            "found": null,
            "insecure_versions": [
                "0.2",
                "0.2.1",
                "0.3",
                "0.3.1",
                "0.4",
                "0.5",
                "0.5.1",
                "0.6",
                "0.6.1",
                "0.6.2",
                "0.6.3",
                "0.7",
                "0.7.1",
                "0.7.2",
                "0.8",
                "0.8.1",
                "0.8.2",
                "0.8.3",
                "1.0",
                "10.0.0",
                "10.0.0b1",
                "10.0.0b2",
                "10.0.1",
                "1.0.1",
                "1.0.2",
                "1.1",
                "1.2",
                "1.2.1",
                "1.3",
                "1.3.1",
                "1.4",
                "1.4.1",
                "1.5",
                "1.5.1",
                "1.5.2",
                "1.5.3",
                "1.5.4",
                "1.5.5",
                "1.5.6",
                "18.0",
                "18.1",
                "19.0",
                "19.0.1",
                "19.0.2",
                "19.0.3",
                "19.1",
                "19.1.1",
                "19.2",
                "19.2.1",
                "19.2.2",
                "19.2.3",
                "19.3",
                "19.3.1",
                "20.0",
                "20.0.1",
                "20.0.2",
                "20.1",
                "20.1.1",
                "20.1b1",
                "20.2",
                "20.2.1",
                "20.2.2",
                "20.2.3",
                "20.2.4",
                "20.2b1",
                "20.3",
                "20.3.1",
                "20.3.2",
                "20.3.3",
                "20.3.4",
                "20.3b1",
                "21.0",
                "21.0.1",
                "21.1",
                "21.1.1",
                "21.1.2",
                "21.1.3",
                "21.2",
                "21.2.1",
                "21.2.2",
                "21.2.3",
                "21.2.4",
                "21.3",
                "21.3.1",
                "22.0",
                "22.0.1",
                "22.0.2",
                "22.0.3",
                "22.0.4",
                "22.1",
                "22.1.1",
                "22.1.2",
                "22.1b1",
                "22.2",
                "22.2.1",
                "22.2.2",
                "22.3",
                "22.3.1",
                "23.0",
                "23.0.1",
                "23.1",
                "23.1.1",
                "23.1.2",
                "23.2",
                "23.2.1",
                "23.3",
                "23.3.1",
                "23.3.2",
                "24.0",
                "24.1",
                "24.1.1",
                "24.1.2",
                "24.1b1",
                "24.1b2",
                "24.2",
                "24.3",
                "24.3.1",
                "6.0",
                "6.0.1",
                "6.0.2",
                "6.0.3",
                "6.0.4",
                "6.0.5",
                "6.0.6",
                "6.0.7",
                "6.0.8",
                "6.1.0",
                "6.1.1",
                "7.0.0",
                "7.0.1",
                "7.0.2",
                "7.0.3",
                "7.1.0",
                "7.1.1",
                "7.1.2",
                "8.0.0",
                "8.0.1",
                "8.0.2",
                "8.0.3",
                "8.1.0",
                "8.1.1",
                "8.1.2",
                "9.0.0",
                "9.0.1",
                "9.0.2",
                "9.0.3"
            ],
            "secure_versions": [
                "25.1.1",
                "25.1",
                "25.0.1",
                "25.0"
            ],
            "latest_version_without_known_vulnerabilities": "25.1.1",
            "latest_version": "25.1.1",
            "more_info_url": "https://data.safetycli.com/p/pypi/pip/97c/"
        },
        "oauthlib": {
            "name": "oauthlib",
            "version": "3.2.0",
            "requirements": [
                {
                    "raw": "oauthlib==3.2.0",
                    "extras": [],
                    "marker": null,
                    "name": "oauthlib",
                    "specifier": "==3.2.0",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ],
            "found": null,
            "insecure_versions": [
                "0.0.1",
                "0.0.2",
                "0.1.0",
                "0.1.1",
                "0.1.2",
                "0.1.3",
                "0.3.0",
                "0.3.2",
                "0.3.3",
                "0.3.4",
                "0.3.5",
                "0.3.6",
                "0.3.7",
                "0.3.8",
                "0.4.0",
                "0.4.1",
                "0.4.2",
                "0.5.0",
                "0.5.1",
                "0.6.0",
                "0.6.1",
                "0.6.2",
                "0.6.3",
                "3.1.1",
                "3.2.0"
            ],
            "secure_versions": [
                "3.2.2",
                "3.2.1",
                "3.1.0",
                "3.0.2",
                "3.0.1",
                "3.0.0",
                "2.1.0",
                "2.0.7",
                "2.0.6",
                "2.0.5",
                "2.0.4",
                "2.0.3",
                "2.0.2",
                "2.0.1",
                "2.0.0",
                "1.1.2",
                "1.1.1",
                "1.1.0",
                "1.0.3",
                "1.0.2",
                "1.0.1",
                "1.0.0",
                "0.7.2",
                "0.7.1",
                "0.7.0"
            ],
            "latest_version_without_known_vulnerabilities": "3.2.2",
            "latest_version": "3.2.2",
            "more_info_url": "https://data.safetycli.com/p/pypi/oauthlib/97c/"
        },
        "cryptography": {
            "name": "cryptography",
            "version": "3.4.8",
            "requirements": [
                {
                    "raw": "cryptography==3.4.8",
                    "extras": [],
                    "marker": null,
                    "name": "cryptography",
                    "specifier": "==3.4.8",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ],
            "found": null,
            "insecure_versions": [
                "0.1",
                "0.2",
                "0.2.1",
                "0.2.2",
                "0.3",
                "0.4",
                "0.5",
                "0.5.1",
                "0.5.2",
                "0.5.3",
                "0.5.4",
                "0.6",
                "0.6.1",
                "0.7",
                "0.7.1",
                "0.7.2",
                "0.8",
                "0.8.1",
                "0.8.2",
                "0.9",
                "0.9.1",
                "0.9.2",
                "0.9.3",
                "1.0",
                "1.0.1",
                "1.0.2",
                "1.1",
                "1.1.1",
                "1.1.2",
                "1.2",
                "1.2.1",
                "1.2.2",
                "1.2.3",
                "1.3",
                "1.3.1",
                "1.3.2",
                "1.3.3",
                "1.3.4",
                "1.4",
                "1.5",
                "1.5.1",
                "1.5.2",
                "1.5.3",
                "1.6",
                "1.7",
                "1.7.1",
                "1.7.2",
                "1.8",
                "1.8.1",
                "1.8.2",
                "1.9",
                "2.0",
                "2.0.1",
                "2.0.2",
                "2.0.3",
                "2.1",
                "2.1.1",
                "2.1.2",
                "2.1.3",
                "2.1.4",
                "2.2",
                "2.2.1",
                "2.2.2",
                "2.3",
                "2.3.1",
                "2.4",
                "2.4.1",
                "2.4.2",
                "2.5",
                "2.6",
                "2.6.1",
                "2.7",
                "2.8",
                "2.9",
                "2.9.1",
                "2.9.2",
                "3.0",
                "3.1",
                "3.1.1",
                "3.2",
                "3.2.1",
                "3.3",
                "3.3.1",
                "3.3.2",
                "3.4",
                "3.4.1",
                "3.4.2",
                "3.4.3",
                "3.4.4",
                "3.4.5",
                "3.4.6",
                "3.4.7",
                "3.4.8",
                "35.0.0",
                "36.0.0",
                "36.0.1",
                "36.0.2",
                "37.0.0",
                "37.0.1",
                "37.0.2",
                "37.0.3",
                "37.0.4",
                "38.0.0",
                "38.0.1",
                "38.0.2",
                "38.0.3",
                "38.0.4",
                "39.0.0",
                "39.0.1",
                "39.0.2",
                "40.0.0",
                "40.0.1",
                "40.0.2",
                "41.0.0",
                "41.0.1",
                "41.0.2",
                "41.0.3",
                "41.0.4",
                "41.0.5",
                "41.0.6",
                "41.0.7",
                "42.0.0",
                "42.0.1",
                "42.0.2",
                "42.0.3",
                "42.0.4",
                "42.0.5",
                "42.0.6",
                "42.0.7",
                "42.0.8",
                "43.0.0",
                "43.0.1",
                "43.0.3",
                "44.0.0"
            ],
            "secure_versions": [
                "45.0.3",
                "45.0.2",
                "45.0.1",
                "45.0.0",
                "44.0.3",
                "44.0.2",
                "44.0.1"
            ],
            "latest_version_without_known_vulnerabilities": "45.0.3",
            "latest_version": "45.0.3",
            "more_info_url": "https://data.safetycli.com/p/pypi/cryptography/97c/"
        },
        "pyjwt": {
            "name": "pyjwt",
            "version": "2.3.0",
            "requirements": [
                {
                    "raw": "pyjwt==2.3.0",
                    "extras": [],
                    "marker": null,
                    "name": "pyjwt",
                    "specifier": "==2.3.0",
                    "url": null,
                    "found": "/usr/lib/python3/dist-packages"
                }
            ],
            "found": null,
            "insecure_versions": [
                "0.1.1",
                "0.1.2",
                "0.1.3",
                "0.1.4",
                "0.1.5",
                "0.1.6",
                "0.1.7",
                "0.1.8",
                "0.1.9",
                "0.2.0",
                "0.2.1",
                "0.2.3",
                "0.3.0",
                "0.3.1",
                "0.3.2",
                "0.4.0",
                "0.4.1",
                "0.4.2",
                "0.4.3",
                "1.0.0",
                "1.0.1",
                "1.1.0",
                "1.3.0",
                "1.4.0",
                "1.4.1",
                "1.4.2",
                "1.5.0",
                "1.5.1",
                "1.5.2",
                "1.5.3",
                "1.6.0",
                "1.6.1",
                "1.6.3",
                "1.6.4",
                "1.7.0",
                "1.7.1",
                "2.0.0",
                "2.0.0a1",
                "2.0.0a2",
                "2.0.1",
                "2.1.0",
                "2.10.0",
                "2.2.0",
                "2.3.0",
                "2.4.0",
                "2.5.0",
                "2.6.0",
                "2.7.0",
                "2.8.0",
                "2.9.0"
            ],
            "secure_versions": [
                "2.10.1"
            ],
            "latest_version_without_known_vulnerabilities": "2.10.1",
            "latest_version": "2.10.1",
            "more_info_url": "https://data.safetycli.com/p/pypi/pyjwt/97c/"
        },
        "pypdf2": {
            "name": "pypdf2",
            "version": "3.0.1",
            "requirements": [
                {
                    "raw": "pypdf2==3.0.1",
                    "extras": [],
                    "marker": null,
                    "name": "pypdf2",
                    "specifier": "==3.0.1",
                    "url": null,
                    "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                }
            ],
            "found": null,
            "insecure_versions": [
                "1.15",
                "1.16",
                "1.17",
                "1.18",
                "1.19",
                "1.20",
                "1.21",
                "1.22",
                "1.23",
                "1.24",
                "1.25",
                "1.25.1",
                "1.26.0",
                "1.27.0",
                "1.27.1",
                "1.27.10",
                "1.27.11",
                "1.27.12",
                "1.27.2",
                "1.27.3",
                "1.27.4",
                "1.27.5",
                "1.27.6",
                "1.27.7",
                "1.27.8",
                "1.27.9",
                "1.28.0",
                "1.28.1",
                "1.28.2",
                "1.28.3",
                "1.28.4",
                "1.28.5",
                "1.28.6",
                "2.0.0",
                "2.1.0",
                "2.10.0",
                "2.10.1",
                "2.10.2",
                "2.10.3",
                "2.10.4",
                "2.10.5",
                "2.10.6",
                "2.10.7",
                "2.10.8",
                "2.10.9",
                "2.1.1",
                "2.11.0",
                "2.11.1",
                "2.11.2",
                "2.12.0",
                "2.12.1",
                "2.2.0",
                "2.2.1",
                "2.3.0",
                "2.3.1",
                "2.4.0",
                "2.4.1",
                "2.4.2",
                "2.5.0",
                "2.6.0",
                "2.7.0",
                "2.8.0",
                "2.8.1",
                "2.9.0",
                "3.0.0",
                "3.0.1"
            ],
            "secure_versions": [],
            "latest_version_without_known_vulnerabilities": "",
            "latest_version": "3.0.1",
            "more_info_url": "https://data.safetycli.com/p/pypi/pypdf2/97c/"
        }
    },
    "announcements": [],
    "vulnerabilities": [
        {
            "vulnerability_id": "72132",
            "package_name": "zipp",
            "ignored": {},
            "ignored_reason": null,
            "ignored_expires": null,
            "vulnerable_spec": [
                "<3.19.1"
            ],
            "all_vulnerable_specs": [
                "<3.19.1"
            ],
            "analyzed_version": "1.0.0",
            "analyzed_requirement": {
                "raw": "zipp==1.0.0",
                "extras": [],
                "marker": null,
                "name": "zipp",
                "specifier": "==1.0.0",
                "url": null,
                "found": "/usr/lib/python3/dist-packages"
            },
            "advisory": "A Denial of Service (DoS) vulnerability exists in the jaraco/zipp library. The vulnerability is triggered when processing a specially crafted zip file that leads to an infinite loop. This issue also impacts the zipfile module of CPython, as features from the third-party zipp library are later merged into CPython, and the affected code is identical in both projects. The infinite loop can be initiated through the use of functions affecting the `Path` module in both zipp and zipfile, such as `joinpath`, the overloaded division operator, and `iterdir`. Although the infinite loop is not resource exhaustive, it prevents the application from responding.",
            "is_transitive": false,
            "published_date": null,
            "fixed_versions": [],
            "closest_versions_without_known_vulnerabilities": [],
            "resources": [],
            "CVE": "CVE-2024-5569",
            "severity": null,
            "affected_versions": [],
            "more_info_url": "https://data.safetycli.com/v/72132/97c"
        },
        {
            "vulnerability_id": "51499",
            "package_name": "wheel",
            "ignored": {},
            "ignored_reason": null,
            "ignored_expires": null,
            "vulnerable_spec": [
                "<0.38.1"
            ],
            "all_vulnerable_specs": [
                "<0.38.1"
            ],
            "analyzed_version": "0.37.1",
            "analyzed_requirement": {
                "raw": "wheel==0.37.1",
                "extras": [],
                "marker": null,
                "name": "wheel",
                "specifier": "==0.37.1",
                "url": null,
                "found": "/usr/lib/python3/dist-packages"
            },
            "advisory": "Wheel 0.38.1 includes a fix for CVE-2022-40898: An issue discovered in Python Packaging Authority (PyPA) Wheel 0.37.1 and earlier allows remote attackers to cause a denial of service via attacker controlled input to wheel cli.\r\nhttps://pyup.io/posts/pyup-discovers-redos-vulnerabilities-in-top-python-packages",
            "is_transitive": false,
            "published_date": null,
            "fixed_versions": [],
            "closest_versions_without_known_vulnerabilities": [],
            "resources": [],
            "CVE": "CVE-2022-40898",
            "severity": null,
            "affected_versions": [],
            "more_info_url": "https://data.safetycli.com/v/51499/97c"
        },
        {
            "vulnerability_id": "51457",
            "package_name": "py",
            "ignored": {},
            "ignored_reason": null,
            "ignored_expires": null,
            "vulnerable_spec": [
                "<=1.11.0"
            ],
            "all_vulnerable_specs": [
                "<=1.11.0"
            ],
            "analyzed_version": "1.10.0",
            "analyzed_requirement": {
                "raw": "py==1.10.0",
                "extras": [],
                "marker": null,
                "name": "py",
                "specifier": "==1.10.0",
                "url": null,
                "found": "/usr/lib/python3/dist-packages"
            },
            "advisory": "** DISPUTED ** Py throughout 1.11.0 allows remote attackers to conduct a ReDoS (Regular expression Denial of Service) attack via a Subversion repository with crafted info data because the InfoSvnCommand argument is mishandled.\r\nhttps://github.com/pytest-dev/py/issues/287",
            "is_transitive": false,
            "published_date": null,
            "fixed_versions": [],
            "closest_versions_without_known_vulnerabilities": [],
            "resources": [],
            "CVE": "CVE-2022-42969",
            "severity": null,
            "affected_versions": [],
            "more_info_url": "https://data.safetycli.com/v/51457/97c"
        },
        {
            "vulnerability_id": "62044",
            "package_name": "pip",
            "ignored": {},
            "ignored_reason": null,
            "ignored_expires": null,
            "vulnerable_spec": [
                "<23.3"
            ],
            "all_vulnerable_specs": [
                "<23.3"
            ],
            "analyzed_version": "22.0.2",
            "analyzed_requirement": {
                "raw": "pip==22.0.2",
                "extras": [],
                "marker": null,
                "name": "pip",
                "specifier": "==22.0.2",
                "url": null,
                "found": "/usr/lib/python3/dist-packages"
            },
            "advisory": "Affected versions of Pip are vulnerable to Command Injection. When installing a package from a Mercurial VCS URL (ie \"pip install hg+...\") with pip prior to v23.3, the specified Mercurial revision could be used to inject arbitrary configuration options to the \"hg clone\" call (ie \"--config\"). Controlling the Mercurial configuration can modify how and which repository is installed. This vulnerability does not affect users who aren't installing from Mercurial.",
            "is_transitive": false,
            "published_date": null,
            "fixed_versions": [],
            "closest_versions_without_known_vulnerabilities": [],
            "resources": [],
            "CVE": "CVE-2023-5752",
            "severity": null,
            "affected_versions": [],
            "more_info_url": "https://data.safetycli.com/v/62044/97c"
        },
        {
            "vulnerability_id": "75180",
            "package_name": "pip",
            "ignored": {},
            "ignored_reason": null,
            "ignored_expires": null,
            "vulnerable_spec": [
                "<25.0"
            ],
            "all_vulnerable_specs": [
                "<25.0"
            ],
            "analyzed_version": "22.0.2",
            "analyzed_requirement": {
                "raw": "pip==22.0.2",
                "extras": [],
                "marker": null,
                "name": "pip",
                "specifier": "==22.0.2",
                "url": null,
                "found": "/usr/lib/python3/dist-packages"
            },
            "advisory": "Pip solves a security vulnerability that previously allowed maliciously crafted wheel files to execute unauthorized code during installation.",
            "is_transitive": false,
            "published_date": null,
            "fixed_versions": [],
            "closest_versions_without_known_vulnerabilities": [],
            "resources": [],
            "CVE": null,
            "severity": null,
            "affected_versions": [],
            "more_info_url": "https://data.safetycli.com/v/75180/97c"
        },
        {
            "vulnerability_id": "50959",
            "package_name": "oauthlib",
            "ignored": {},
            "ignored_reason": null,
            "ignored_expires": null,
            "vulnerable_spec": [
                ">=3.1.1,<3.2.1"
            ],
            "all_vulnerable_specs": [
                ">=3.1.1,<3.2.1"
            ],
            "analyzed_version": "3.2.0",
            "analyzed_requirement": {
                "raw": "oauthlib==3.2.0",
                "extras": [],
                "marker": null,
                "name": "oauthlib",
                "specifier": "==3.2.0",
                "url": null,
                "found": "/usr/lib/python3/dist-packages"
            },
            "advisory": "OAuthLib 3.2.1 includes a fix for CVE-2022-36087: In OAuthLib versions 3.1.1 until 3.2.1, an attacker providing malicious redirect uri can cause denial of service. An attacker can also leverage usage of 'uri_validate' functions depending where it is used. OAuthLib applications using OAuth2.0 provider support or use directly 'uri_validate' are affected by this issue. There are no known workarounds.\r\nhttps://github.com/oauthlib/oauthlib/security/advisories/GHSA-3pgj-pg6c-r5p7",
            "is_transitive": false,
            "published_date": null,
            "fixed_versions": [],
            "closest_versions_without_known_vulnerabilities": [],
            "resources": [],
            "CVE": "CVE-2022-36087",
            "severity": null,
            "affected_versions": [],
            "more_info_url": "https://data.safetycli.com/v/50959/97c"
        },
        {
            "vulnerability_id": "65647",
            "package_name": "cryptography",
            "ignored": {},
            "ignored_reason": null,
            "ignored_expires": null,
            "vulnerable_spec": [
                "<42.0.5"
            ],
            "all_vulnerable_specs": [
                "<42.0.5"
            ],
            "analyzed_version": "3.4.8",
            "analyzed_requirement": {
                "raw": "cryptography==3.4.8",
                "extras": [],
                "marker": null,
                "name": "cryptography",
                "specifier": "==3.4.8",
                "url": null,
                "found": "/usr/lib/python3/dist-packages"
            },
            "advisory": "Cryptography version 42.0.5 introduces a limit on the number of name constraint checks during X.509 path validation to prevent denial of service attacks.\r\nhttps://github.com/pyca/cryptography/commit/4be53bf20cc90cbac01f5f94c5d1aecc5289ba1f",
            "is_transitive": false,
            "published_date": null,
            "fixed_versions": [],
            "closest_versions_without_known_vulnerabilities": [],
            "resources": [],
            "CVE": null,
            "severity": null,
            "affected_versions": [],
            "more_info_url": "https://data.safetycli.com/v/65647/97c"
        },
        {
            "vulnerability_id": "59473",
            "package_name": "cryptography",
            "ignored": {},
            "ignored_reason": null,
            "ignored_expires": null,
            "vulnerable_spec": [
                "<41.0.2"
            ],
            "all_vulnerable_specs": [
                "<41.0.2"
            ],
            "analyzed_version": "3.4.8",
            "analyzed_requirement": {
                "raw": "cryptography==3.4.8",
                "extras": [],
                "marker": null,
                "name": "cryptography",
                "specifier": "==3.4.8",
                "url": null,
                "found": "/usr/lib/python3/dist-packages"
            },
            "advisory": "The cryptography package before 41.0.2 for Python mishandles SSH certificates that have critical options.",
            "is_transitive": false,
            "published_date": null,
            "fixed_versions": [],
            "closest_versions_without_known_vulnerabilities": [],
            "resources": [],
            "CVE": "CVE-2023-38325",
            "severity": null,
            "affected_versions": [],
            "more_info_url": "https://data.safetycli.com/v/59473/97c"
        },
        {
            "vulnerability_id": "62556",
            "package_name": "cryptography",
            "ignored": {},
            "ignored_reason": null,
            "ignored_expires": null,
            "vulnerable_spec": [
                ">=3.1,<41.0.6"
            ],
            "all_vulnerable_specs": [
                ">=3.1,<41.0.6"
            ],
            "analyzed_version": "3.4.8",
            "analyzed_requirement": {
                "raw": "cryptography==3.4.8",
                "extras": [],
                "marker": null,
                "name": "cryptography",
                "specifier": "==3.4.8",
                "url": null,
                "found": "/usr/lib/python3/dist-packages"
            },
            "advisory": "Affected versions of Cryptography are vulnerable to NULL-dereference when loading PKCS7 certificates. Calling 'load_pem_pkcs7_certificates' or 'load_der_pkcs7_certificates' could lead to a NULL-pointer dereference and segfault. Exploitation of this vulnerability poses a serious risk of Denial of Service (DoS) for any application attempting to deserialize a PKCS7 blob/certificate. The consequences extend to potential disruptions in system availability and stability.",
            "is_transitive": false,
            "published_date": null,
            "fixed_versions": [],
            "closest_versions_without_known_vulnerabilities": [],
            "resources": [],
            "CVE": "CVE-2023-49083",
            "severity": null,
            "affected_versions": [],
            "more_info_url": "https://data.safetycli.com/v/62556/97c"
        },
        {
            "vulnerability_id": "65278",
            "package_name": "cryptography",
            "ignored": {},
            "ignored_reason": null,
            "ignored_expires": null,
            "vulnerable_spec": [
                "<42.0.0"
            ],
            "all_vulnerable_specs": [
                "<42.0.0"
            ],
            "analyzed_version": "3.4.8",
            "analyzed_requirement": {
                "raw": "cryptography==3.4.8",
                "extras": [],
                "marker": null,
                "name": "cryptography",
                "specifier": "==3.4.8",
                "url": null,
                "found": "/usr/lib/python3/dist-packages"
            },
            "advisory": "Affected versions of Cryptography may allow a remote attacker to decrypt captured messages in TLS servers that use RSA key exchanges, which may lead to exposure of confidential or sensitive data.",
            "is_transitive": false,
            "published_date": null,
            "fixed_versions": [],
            "closest_versions_without_known_vulnerabilities": [],
            "resources": [],
            "CVE": "CVE-2023-50782",
            "severity": null,
            "affected_versions": [],
            "more_info_url": "https://data.safetycli.com/v/65278/97c"
        },
        {
            "vulnerability_id": "53048",
            "package_name": "cryptography",
            "ignored": {},
            "ignored_reason": null,
            "ignored_expires": null,
            "vulnerable_spec": [
                ">=1.8,<39.0.1"
            ],
            "all_vulnerable_specs": [
                ">=1.8,<39.0.1"
            ],
            "analyzed_version": "3.4.8",
            "analyzed_requirement": {
                "raw": "cryptography==3.4.8",
                "extras": [],
                "marker": null,
                "name": "cryptography",
                "specifier": "==3.4.8",
                "url": null,
                "found": "/usr/lib/python3/dist-packages"
            },
            "advisory": "Cryptography 39.0.1 includes a fix for CVE-2023-23931: In affected versions 'Cipher.update_into' would accept Python objects which implement the buffer protocol, but provide only immutable buffers. This would allow immutable objects (such as 'bytes') to be mutated, thus violating fundamental rules of Python and resulting in corrupted output. This issue has been present since 'update_into' was originally introduced in cryptography 1.8.",
            "is_transitive": false,
            "published_date": null,
            "fixed_versions": [],
            "closest_versions_without_known_vulnerabilities": [],
            "resources": [],
            "CVE": "CVE-2023-23931",
            "severity": null,
            "affected_versions": [],
            "more_info_url": "https://data.safetycli.com/v/53048/97c"
        },
        {
            "vulnerability_id": "48542",
            "package_name": "pyjwt",
            "ignored": {},
            "ignored_reason": null,
            "ignored_expires": null,
            "vulnerable_spec": [
                ">=1.5.0,<2.4.0"
            ],
            "all_vulnerable_specs": [
                ">=1.5.0,<2.4.0"
            ],
            "analyzed_version": "2.3.0",
            "analyzed_requirement": {
                "raw": "pyjwt==2.3.0",
                "extras": [],
                "marker": null,
                "name": "pyjwt",
                "specifier": "==2.3.0",
                "url": null,
                "found": "/usr/lib/python3/dist-packages"
            },
            "advisory": "PyJWT 2.4.0 includes a fix for CVE-2022-29217: An attacker submitting the JWT token can choose the used signing algorithm. The PyJWT library requires that the application chooses what algorithms are supported. The application can specify 'jwt.algorithms.get_default_algorithms()' to get support for all algorithms, or specify a single algorithm. The issue is not that big as 'algorithms=jwt.algorithms.get_default_algorithms()' has to be used. As a workaround, always be explicit with the algorithms that are accepted and expected when decoding.",
            "is_transitive": false,
            "published_date": null,
            "fixed_versions": [],
            "closest_versions_without_known_vulnerabilities": [],
            "resources": [],
            "CVE": "CVE-2022-29217",
            "severity": null,
            "affected_versions": [],
            "more_info_url": "https://data.safetycli.com/v/48542/97c"
        },
        {
            "vulnerability_id": "74429",
            "package_name": "pyjwt",
            "ignored": {},
            "ignored_reason": null,
            "ignored_expires": null,
            "vulnerable_spec": [
                "<2.10.1"
            ],
            "all_vulnerable_specs": [
                "<2.10.1"
            ],
            "analyzed_version": "2.3.0",
            "analyzed_requirement": {
                "raw": "pyjwt==2.3.0",
                "extras": [],
                "marker": null,
                "name": "pyjwt",
                "specifier": "==2.3.0",
                "url": null,
                "found": "/usr/lib/python3/dist-packages"
            },
            "advisory": "Affected versions of pyjwt are vulnerable to Partial Comparison (CWE-187). This flaw allows attackers to bypass issuer (iss) verification by providing partial matches, potentially granting unauthorized access. The vulnerability arises in the decode method of api_jwt.py, where issuer validation incorrectly treats strings as sequences, leading to partial matches (e.g., \"abc\" being accepted for \"__abc__\"). Exploiting this requires crafting JWTs with partially matching iss claims, which is straightforward.",
            "is_transitive": false,
            "published_date": null,
            "fixed_versions": [],
            "closest_versions_without_known_vulnerabilities": [],
            "resources": [],
            "CVE": "CVE-2024-53861",
            "severity": null,
            "affected_versions": [],
            "more_info_url": "https://data.safetycli.com/v/74429/97c"
        },
        {
            "vulnerability_id": "59234",
            "package_name": "pypdf2",
            "ignored": {},
            "ignored_reason": null,
            "ignored_expires": null,
            "vulnerable_spec": [
                ">=2.2.0,<=3.0.1"
            ],
            "all_vulnerable_specs": [
                ">=2.2.0,<=3.0.1"
            ],
            "analyzed_version": "3.0.1",
            "analyzed_requirement": {
                "raw": "pypdf2==3.0.1",
                "extras": [],
                "marker": null,
                "name": "pypdf2",
                "specifier": "==3.0.1",
                "url": null,
                "found": "/home/<USER>/.local/lib/python3.10/site-packages"
            },
            "advisory": "Pypdf2 is vulnerable to CVE-2023-36464: An attacker may craft a PDF which leads to an infinite loop if '__parse_content_stream' is executed. That is, for example, the case if the user extracted text from such a PDF. Users may modify the line 'while peek not in (b\"\\r\", b\"\\n\")' in 'pypdf/generic/_data_structures.py' to 'while peek not in (b\"\\r\", b\"\\n\", b\"\")' as a workaround.\r\nhttps://github.com/py-pdf/pypdf/pull/1828\r\nhttps://github.com/py-pdf/pypdf/security/advisories/GHSA-4vvm-4w3v-6mr8",
            "is_transitive": false,
            "published_date": null,
            "fixed_versions": [],
            "closest_versions_without_known_vulnerabilities": [],
            "resources": [],
            "CVE": "CVE-2023-36464",
            "severity": null,
            "affected_versions": [],
            "more_info_url": "https://data.safetycli.com/v/59234/97c"
        }
    ],
    "ignored_vulnerabilities": [],
    "remediations": {
        "zipp": {
            "requirements": {
                "==1.0.0": {
                    "vulnerabilities_found": 1,
                    "version": "1.0.0",
                    "requirement": {
                        "raw": "zipp==1.0.0",
                        "extras": [],
                        "marker": null,
                        "name": "zipp",
                        "specifier": "==1.0.0",
                        "url": null,
                        "found": "/usr/lib/python3/dist-packages"
                    },
                    "more_info_url": "https://data.safetycli.com/p/pypi/zipp/97c/"
                }
            },
            "current_version": null,
            "vulnerabilities_found": null,
            "recommended_version": null,
            "other_recommended_versions": [],
            "more_info_url": null
        },
        "wheel": {
            "requirements": {
                "==0.37.1": {
                    "vulnerabilities_found": 1,
                    "version": "0.37.1",
                    "requirement": {
                        "raw": "wheel==0.37.1",
                        "extras": [],
                        "marker": null,
                        "name": "wheel",
                        "specifier": "==0.37.1",
                        "url": null,
                        "found": "/usr/lib/python3/dist-packages"
                    },
                    "more_info_url": "https://data.safetycli.com/p/pypi/wheel/97c/"
                }
            },
            "current_version": null,
            "vulnerabilities_found": null,
            "recommended_version": null,
            "other_recommended_versions": [],
            "more_info_url": null
        },
        "py": {
            "requirements": {
                "==1.10.0": {
                    "vulnerabilities_found": 1,
                    "version": "1.10.0",
                    "requirement": {
                        "raw": "py==1.10.0",
                        "extras": [],
                        "marker": null,
                        "name": "py",
                        "specifier": "==1.10.0",
                        "url": null,
                        "found": "/usr/lib/python3/dist-packages"
                    },
                    "more_info_url": "https://data.safetycli.com/p/pypi/py/97c/"
                }
            },
            "current_version": null,
            "vulnerabilities_found": null,
            "recommended_version": null,
            "other_recommended_versions": [],
            "more_info_url": null
        },
        "pip": {
            "requirements": {
                "==22.0.2": {
                    "vulnerabilities_found": 2,
                    "version": "22.0.2",
                    "requirement": {
                        "raw": "pip==22.0.2",
                        "extras": [],
                        "marker": null,
                        "name": "pip",
                        "specifier": "==22.0.2",
                        "url": null,
                        "found": "/usr/lib/python3/dist-packages"
                    },
                    "more_info_url": "https://data.safetycli.com/p/pypi/pip/97c/"
                }
            },
            "current_version": null,
            "vulnerabilities_found": null,
            "recommended_version": null,
            "other_recommended_versions": [],
            "more_info_url": null
        },
        "oauthlib": {
            "requirements": {
                "==3.2.0": {
                    "vulnerabilities_found": 1,
                    "version": "3.2.0",
                    "requirement": {
                        "raw": "oauthlib==3.2.0",
                        "extras": [],
                        "marker": null,
                        "name": "oauthlib",
                        "specifier": "==3.2.0",
                        "url": null,
                        "found": "/usr/lib/python3/dist-packages"
                    },
                    "more_info_url": "https://data.safetycli.com/p/pypi/oauthlib/97c/"
                }
            },
            "current_version": null,
            "vulnerabilities_found": null,
            "recommended_version": null,
            "other_recommended_versions": [],
            "more_info_url": null
        },
        "cryptography": {
            "requirements": {
                "==3.4.8": {
                    "vulnerabilities_found": 5,
                    "version": "3.4.8",
                    "requirement": {
                        "raw": "cryptography==3.4.8",
                        "extras": [],
                        "marker": null,
                        "name": "cryptography",
                        "specifier": "==3.4.8",
                        "url": null,
                        "found": "/usr/lib/python3/dist-packages"
                    },
                    "more_info_url": "https://data.safetycli.com/p/pypi/cryptography/97c/"
                }
            },
            "current_version": null,
            "vulnerabilities_found": null,
            "recommended_version": null,
            "other_recommended_versions": [],
            "more_info_url": null
        },
        "pyjwt": {
            "requirements": {
                "==2.3.0": {
                    "vulnerabilities_found": 2,
                    "version": "2.3.0",
                    "requirement": {
                        "raw": "pyjwt==2.3.0",
                        "extras": [],
                        "marker": null,
                        "name": "pyjwt",
                        "specifier": "==2.3.0",
                        "url": null,
                        "found": "/usr/lib/python3/dist-packages"
                    },
                    "more_info_url": "https://data.safetycli.com/p/pypi/pyjwt/97c/"
                }
            },
            "current_version": null,
            "vulnerabilities_found": null,
            "recommended_version": null,
            "other_recommended_versions": [],
            "more_info_url": null
        },
        "pypdf2": {
            "requirements": {
                "==3.0.1": {
                    "vulnerabilities_found": 1,
                    "version": "3.0.1",
                    "requirement": {
                        "raw": "pypdf2==3.0.1",
                        "extras": [],
                        "marker": null,
                        "name": "pypdf2",
                        "specifier": "==3.0.1",
                        "url": null,
                        "found": "/home/<USER>/.local/lib/python3.10/site-packages"
                    },
                    "more_info_url": "https://data.safetycli.com/p/pypi/pypdf2/97c/"
                }
            },
            "current_version": null,
            "vulnerabilities_found": null,
            "recommended_version": null,
            "other_recommended_versions": [],
            "more_info_url": null
        }
    },
    "remediations_results": {
        "vulnerabilities_fixed": [],
        "remediations_applied": {},
        "remediations_skipped": {}
    }
}


+===========================================================================================================================================================================================+


DEPRECATED: this command (`check`) has been DEPRECATED, and will be unsupported beyond 01 June 2024.


We highly encourage switching to the new `scan` command which is easier to use, more powerful, and can be set up to mimic the deprecated command if required.


+===========================================================================================================================================================================================+


