{"errors": [], "generated_at": "2025-06-10T16:10:15Z", "metrics": {"_totals": {"CONFIDENCE.HIGH": 134, "CONFIDENCE.LOW": 1, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 3, "SEVERITY.LOW": 132, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 22514, "nosec": 0, "skipped_tests": 0}, "app/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1, "nosec": 0, "skipped_tests": 0}, "app/admin/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 15, "nosec": 0, "skipped_tests": 0}, "app/admin/admin.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 65, "nosec": 0, "skipped_tests": 0}, "app/api/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1, "nosec": 0, "skipped_tests": 0}, "app/api/ai/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 3, "nosec": 0, "skipped_tests": 0}, "app/api/ai/gemini_client.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 175, "nosec": 0, "skipped_tests": 0}, "app/api/ai/rate_limiter.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 118, "nosec": 0, "skipped_tests": 0}, "app/api/ai/router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 54, "nosec": 0, "skipped_tests": 0}, "app/api/ai/schemas.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 20, "nosec": 0, "skipped_tests": 0}, "app/api/alerts/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1, "nosec": 0, "skipped_tests": 0}, "app/api/alerts/router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 103, "nosec": 0, "skipped_tests": 0}, "app/api/analytics.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 73, "nosec": 0, "skipped_tests": 0}, "app/api/benchmarks.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 113, "nosec": 0, "skipped_tests": 0}, "app/api/bulk_processor.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 123, "nosec": 0, "skipped_tests": 0}, "app/api/calendar.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 627, "nosec": 0, "skipped_tests": 0}, "app/api/calendar_analytics.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 374, "nosec": 0, "skipped_tests": 0}, "app/api/compliance.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 123, "nosec": 0, "skipped_tests": 0}, "app/api/compliance_calendar.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 364, "nosec": 0, "skipped_tests": 0}, "app/api/compliance_requirements/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 5, "nosec": 0, "skipped_tests": 0}, "app/api/compliance_requirements/router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 161, "nosec": 0, "skipped_tests": 0}, "app/api/countries/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 5, "nosec": 0, "skipped_tests": 0}, "app/api/countries/router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 157, "nosec": 0, "skipped_tests": 0}, "app/api/data_collection.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 328, "nosec": 0, "skipped_tests": 0}, "app/api/data_enrichment.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 393, "nosec": 0, "skipped_tests": 0}, "app/api/data_sources/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 4, "nosec": 0, "skipped_tests": 0}, "app/api/data_sources/base_client.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 121, "nosec": 0, "skipped_tests": 0}, "app/api/data_sources/public/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 4, "nosec": 0, "skipped_tests": 0}, "app/api/data_sources/public/cfpb_complaints.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 158, "nosec": 0, "skipped_tests": 0}, "app/api/data_sources/public/federal_reserve.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 189, "nosec": 0, "skipped_tests": 0}, "app/api/data_sources/public/finra_rules.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 151, "nosec": 0, "skipped_tests": 0}, "app/api/data_sources/public/manager.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 107, "nosec": 0, "skipped_tests": 0}, "app/api/data_sources/public/sec_edgar.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 152, "nosec": 0, "skipped_tests": 0}, "app/api/data_sources/public/usa_spending.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 205, "nosec": 0, "skipped_tests": 0}, "app/api/data_sources/router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 483, "nosec": 0, "skipped_tests": 0}, "app/api/document_analysis.py": {"CONFIDENCE.HIGH": 6, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 6, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 298, "nosec": 0, "skipped_tests": 0}, "app/api/document_import.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 233, "nosec": 0, "skipped_tests": 0}, "app/api/governance/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 3, "nosec": 0, "skipped_tests": 0}, "app/api/governance/action_plan.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 392, "nosec": 0, "skipped_tests": 0}, "app/api/governance/assessment.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 201, "nosec": 0, "skipped_tests": 0}, "app/api/governance/control.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 140, "nosec": 0, "skipped_tests": 0}, "app/api/governance/incident.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 256, "nosec": 0, "skipped_tests": 0}, "app/api/governance/regulation.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 108, "nosec": 0, "skipped_tests": 0}, "app/api/governance/risk.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 168, "nosec": 0, "skipped_tests": 0}, "app/api/governance/router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 15, "nosec": 0, "skipped_tests": 0}, "app/api/impact_assessment.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 309, "nosec": 0, "skipped_tests": 0}, "app/api/industries/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 5, "nosec": 0, "skipped_tests": 0}, "app/api/industries/router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 188, "nosec": 0, "skipped_tests": 0}, "app/api/integrations/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 6, "nosec": 0, "skipped_tests": 0}, "app/api/integrations/router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 25, "nosec": 0, "skipped_tests": 0}, "app/api/integrations/superglu.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 108, "nosec": 0, "skipped_tests": 0}, "app/api/openapi.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 161, "nosec": 0, "skipped_tests": 0}, "app/api/pdf_analysis.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 167, "nosec": 0, "skipped_tests": 0}, "app/api/regulation_categories/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 5, "nosec": 0, "skipped_tests": 0}, "app/api/regulation_categories/router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 189, "nosec": 0, "skipped_tests": 0}, "app/api/regulation_documents/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 5, "nosec": 0, "skipped_tests": 0}, "app/api/regulation_documents/router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 209, "nosec": 0, "skipped_tests": 0}, "app/api/regulation_management/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 3, "nosec": 0, "skipped_tests": 0}, "app/api/regulation_management/gemini_enrichment.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 937, "nosec": 0, "skipped_tests": 0}, "app/api/regulation_management/reporting.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 301, "nosec": 0, "skipped_tests": 0}, "app/api/regulation_management/reporting_router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 149, "nosec": 0, "skipped_tests": 0}, "app/api/regulation_management/router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 453, "nosec": 0, "skipped_tests": 0}, "app/api/regulation_management/service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 406, "nosec": 0, "skipped_tests": 0}, "app/api/regulation_tags/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 5, "nosec": 0, "skipped_tests": 0}, "app/api/regulation_tags/router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 185, "nosec": 0, "skipped_tests": 0}, "app/api/regulation_urls/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 5, "nosec": 0, "skipped_tests": 0}, "app/api/regulation_urls/router.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 207, "nosec": 0, "skipped_tests": 0}, "app/api/regulations.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 130, "nosec": 0, "skipped_tests": 0}, "app/api/regulations/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 5, "nosec": 0, "skipped_tests": 0}, "app/api/regulations/router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 243, "nosec": 0, "skipped_tests": 0}, "app/api/regulators/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 5, "nosec": 0, "skipped_tests": 0}, "app/api/regulators/router.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 179, "nosec": 0, "skipped_tests": 0}, "app/api/regulatory_alerts.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 251, "nosec": 0, "skipped_tests": 0}, "app/api/relationship_mapping.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 109, "nosec": 0, "skipped_tests": 0}, "app/api/routes.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 106, "nosec": 0, "skipped_tests": 0}, "app/api/scheduled_digests.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 334, "nosec": 0, "skipped_tests": 0}, "app/api/trend_analysis.py": {"CONFIDENCE.HIGH": 3, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 3, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 175, "nosec": 0, "skipped_tests": 0}, "app/auth/models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 95, "nosec": 0, "skipped_tests": 0}, "app/core/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1, "nosec": 0, "skipped_tests": 0}, "app/db/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 13, "nosec": 0, "skipped_tests": 0}, "app/db/database.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 27, "nosec": 0, "skipped_tests": 0}, "app/db/models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 434, "nosec": 0, "skipped_tests": 0}, "app/db/models/calendar.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 54, "nosec": 0, "skipped_tests": 0}, "app/db/models/change_tracking.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 43, "nosec": 0, "skipped_tests": 0}, "app/db/models/country.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 1, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 72, "nosec": 0, "skipped_tests": 0}, "app/db/models/external_api_source.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 44, "nosec": 0, "skipped_tests": 0}, "app/db/models/governance/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 30, "nosec": 0, "skipped_tests": 0}, "app/db/models/governance/action_plan.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 54, "nosec": 0, "skipped_tests": 0}, "app/db/models/governance/assessment.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 65, "nosec": 0, "skipped_tests": 0}, "app/db/models/governance/audit_log.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 26, "nosec": 0, "skipped_tests": 0}, "app/db/models/governance/base.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 66, "nosec": 0, "skipped_tests": 0}, "app/db/models/governance/control.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 40, "nosec": 0, "skipped_tests": 0}, "app/db/models/governance/dashboard.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 37, "nosec": 0, "skipped_tests": 0}, "app/db/models/governance/incident.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 38, "nosec": 0, "skipped_tests": 0}, "app/db/models/governance/organization.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 29, "nosec": 0, "skipped_tests": 0}, "app/db/models/governance/regulation.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 25, "nosec": 0, "skipped_tests": 0}, "app/db/models/governance/report.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 27, "nosec": 0, "skipped_tests": 0}, "app/db/models/governance/risk.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 52, "nosec": 0, "skipped_tests": 0}, "app/db/models/governance/user.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 34, "nosec": 0, "skipped_tests": 0}, "app/db/models/regulation.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 157, "nosec": 0, "skipped_tests": 0}, "app/db/models/regulation_url.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 27, "nosec": 0, "skipped_tests": 0}, "app/db/models/regulation_url_update.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 18, "nosec": 0, "skipped_tests": 0}, "app/db/models/regulator.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 33, "nosec": 0, "skipped_tests": 0}, "app/db/models/regulatory_changes.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 55, "nosec": 0, "skipped_tests": 0}, "app/db/models/regulatory_source.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 27, "nosec": 0, "skipped_tests": 0}, "app/i18n/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 33, "nosec": 0, "skipped_tests": 0}, "app/i18n/i18n.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 42, "nosec": 0, "skipped_tests": 0}, "app/i18n/translations.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 77, "nosec": 0, "skipped_tests": 0}, "app/main.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 468, "nosec": 0, "skipped_tests": 0}, "app/middleware/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 5, "nosec": 0, "skipped_tests": 0}, "app/middleware/caching_middleware.py": {"CONFIDENCE.HIGH": 3, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 122, "nosec": 0, "skipped_tests": 0}, "app/middleware/i18n_middleware.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 28, "nosec": 0, "skipped_tests": 0}, "app/middleware/swagger_ui.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 21, "nosec": 0, "skipped_tests": 0}, "app/schemas/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1, "nosec": 0, "skipped_tests": 0}, "app/schemas/alerts.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 48, "nosec": 0, "skipped_tests": 0}, "app/schemas/analytics.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 72, "nosec": 0, "skipped_tests": 0}, "app/schemas/calendar.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 237, "nosec": 0, "skipped_tests": 0}, "app/schemas/governance/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 37, "nosec": 0, "skipped_tests": 0}, "app/schemas/governance/action_plan.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 164, "nosec": 0, "skipped_tests": 0}, "app/schemas/governance/assessment.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 87, "nosec": 0, "skipped_tests": 0}, "app/schemas/governance/control.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 50, "nosec": 0, "skipped_tests": 0}, "app/schemas/governance/incident.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 109, "nosec": 0, "skipped_tests": 0}, "app/schemas/governance/organization.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 29, "nosec": 0, "skipped_tests": 0}, "app/schemas/governance/regulation.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 34, "nosec": 0, "skipped_tests": 0}, "app/schemas/governance/risk.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 64, "nosec": 0, "skipped_tests": 0}, "app/schemas/governance/user.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 34, "nosec": 0, "skipped_tests": 0}, "app/schemas/regulation.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 369, "nosec": 0, "skipped_tests": 0}, "app/schemas/schemas.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 562, "nosec": 0, "skipped_tests": 0}, "app/services/governance/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 3, "nosec": 0, "skipped_tests": 0}, "app/services/governance/action_plan.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 434, "nosec": 0, "skipped_tests": 0}, "app/services/governance/assessment.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 206, "nosec": 0, "skipped_tests": 0}, "app/services/governance/control.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 140, "nosec": 0, "skipped_tests": 0}, "app/services/governance/incident.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 293, "nosec": 0, "skipped_tests": 0}, "app/services/governance/regulation.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 99, "nosec": 0, "skipped_tests": 0}, "app/services/governance/risk.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 175, "nosec": 0, "skipped_tests": 0}, "app/tests/api/governance/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 3, "nosec": 0, "skipped_tests": 0}, "app/tests/api/governance/test_action_plan_api.py": {"CONFIDENCE.HIGH": 26, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 26, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 257, "nosec": 0, "skipped_tests": 0}, "app/tests/api/governance/test_assessment_api.py": {"CONFIDENCE.HIGH": 26, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 26, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 232, "nosec": 0, "skipped_tests": 0}, "app/tests/api/governance/test_control_api.py": {"CONFIDENCE.HIGH": 21, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 21, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 198, "nosec": 0, "skipped_tests": 0}, "app/tests/api/governance/test_incident_api.py": {"CONFIDENCE.HIGH": 23, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 23, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 221, "nosec": 0, "skipped_tests": 0}, "app/tests/api/governance/test_risk_api.py": {"CONFIDENCE.HIGH": 17, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 17, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 184, "nosec": 0, "skipped_tests": 0}, "app/utils/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1, "nosec": 0, "skipped_tests": 0}, "app/utils/ai_summarizer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 184, "nosec": 0, "skipped_tests": 0}, "app/utils/cache.py": {"CONFIDENCE.HIGH": 4, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 2, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 194, "nosec": 0, "skipped_tests": 0}, "app/utils/document_classifier.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 637, "nosec": 0, "skipped_tests": 0}, "app/utils/notification.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 173, "nosec": 0, "skipped_tests": 0}, "app/utils/pdf_analyzer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 653, "nosec": 0, "skipped_tests": 0}, "app/utils/regulatory_analysis.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 133, "nosec": 0, "skipped_tests": 0}, "app/utils/regulatory_parser.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 453, "nosec": 0, "skipped_tests": 0}, "app/utils/regulatory_scraper.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 265, "nosec": 0, "skipped_tests": 0}, "app/utils/relationship_mapper.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 318, "nosec": 0, "skipped_tests": 0}, "app/utils/url_processor.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 340, "nosec": 0, "skipped_tests": 0}, "app/visualization/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1, "nosec": 0, "skipped_tests": 0}, "app/visualization/worldmap.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 46, "nosec": 0, "skipped_tests": 0}}, "results": [{"code": "65 middleware = [\n66     Middleware(SessionMiddleware, secret_key=\"your-secret-key-here\")\n67 ]\n", "col_offset": 4, "end_col_offset": 68, "filename": "app/admin/admin.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'your-secret-key-here'", "line_number": 66, "line_range": [66], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b106_hardcoded_password_funcarg.html", "test_id": "B106", "test_name": "hardcoded_password_funcarg"}, {"code": "98             except ImportError:\n99                 import subprocess\n100                 subprocess.check_call([\"pip\", \"install\", \"PyPDF2\"])\n", "col_offset": 16, "end_col_offset": 33, "filename": "app/api/document_analysis.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 99, "line_range": [99], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "99                 import subprocess\n100                 subprocess.check_call([\"pip\", \"install\", \"PyPDF2\"])\n101                 import PyPDF2\n", "col_offset": 16, "end_col_offset": 67, "filename": "app/api/document_analysis.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 100, "line_range": [100], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "99                 import subprocess\n100                 subprocess.check_call([\"pip\", \"install\", \"PyPDF2\"])\n101                 import PyPDF2\n", "col_offset": 16, "end_col_offset": 67, "filename": "app/api/document_analysis.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 100, "line_range": [100], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "114             except ImportError:\n115                 import subprocess\n116                 subprocess.check_call([\"pip\", \"install\", \"python-docx\"])\n", "col_offset": 16, "end_col_offset": 33, "filename": "app/api/document_analysis.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 115, "line_range": [115], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "115                 import subprocess\n116                 subprocess.check_call([\"pip\", \"install\", \"python-docx\"])\n117                 import docx\n", "col_offset": 16, "end_col_offset": 72, "filename": "app/api/document_analysis.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 116, "line_range": [116], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "115                 import subprocess\n116                 subprocess.check_call([\"pip\", \"install\", \"python-docx\"])\n117                 import docx\n", "col_offset": 16, "end_col_offset": 72, "filename": "app/api/document_analysis.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 116, "line_range": [116], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "1152             requirements = regulation.requirements\n1153         except:\n1154             pass\n1155 \n", "col_offset": 8, "end_col_offset": 16, "filename": "app/api/regulation_management/gemini_enrichment.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 1153, "line_range": [1153, 1154], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "1210             requirements = regulation.requirements\n1211         except:\n1212             pass\n1213 \n", "col_offset": 8, "end_col_offset": 16, "filename": "app/api/regulation_management/gemini_enrichment.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 1211, "line_range": [1211, 1212], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "214                 regulation_url_data[\"domain\"] = parsed_url.netloc\n215             except Exception:\n216                 # If domain extraction fails, continue without updating domain\n217                 pass\n218         \n", "col_offset": 12, "end_col_offset": 20, "filename": "app/api/regulation_urls/router.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 215, "line_range": [215, 216, 217], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "115                 os.unlink(temp_path)\n116             except:\n117                 pass\n118 \n", "col_offset": 12, "end_col_offset": 20, "filename": "app/api/relationship_mapping.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 116, "line_range": [116, 117], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "64                 # Generate random counts for current and previous periods\n65                 current_count = random.randint(5, 30)\n66                 prev_count = random.randint(3, 25)\n", "col_offset": 32, "end_col_offset": 53, "filename": "app/api/trend_analysis.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 65, "line_range": [65], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "65                 current_count = random.randint(5, 30)\n66                 prev_count = random.randint(3, 25)\n67                 \n", "col_offset": 29, "end_col_offset": 50, "filename": "app/api/trend_analysis.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 66, "line_range": [66], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "78                     \"change_rate\": round(change_rate, 2),\n79                     \"severity_avg\": round(random.uniform(3.0, 9.0), 2)\n80                 })\n", "col_offset": 42, "end_col_offset": 66, "filename": "app/api/trend_analysis.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 330, "link": "https://cwe.mitre.org/data/definitions/330.html"}, "issue_severity": "LOW", "issue_text": "Standard pseudo-random generators are not suitable for security/cryptographic purposes.", "line_number": 79, "line_range": [79], "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_calls.html#b311-random", "test_id": "B311", "test_name": "blacklist"}, {"code": "13         try:\n14             response = requests.get(url)\n15             response.raise_for_status()  # Raise an exception for bad status codes\n", "col_offset": 23, "end_col_offset": 40, "filename": "app/db/models/country.py", "issue_confidence": "LOW", "issue_cwe": {"id": 400, "link": "https://cwe.mitre.org/data/definitions/400.html"}, "issue_severity": "MEDIUM", "issue_text": "Call to requests without timeout", "line_number": 14, "line_range": [14], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b113_request_without_timeout.html", "test_id": "B113", "test_name": "request_without_timeout"}, {"code": "91                 )\n92             except:\n93                 # If there's an error with the cached response, continue with the request\n94                 pass\n95         \n", "col_offset": 12, "end_col_offset": 20, "filename": "app/middleware/caching_middleware.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 92, "line_range": [92, 93, 94], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "119                 )\n120             except:\n121                 # If caching fails, just return the response\n122                 pass\n123         \n", "col_offset": 12, "end_col_offset": 20, "filename": "app/middleware/caching_middleware.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 120, "line_range": [120, 121, 122], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "160         cache_key = \":\".join(cache_key_parts)\n161         return hashlib.md5(cache_key.encode()).hexdigest()\n", "col_offset": 15, "end_col_offset": 46, "filename": "app/middleware/caching_middleware.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "HIGH", "issue_text": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "line_number": 161, "line_range": [161], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html", "test_id": "B324", "test_name": "<PERSON><PERSON><PERSON>"}, {"code": "76     # Check response\n77     assert response.status_code == 200\n78     assert len(response.json()) == 2\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 77, "line_range": [77], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "77     assert response.status_code == 200\n78     assert len(response.json()) == 2\n79     assert response.json()[0][\"title\"] == \"Test Action Plan 1\"\n", "col_offset": 4, "end_col_offset": 36, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 78, "line_range": [78], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "78     assert len(response.json()) == 2\n79     assert response.json()[0][\"title\"] == \"Test Action Plan 1\"\n80     assert response.json()[1][\"title\"] == \"Test Action Plan 2\"\n", "col_offset": 4, "end_col_offset": 62, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 79, "line_range": [79], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "79     assert response.json()[0][\"title\"] == \"Test Action Plan 1\"\n80     assert response.json()[1][\"title\"] == \"Test Action Plan 2\"\n81     \n", "col_offset": 4, "end_col_offset": 62, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 80, "line_range": [80], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "127     # Check response\n128     assert response.status_code == 201\n129     assert response.json()[\"title\"] == \"New Action Plan\"\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 128, "line_range": [128], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "128     assert response.status_code == 201\n129     assert response.json()[\"title\"] == \"New Action Plan\"\n130     assert response.json()[\"id\"] == str(action_plan_id)\n", "col_offset": 4, "end_col_offset": 56, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 129, "line_range": [129], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "129     assert response.json()[\"title\"] == \"New Action Plan\"\n130     assert response.json()[\"id\"] == str(action_plan_id)\n131     \n", "col_offset": 4, "end_col_offset": 55, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 130, "line_range": [130], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "166     # Check response\n167     assert response.status_code == 200\n168     assert response.json()[\"title\"] == \"Test Action Plan\"\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 167, "line_range": [167], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "167     assert response.status_code == 200\n168     assert response.json()[\"title\"] == \"Test Action Plan\"\n169     assert response.json()[\"id\"] == str(action_plan_id)\n", "col_offset": 4, "end_col_offset": 57, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 168, "line_range": [168], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "168     assert response.json()[\"title\"] == \"Test Action Plan\"\n169     assert response.json()[\"id\"] == str(action_plan_id)\n170     \n", "col_offset": 4, "end_col_offset": 55, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 169, "line_range": [169], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "186     # Check response\n187     assert response.status_code == 404\n188     assert response.json()[\"detail\"] == \"Action plan not found\"\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 187, "line_range": [187], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "187     assert response.status_code == 404\n188     assert response.json()[\"detail\"] == \"Action plan not found\"\n189     \n", "col_offset": 4, "end_col_offset": 63, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 188, "line_range": [188], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "231     # Check response\n232     assert response.status_code == 200\n233     assert response.json()[\"title\"] == \"Updated Action Plan\"\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 232, "line_range": [232], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "232     assert response.status_code == 200\n233     assert response.json()[\"title\"] == \"Updated Action Plan\"\n234     assert response.json()[\"description\"] == \"Updated Description\"\n", "col_offset": 4, "end_col_offset": 60, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 233, "line_range": [233], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "233     assert response.json()[\"title\"] == \"Updated Action Plan\"\n234     assert response.json()[\"description\"] == \"Updated Description\"\n235     assert response.json()[\"status\"] == \"in_progress\"\n", "col_offset": 4, "end_col_offset": 66, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 234, "line_range": [234], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "234     assert response.json()[\"description\"] == \"Updated Description\"\n235     assert response.json()[\"status\"] == \"in_progress\"\n236     assert response.json()[\"priority\"] == \"medium\"\n", "col_offset": 4, "end_col_offset": 53, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 235, "line_range": [235], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "235     assert response.json()[\"status\"] == \"in_progress\"\n236     assert response.json()[\"priority\"] == \"medium\"\n237     \n", "col_offset": 4, "end_col_offset": 50, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 236, "line_range": [236], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "273     # Check response\n274     assert response.status_code == 200\n275     assert response.json()[\"is_deleted\"] == True\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 274, "line_range": [274], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "274     assert response.status_code == 200\n275     assert response.json()[\"is_deleted\"] == True\n276     \n", "col_offset": 4, "end_col_offset": 48, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 275, "line_range": [275], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "310     # Check response\n311     assert response.status_code == 200\n312     assert len(response.json()) == 1\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 311, "line_range": [311], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "311     assert response.status_code == 200\n312     assert len(response.json()) == 1\n313     assert response.json()[0][\"action_plan_id\"] == str(action_plan_id)\n", "col_offset": 4, "end_col_offset": 36, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 312, "line_range": [312], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "312     assert len(response.json()) == 1\n313     assert response.json()[0][\"action_plan_id\"] == str(action_plan_id)\n314     assert response.json()[0][\"title\"] == \"Test Action Item\"\n", "col_offset": 4, "end_col_offset": 70, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 313, "line_range": [313], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "313     assert response.json()[0][\"action_plan_id\"] == str(action_plan_id)\n314     assert response.json()[0][\"title\"] == \"Test Action Item\"\n315     \n", "col_offset": 4, "end_col_offset": 60, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 314, "line_range": [314], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "356     # Check response\n357     assert response.status_code == 201\n358     assert response.json()[\"title\"] == \"New Action Item\"\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 357, "line_range": [357], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "357     assert response.status_code == 201\n358     assert response.json()[\"title\"] == \"New Action Item\"\n359     assert response.json()[\"id\"] == str(item_id)\n", "col_offset": 4, "end_col_offset": 56, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 358, "line_range": [358], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "358     assert response.json()[\"title\"] == \"New Action Item\"\n359     assert response.json()[\"id\"] == str(item_id)\n360     \n", "col_offset": 4, "end_col_offset": 48, "filename": "app/tests/api/governance/test_action_plan_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 359, "line_range": [359], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "72     # Check response\n73     assert response.status_code == 200\n74     assert len(response.json()) == 2\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 73, "line_range": [73], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "73     assert response.status_code == 200\n74     assert len(response.json()) == 2\n75     assert response.json()[0][\"title\"] == \"Test Assessment 1\"\n", "col_offset": 4, "end_col_offset": 36, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 74, "line_range": [74], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "74     assert len(response.json()) == 2\n75     assert response.json()[0][\"title\"] == \"Test Assessment 1\"\n76     assert response.json()[1][\"title\"] == \"Test Assessment 2\"\n", "col_offset": 4, "end_col_offset": 61, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 75, "line_range": [75], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "75     assert response.json()[0][\"title\"] == \"Test Assessment 1\"\n76     assert response.json()[1][\"title\"] == \"Test Assessment 2\"\n77     \n", "col_offset": 4, "end_col_offset": 61, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 76, "line_range": [76], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "118     # Check response\n119     assert response.status_code == 201\n120     assert response.json()[\"title\"] == \"New Assessment\"\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 119, "line_range": [119], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "119     assert response.status_code == 201\n120     assert response.json()[\"title\"] == \"New Assessment\"\n121     assert response.json()[\"id\"] == str(assessment_id)\n", "col_offset": 4, "end_col_offset": 55, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 120, "line_range": [120], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "120     assert response.json()[\"title\"] == \"New Assessment\"\n121     assert response.json()[\"id\"] == str(assessment_id)\n122     \n", "col_offset": 4, "end_col_offset": 54, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 121, "line_range": [121], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "154     # Check response\n155     assert response.status_code == 200\n156     assert response.json()[\"title\"] == \"Test Assessment\"\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 155, "line_range": [155], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "155     assert response.status_code == 200\n156     assert response.json()[\"title\"] == \"Test Assessment\"\n157     assert response.json()[\"id\"] == str(assessment_id)\n", "col_offset": 4, "end_col_offset": 56, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 156, "line_range": [156], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "156     assert response.json()[\"title\"] == \"Test Assessment\"\n157     assert response.json()[\"id\"] == str(assessment_id)\n158     \n", "col_offset": 4, "end_col_offset": 54, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 157, "line_range": [157], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "174     # Check response\n175     assert response.status_code == 404\n176     assert response.json()[\"detail\"] == \"Assessment not found\"\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 175, "line_range": [175], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "175     assert response.status_code == 404\n176     assert response.json()[\"detail\"] == \"Assessment not found\"\n177     \n", "col_offset": 4, "end_col_offset": 62, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 176, "line_range": [176], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "215     # Check response\n216     assert response.status_code == 200\n217     assert response.json()[\"title\"] == \"Updated Assessment\"\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 216, "line_range": [216], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "216     assert response.status_code == 200\n217     assert response.json()[\"title\"] == \"Updated Assessment\"\n218     assert response.json()[\"description\"] == \"Updated Description\"\n", "col_offset": 4, "end_col_offset": 59, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 217, "line_range": [217], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "217     assert response.json()[\"title\"] == \"Updated Assessment\"\n218     assert response.json()[\"description\"] == \"Updated Description\"\n219     assert response.json()[\"status\"] == \"in_progress\"\n", "col_offset": 4, "end_col_offset": 66, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 218, "line_range": [218], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "218     assert response.json()[\"description\"] == \"Updated Description\"\n219     assert response.json()[\"status\"] == \"in_progress\"\n220     \n", "col_offset": 4, "end_col_offset": 53, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 219, "line_range": [219], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "253     # Check response\n254     assert response.status_code == 200\n255     assert response.json()[\"is_deleted\"] == True\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 254, "line_range": [254], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "254     assert response.status_code == 200\n255     assert response.json()[\"is_deleted\"] == True\n256     \n", "col_offset": 4, "end_col_offset": 48, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 255, "line_range": [255], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "287     # Check response\n288     assert response.status_code == 200\n289     assert len(response.json()) == 1\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 288, "line_range": [288], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "288     assert response.status_code == 200\n289     assert len(response.json()) == 1\n290     assert response.json()[0][\"assessment_id\"] == str(assessment_id)\n", "col_offset": 4, "end_col_offset": 36, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 289, "line_range": [289], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "289     assert len(response.json()) == 1\n290     assert response.json()[0][\"assessment_id\"] == str(assessment_id)\n291     assert response.json()[0][\"result\"] == \"compliant\"\n", "col_offset": 4, "end_col_offset": 68, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 290, "line_range": [290], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "290     assert response.json()[0][\"assessment_id\"] == str(assessment_id)\n291     assert response.json()[0][\"result\"] == \"compliant\"\n292     \n", "col_offset": 4, "end_col_offset": 54, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 291, "line_range": [291], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "330     # Check response\n331     assert response.status_code == 201\n332     assert response.json()[\"assessment_id\"] == str(assessment_id)\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 331, "line_range": [331], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "331     assert response.status_code == 201\n332     assert response.json()[\"assessment_id\"] == str(assessment_id)\n333     assert response.json()[\"entity_id\"] == str(entity_id)\n", "col_offset": 4, "end_col_offset": 65, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 332, "line_range": [332], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "332     assert response.json()[\"assessment_id\"] == str(assessment_id)\n333     assert response.json()[\"entity_id\"] == str(entity_id)\n334     assert response.json()[\"result\"] == \"compliant\"\n", "col_offset": 4, "end_col_offset": 57, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 333, "line_range": [333], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "333     assert response.json()[\"entity_id\"] == str(entity_id)\n334     assert response.json()[\"result\"] == \"compliant\"\n335     \n", "col_offset": 4, "end_col_offset": 51, "filename": "app/tests/api/governance/test_assessment_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 334, "line_range": [334], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "72     # Check response\n73     assert response.status_code == 200\n74     assert len(response.json()) == 2\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_control_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 73, "line_range": [73], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "73     assert response.status_code == 200\n74     assert len(response.json()) == 2\n75     assert response.json()[0][\"name\"] == \"Test Control 1\"\n", "col_offset": 4, "end_col_offset": 36, "filename": "app/tests/api/governance/test_control_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 74, "line_range": [74], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "74     assert len(response.json()) == 2\n75     assert response.json()[0][\"name\"] == \"Test Control 1\"\n76     assert response.json()[1][\"name\"] == \"Test Control 2\"\n", "col_offset": 4, "end_col_offset": 57, "filename": "app/tests/api/governance/test_control_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 75, "line_range": [75], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "75     assert response.json()[0][\"name\"] == \"Test Control 1\"\n76     assert response.json()[1][\"name\"] == \"Test Control 2\"\n77     \n", "col_offset": 4, "end_col_offset": 57, "filename": "app/tests/api/governance/test_control_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 76, "line_range": [76], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "119     # Check response\n120     assert response.status_code == 201\n121     assert response.json()[\"name\"] == \"New Control\"\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_control_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 120, "line_range": [120], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "120     assert response.status_code == 201\n121     assert response.json()[\"name\"] == \"New Control\"\n122     assert response.json()[\"id\"] == str(control_id)\n", "col_offset": 4, "end_col_offset": 51, "filename": "app/tests/api/governance/test_control_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 121, "line_range": [121], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "121     assert response.json()[\"name\"] == \"New Control\"\n122     assert response.json()[\"id\"] == str(control_id)\n123     \n", "col_offset": 4, "end_col_offset": 51, "filename": "app/tests/api/governance/test_control_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 122, "line_range": [122], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "155     # Check response\n156     assert response.status_code == 200\n157     assert response.json()[\"name\"] == \"Test Control\"\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_control_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 156, "line_range": [156], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "156     assert response.status_code == 200\n157     assert response.json()[\"name\"] == \"Test Control\"\n158     assert response.json()[\"id\"] == str(control_id)\n", "col_offset": 4, "end_col_offset": 52, "filename": "app/tests/api/governance/test_control_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 157, "line_range": [157], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "157     assert response.json()[\"name\"] == \"Test Control\"\n158     assert response.json()[\"id\"] == str(control_id)\n159     \n", "col_offset": 4, "end_col_offset": 51, "filename": "app/tests/api/governance/test_control_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 158, "line_range": [158], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "175     # Check response\n176     assert response.status_code == 404\n177     assert response.json()[\"detail\"] == \"Control not found\"\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_control_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 176, "line_range": [176], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "176     assert response.status_code == 404\n177     assert response.json()[\"detail\"] == \"Control not found\"\n178     \n", "col_offset": 4, "end_col_offset": 59, "filename": "app/tests/api/governance/test_control_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 177, "line_range": [177], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "215     # Check response\n216     assert response.status_code == 200\n217     assert response.json()[\"name\"] == \"Updated Control\"\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_control_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 216, "line_range": [216], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "216     assert response.status_code == 200\n217     assert response.json()[\"name\"] == \"Updated Control\"\n218     assert response.json()[\"description\"] == \"Updated Description\"\n", "col_offset": 4, "end_col_offset": 55, "filename": "app/tests/api/governance/test_control_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 217, "line_range": [217], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "217     assert response.json()[\"name\"] == \"Updated Control\"\n218     assert response.json()[\"description\"] == \"Updated Description\"\n219     \n", "col_offset": 4, "end_col_offset": 66, "filename": "app/tests/api/governance/test_control_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 218, "line_range": [218], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "252     # Check response\n253     assert response.status_code == 200\n254     assert response.json()[\"is_deleted\"] == True\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_control_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 253, "line_range": [253], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "253     assert response.status_code == 200\n254     assert response.json()[\"is_deleted\"] == True\n255     \n", "col_offset": 4, "end_col_offset": 48, "filename": "app/tests/api/governance/test_control_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 254, "line_range": [254], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "283     # Check response\n284     assert response.status_code == 200\n285     assert len(response.json()) == 1\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_control_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 284, "line_range": [284], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "284     assert response.status_code == 200\n285     assert len(response.json()) == 1\n286     assert response.json()[0][\"control_id\"] == str(control_id)\n", "col_offset": 4, "end_col_offset": 36, "filename": "app/tests/api/governance/test_control_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 285, "line_range": [285], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "285     assert len(response.json()) == 1\n286     assert response.json()[0][\"control_id\"] == str(control_id)\n287     assert response.json()[0][\"regulation_id\"] == str(regulation_id)\n", "col_offset": 4, "end_col_offset": 62, "filename": "app/tests/api/governance/test_control_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 286, "line_range": [286], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "286     assert response.json()[0][\"control_id\"] == str(control_id)\n287     assert response.json()[0][\"regulation_id\"] == str(regulation_id)\n288     \n", "col_offset": 4, "end_col_offset": 68, "filename": "app/tests/api/governance/test_control_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 287, "line_range": [287], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "76     # Check response\n77     assert response.status_code == 200\n78     assert len(response.json()) == 2\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_incident_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 77, "line_range": [77], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "77     assert response.status_code == 200\n78     assert len(response.json()) == 2\n79     assert response.json()[0][\"title\"] == \"Test Incident 1\"\n", "col_offset": 4, "end_col_offset": 36, "filename": "app/tests/api/governance/test_incident_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 78, "line_range": [78], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "78     assert len(response.json()) == 2\n79     assert response.json()[0][\"title\"] == \"Test Incident 1\"\n80     assert response.json()[1][\"title\"] == \"Test Incident 2\"\n", "col_offset": 4, "end_col_offset": 59, "filename": "app/tests/api/governance/test_incident_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 79, "line_range": [79], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "79     assert response.json()[0][\"title\"] == \"Test Incident 1\"\n80     assert response.json()[1][\"title\"] == \"Test Incident 2\"\n81     \n", "col_offset": 4, "end_col_offset": 59, "filename": "app/tests/api/governance/test_incident_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 80, "line_range": [80], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "127     # Check response\n128     assert response.status_code == 201\n129     assert response.json()[\"title\"] == \"New Incident\"\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_incident_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 128, "line_range": [128], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "128     assert response.status_code == 201\n129     assert response.json()[\"title\"] == \"New Incident\"\n130     assert response.json()[\"id\"] == str(incident_id)\n", "col_offset": 4, "end_col_offset": 53, "filename": "app/tests/api/governance/test_incident_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 129, "line_range": [129], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "129     assert response.json()[\"title\"] == \"New Incident\"\n130     assert response.json()[\"id\"] == str(incident_id)\n131     \n", "col_offset": 4, "end_col_offset": 52, "filename": "app/tests/api/governance/test_incident_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 130, "line_range": [130], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "166     # Check response\n167     assert response.status_code == 200\n168     assert response.json()[\"title\"] == \"Test Incident\"\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_incident_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 167, "line_range": [167], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "167     assert response.status_code == 200\n168     assert response.json()[\"title\"] == \"Test Incident\"\n169     assert response.json()[\"id\"] == str(incident_id)\n", "col_offset": 4, "end_col_offset": 54, "filename": "app/tests/api/governance/test_incident_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 168, "line_range": [168], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "168     assert response.json()[\"title\"] == \"Test Incident\"\n169     assert response.json()[\"id\"] == str(incident_id)\n170     \n", "col_offset": 4, "end_col_offset": 52, "filename": "app/tests/api/governance/test_incident_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 169, "line_range": [169], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "186     # Check response\n187     assert response.status_code == 404\n188     assert response.json()[\"detail\"] == \"Incident not found\"\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_incident_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 187, "line_range": [187], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "187     assert response.status_code == 404\n188     assert response.json()[\"detail\"] == \"Incident not found\"\n189     \n", "col_offset": 4, "end_col_offset": 60, "filename": "app/tests/api/governance/test_incident_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 188, "line_range": [188], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "231     # Check response\n232     assert response.status_code == 200\n233     assert response.json()[\"title\"] == \"Updated Incident\"\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_incident_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 232, "line_range": [232], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "232     assert response.status_code == 200\n233     assert response.json()[\"title\"] == \"Updated Incident\"\n234     assert response.json()[\"description\"] == \"Updated Description\"\n", "col_offset": 4, "end_col_offset": 57, "filename": "app/tests/api/governance/test_incident_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 233, "line_range": [233], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "233     assert response.json()[\"title\"] == \"Updated Incident\"\n234     assert response.json()[\"description\"] == \"Updated Description\"\n235     assert response.json()[\"severity\"] == \"medium\"\n", "col_offset": 4, "end_col_offset": 66, "filename": "app/tests/api/governance/test_incident_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 234, "line_range": [234], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "234     assert response.json()[\"description\"] == \"Updated Description\"\n235     assert response.json()[\"severity\"] == \"medium\"\n236     assert response.json()[\"status\"] == \"in_progress\"\n", "col_offset": 4, "end_col_offset": 50, "filename": "app/tests/api/governance/test_incident_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 235, "line_range": [235], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "235     assert response.json()[\"severity\"] == \"medium\"\n236     assert response.json()[\"status\"] == \"in_progress\"\n237     \n", "col_offset": 4, "end_col_offset": 53, "filename": "app/tests/api/governance/test_incident_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 236, "line_range": [236], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "273     # Check response\n274     assert response.status_code == 200\n275     assert response.json()[\"is_deleted\"] == True\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_incident_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 274, "line_range": [274], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "274     assert response.status_code == 200\n275     assert response.json()[\"is_deleted\"] == True\n276     \n", "col_offset": 4, "end_col_offset": 48, "filename": "app/tests/api/governance/test_incident_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 275, "line_range": [275], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "306     # Check response\n307     assert response.status_code == 200\n308     assert len(response.json()) == 1\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_incident_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 307, "line_range": [307], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "307     assert response.status_code == 200\n308     assert len(response.json()) == 1\n309     assert response.json()[0][\"incident_id\"] == str(incident_id)\n", "col_offset": 4, "end_col_offset": 36, "filename": "app/tests/api/governance/test_incident_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 308, "line_range": [308], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "308     assert len(response.json()) == 1\n309     assert response.json()[0][\"incident_id\"] == str(incident_id)\n310     assert response.json()[0][\"content\"] == \"Test comment\"\n", "col_offset": 4, "end_col_offset": 64, "filename": "app/tests/api/governance/test_incident_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 309, "line_range": [309], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "309     assert response.json()[0][\"incident_id\"] == str(incident_id)\n310     assert response.json()[0][\"content\"] == \"Test comment\"\n311     \n", "col_offset": 4, "end_col_offset": 58, "filename": "app/tests/api/governance/test_incident_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 310, "line_range": [310], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "74     # Check response\n75     assert response.status_code == 200\n76     assert len(response.json()) == 2\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_risk_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 75, "line_range": [75], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "75     assert response.status_code == 200\n76     assert len(response.json()) == 2\n77     assert response.json()[0][\"title\"] == \"Test Risk 1\"\n", "col_offset": 4, "end_col_offset": 36, "filename": "app/tests/api/governance/test_risk_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 76, "line_range": [76], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "76     assert len(response.json()) == 2\n77     assert response.json()[0][\"title\"] == \"Test Risk 1\"\n78     assert response.json()[1][\"title\"] == \"Test Risk 2\"\n", "col_offset": 4, "end_col_offset": 55, "filename": "app/tests/api/governance/test_risk_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 77, "line_range": [77], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "77     assert response.json()[0][\"title\"] == \"Test Risk 1\"\n78     assert response.json()[1][\"title\"] == \"Test Risk 2\"\n79     \n", "col_offset": 4, "end_col_offset": 55, "filename": "app/tests/api/governance/test_risk_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 78, "line_range": [78], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "123     # Check response\n124     assert response.status_code == 201\n125     assert response.json()[\"title\"] == \"New Risk\"\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_risk_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 124, "line_range": [124], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "124     assert response.status_code == 201\n125     assert response.json()[\"title\"] == \"New Risk\"\n126     assert response.json()[\"id\"] == str(risk_id)\n", "col_offset": 4, "end_col_offset": 49, "filename": "app/tests/api/governance/test_risk_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 125, "line_range": [125], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "125     assert response.json()[\"title\"] == \"New Risk\"\n126     assert response.json()[\"id\"] == str(risk_id)\n127     \n", "col_offset": 4, "end_col_offset": 48, "filename": "app/tests/api/governance/test_risk_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 126, "line_range": [126], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "160     # Check response\n161     assert response.status_code == 200\n162     assert response.json()[\"title\"] == \"Test Risk\"\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_risk_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 161, "line_range": [161], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "161     assert response.status_code == 200\n162     assert response.json()[\"title\"] == \"Test Risk\"\n163     assert response.json()[\"id\"] == str(risk_id)\n", "col_offset": 4, "end_col_offset": 50, "filename": "app/tests/api/governance/test_risk_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 162, "line_range": [162], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "162     assert response.json()[\"title\"] == \"Test Risk\"\n163     assert response.json()[\"id\"] == str(risk_id)\n164     \n", "col_offset": 4, "end_col_offset": 48, "filename": "app/tests/api/governance/test_risk_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 163, "line_range": [163], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "180     # Check response\n181     assert response.status_code == 404\n182     assert response.json()[\"detail\"] == \"Risk not found\"\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_risk_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 181, "line_range": [181], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "181     assert response.status_code == 404\n182     assert response.json()[\"detail\"] == \"Risk not found\"\n183     \n", "col_offset": 4, "end_col_offset": 56, "filename": "app/tests/api/governance/test_risk_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 182, "line_range": [182], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "221     # Check response\n222     assert response.status_code == 200\n223     assert response.json()[\"title\"] == \"Updated Risk\"\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_risk_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 222, "line_range": [222], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "222     assert response.status_code == 200\n223     assert response.json()[\"title\"] == \"Updated Risk\"\n224     assert response.json()[\"description\"] == \"Updated Description\"\n", "col_offset": 4, "end_col_offset": 53, "filename": "app/tests/api/governance/test_risk_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 223, "line_range": [223], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "223     assert response.json()[\"title\"] == \"Updated Risk\"\n224     assert response.json()[\"description\"] == \"Updated Description\"\n225     \n", "col_offset": 4, "end_col_offset": 66, "filename": "app/tests/api/governance/test_risk_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 224, "line_range": [224], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "259     # Check response\n260     assert response.status_code == 200\n261     assert response.json()[\"is_deleted\"] == True\n", "col_offset": 4, "end_col_offset": 38, "filename": "app/tests/api/governance/test_risk_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 260, "line_range": [260], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "260     assert response.status_code == 200\n261     assert response.json()[\"is_deleted\"] == True\n262     \n", "col_offset": 4, "end_col_offset": 48, "filename": "app/tests/api/governance/test_risk_api.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 261, "line_range": [261], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "51     key = f\"{func_name}:{args_str}:{kwargs_str}\"\n52     return hashlib.md5(key.encode()).hexdigest()\n53 \n", "col_offset": 11, "end_col_offset": 36, "filename": "app/utils/cache.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "HIGH", "issue_text": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "line_number": 52, "line_range": [52], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html", "test_id": "B324", "test_name": "<PERSON><PERSON><PERSON>"}, {"code": "106                 )\n107             except:\n108                 # If caching fails, just return the result\n109                 pass\n110             \n", "col_offset": 12, "end_col_offset": 20, "filename": "app/utils/cache.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 107, "line_range": [107, 108, 109], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "172             \n173             cache_key = hashlib.md5(\":\".join(cache_key_parts).encode()).hexdigest()\n174             \n", "col_offset": 24, "end_col_offset": 71, "filename": "app/utils/cache.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "HIGH", "issue_text": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "line_number": 173, "line_range": [173], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html", "test_id": "B324", "test_name": "<PERSON><PERSON><PERSON>"}, {"code": "210                 )\n211             except:\n212                 # If caching fails, just return the response\n213                 pass\n214             \n", "col_offset": 12, "end_col_offset": 20, "filename": "app/utils/cache.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Pass detected.", "line_number": 211, "line_range": [211, 212, 213], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html", "test_id": "B110", "test_name": "try_except_pass"}, {"code": "242                         return href\n243             except:\n244                 continue\n245         \n", "col_offset": 12, "end_col_offset": 24, "filename": "app/utils/regulatory_scraper.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Try, Except, Continue detected.", "line_number": 243, "line_range": [243, 244], "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b112_try_except_continue.html", "test_id": "B112", "test_name": "try_except_continue"}]}