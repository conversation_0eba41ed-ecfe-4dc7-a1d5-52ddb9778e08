{"timestamp": "2025-06-10T16:09:30.901942", "unit_tests": {"status": "failed", "returncode": 5, "stdout": "============================= test session starts ==============================\nplatform linux -- Python 3.10.12, pytest-8.4.0, pluggy-1.6.0 -- /usr/bin/python\ncachedir: .pytest_cache\nmetadata: {'Python': '3.10.12', 'Platform': 'Linux-6.12.29-aug1-x86_64-with-glibc2.35', 'Packages': {'pytest': '8.4.0', 'pluggy': '1.6.0'}, 'Plugins': {'anyio': '4.9.0', 'asyncio': '1.0.0', 'xdist': '3.7.0', 'metadata': '3.1.1', 'cov': '6.1.1', 'bdd': '8.1.0', 'base-url': '2.1.0', 'playwright': '0.7.0', 'html': '4.1.1'}, 'Base URL': ''}\nrootdir: /mnt/persist/workspace\nconfigfile: pytest.ini\nplugins: anyio-4.9.0, asyncio-1.0.0, xdist-3.7.0, metadata-3.1.1, cov-6.1.1, bdd-8.1.0, base-url-2.1.0, playwright-0.7.0, html-4.1.1\nasyncio: mode=strict, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function\ncreated: 4/4 workers\n4 workers [0 items]\n\nscheduling tests via WorkStealingScheduling\n\n=============================== warnings summary ===============================\n../../../home/<USER>/.local/lib/python3.10/site-packages/starlette/middleware/wsgi.py:14\n../../../home/<USER>/.local/lib/python3.10/site-packages/starlette/middleware/wsgi.py:14\n../../../home/<USER>/.local/lib/python3.10/site-packages/starlette/middleware/wsgi.py:14\n../../../home/<USER>/.local/lib/python3.10/site-packages/starlette/middleware/wsgi.py:14\n../../../home/<USER>/.local/lib/python3.10/site-packages/starlette/middleware/wsgi.py:14\n  /home/<USER>/.local/lib/python3.10/site-packages/starlette/middleware/wsgi.py:14: DeprecationWarning: starlette.middleware.wsgi is deprecated and will be removed in a future release. Please refer to https://github.com/abersheeran/a2wsgi as a replacement.\n    warnings.warn(\n\napp/db/models.py:10\napp/db/models.py:10\napp/db/models.py:10\napp/db/models.py:10\napp/db/models.py:10\n  /mnt/persist/workspace/app/db/models.py:10: MovedIn20Warning: The ``declarative_base()`` function is now available as sqlalchemy.orm.declarative_base(). (deprecated since: 2.0) (Background on SQLAlchemy 2.0 at: https://sqlalche.me/e/b8d9)\n    Base = declarative_base()\n\n../../../home/<USER>/.local/lib/python3.10/site-packages/pydantic/_internal/_config.py:291: 25 warnings\n  /home/<USER>/.local/lib/python3.10/site-packages/pydantic/_internal/_config.py:291: PydanticDeprecatedSince20: Support for class-based `config` is deprecated, use ConfigDict instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    warnings.warn(DEPRECATION_MESSAGE, DeprecationWarning)\n\n../../../home/<USER>/.local/lib/python3.10/site-packages/pydantic/_internal/_config.py:341: 10 warnings\n  /home/<USER>/.local/lib/python3.10/site-packages/pydantic/_internal/_config.py:341: UserWarning: Valid config keys have changed in V2:\n  * 'orm_mode' has been renamed to 'from_attributes'\n    warnings.warn(message, UserWarning)\n\n../../../home/<USER>/.local/lib/python3.10/site-packages/PyPDF2/__init__.py:21\n../../../home/<USER>/.local/lib/python3.10/site-packages/PyPDF2/__init__.py:21\n../../../home/<USER>/.local/lib/python3.10/site-packages/PyPDF2/__init__.py:21\n../../../home/<USER>/.local/lib/python3.10/site-packages/PyPDF2/__init__.py:21\n../../../home/<USER>/.local/lib/python3.10/site-packages/PyPDF2/__init__.py:21\n  /home/<USER>/.local/lib/python3.10/site-packages/PyPDF2/__init__.py:21: DeprecationWarning: PyPDF2 is deprecated. Please move to the pypdf library instead.\n    warnings.warn(\n\napp/schemas/alerts.py:29\napp/schemas/alerts.py:29\napp/schemas/alerts.py:29\napp/schemas/alerts.py:29\napp/schemas/alerts.py:29\n  /mnt/persist/workspace/app/schemas/alerts.py:29: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('countries', each_item=True)\n\napp/schemas/calendar.py:26\napp/schemas/calendar.py:26\napp/schemas/calendar.py:26\napp/schemas/calendar.py:26\napp/schemas/calendar.py:26\n  /mnt/persist/workspace/app/schemas/calendar.py:26: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('end_date')\n\napp/schemas/calendar.py:33\napp/schemas/calendar.py:33\napp/schemas/calendar.py:33\napp/schemas/calendar.py:33\napp/schemas/calendar.py:33\n  /mnt/persist/workspace/app/schemas/calendar.py:33: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('priority')\n\napp/schemas/calendar.py:41\napp/schemas/calendar.py:41\napp/schemas/calendar.py:41\napp/schemas/calendar.py:41\napp/schemas/calendar.py:41\n  /mnt/persist/workspace/app/schemas/calendar.py:41: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('status')\n\napp/schemas/calendar.py:105\napp/schemas/calendar.py:105\napp/schemas/calendar.py:105\napp/schemas/calendar.py:105\napp/schemas/calendar.py:105\n  /mnt/persist/workspace/app/schemas/calendar.py:105: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('priority')\n\napp/schemas/calendar.py:114\napp/schemas/calendar.py:114\napp/schemas/calendar.py:114\napp/schemas/calendar.py:114\napp/schemas/calendar.py:114\n  /mnt/persist/workspace/app/schemas/calendar.py:114: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('status')\n\napp/schemas/calendar.py:149\napp/schemas/calendar.py:149\napp/schemas/calendar.py:149\napp/schemas/calendar.py:149\napp/schemas/calendar.py:149\n  /mnt/persist/workspace/app/schemas/calendar.py:149: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('reminder_type')\n\napp/schemas/calendar.py:157\napp/schemas/calendar.py:157\napp/schemas/calendar.py:157\napp/schemas/calendar.py:157\napp/schemas/calendar.py:157\n  /mnt/persist/workspace/app/schemas/calendar.py:157: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('time_unit')\n\napp/schemas/calendar.py:190\napp/schemas/calendar.py:190\napp/schemas/calendar.py:190\napp/schemas/calendar.py:190\napp/schemas/calendar.py:190\n  /mnt/persist/workspace/app/schemas/calendar.py:190: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('reminder_type')\n\napp/schemas/calendar.py:199\napp/schemas/calendar.py:199\napp/schemas/calendar.py:199\napp/schemas/calendar.py:199\napp/schemas/calendar.py:199\n  /mnt/persist/workspace/app/schemas/calendar.py:199: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('time_unit')\n\napp/schemas/calendar.py:245\napp/schemas/calendar.py:245\napp/schemas/calendar.py:245\napp/schemas/calendar.py:245\napp/schemas/calendar.py:245\n  /mnt/persist/workspace/app/schemas/calendar.py:245: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('end_date')\n\napp/schemas/calendar.py:266\napp/schemas/calendar.py:266\napp/schemas/calendar.py:266\napp/schemas/calendar.py:266\napp/schemas/calendar.py:266\n  /mnt/persist/workspace/app/schemas/calendar.py:266: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('format')\n\napp/schemas/regulation.py:387\napp/schemas/regulation.py:387\napp/schemas/regulation.py:387\napp/schemas/regulation.py:387\napp/schemas/regulation.py:387\n  /mnt/persist/workspace/app/schemas/regulation.py:387: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator(\"enrichment_type\", pre=True)\n\napp/schemas/regulation.py:415\napp/schemas/regulation.py:415\napp/schemas/regulation.py:415\napp/schemas/regulation.py:415\napp/schemas/regulation.py:415\n  /mnt/persist/workspace/app/schemas/regulation.py:415: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator(\"enrichment_type\", pre=True)\n\napp/schemas/analytics.py:19\napp/schemas/analytics.py:19\napp/schemas/analytics.py:19\napp/schemas/analytics.py:19\napp/schemas/analytics.py:19\n  /mnt/persist/workspace/app/schemas/analytics.py:19: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('end_date')\n\n../../../home/<USER>/.local/lib/python3.10/site-packages/_pytest/config/__init__.py:1474\n../../../home/<USER>/.local/lib/python3.10/site-packages/_pytest/config/__init__.py:1474\n../../../home/<USER>/.local/lib/python3.10/site-packages/_pytest/config/__init__.py:1474\n../../../home/<USER>/.local/lib/python3.10/site-packages/_pytest/config/__init__.py:1474\n../../../home/<USER>/.local/lib/python3.10/site-packages/_pytest/config/__init__.py:1474\n  /home/<USER>/.local/lib/python3.10/site-packages/_pytest/config/__init__.py:1474: PytestConfigWarning: Unknown config option: timeout\n  \n    self._warn_or_fail_if_strict(f\"Unknown config option: {key}\\n\")\n\n../../../home/<USER>/.local/lib/python3.10/site-packages/_pytest/config/__init__.py:1474\n../../../home/<USER>/.local/lib/python3.10/site-packages/_pytest/config/__init__.py:1474\n../../../home/<USER>/.local/lib/python3.10/site-packages/_pytest/config/__init__.py:1474\n../../../home/<USER>/.local/lib/python3.10/site-packages/_pytest/config/__init__.py:1474\n../../../home/<USER>/.local/lib/python3.10/site-packages/_pytest/config/__init__.py:1474\n  /home/<USER>/.local/lib/python3.10/site-packages/_pytest/config/__init__.py:1474: PytestConfigWarning: Unknown config option: timeout_method\n  \n    self._warn_or_fail_if_strict(f\"Unknown config option: {key}\\n\")\n\n-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html\n---- generated xml file: /mnt/persist/workspace/test-reports/unit-tests.xml ----\n================================ tests coverage ================================\n___________________________________ coverage ___________________________________\n\n___________________________ coverage: failed workers ___________________________\n\nThe following workers failed to return coverage data, ensure that pytest-cov is installed on these workers.\ngw1\ngw3\ngw0\ngw2\nName                                                 Stmts   Miss  Cover   Missing\n----------------------------------------------------------------------------------\napp/__init__.py                                          0      0   100%\napp/admin/__init__.py                                   14      5    64%   13-18\napp/admin/admin.py                                      44      0   100%\napp/api/__init__.py                                      0      0   100%\napp/api/ai/__init__.py                                   0      0   100%\napp/api/ai/gemini_client.py                             75     48    36%   34-51, 98-180, 203-237\napp/api/ai/rate_limiter.py                              61     29    52%   39-45, 58-80, 122-126, 151-154\napp/api/ai/router.py                                    18      8    56%   35-70\napp/api/ai/schemas.py                                   15      0   100%\napp/api/alerts/__init__.py                               0      0   100%\napp/api/alerts/router.py                                35     21    40%   35-69, 87-99, 118-131\napp/api/analytics.py                                    17     17     0%   3-81\napp/api/benchmarks.py                                   50     39    22%   26-79, 91-141\napp/api/bulk_processor.py                               71     51    28%   44-61, 76-137, 151-162\napp/api/calendar.py                                    255    208    18%   50-96, 132-165, 183-188, 210-232, 250-259, 282-307, 325-330, 352-374, 392-401, 421-455, 474-516, 531-553, 572-602, 621-657, 669-714, 729-739, 753-850\napp/api/calendar_analytics.py                          201    171    15%   46-78, 104-160, 183-251, 277-326, 350-382, 399-441, 455-514\napp/api/compliance.py                                   49     38    22%   29-119, 129-155\napp/api/compliance_calendar.py                          60     60     0%   5-400\napp/api/compliance_requirements/__init__.py              2      0   100%\napp/api/compliance_requirements/router.py               79     60    24%   42-62, 82-85, 105-123, 145-169, 189-198\napp/api/countries/__init__.py                            2      0   100%\napp/api/countries/router.py                             73     54    26%   38-52, 72-75, 95-116, 138-156, 176-192\napp/api/data_collection.py                             160    122    24%   55-104, 133-166, 190-222, 244-287, 308-338, 354-419\napp/api/data_enrichment.py                             252    197    22%   59-71, 81-92, 101-163, 173-199, 208-268, 272-326, 330-391, 395-437, 442-457, 461-473, 477-495, 499-511\napp/api/data_sources/__init__.py                         0      0   100%\napp/api/data_sources/base_client.py                     58     35    40%   35-53, 57-67, 90-123, 132\napp/api/data_sources/public/__init__.py                  0      0   100%\napp/api/data_sources/public/cfpb_complaints.py          74     62    16%   23-30, 39-45, 78-150, 162, 171-184, 193-206\napp/api/data_sources/public/federal_reserve.py          87     74    15%   22-29, 38-44, 67-109, 132-170, 183-190, 199-216, 228-247\napp/api/data_sources/public/finra_rules.py              85     72    15%   19-25, 34-40, 62-116, 135-197, 210-213\napp/api/data_sources/public/manager.py                  61     43    30%   40, 53-68, 77-87, 106-135\napp/api/data_sources/public/sec_edgar.py                69     56    19%   24-36, 45-51, 74-105, 127-171, 184, 197-207\napp/api/data_sources/public/usa_spending.py             85     73    14%   22-29, 38-44, 65-105, 134-211, 224-227, 236-263\napp/api/data_sources/router.py                         227    153    33%   86-133, 144, 168-190, 209-242, 266-274, 292-302, 322-352, 370-384, 406-450, 477-493, 505-634\napp/api/document_analysis.py                           151    115    24%   42-79, 93-131, 149-174, 223-258, 275-324, 343-358, 375-380\napp/api/document_import.py                             134    112    16%   40-80, 96-106, 126-255, 273-291\napp/api/governance/__init__.py                           0      0   100%\napp/api/governance/action_plan.py                      157    157     0%   4-451\napp/api/governance/assessment.py                        83     83     0%   4-232\napp/api/governance/control.py                           58     58     0%   4-161\napp/api/governance/incident.py                         105    105     0%   4-295\napp/api/governance/regulation.py                        48     48     0%   4-125\napp/api/governance/risk.py                              68     68     0%   4-193\napp/api/governance/router.py                            12     12     0%   4-20\napp/api/impact_assessment.py                           166    125    25%   57-119, 132-174, 178-185, 194-225, 235-265, 291-327, 350-397\napp/api/industries/__init__.py                           2      0   100%\napp/api/industries/router.py                            84     63    25%   36-47, 67-70, 90-107, 129-156, 176-192, 216-233\napp/api/integrations/__init__.py                         2      0   100%\napp/api/integrations/router.py                           8      1    88%   22\napp/api/integrations/superglu.py                        41     15    63%   34-42, 64-88, 96, 123\napp/api/openapi.py                                      12     12     0%   4-188\napp/api/pdf_analysis.py                                 67     46    31%   47-73, 88-114, 136-167, 184-190, 201-224\napp/api/regulation_categories/__init__.py                2      2     0%   4-6\napp/api/regulation_categories/router.py                 85     85     0%   5-234\napp/api/regulation_documents/__init__.py                 2      0   100%\napp/api/regulation_documents/router.py                  93     72    23%   40-57, 77-80, 100-118, 140-164, 184-193, 221-260\napp/api/regulation_management/__init__.py                0      0   100%\napp/api/regulation_management/gemini_enrichment.py     463    435     6%   42-87, 101-134, 152-270, 289-423, 443-549, 567-628, 647-702, 720-782, 801-864, 880, 912-954, 967-1010, 1023-1064, 1077-1120, 1132-1178, 1190-1239\napp/api/regulation_management/reporting.py              87     87     0%   4-381\napp/api/regulation_management/reporting_router.py       76     76     0%   4-173\napp/api/regulation_management/router.py                180    120    33%   40-45, 66, 91-94, 107-125, 137-142, 155-160, 173, 186-191, 204, 217-222, 235, 248-256, 271, 289-292, 305-312, 324-329, 342-350, 369-392, 407, 425-428, 441-448, 460-465, 488-512, 521\napp/api/regulation_management/service.py               182    146    20%   30-98, 115-159, 163, 167-238, 242-244, 249-256, 260, 264, 269-276, 280, 284, 289-295, 299, 303, 308-324, 335-343, 347, 356-366, 370-372, 377-392, 403-431, 442-450, 454, 463-473, 477-484, 489-498, 508\napp/api/regulation_tags/__init__.py                      2      0   100%\napp/api/regulation_tags/router.py                       84     63    25%   36-44, 64-67, 87-104, 126-153, 173-189, 213-230\napp/api/regulation_urls/__init__.py                      2      0   100%\napp/api/regulation_urls/router.py                      112     93    17%   44-67, 87-90, 110-156, 178-231, 251-260\napp/api/regulations.py                                  11     11     0%   8-146\napp/api/regulations/__init__.py                          2      0   100%\napp/api/regulations/router.py                          148    129    13%   48-77, 97-100, 120-184, 206-280, 300-321\napp/api/regulators/__init__.py                           2      0   100%\napp/api/regulators/router.py                            86     67    22%   40-54, 74-77, 97-126, 148-187, 207-223\napp/api/regulatory_alerts.py                           115     74    36%   62, 93-145, 162-185, 204-291, 314-316\napp/api/relationship_mapping.py                         67     48    28%   30-46, 59-65, 78-116, 130-139\napp/api/routes.py                                       57     23    60%   23-26, 38-49, 56-57, 67-68, 76, 81, 89, 97, 105, 113, 121\napp/api/scheduled_digests.py                           161    115    29%   71-132, 155-172, 191-298, 311-351, 371-417\napp/api/trend_analysis.py                               70     46    34%   40-132, 142-212\napp/core/__init__.py                                     0      0   100%\napp/db/__init__.py                                       6      4    33%   12-16\napp/db/database.py                                      14      5    64%   13, 35-39\napp/db/models.py                                       340     15    96%   24-25, 149, 197-198, 203-204, 209-210, 215-216, 221-222, 227-228\napp/i18n/__init__.py                                    18      4    78%   26, 30-37\napp/i18n/i18n.py                                        27     17    37%   29-48, 63-66\napp/i18n/translations.py                                35     23    34%   33-41, 55-65, 78-84, 97-101, 109\napp/main.py                                            245     95    61%   142-149, 154-156, 172-181, 188-192, 197-202, 207-212, 217-222, 228, 243-287, 300, 401-407, 418, 443-447, 469-474, 495-503, 526-539, 562-576, 597-609\napp/middleware/__init__.py                               2      0   100%\napp/middleware/caching_middleware.py                    62     50    19%   39-52, 66-124, 137-161\napp/middleware/i18n_middleware.py                       11      5    55%   25-38\napp/middleware/swagger_ui.py                            16     10    38%   11-33\napp/schemas/__init__.py                                  0      0   100%\napp/schemas/alerts.py                                   40      3    92%   31-33\napp/schemas/analytics.py                                53      3    94%   22-24\napp/schemas/calendar.py                                149     46    69%   29-31, 36-39, 44-47, 108-112, 117-121, 152-155, 160-163, 193-197, 202-206, 248-250, 269-272\napp/schemas/governance/__init__.py                       9      9     0%   4-13\napp/schemas/governance/action_plan.py                  126    126     0%   4-219\napp/schemas/governance/assessment.py                    66     66     0%   4-116\napp/schemas/governance/control.py                       38     38     0%   4-65\napp/schemas/governance/incident.py                      83     83     0%   4-145\napp/schemas/governance/organization.py                  20     20     0%   4-39\napp/schemas/governance/regulation.py                    25     25     0%   4-44\napp/schemas/governance/risk.py                          49     49     0%   4-84\napp/schemas/governance/user.py                          23     23     0%   4-43\napp/schemas/regulation.py                              231     14    94%   390-396, 418-424\napp/schemas/schemas.py                                 341      0   100%\napp/utils/__init__.py                                    0      0   100%\napp/utils/ai_summarizer.py                              95     78    18%   25, 38-72, 89-104, 125-170, 182-239\napp/utils/cache.py                                     103     88    15%   24-28, 47-52, 72-115, 135-219, 231-238, 247-254\napp/utils/document_classifier.py                       373    340     9%   25-75, 87-129, 141-192, 204-254, 271-387, 393-425, 430-468, 476-491, 495-507, 514-540, 544-575, 582-602, 618-634, 652-691, 703-747, 759-787, 799-835, 850-866, 881-904\napp/utils/notification.py                               97     74    24%   49-63, 78-106, 120-144, 165-183, 197-217\napp/utils/pdf_analyzer.py                              391    347    11%   17-19, 31-41, 53-112, 124-196, 217-219, 231-246, 258-304, 316-381, 395-445, 459-507, 520-555, 566-567, 582-594, 606-648, 660-726, 738-765, 779-800, 813-844, 858-869\napp/utils/regulatory_analysis.py                        58     50    14%   19-24, 43-63, 77-96, 113-144, 159-187\napp/utils/regulatory_parser.py                         274    244    11%   17-23, 42-53, 65-75, 88-101, 122-152, 164-222, 234-261, 273-306, 318-350, 364-441, 456-492, 496-500, 504-521, 526-534, 538-544, 549-565, 569-601, 605-616\napp/utils/regulatory_scraper.py                        136    116    15%   25-27, 42-105, 119-143, 156-182, 194-208, 222-246, 258-295, 308-326, 339-358, 373\napp/utils/relationship_mapper.py                       148    125    16%   36-56, 60-90, 94-109, 114-130, 134-179, 183-227, 231-252, 256-327\napp/utils/url_processor.py                             117    117     0%   8-423\napp/visualization/__init__.py                            0      0   100%\napp/visualization/worldmap.py                           14      8    43%   26-37, 51-52, 63\n----------------------------------------------------------------------------------\nTOTAL                                                 9401   6728    28%\nCoverage HTML written to dir coverage_html\nCoverage JSON written to file coverage.json\nLoading FastAPI app modules...\n  Loaded database engine in 0.01s\n  Loaded Base in 0.06s\n  Loaded models in 0.00s\n  Loaded schemas in 0.14s\n  Loaded database session in 0.00s\n  Loaded i18n in 0.02s\n  Loaded visualization in 0.69s\n  Loaded admin in 0.15s\nCreating database tables...\nDatabase tables created in 0.00s\n- Generated html report: file:///mnt/persist/workspace/test-reports/unit-tests.html -\n============================ 135 warnings in 12.81s ============================\n", "stderr": "2025-06-10 16:09:41,886 - WARNING - No OpenAI API key provided. AI summarization will not work.\n2025-06-10 16:09:42,867 - INFO - Registered rate limiter 'gemini' with 14 requests per minute\n2025-06-10 16:09:42,872 - WARNING - No Gemini API key provided. Gemini integration will not work.\n2025-06-10 16:09:42,952 - WARNING - No Gemini API key provided. Gemini integration will not work.\n2025-06-10 16:09:41,964 - WARNING - No OpenAI API key provided. AI summarization will not work.\n2025-06-10 16:09:42,942 - INFO - Registered rate limiter 'gemini' with 14 requests per minute\n2025-06-10 16:09:42,947 - WARNING - No Gemini API key provided. Gemini integration will not work.\n2025-06-10 16:09:43,026 - WARNING - No Gemini API key provided. Gemini integration will not work.\n2025-06-10 16:09:41,957 - WARNING - No OpenAI API key provided. AI summarization will not work.\n2025-06-10 16:09:42,939 - INFO - Registered rate limiter 'gemini' with 14 requests per minute\n2025-06-10 16:09:42,944 - WARNING - No Gemini API key provided. Gemini integration will not work.\n2025-06-10 16:09:43,024 - WARNING - No Gemini API key provided. Gemini integration will not work.\n2025-06-10 16:09:41,965 - WARNING - No OpenAI API key provided. AI summarization will not work.\n2025-06-10 16:09:42,960 - INFO - Registered rate limiter 'gemini' with 14 requests per minute\n2025-06-10 16:09:42,965 - WARNING - No Gemini API key provided. Gemini integration will not work.\n2025-06-10 16:09:43,045 - WARNING - No Gemini API key provided. Gemini integration will not work.\n2025-06-10 16:09:35,395 - WARNING - No OpenAI API key provided. AI summarization will not work.\n2025-06-10 16:09:36,384 - INFO - Registered rate limiter 'gemini' with 14 requests per minute\n2025-06-10 16:09:36,390 - WARNING - No Gemini API key provided. Gemini integration will not work.\n2025-06-10 16:09:36,482 - WARNING - No Gemini API key provided. Gemini integration will not work.\n"}, "integration_tests": {"status": "failed", "returncode": 5, "stdout": "============================= test session starts ==============================\nplatform linux -- Python 3.10.12, pytest-8.4.0, pluggy-1.6.0 -- /usr/bin/python\ncachedir: .pytest_cache\nmetadata: {'Python': '3.10.12', 'Platform': 'Linux-6.12.29-aug1-x86_64-with-glibc2.35', 'Packages': {'pytest': '8.4.0', 'pluggy': '1.6.0'}, 'Plugins': {'anyio': '4.9.0', 'asyncio': '1.0.0', 'xdist': '3.7.0', 'metadata': '3.1.1', 'cov': '6.1.1', 'bdd': '8.1.0', 'base-url': '2.1.0', 'playwright': '0.7.0', 'html': '4.1.1'}, 'Base URL': ''}\nrootdir: /mnt/persist/workspace\nconfigfile: pytest.ini\nplugins: anyio-4.9.0, asyncio-1.0.0, xdist-3.7.0, metadata-3.1.1, cov-6.1.1, bdd-8.1.0, base-url-2.1.0, playwright-0.7.0, html-4.1.1\nasyncio: mode=strict, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function\ncreated: 4/4 workers\n4 workers [0 items]\n\nscheduling tests via WorkStealingScheduling\n\n=============================== warnings summary ===============================\n../../../home/<USER>/.local/lib/python3.10/site-packages/starlette/middleware/wsgi.py:14\n../../../home/<USER>/.local/lib/python3.10/site-packages/starlette/middleware/wsgi.py:14\n../../../home/<USER>/.local/lib/python3.10/site-packages/starlette/middleware/wsgi.py:14\n../../../home/<USER>/.local/lib/python3.10/site-packages/starlette/middleware/wsgi.py:14\n../../../home/<USER>/.local/lib/python3.10/site-packages/starlette/middleware/wsgi.py:14\n  /home/<USER>/.local/lib/python3.10/site-packages/starlette/middleware/wsgi.py:14: DeprecationWarning: starlette.middleware.wsgi is deprecated and will be removed in a future release. Please refer to https://github.com/abersheeran/a2wsgi as a replacement.\n    warnings.warn(\n\napp/db/models.py:10\napp/db/models.py:10\napp/db/models.py:10\napp/db/models.py:10\napp/db/models.py:10\n  /mnt/persist/workspace/app/db/models.py:10: MovedIn20Warning: The ``declarative_base()`` function is now available as sqlalchemy.orm.declarative_base(). (deprecated since: 2.0) (Background on SQLAlchemy 2.0 at: https://sqlalche.me/e/b8d9)\n    Base = declarative_base()\n\n../../../home/<USER>/.local/lib/python3.10/site-packages/pydantic/_internal/_config.py:291: 25 warnings\n  /home/<USER>/.local/lib/python3.10/site-packages/pydantic/_internal/_config.py:291: PydanticDeprecatedSince20: Support for class-based `config` is deprecated, use ConfigDict instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    warnings.warn(DEPRECATION_MESSAGE, DeprecationWarning)\n\n../../../home/<USER>/.local/lib/python3.10/site-packages/pydantic/_internal/_config.py:341: 10 warnings\n  /home/<USER>/.local/lib/python3.10/site-packages/pydantic/_internal/_config.py:341: UserWarning: Valid config keys have changed in V2:\n  * 'orm_mode' has been renamed to 'from_attributes'\n    warnings.warn(message, UserWarning)\n\n../../../home/<USER>/.local/lib/python3.10/site-packages/PyPDF2/__init__.py:21\n../../../home/<USER>/.local/lib/python3.10/site-packages/PyPDF2/__init__.py:21\n../../../home/<USER>/.local/lib/python3.10/site-packages/PyPDF2/__init__.py:21\n../../../home/<USER>/.local/lib/python3.10/site-packages/PyPDF2/__init__.py:21\n../../../home/<USER>/.local/lib/python3.10/site-packages/PyPDF2/__init__.py:21\n  /home/<USER>/.local/lib/python3.10/site-packages/PyPDF2/__init__.py:21: DeprecationWarning: PyPDF2 is deprecated. Please move to the pypdf library instead.\n    warnings.warn(\n\napp/schemas/alerts.py:29\napp/schemas/alerts.py:29\napp/schemas/alerts.py:29\napp/schemas/alerts.py:29\napp/schemas/alerts.py:29\n  /mnt/persist/workspace/app/schemas/alerts.py:29: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('countries', each_item=True)\n\napp/schemas/calendar.py:26\napp/schemas/calendar.py:26\napp/schemas/calendar.py:26\napp/schemas/calendar.py:26\napp/schemas/calendar.py:26\n  /mnt/persist/workspace/app/schemas/calendar.py:26: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('end_date')\n\napp/schemas/calendar.py:33\napp/schemas/calendar.py:33\napp/schemas/calendar.py:33\napp/schemas/calendar.py:33\napp/schemas/calendar.py:33\n  /mnt/persist/workspace/app/schemas/calendar.py:33: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('priority')\n\napp/schemas/calendar.py:41\napp/schemas/calendar.py:41\napp/schemas/calendar.py:41\napp/schemas/calendar.py:41\napp/schemas/calendar.py:41\n  /mnt/persist/workspace/app/schemas/calendar.py:41: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('status')\n\napp/schemas/calendar.py:105\napp/schemas/calendar.py:105\napp/schemas/calendar.py:105\napp/schemas/calendar.py:105\napp/schemas/calendar.py:105\n  /mnt/persist/workspace/app/schemas/calendar.py:105: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('priority')\n\napp/schemas/calendar.py:114\napp/schemas/calendar.py:114\napp/schemas/calendar.py:114\napp/schemas/calendar.py:114\napp/schemas/calendar.py:114\n  /mnt/persist/workspace/app/schemas/calendar.py:114: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('status')\n\napp/schemas/calendar.py:149\napp/schemas/calendar.py:149\napp/schemas/calendar.py:149\napp/schemas/calendar.py:149\napp/schemas/calendar.py:149\n  /mnt/persist/workspace/app/schemas/calendar.py:149: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('reminder_type')\n\napp/schemas/calendar.py:157\napp/schemas/calendar.py:157\napp/schemas/calendar.py:157\napp/schemas/calendar.py:157\napp/schemas/calendar.py:157\n  /mnt/persist/workspace/app/schemas/calendar.py:157: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('time_unit')\n\napp/schemas/calendar.py:190\napp/schemas/calendar.py:190\napp/schemas/calendar.py:190\napp/schemas/calendar.py:190\napp/schemas/calendar.py:190\n  /mnt/persist/workspace/app/schemas/calendar.py:190: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('reminder_type')\n\napp/schemas/calendar.py:199\napp/schemas/calendar.py:199\napp/schemas/calendar.py:199\napp/schemas/calendar.py:199\napp/schemas/calendar.py:199\n  /mnt/persist/workspace/app/schemas/calendar.py:199: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('time_unit')\n\napp/schemas/calendar.py:245\napp/schemas/calendar.py:245\napp/schemas/calendar.py:245\napp/schemas/calendar.py:245\napp/schemas/calendar.py:245\n  /mnt/persist/workspace/app/schemas/calendar.py:245: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('end_date')\n\napp/schemas/calendar.py:266\napp/schemas/calendar.py:266\napp/schemas/calendar.py:266\napp/schemas/calendar.py:266\napp/schemas/calendar.py:266\n  /mnt/persist/workspace/app/schemas/calendar.py:266: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('format')\n\napp/schemas/regulation.py:387\napp/schemas/regulation.py:387\napp/schemas/regulation.py:387\napp/schemas/regulation.py:387\napp/schemas/regulation.py:387\n  /mnt/persist/workspace/app/schemas/regulation.py:387: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator(\"enrichment_type\", pre=True)\n\napp/schemas/regulation.py:415\napp/schemas/regulation.py:415\napp/schemas/regulation.py:415\napp/schemas/regulation.py:415\napp/schemas/regulation.py:415\n  /mnt/persist/workspace/app/schemas/regulation.py:415: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator(\"enrichment_type\", pre=True)\n\napp/schemas/analytics.py:19\napp/schemas/analytics.py:19\napp/schemas/analytics.py:19\napp/schemas/analytics.py:19\napp/schemas/analytics.py:19\n  /mnt/persist/workspace/app/schemas/analytics.py:19: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.9/migration/\n    @validator('end_date')\n\n../../../home/<USER>/.local/lib/python3.10/site-packages/_pytest/config/__init__.py:1474\n../../../home/<USER>/.local/lib/python3.10/site-packages/_pytest/config/__init__.py:1474\n../../../home/<USER>/.local/lib/python3.10/site-packages/_pytest/config/__init__.py:1474\n../../../home/<USER>/.local/lib/python3.10/site-packages/_pytest/config/__init__.py:1474\n../../../home/<USER>/.local/lib/python3.10/site-packages/_pytest/config/__init__.py:1474\n  /home/<USER>/.local/lib/python3.10/site-packages/_pytest/config/__init__.py:1474: PytestConfigWarning: Unknown config option: timeout\n  \n    self._warn_or_fail_if_strict(f\"Unknown config option: {key}\\n\")\n\n../../../home/<USER>/.local/lib/python3.10/site-packages/_pytest/config/__init__.py:1474\n../../../home/<USER>/.local/lib/python3.10/site-packages/_pytest/config/__init__.py:1474\n../../../home/<USER>/.local/lib/python3.10/site-packages/_pytest/config/__init__.py:1474\n../../../home/<USER>/.local/lib/python3.10/site-packages/_pytest/config/__init__.py:1474\n../../../home/<USER>/.local/lib/python3.10/site-packages/_pytest/config/__init__.py:1474\n  /home/<USER>/.local/lib/python3.10/site-packages/_pytest/config/__init__.py:1474: PytestConfigWarning: Unknown config option: timeout_method\n  \n    self._warn_or_fail_if_strict(f\"Unknown config option: {key}\\n\")\n\n-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html\n- generated xml file: /mnt/persist/workspace/test-reports/integration-tests.xml -\n================================ tests coverage ================================\n___________________________________ coverage ___________________________________\n\n___________________________ coverage: failed workers ___________________________\n\nThe following workers failed to return coverage data, ensure that pytest-cov is installed on these workers.\ngw3\ngw2\ngw0\ngw1\nName                                                 Stmts   Miss  Cover   Missing\n----------------------------------------------------------------------------------\napp/__init__.py                                          0      0   100%\napp/admin/__init__.py                                   14      5    64%   13-18\napp/admin/admin.py                                      44      0   100%\napp/api/__init__.py                                      0      0   100%\napp/api/ai/__init__.py                                   0      0   100%\napp/api/ai/gemini_client.py                             75     48    36%   34-51, 98-180, 203-237\napp/api/ai/rate_limiter.py                              61     29    52%   39-45, 58-80, 122-126, 151-154\napp/api/ai/router.py                                    18      8    56%   35-70\napp/api/ai/schemas.py                                   15      0   100%\napp/api/alerts/__init__.py                               0      0   100%\napp/api/alerts/router.py                                35     21    40%   35-69, 87-99, 118-131\napp/api/analytics.py                                    17     17     0%   3-81\napp/api/benchmarks.py                                   50     39    22%   26-79, 91-141\napp/api/bulk_processor.py                               71     51    28%   44-61, 76-137, 151-162\napp/api/calendar.py                                    255    208    18%   50-96, 132-165, 183-188, 210-232, 250-259, 282-307, 325-330, 352-374, 392-401, 421-455, 474-516, 531-553, 572-602, 621-657, 669-714, 729-739, 753-850\napp/api/calendar_analytics.py                          201    171    15%   46-78, 104-160, 183-251, 277-326, 350-382, 399-441, 455-514\napp/api/compliance.py                                   49     38    22%   29-119, 129-155\napp/api/compliance_calendar.py                          60     60     0%   5-400\napp/api/compliance_requirements/__init__.py              2      0   100%\napp/api/compliance_requirements/router.py               79     60    24%   42-62, 82-85, 105-123, 145-169, 189-198\napp/api/countries/__init__.py                            2      0   100%\napp/api/countries/router.py                             73     54    26%   38-52, 72-75, 95-116, 138-156, 176-192\napp/api/data_collection.py                             160    122    24%   55-104, 133-166, 190-222, 244-287, 308-338, 354-419\napp/api/data_enrichment.py                             252    197    22%   59-71, 81-92, 101-163, 173-199, 208-268, 272-326, 330-391, 395-437, 442-457, 461-473, 477-495, 499-511\napp/api/data_sources/__init__.py                         0      0   100%\napp/api/data_sources/base_client.py                     58     35    40%   35-53, 57-67, 90-123, 132\napp/api/data_sources/public/__init__.py                  0      0   100%\napp/api/data_sources/public/cfpb_complaints.py          74     62    16%   23-30, 39-45, 78-150, 162, 171-184, 193-206\napp/api/data_sources/public/federal_reserve.py          87     74    15%   22-29, 38-44, 67-109, 132-170, 183-190, 199-216, 228-247\napp/api/data_sources/public/finra_rules.py              85     72    15%   19-25, 34-40, 62-116, 135-197, 210-213\napp/api/data_sources/public/manager.py                  61     43    30%   40, 53-68, 77-87, 106-135\napp/api/data_sources/public/sec_edgar.py                69     56    19%   24-36, 45-51, 74-105, 127-171, 184, 197-207\napp/api/data_sources/public/usa_spending.py             85     73    14%   22-29, 38-44, 65-105, 134-211, 224-227, 236-263\napp/api/data_sources/router.py                         227    153    33%   86-133, 144, 168-190, 209-242, 266-274, 292-302, 322-352, 370-384, 406-450, 477-493, 505-634\napp/api/document_analysis.py                           151    115    24%   42-79, 93-131, 149-174, 223-258, 275-324, 343-358, 375-380\napp/api/document_import.py                             134    112    16%   40-80, 96-106, 126-255, 273-291\napp/api/governance/__init__.py                           0      0   100%\napp/api/governance/action_plan.py                      157    157     0%   4-451\napp/api/governance/assessment.py                        83     83     0%   4-232\napp/api/governance/control.py                           58     58     0%   4-161\napp/api/governance/incident.py                         105    105     0%   4-295\napp/api/governance/regulation.py                        48     48     0%   4-125\napp/api/governance/risk.py                              68     68     0%   4-193\napp/api/governance/router.py                            12     12     0%   4-20\napp/api/impact_assessment.py                           166    125    25%   57-119, 132-174, 178-185, 194-225, 235-265, 291-327, 350-397\napp/api/industries/__init__.py                           2      0   100%\napp/api/industries/router.py                            84     63    25%   36-47, 67-70, 90-107, 129-156, 176-192, 216-233\napp/api/integrations/__init__.py                         2      0   100%\napp/api/integrations/router.py                           8      1    88%   22\napp/api/integrations/superglu.py                        41     15    63%   34-42, 64-88, 96, 123\napp/api/openapi.py                                      12     12     0%   4-188\napp/api/pdf_analysis.py                                 67     46    31%   47-73, 88-114, 136-167, 184-190, 201-224\napp/api/regulation_categories/__init__.py                2      2     0%   4-6\napp/api/regulation_categories/router.py                 85     85     0%   5-234\napp/api/regulation_documents/__init__.py                 2      0   100%\napp/api/regulation_documents/router.py                  93     72    23%   40-57, 77-80, 100-118, 140-164, 184-193, 221-260\napp/api/regulation_management/__init__.py                0      0   100%\napp/api/regulation_management/gemini_enrichment.py     463    435     6%   42-87, 101-134, 152-270, 289-423, 443-549, 567-628, 647-702, 720-782, 801-864, 880, 912-954, 967-1010, 1023-1064, 1077-1120, 1132-1178, 1190-1239\napp/api/regulation_management/reporting.py              87     87     0%   4-381\napp/api/regulation_management/reporting_router.py       76     76     0%   4-173\napp/api/regulation_management/router.py                180    120    33%   40-45, 66, 91-94, 107-125, 137-142, 155-160, 173, 186-191, 204, 217-222, 235, 248-256, 271, 289-292, 305-312, 324-329, 342-350, 369-392, 407, 425-428, 441-448, 460-465, 488-512, 521\napp/api/regulation_management/service.py               182    146    20%   30-98, 115-159, 163, 167-238, 242-244, 249-256, 260, 264, 269-276, 280, 284, 289-295, 299, 303, 308-324, 335-343, 347, 356-366, 370-372, 377-392, 403-431, 442-450, 454, 463-473, 477-484, 489-498, 508\napp/api/regulation_tags/__init__.py                      2      0   100%\napp/api/regulation_tags/router.py                       84     63    25%   36-44, 64-67, 87-104, 126-153, 173-189, 213-230\napp/api/regulation_urls/__init__.py                      2      0   100%\napp/api/regulation_urls/router.py                      112     93    17%   44-67, 87-90, 110-156, 178-231, 251-260\napp/api/regulations.py                                  11     11     0%   8-146\napp/api/regulations/__init__.py                          2      0   100%\napp/api/regulations/router.py                          148    129    13%   48-77, 97-100, 120-184, 206-280, 300-321\napp/api/regulators/__init__.py                           2      0   100%\napp/api/regulators/router.py                            86     67    22%   40-54, 74-77, 97-126, 148-187, 207-223\napp/api/regulatory_alerts.py                           115     74    36%   62, 93-145, 162-185, 204-291, 314-316\napp/api/relationship_mapping.py                         67     48    28%   30-46, 59-65, 78-116, 130-139\napp/api/routes.py                                       57     23    60%   23-26, 38-49, 56-57, 67-68, 76, 81, 89, 97, 105, 113, 121\napp/api/scheduled_digests.py                           161    115    29%   71-132, 155-172, 191-298, 311-351, 371-417\napp/api/trend_analysis.py                               70     46    34%   40-132, 142-212\napp/core/__init__.py                                     0      0   100%\napp/db/__init__.py                                       6      4    33%   12-16\napp/db/database.py                                      14      5    64%   13, 35-39\napp/db/models.py                                       340     15    96%   24-25, 149, 197-198, 203-204, 209-210, 215-216, 221-222, 227-228\napp/i18n/__init__.py                                    18      4    78%   26, 30-37\napp/i18n/i18n.py                                        27     17    37%   29-48, 63-66\napp/i18n/translations.py                                35     23    34%   33-41, 55-65, 78-84, 97-101, 109\napp/main.py                                            245     95    61%   142-149, 154-156, 172-181, 188-192, 197-202, 207-212, 217-222, 228, 243-287, 300, 401-407, 418, 443-447, 469-474, 495-503, 526-539, 562-576, 597-609\napp/middleware/__init__.py                               2      0   100%\napp/middleware/caching_middleware.py                    62     50    19%   39-52, 66-124, 137-161\napp/middleware/i18n_middleware.py                       11      5    55%   25-38\napp/middleware/swagger_ui.py                            16     10    38%   11-33\napp/schemas/__init__.py                                  0      0   100%\napp/schemas/alerts.py                                   40      3    92%   31-33\napp/schemas/analytics.py                                53      3    94%   22-24\napp/schemas/calendar.py                                149     46    69%   29-31, 36-39, 44-47, 108-112, 117-121, 152-155, 160-163, 193-197, 202-206, 248-250, 269-272\napp/schemas/governance/__init__.py                       9      9     0%   4-13\napp/schemas/governance/action_plan.py                  126    126     0%   4-219\napp/schemas/governance/assessment.py                    66     66     0%   4-116\napp/schemas/governance/control.py                       38     38     0%   4-65\napp/schemas/governance/incident.py                      83     83     0%   4-145\napp/schemas/governance/organization.py                  20     20     0%   4-39\napp/schemas/governance/regulation.py                    25     25     0%   4-44\napp/schemas/governance/risk.py                          49     49     0%   4-84\napp/schemas/governance/user.py                          23     23     0%   4-43\napp/schemas/regulation.py                              231     14    94%   390-396, 418-424\napp/schemas/schemas.py                                 341      0   100%\napp/utils/__init__.py                                    0      0   100%\napp/utils/ai_summarizer.py                              95     78    18%   25, 38-72, 89-104, 125-170, 182-239\napp/utils/cache.py                                     103     88    15%   24-28, 47-52, 72-115, 135-219, 231-238, 247-254\napp/utils/document_classifier.py                       373    340     9%   25-75, 87-129, 141-192, 204-254, 271-387, 393-425, 430-468, 476-491, 495-507, 514-540, 544-575, 582-602, 618-634, 652-691, 703-747, 759-787, 799-835, 850-866, 881-904\napp/utils/notification.py                               97     74    24%   49-63, 78-106, 120-144, 165-183, 197-217\napp/utils/pdf_analyzer.py                              391    347    11%   17-19, 31-41, 53-112, 124-196, 217-219, 231-246, 258-304, 316-381, 395-445, 459-507, 520-555, 566-567, 582-594, 606-648, 660-726, 738-765, 779-800, 813-844, 858-869\napp/utils/regulatory_analysis.py                        58     50    14%   19-24, 43-63, 77-96, 113-144, 159-187\napp/utils/regulatory_parser.py                         274    244    11%   17-23, 42-53, 65-75, 88-101, 122-152, 164-222, 234-261, 273-306, 318-350, 364-441, 456-492, 496-500, 504-521, 526-534, 538-544, 549-565, 569-601, 605-616\napp/utils/regulatory_scraper.py                        136    116    15%   25-27, 42-105, 119-143, 156-182, 194-208, 222-246, 258-295, 308-326, 339-358, 373\napp/utils/relationship_mapper.py                       148    125    16%   36-56, 60-90, 94-109, 114-130, 134-179, 183-227, 231-252, 256-327\napp/utils/url_processor.py                             117    117     0%   8-423\napp/visualization/__init__.py                            0      0   100%\napp/visualization/worldmap.py                           14      8    43%   26-37, 51-52, 63\n----------------------------------------------------------------------------------\nTOTAL                                                 9401   6728    28%\nCoverage HTML written to dir coverage_html\nCoverage JSON written to file coverage.json\nLoading FastAPI app modules...\n  Loaded database engine in 0.01s\n  Loaded Base in 0.06s\n  Loaded models in 0.00s\n  Loaded schemas in 0.14s\n  Loaded database session in 0.00s\n  Loaded i18n in 0.02s\n  Loaded visualization in 0.69s\n  Loaded admin in 0.16s\nCreating database tables...\nDatabase tables created in 0.00s\n- Generated html report: file:///mnt/persist/workspace/test-reports/integration-tests.html -\n============================ 135 warnings in 13.40s ============================\n", "stderr": "2025-06-10 16:10:02,669 - WARNING - No OpenAI API key provided. AI summarization will not work.\n2025-06-10 16:10:03,639 - INFO - Registered rate limiter 'gemini' with 14 requests per minute\n2025-06-10 16:10:03,644 - WARNING - No Gemini API key provided. Gemini integration will not work.\n2025-06-10 16:10:03,725 - WARNING - No Gemini API key provided. Gemini integration will not work.\n2025-06-10 16:10:02,766 - WARNING - No OpenAI API key provided. AI summarization will not work.\n2025-06-10 16:10:03,782 - INFO - Registered rate limiter 'gemini' with 14 requests per minute\n2025-06-10 16:10:03,787 - WARNING - No Gemini API key provided. Gemini integration will not work.\n2025-06-10 16:10:03,871 - WARNING - No Gemini API key provided. Gemini integration will not work.\n2025-06-10 16:10:02,939 - WARNING - No OpenAI API key provided. AI summarization will not work.\n2025-06-10 16:10:03,916 - INFO - Registered rate limiter 'gemini' with 14 requests per minute\n2025-06-10 16:10:03,921 - WARNING - No Gemini API key provided. Gemini integration will not work.\n2025-06-10 16:10:04,003 - WARNING - No Gemini API key provided. Gemini integration will not work.\n2025-06-10 16:10:02,866 - WARNING - No OpenAI API key provided. AI summarization will not work.\n2025-06-10 16:10:03,949 - INFO - Registered rate limiter 'gemini' with 14 requests per minute\n2025-06-10 16:10:03,954 - WARNING - No Gemini API key provided. Gemini integration will not work.\n2025-06-10 16:10:04,038 - WARNING - No Gemini API key provided. Gemini integration will not work.\n2025-06-10 16:09:56,032 - WARNING - No OpenAI API key provided. AI summarization will not work.\n2025-06-10 16:09:57,079 - INFO - Registered rate limiter 'gemini' with 14 requests per minute\n2025-06-10 16:09:57,084 - WARNING - No Gemini API key provided. Gemini integration will not work.\n2025-06-10 16:09:57,167 - WARNING - No Gemini API key provided. Gemini integration will not work.\n"}, "security_tests": {"bandit": {"status": "failed", "returncode": 1, "stdout": "Working... ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 100% 0:00:02\n", "stderr": "[main]\tINFO\tprofile include tests: None\n[main]\tINFO\tprofile exclude tests: None\n[main]\tINFO\tcli include tests: None\n[main]\tINFO\tcli exclude tests: None\n[json]\tINFO\tJSON output written to file: test-reports/bandit-report.json\n"}, "safety": {"status": "failed", "returncode": 2, "stdout": "", "stderr": "Usage: safety check [OPTIONS]\nTry 'safety check --help' for help.\n\nError: Invalid value for '--output' / '-o': 'test-reports/safety-report.json' is not one of 'screen', 'text', 'json', 'bare', 'html'.\n"}}, "behavior_tests": {"status": "failed", "returncode": 1, "stdout": "🚀 Setting up test environment...\n🔄 Starting application for testing...\n✅ Application started successfully\n🎬 Starting scenario: View regulatory dashboard\n🎬 Completed scenario: View regulatory dashboard - Status: Status.passed\n🎬 Starting scenario: Search for regulations\n🎬 Completed scenario: Search for regulations - Status: Status.passed\n🎬 Starting scenario: Add a new regulation\n🎬 Completed scenario: Add a new regulation - Status: Status.failed\n🎬 Starting scenario: View regulation details\n🎬 Completed scenario: View regulation details - Status: Status.passed\n🎬 Starting scenario: Update regulation information\n🎬 Completed scenario: Update regulation information - Status: Status.failed\n🎬 Starting scenario: Delete a regulation\n🎬 Completed scenario: Delete a regulation - Status: Status.failed\n🎬 Starting scenario: Filter regulations by category\n🎬 Completed scenario: Filter regulations by category - Status: Status.failed\n🎬 Starting scenario: Export regulations data\n🎬 Completed scenario: Export regulations data - Status: Status.failed\n🎬 Starting scenario: View regulatory compliance status\n🎬 Completed scenario: View regulatory compliance status - Status: Status.failed\n🎬 Starting scenario: Manage regulatory alerts\n🎬 Completed scenario: Manage regulatory alerts - Status: Status.failed\n🧹 Cleaning up test environment...\n🛑 Stopping application...\n✅ Application stopped successfully\n\nFailing scenarios:\n  features/regulatory_management.feature:23  Add a new regulation\n  features/regulatory_management.feature:43  Update regulation information\n  features/regulatory_management.feature:51  Delete a regulation\n  features/regulatory_management.feature:58  Filter regulations by category\n  features/regulatory_management.feature:63  Export regulations data\n  features/regulatory_management.feature:70  View regulatory compliance status\n  features/regulatory_management.feature:77  Manage regulatory alerts\n\n0 features passed, 1 failed, 0 skipped\n3 scenarios passed, 7 failed, 0 skipped\n43 steps passed, 1 failed, 3 skipped, 23 undefined\nTook 0m0.195s\n", "stderr": "\u001b[33m\nYou can implement step definitions for undefined steps with these snippets:\n\n@given(u'I am viewing a regulation details page')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: Given I am viewing a regulation details page')\n\n\n@when(u'I click on \"Edit\"')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: When I click on \"Edit\"')\n\n\n@when(u'I update the description to \"Updated test regulation\"')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: When I update the description to \"Updated test regulation\"')\n\n\n@when(u'I save the changes')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: When I save the changes')\n\n\n@then(u'the updated description should be displayed')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: Then the updated description should be displayed')\n\n\n@when(u'I delete the regulation')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: When I delete the regulation')\n\n\n@when(u'I confirm the deletion')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: When I confirm the deletion')\n\n\n@then(u'the regulation should no longer appear in the list')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: Then the regulation should no longer appear in the list')\n\n\n@given(u'there are regulations in multiple categories')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: Given there are regulations in multiple categories')\n\n\n@when(u'I filter by \"Financial Services\" category')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: When I filter by \"Financial Services\" category')\n\n\n@then(u'I should only see regulations in the \"Financial Services\" category')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: Then I should only see regulations in the \"Financial Services\" category')\n\n\n@when(u'I click on \"Export Data\"')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: When I click on \"Export Data\"')\n\n\n@when(u'I select \"CSV\" format')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: When I select \"CSV\" format')\n\n\n@then(u'I should receive a downloadable CSV file')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: Then I should receive a downloadable CSV file')\n\n\n@then(u'the file should contain regulation data')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: Then the file should contain regulation data')\n\n\n@when(u'I navigate to the compliance status section')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: When I navigate to the compliance status section')\n\n\n@then(u'I should see compliance metrics')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: Then I should see compliance metrics')\n\n\n@then(u'I should see compliance percentages')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: Then I should see compliance percentages')\n\n\n@then(u'I should see any compliance gaps')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: Then I should see any compliance gaps')\n\n\n@when(u'I navigate to the alerts section')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: When I navigate to the alerts section')\n\n\n@then(u'I should see active regulatory alerts')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: Then I should see active regulatory alerts')\n\n\n@then(u'I should be able to mark alerts as read')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: Then I should be able to mark alerts as read')\n\n\n@then(u'I should be able to create new alerts')\ndef step_impl(context):\n    raise NotImplementedError(u'STEP: Then I should be able to create new alerts')\n\n\u001b[0m"}, "ui_tests": {"status": "failed", "returncode": 4, "stdout": "", "stderr": "ERROR: usage: __main__.py [options] [file_or_dir] [file_or_dir] [...]\n__main__.py: error: argument --headed: ignored explicit argument 'false'\n\n"}, "coverage": {"total_coverage": 28.43314541006276, "files_covered": 116}, "summary": {"total_test_suites": 5, "passed_suites": 0, "failed_suites": 4, "success_rate": 0.0, "overall_status": "FAILED"}}