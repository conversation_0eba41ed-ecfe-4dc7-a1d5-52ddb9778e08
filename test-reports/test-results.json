{"timestamp": "2025-06-09T09:27:19.984311", "duration": 7.825934, "statistics": {"total_suites": 7, "passed_suites": 0, "failed_suites": 7, "success_rate": 0.0}, "results": {"smoke": {"success": false, "type": "Smoke Tests", "description": "Basic functionality validation tests"}, "unit": {"success": false, "type": "Unit Tests", "description": "Core application logic and component tests"}, "integration": {"success": false, "type": "Integration Tests", "description": "API integration and database interaction tests"}, "security": {"success": false, "type": "Security Tests", "description": "Security vulnerability and penetration tests"}, "performance": {"success": false, "type": "Performance Tests", "description": "API response time and load testing"}, "accessibility": {"success": false, "type": "Accessibility Tests", "description": "WCAG compliance and screen reader compatibility tests"}, "e2e": {"success": false, "type": "E2E Tests", "description": "End-to-end browser automation tests"}}}