#!/usr/bin/env python
"""
Start services for RegulationGuru application with proper port forwarding.
"""
import os
import subprocess
import sys
import time

def start_api_service():
    """Start the FastAPI backend service."""
    print("Starting FastAPI backend service...")

    # Kill any existing uvicorn processes
    subprocess.run(["pkill", "-f", "uvicorn"], stderr=subprocess.DEVNULL)

    # Start the API service with proper port forwarding
    api_process = subprocess.Popen(
        ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8080", "--reload"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    time.sleep(2)  # Give the service time to start

    # Check if the service started successfully
    if api_process.poll() is None:
        print("✅ API service started successfully on port 8080")
        return api_process
    else:
        stdout, stderr = api_process.communicate()
        print("❌ Failed to start API service:")
        print(f"STDOUT: {stdout}")
        print(f"STDERR: {stderr}")
        return None

def main():
    """Main function to start all services."""
    print("Starting RegulationGuru services...")

    # Set environment variables
    os.environ["DATABASE_URL"] = os.environ.get("DATABASE_URL", "sqlite:///./test.db")

    # Start API service
    api_process = start_api_service()

    if api_process is None:
        print("❌ Failed to start essential services. Exiting.")
        sys.exit(1)

    print("\n✅ All services started successfully!")
    print("API service: http://localhost:8080")
    print("\nPress Ctrl+C to stop all services.")

    try:
        # Keep the script running
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nStopping all services...")
        if api_process is not None and api_process.poll() is None:
            api_process.terminate()
        print("All services stopped.")

if __name__ == "__main__":
    main()
