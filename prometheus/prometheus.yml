global:
  scrape_interval: 15s
  evaluation_interval: 15s
  scrape_timeout: 10s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'regulationguru_api'
    metrics_path: '/api/v1/metrics'
    static_configs:
      - targets: ['api:8000']
    
  - job_name: 'node_exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']

  - job_name: 'postgres_exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis_exporter'
    static_configs:
      - targets: ['redis-exporter:9121']
