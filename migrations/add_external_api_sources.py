"""
Migration script to add external API sources tables and update the RegulationURL model.
"""
import sys
import os
import logging
from datetime import datetime
from sqlalchemy import create_engine, Column, Integer, String, Foreign<PERSON>ey, DateTime, Text, Float, <PERSON><PERSON>an, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import the database URL from the application
try:
    from app.db.database import SQLALCHEMY_DATABASE_URL
except ImportError:
    logger.error("Could not import SQLALCHEMY_DATABASE_URL from app.db.database")
    SQLALCHEMY_DATABASE_URL = "sqlite:///./regulatory_compliance.db"

# Create the SQLAlchemy engine and session
engine = create_engine(SQLALCHEMY_DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def run_migration():
    """Run the migration to add external API sources tables and update the RegulationURL model."""
    logger.info("Starting migration to add external API sources")
    
    # Create a database session
    db = SessionLocal()
    
    try:
        # Check if the external_api_sources table already exists
        if engine.dialect.has_table(engine.connect(), "external_api_sources"):
            logger.info("Table 'external_api_sources' already exists, skipping creation")
        else:
            # Create the external_api_sources table
            logger.info("Creating 'external_api_sources' table")
            
            class ExternalAPISource(Base):
                """Model for external API data sources."""
                __tablename__ = "external_api_sources"
                
                id = Column(Integer, primary_key=True, index=True)
                name = Column(String(255), nullable=False)
                source_type = Column(String(50), nullable=False)
                base_url = Column(String(2048), nullable=False)
                api_key = Column(String(255), nullable=True)
                description = Column(Text, nullable=True)
                config = Column(JSON, nullable=True)
                is_active = Column(Boolean, default=True)
                last_sync = Column(DateTime, nullable=True)
                last_error = Column(Text, nullable=True)
                sync_frequency = Column(Integer, default=24)
                created_at = Column(DateTime, default=datetime.utcnow)
                changed_on = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
            
            # Create the external_api_sync_logs table
            logger.info("Creating 'external_api_sync_logs' table")
            
            class ExternalAPISyncLog(Base):
                """Model for logging external API synchronization events."""
                __tablename__ = "external_api_sync_logs"
                
                id = Column(Integer, primary_key=True, index=True)
                source_id = Column(Integer, ForeignKey("external_api_sources.id"), nullable=False)
                status = Column(String(50), nullable=False)
                items_processed = Column(Integer, default=0)
                items_created = Column(Integer, default=0)
                items_updated = Column(Integer, default=0)
                items_failed = Column(Integer, default=0)
                error_message = Column(Text, nullable=True)
                duration_seconds = Column(Float, nullable=True)
                created_at = Column(DateTime, default=datetime.utcnow)
                changed_on = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
            
            # Create the tables
            ExternalAPISource.__table__.create(engine)
            ExternalAPISyncLog.__table__.create(engine)
            
            logger.info("Tables created successfully")
        
        # Check if the regulation_urls table has the external_api_source_id column
        inspector = db.get_bind().dialect.inspector
        columns = [column["name"] for column in inspector.get_columns("regulation_urls")]
        
        if "external_api_source_id" not in columns:
            logger.info("Adding 'external_api_source_id' column to 'regulation_urls' table")
            
            # Add the external_api_source_id column to the regulation_urls table
            db.execute("""
                ALTER TABLE regulation_urls
                ADD COLUMN external_api_source_id INTEGER
                REFERENCES external_api_sources(id)
            """)
            
            # Create an index on the external_api_source_id column
            db.execute("""
                CREATE INDEX ix_regulation_urls_external_api_source_id
                ON regulation_urls (external_api_source_id)
            """)
            
            logger.info("Column added successfully")
        else:
            logger.info("Column 'external_api_source_id' already exists in 'regulation_urls' table")
        
        # Commit the changes
        db.commit()
        logger.info("Migration completed successfully")
    except Exception as e:
        db.rollback()
        logger.error(f"Error during migration: {str(e)}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    run_migration()
