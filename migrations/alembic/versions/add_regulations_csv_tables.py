"""Add regulations CSV tables with soft delete support

Revision ID: add_regulations_csv_tables
Revises: f4124f6b367e
Create Date: 2025-01-28 12:00:00.000000

"""
from typing import Sequence, Union
from uuid import uuid4

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'add_regulations_csv_tables'
down_revision: Union[str, None] = 'f4124f6b367e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Create regulations CSV tables with soft delete support."""
    
    # Create regulations_csv_records table
    op.create_table('regulations_csv_records',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False, default=uuid4),
        sa.Column('country_name', sa.String(length=255), nullable=False),
        sa.Column('country_code', sa.String(length=3), nullable=False),
        sa.Column('document_title', sa.Text(), nullable=False),
        sa.Column('document_type', sa.String(length=100), nullable=False),
        sa.Column('issuing_authority', sa.String(length=500), nullable=False),
        sa.Column('publication_date', sa.Date(), nullable=True),
        sa.Column('effective_date', sa.Date(), nullable=True),
        sa.Column('legal_status', sa.String(length=50), nullable=False),
        sa.Column('document_url', sa.Text(), nullable=True),
        sa.Column('language', sa.String(length=100), nullable=False),
        sa.Column('scope_application', sa.Text(), nullable=False),
        sa.Column('key_compliance_requirements', sa.Text(), nullable=False),
        sa.Column('enforcement_mechanisms', sa.Text(), nullable=False),
        sa.Column('penalties', sa.Text(), nullable=False),
        sa.Column('cross_border_elements', sa.Text(), nullable=False),
        sa.Column('extraterritorial_reach', sa.Text(), nullable=False),
        sa.Column('international_standards_alignment', sa.Text(), nullable=False),
        sa.Column('data_protection_provisions', sa.Text(), nullable=False),
        sa.Column('incident_reporting_requirements', sa.Text(), nullable=False),
        sa.Column('risk_management_mandates', sa.Text(), nullable=False),
        sa.Column('third_party_requirements', sa.Text(), nullable=False),
        sa.Column('audit_obligations', sa.Text(), nullable=False),
        sa.Column('certification_requirements', sa.Text(), nullable=False),
        sa.Column('implementation_timeline', sa.Text(), nullable=False),
        sa.Column('safe_harbor_provisions', sa.Text(), nullable=False),
        sa.Column('industry_specific_provisions', sa.Text(), nullable=False),
        sa.Column('technology_specific_provisions', sa.Text(), nullable=False),
        sa.Column('import_batch_id', sa.String(length=100), nullable=True),
        sa.Column('data_quality_score', sa.Float(), nullable=True),
        sa.Column('validation_errors', sa.JSON(), nullable=True),
        sa.Column('source_file_name', sa.String(length=255), nullable=True),
        sa.Column('source_file_hash', sa.String(length=64), nullable=True),
        sa.Column('deleted_by_id', postgresql.UUID(as_uuid=True), nullable=True),
        # TimestampMixin columns
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('changed_on', sa.DateTime(), nullable=False, default=sa.func.now(), onupdate=sa.func.now()),
        # SoftDeleteMixin columns
        sa.Column('is_deleted', sa.Boolean(), nullable=False, default=False),
        sa.Column('deleted_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for regulations_csv_records
    op.create_index(op.f('ix_regulations_csv_records_id'), 'regulations_csv_records', ['id'], unique=False)
    op.create_index(op.f('ix_regulations_csv_records_country_name'), 'regulations_csv_records', ['country_name'], unique=False)
    op.create_index(op.f('ix_regulations_csv_records_country_code'), 'regulations_csv_records', ['country_code'], unique=False)
    op.create_index(op.f('ix_regulations_csv_records_document_title'), 'regulations_csv_records', ['document_title'], unique=False)
    op.create_index(op.f('ix_regulations_csv_records_document_type'), 'regulations_csv_records', ['document_type'], unique=False)
    op.create_index(op.f('ix_regulations_csv_records_issuing_authority'), 'regulations_csv_records', ['issuing_authority'], unique=False)
    op.create_index(op.f('ix_regulations_csv_records_publication_date'), 'regulations_csv_records', ['publication_date'], unique=False)
    op.create_index(op.f('ix_regulations_csv_records_effective_date'), 'regulations_csv_records', ['effective_date'], unique=False)
    op.create_index(op.f('ix_regulations_csv_records_legal_status'), 'regulations_csv_records', ['legal_status'], unique=False)
    op.create_index(op.f('ix_regulations_csv_records_import_batch_id'), 'regulations_csv_records', ['import_batch_id'], unique=False)
    op.create_index(op.f('ix_regulations_csv_records_is_deleted'), 'regulations_csv_records', ['is_deleted'], unique=False)
    
    # Create regulations_csv_import_logs table
    op.create_table('regulations_csv_import_logs',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False, default=uuid4),
        sa.Column('batch_id', sa.String(length=100), nullable=False),
        sa.Column('file_name', sa.String(length=255), nullable=False),
        sa.Column('file_hash', sa.String(length=64), nullable=True),
        sa.Column('file_size', sa.Float(), nullable=True),
        sa.Column('total_records', sa.Float(), nullable=False, default=0),
        sa.Column('successful_imports', sa.Float(), nullable=False, default=0),
        sa.Column('failed_imports', sa.Float(), nullable=False, default=0),
        sa.Column('updated_records', sa.Float(), nullable=False, default=0),
        sa.Column('processing_time', sa.Float(), nullable=True),
        sa.Column('import_status', sa.String(length=50), nullable=False, default='pending'),
        sa.Column('error_summary', sa.JSON(), nullable=True),
        sa.Column('warnings_summary', sa.JSON(), nullable=True),
        sa.Column('imported_by_id', postgresql.UUID(as_uuid=True), nullable=True),
        # TimestampMixin columns
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('changed_on', sa.DateTime(), nullable=False, default=sa.func.now(), onupdate=sa.func.now()),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('batch_id')
    )
    
    # Create indexes for regulations_csv_import_logs
    op.create_index(op.f('ix_regulations_csv_import_logs_id'), 'regulations_csv_import_logs', ['id'], unique=False)
    op.create_index(op.f('ix_regulations_csv_import_logs_batch_id'), 'regulations_csv_import_logs', ['batch_id'], unique=True)
    op.create_index(op.f('ix_regulations_csv_import_logs_import_status'), 'regulations_csv_import_logs', ['import_status'], unique=False)
    
    # Create regulations_csv_export_logs table
    op.create_table('regulations_csv_export_logs',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False, default=uuid4),
        sa.Column('export_id', sa.String(length=100), nullable=False),
        sa.Column('file_name', sa.String(length=255), nullable=False),
        sa.Column('filters_applied', sa.JSON(), nullable=True),
        sa.Column('include_deleted', sa.Boolean(), nullable=False, default=False),
        sa.Column('total_records_exported', sa.Float(), nullable=False, default=0),
        sa.Column('processing_time', sa.Float(), nullable=True),
        sa.Column('export_status', sa.String(length=50), nullable=False, default='pending'),
        sa.Column('file_path', sa.String(length=500), nullable=True),
        sa.Column('exported_by_id', postgresql.UUID(as_uuid=True), nullable=True),
        # TimestampMixin columns
        sa.Column('created_at', sa.DateTime(), nullable=False, default=sa.func.now()),
        sa.Column('changed_on', sa.DateTime(), nullable=False, default=sa.func.now(), onupdate=sa.func.now()),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('export_id')
    )
    
    # Create indexes for regulations_csv_export_logs
    op.create_index(op.f('ix_regulations_csv_export_logs_id'), 'regulations_csv_export_logs', ['id'], unique=False)
    op.create_index(op.f('ix_regulations_csv_export_logs_export_id'), 'regulations_csv_export_logs', ['export_id'], unique=True)
    op.create_index(op.f('ix_regulations_csv_export_logs_export_status'), 'regulations_csv_export_logs', ['export_status'], unique=False)


def downgrade() -> None:
    """Drop regulations CSV tables."""
    
    # Drop export logs table
    op.drop_index(op.f('ix_regulations_csv_export_logs_export_status'), table_name='regulations_csv_export_logs')
    op.drop_index(op.f('ix_regulations_csv_export_logs_export_id'), table_name='regulations_csv_export_logs')
    op.drop_index(op.f('ix_regulations_csv_export_logs_id'), table_name='regulations_csv_export_logs')
    op.drop_table('regulations_csv_export_logs')
    
    # Drop import logs table
    op.drop_index(op.f('ix_regulations_csv_import_logs_import_status'), table_name='regulations_csv_import_logs')
    op.drop_index(op.f('ix_regulations_csv_import_logs_batch_id'), table_name='regulations_csv_import_logs')
    op.drop_index(op.f('ix_regulations_csv_import_logs_id'), table_name='regulations_csv_import_logs')
    op.drop_table('regulations_csv_import_logs')
    
    # Drop main records table
    op.drop_index(op.f('ix_regulations_csv_records_is_deleted'), table_name='regulations_csv_records')
    op.drop_index(op.f('ix_regulations_csv_records_import_batch_id'), table_name='regulations_csv_records')
    op.drop_index(op.f('ix_regulations_csv_records_legal_status'), table_name='regulations_csv_records')
    op.drop_index(op.f('ix_regulations_csv_records_effective_date'), table_name='regulations_csv_records')
    op.drop_index(op.f('ix_regulations_csv_records_publication_date'), table_name='regulations_csv_records')
    op.drop_index(op.f('ix_regulations_csv_records_issuing_authority'), table_name='regulations_csv_records')
    op.drop_index(op.f('ix_regulations_csv_records_document_type'), table_name='regulations_csv_records')
    op.drop_index(op.f('ix_regulations_csv_records_document_title'), table_name='regulations_csv_records')
    op.drop_index(op.f('ix_regulations_csv_records_country_code'), table_name='regulations_csv_records')
    op.drop_index(op.f('ix_regulations_csv_records_country_name'), table_name='regulations_csv_records')
    op.drop_index(op.f('ix_regulations_csv_records_id'), table_name='regulations_csv_records')
    op.drop_table('regulations_csv_records')
