
"""Create regulatory tables

Revision ID: 3a4b7659cd12
Revises: 
Create Date: 2023-05-21 15:42:38.857758

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3a4b7659cd12'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # Create countries table
    op.create_table(
        'countries',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('code', sa.String(length=10), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('changed_on', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_countries_code'), 'countries', ['code'], unique=False)
    op.create_index(op.f('ix_countries_name'), 'countries', ['name'], unique=True)
    
    # Create regulators table
    op.create_table(
        'regulators',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('type', sa.String(length=100), nullable=True),
        sa.Column('website', sa.String(length=255), nullable=True),
        sa.Column('country_id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('changed_on', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['country_id'], ['countries.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name', 'country_id', name='_regulator_country_uc')
    )
    op.create_index(op.f('ix_regulators_country_id'), 'regulators', ['country_id'], unique=False)
    op.create_index(op.f('ix_regulators_name'), 'regulators', ['name'], unique=False)
    
    # Create regulation_urls table
    op.create_table(
        'regulation_urls',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('url', sa.String(length=1024), nullable=False),
        sa.Column('domain', sa.String(length=255), nullable=True),
        sa.Column('normalized_url', sa.String(length=1024), nullable=True),
        sa.Column('category', sa.String(length=100), nullable=True),
        sa.Column('confidence_level', sa.Float(), nullable=False),
        sa.Column('regulator_id', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('changed_on', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['regulator_id'], ['regulators.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_regulation_urls_category'), 'regulation_urls', ['category'], unique=False)
    op.create_index(op.f('ix_regulation_urls_domain'), 'regulation_urls', ['domain'], unique=False)
    op.create_index(op.f('ix_regulation_urls_normalized_url'), 'regulation_urls', ['normalized_url'], unique=False)
    op.create_index(op.f('ix_regulation_urls_regulator_id'), 'regulation_urls', ['regulator_id'], unique=False)
    op.create_index(op.f('ix_regulation_urls_url'), 'regulation_urls', ['url'], unique=True)


def downgrade():
    op.drop_index(op.f('ix_regulation_urls_url'), table_name='regulation_urls')
    op.drop_index(op.f('ix_regulation_urls_regulator_id'), table_name='regulation_urls')
    op.drop_index(op.f('ix_regulation_urls_normalized_url'), table_name='regulation_urls')
    op.drop_index(op.f('ix_regulation_urls_domain'), table_name='regulation_urls')
    op.drop_index(op.f('ix_regulation_urls_category'), table_name='regulation_urls')
    op.drop_table('regulation_urls')
    
    op.drop_index(op.f('ix_regulators_name'), table_name='regulators')
    op.drop_index(op.f('ix_regulators_country_id'), table_name='regulators')
    op.drop_table('regulators')
    
    op.drop_index(op.f('ix_countries_name'), table_name='countries')
    op.drop_index(op.f('ix_countries_code'), table_name='countries')
    op.drop_table('countries')
