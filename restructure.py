
#!/usr/bin/env python
"""
<PERSON>ript to restructure the project files into a more organized directory layout.
"""
import os
import shutil


def create_directory(path):
    """Create directory if it doesn't exist."""
    if not os.path.exists(path):
        os.makedirs(path)
        print(f"Created directory: {path}")


def move_file(src, dest):
    """Move a file from src to dest."""
    if os.path.exists(src):
        # Create the destination directory if it doesn't exist
        dest_dir = os.path.dirname(dest)
        create_directory(dest_dir)
        
        # Move the file
        shutil.move(src, dest)
        print(f"Moved: {src} -> {dest}")
    else:
        print(f"Warning: Source file does not exist: {src}")


def main():
    """Restructure the project."""
    # Create main directories
    dirs = [
        "app",
        "app/api",
        "app/core",
        "app/db",
        "app/schemas",
        "app/utils",
        "app/admin",
        "app/i18n",
        "app/visualization",
        "scripts",
        "migrations"
    ]
    
    for d in dirs:
        create_directory(d)
    
    # Move core application files
    move_file("main.py", "app/main.py")
    move_file("models.py", "app/db/models.py")
    move_file("database.py", "app/db/database.py")
    move_file("schemas.py", "app/schemas/schemas.py")
    move_file("admin.py", "app/admin/admin.py")
    move_file("i18n.py", "app/i18n/i18n.py")
    move_file("worldmap.py", "app/visualization/worldmap.py")
    move_file("regulations.py", "app/api/regulations.py")
    
    # Move utility scripts
    move_file("translate.py", "scripts/translate.py")
    move_file("generate_coverage.py", "scripts/generate_coverage.py")
    move_file("run_tests_with_coverage.py", "scripts/run_tests_with_coverage.py")
    move_file("analyze_test_coverage.py", "scripts/analyze_test_coverage.py")
    move_file("import_urls.py", "scripts/import_urls.py")
    move_file("analyze_confidence.py", "scripts/analyze_confidence.py")
    move_file("analyze_regulatory_results.py", "scripts/analyze_regulatory_results.py")
    move_file("url_processor.py", "app/utils/url_processor.py")
    move_file("migrate.py", "scripts/migrate.py")
    
    # Move alembic files
    if os.path.exists("alembic"):
        create_directory("migrations/alembic")
        for file in os.listdir("alembic"):
            src = os.path.join("alembic", file)
            dest = os.path.join("migrations/alembic", file)
            if os.path.isdir(src):
                if not os.path.exists(dest):
                    shutil.copytree(src, dest)
                    print(f"Copied directory: {src} -> {dest}")
                shutil.rmtree(src)
                print(f"Removed original directory: {src}")
            else:
                move_file(src, dest)
        # Remove the empty alembic directory if it still exists
        if os.path.exists("alembic") and not os.listdir("alembic"):
            os.rmdir("alembic")
            print("Removed empty alembic directory")
    
    # Create a new entry point that imports from the restructured modules
    with open("run.py", "w") as f:
        f.write("""#!/usr/bin/env python
\"\"\"
Entry point for the FastAPI application.
\"\"\"
import uvicorn

if __name__ == "__main__":
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True)
""")
    print("Created new entry point: run.py")
    
    # Make the new run.py executable
    os.chmod("run.py", 0o755)
    
    print("\nProject restructuring completed successfully!")
    print("\nUpdate imports in your files to reflect the new structure.")
    print("For example, change 'import models' to 'from app.db import models'")


if __name__ == "__main__":
    main()
