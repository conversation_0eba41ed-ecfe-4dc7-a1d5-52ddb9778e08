
#!/usr/bin/env python
"""
Script to update imports in Python files after restructuring.
"""
import os
import re
from typing import Dict, List, <PERSON><PERSON>


def find_python_files(directory: str) -> List[str]:
    """Find all Python files in the directory."""
    python_files = []
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith(".py"):
                python_files.append(os.path.join(root, file))
    return python_files


def update_imports(file_path: str, import_map: Dict[str, str]) -> Tuple[bool, int]:
    """
    Update imports in a Python file based on the provided mapping.
    
    Returns:
        bool: Whether the file was modified
        int: Number of replacements made
    """
    with open(file_path, "r") as f:
        content = f.read()
    
    original_content = content
    replacements = 0
    
    # Update direct imports (e.g., "import models" -> "from app.db import models")
    for old_import, new_import in import_map.items():
        pattern = rf"import\s+{old_import}(\s|$|\n|;|,)"
        if new_import.startswith("from "):
            replacement = f"{new_import}\\1"
        else:
            replacement = f"import {new_import}\\1"
        
        new_content, count = re.subn(pattern, replacement, content)
        if count > 0:
            content = new_content
            replacements += count
    
    # Update "from" imports (e.g., "from models import X" -> "from app.db.models import X")
    for old_import, new_import in import_map.items():
        if "." not in new_import:
            continue
        
        # Extract the module part from the new import path
        if new_import.startswith("from "):
            module_parts = new_import.split(" import ")
            if len(module_parts) == 2:
                new_path = module_parts[0][5:]  # Remove "from "
            else:
                continue
        else:
            new_path = new_import
        
        pattern = rf"from\s+{old_import}\s+import"
        replacement = f"from {new_path} import"
        
        new_content, count = re.subn(pattern, replacement, content)
        if count > 0:
            content = new_content
            replacements += count
    
    # Write changes back if modifications were made
    if content != original_content:
        with open(file_path, "w") as f:
            f.write(content)
        return True, replacements
    
    return False, 0


def main():
    """Update imports in Python files."""
    # Define import mapping: old_import -> new_import
    import_map = {
        "models": "from app.db import models",
        "database": "from app.db import database",
        "schemas": "from app.schemas import schemas",
        "admin": "from app.admin import admin",
        "i18n": "from app.i18n import i18n",
        "worldmap": "from app.visualization import worldmap",
        "regulations": "from app.api import regulations",
        "url_processor": "from app.utils import url_processor",
    }
    
    # Find all Python files
    python_files = find_python_files(".")
    
    # Update imports in each file
    modified_files = 0
    total_replacements = 0
    
    for file_path in python_files:
        # Skip the script itself and update_imports.py
        if file_path in ["./restructure.py", "./update_imports.py"]:
            continue
        
        modified, replacements = update_imports(file_path, import_map)
        if modified:
            print(f"Updated {file_path} ({replacements} replacements)")
            modified_files += 1
            total_replacements += replacements
    
    print(f"\nUpdated imports in {modified_files} files ({total_replacements} total replacements)")
    print("Note: You may need to manually check and fix some imports.")


if __name__ == "__main__":
    main()
