#!/usr/bin/env python
"""
Start services for RegulationGuru application.
"""
import os
import subprocess
import sys
import time

def start_api_service():
    """Start the FastAPI backend service."""
    print("Starting FastAPI backend service...")
    api_process = subprocess.Popen(
        ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    time.sleep(2)  # Give the service time to start
    
    # Check if the service started successfully
    if api_process.poll() is None:
        print("✅ API service started successfully on port 8000")
        return api_process
    else:
        stdout, stderr = api_process.communicate()
        print("❌ Failed to start API service:")
        print(f"STDOUT: {stdout}")
        print(f"STDERR: {stderr}")
        return None

def start_ui_service():
    """Start the React UI service."""
    print("Starting React UI service...")
    # Check if we have a frontend directory
    if os.path.exists("frontend"):
        os.chdir("frontend")
        ui_process = subprocess.Popen(
            ["npm", "start"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        os.chdir("..")
        time.sleep(5)  # Give the service time to start
        
        # Check if the service started successfully
        if ui_process.poll() is None:
            print("✅ UI service started successfully on port 3000")
            return ui_process
        else:
            stdout, stderr = ui_process.communicate()
            print("❌ Failed to start UI service:")
            print(f"STDOUT: {stdout}")
            print(f"STDERR: {stderr}")
            return None
    else:
        print("⚠️ Frontend directory not found, skipping UI service")
        return None

def start_database():
    """Start the database service."""
    print("Starting database service...")
    # Check if we're using SQLite or need to start PostgreSQL
    if os.environ.get("DATABASE_URL", "").startswith("sqlite"):
        print("✅ Using SQLite database, no service needed")
        return None
    else:
        # Try to start PostgreSQL
        db_process = subprocess.Popen(
            ["pg_ctl", "-D", "/usr/local/var/postgres", "start"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        time.sleep(2)  # Give the service time to start
        
        # Check if the service started successfully
        stdout, stderr = db_process.communicate()
        if db_process.returncode == 0:
            print("✅ PostgreSQL database started successfully")
            return db_process
        else:
            print("❌ Failed to start PostgreSQL database:")
            print(f"STDOUT: {stdout}")
            print(f"STDERR: {stderr}")
            print("⚠️ Using SQLite as fallback")
            os.environ["DATABASE_URL"] = "sqlite:///./test.db"
            return None

def main():
    """Main function to start all services."""
    print("Starting RegulationGuru services...")
    
    # Set environment variables
    os.environ["DATABASE_URL"] = os.environ.get("DATABASE_URL", "sqlite:///./test.db")
    
    # Start services
    db_process = start_database()
    api_process = start_api_service()
    ui_process = start_ui_service()
    
    if api_process is None:
        print("❌ Failed to start essential services. Exiting.")
        sys.exit(1)
    
    print("\n✅ All services started successfully!")
    print("API service: http://localhost:8000")
    if ui_process is not None:
        print("UI service: http://localhost:3000")
    print("\nPress Ctrl+C to stop all services.")
    
    try:
        # Keep the script running
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nStopping all services...")
        if api_process is not None and api_process.poll() is None:
            api_process.terminate()
        if ui_process is not None and ui_process.poll() is None:
            ui_process.terminate()
        print("All services stopped.")

if __name__ == "__main__":
    main()
