
[pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
markers =
    ui: mark test as requiring UI testing with playwright
    slow: mark test as taking a long time to run
    integration: mark test as requiring external services
    security: mark test as security-related
    unit: mark test as unit test
    e2e: mark test as end-to-end test
    api: mark test as API test
    smoke: mark test as smoke test

# Configure test coverage settings and execution
addopts = --cov=app --cov-report=term-missing --cov-report=html:coverage_html --cov-report=json:coverage.json --strict-markers --tb=short -n auto --dist=worksteal

# Test discovery
minversion = 6.0

# Timeout settings
timeout = 300
timeout_method = thread

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S
