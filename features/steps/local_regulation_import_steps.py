"""
Step definitions for local regulation import BDD tests.

This module implements the step definitions for Behave tests
covering the local regulation import functionality.
"""

import os
import tempfile
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import Mock, patch

from behave import given, when, then, step
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.db.models import Base
from app.db.models.regulations_csv import RegulationCSVRecord, RegulationCSVImportLog
from app.services.local_regulation_importer import LocalRegulationImporter, LocalStorageConfig
from app.schemas.regulations_csv import RegulationCSVImportResult


# Sample CSV content for testing
VALID_CSV_HEADER = """Country_Name,Country_Code,Document_Title,Document_Type,Issuing_Authority,Publication_Date,Effective_Date,Legal_Status,Document_URL,Language,Scope_Application,Key_Compliance_Requirements,Enforcement_Mechanisms,Penalties,Cross_Border_Elements,Data_Protection_Provisions,Incident_Reporting_Requirements,Risk_Management_Mandates,Third_Party_Requirements,Audit_Obligations,Certification_Requirements,Implementation_Timeline,International_Standards_Alignment,Extraterritorial_Reach,Safe_Harbor_Provisions,Industry_Specific_Provisions,Technology_Specific_Provisions"""

VALID_CSV_ROW = """United States,US,Test Regulation,Federal Law,Test Authority,2024-01-01,2024-01-01,Binding,https://example.com,English,Test scope,Test requirements,Test enforcement,Test penalties,Test cross-border,Test data protection,Test incident reporting,Test risk management,Test third party,Test audit,Test certification,Test timeline,Test standards,Test extraterritorial,Test safe harbor,Test industry,Test technology"""


@given('I have a local storage system configured')
def step_setup_local_storage(context):
    """Set up local storage system for testing."""
    context.temp_dir = tempfile.mkdtemp()
    context.storage_config = LocalStorageConfig(context.temp_dir)

    # Ensure cleanup happens
    if not hasattr(context, 'cleanup_dirs'):
        context.cleanup_dirs = []
    context.cleanup_dirs.append(context.temp_dir)


@given('I have a database connection')
def step_setup_database(context):
    """Set up database connection for testing."""
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(engine)
    SessionLocal = sessionmaker(bind=engine)
    context.db = SessionLocal()
    context.importer = LocalRegulationImporter(context.db, context.storage_config)


@given('I have CSV files in the import directory')
def step_create_csv_files(context):
    """Create CSV files in the import directory based on table data."""
    context.created_files = []

    for row in context.table:
        filename = row['filename']
        content_type = row['content_type']

        file_path = context.storage_config.imports_path / filename

        if content_type == 'valid':
            content = f"{VALID_CSV_HEADER}\n{VALID_CSV_ROW}"
        elif content_type == 'text':
            content = "This is not a CSV file"
        else:
            content = "invalid,content"

        file_path.write_text(content)
        context.created_files.append(file_path)


@given('I have a CSV file "{filename}" with proper structure')
def step_create_valid_csv_file(context, filename):
    """Create a valid CSV file with proper structure."""
    file_path = context.storage_config.imports_path / filename

    # Build CSV content from table data
    if context.table:
        headers = list(context.table.headings)
        rows = [list(row) for row in context.table]

        # Add missing required columns
        required_cols = ['Publication_Date', 'Effective_Date', 'Legal_Status', 'Document_URL', 'Language',
                        'Scope_Application', 'Key_Compliance_Requirements', 'Enforcement_Mechanisms',
                        'Penalties', 'Cross_Border_Elements', 'Data_Protection_Provisions',
                        'Incident_Reporting_Requirements', 'Risk_Management_Mandates',
                        'Third_Party_Requirements', 'Audit_Obligations', 'Certification_Requirements',
                        'Implementation_Timeline', 'International_Standards_Alignment',
                        'Extraterritorial_Reach', 'Safe_Harbor_Provisions', 'Industry_Specific_Provisions',
                        'Technology_Specific_Provisions']

        for col in required_cols:
            if col not in headers:
                headers.append(col)
                for i, row in enumerate(rows):
                    rows[i].append('Test Value')

        content = ','.join(headers) + '\n'
        for row in rows:
            content += ','.join(row) + '\n'
    else:
        content = f"{VALID_CSV_HEADER}\n{VALID_CSV_ROW}"

    file_path.write_text(content)
    context.test_file = file_path


@given('I have a CSV file "{filename}" with missing columns')
def step_create_invalid_csv_file(context, filename):
    """Create an invalid CSV file with missing required columns."""
    file_path = context.storage_config.imports_path / filename

    # Build CSV content from table data (which has missing columns)
    if context.table:
        headers = list(context.table.headings)
        rows = [list(row) for row in context.table]

        content = ','.join(headers) + '\n'
        for row in rows:
            content += ','.join(row) + '\n'
    else:
        content = "Name,Code\nUS,Test"

    file_path.write_text(content)
    context.test_file = file_path


@given('I have a valid CSV file "{filename}" in the import directory')
def step_create_single_valid_file(context, filename):
    """Create a single valid CSV file in the import directory."""
    file_path = context.storage_config.imports_path / filename
    content = f"{VALID_CSV_HEADER}\n{VALID_CSV_ROW}"
    file_path.write_text(content)
    context.test_file = file_path


@given('I have an invalid CSV file "{filename}" in the import directory')
def step_create_single_invalid_file(context, filename):
    """Create a single invalid CSV file in the import directory."""
    file_path = context.storage_config.imports_path / filename
    content = "invalid,content\nwith,missing,columns"
    file_path.write_text(content)
    context.test_file = file_path


@given('I have multiple CSV files in the import directory')
def step_create_multiple_files(context):
    """Create multiple CSV files with different validity."""
    context.created_files = []

    for row in context.table:
        filename = row['filename']
        validity = row['validity']

        file_path = context.storage_config.imports_path / filename

        if validity == 'valid':
            content = f"{VALID_CSV_HEADER}\n{VALID_CSV_ROW}"
        else:
            content = "invalid,content"

        file_path.write_text(content)
        context.created_files.append((file_path, validity))


@given('I have previously imported a file with hash "{file_hash}"')
def step_create_previous_import_log(context, file_hash):
    """Create a previous import log with the specified hash."""
    import_log = RegulationCSVImportLog(
        batch_id="previous_batch",
        file_name="previous_file.csv",
        file_hash=file_hash,
        import_status="completed",
        total_records=1,
        successful_imports=1,
        failed_imports=0,
        updated_records=0
    )
    context.db.add(import_log)
    context.db.commit()
    context.previous_hash = file_hash


@given('I have a new file with the same hash "{file_hash}"')
def step_create_duplicate_hash_file(context, file_hash):
    """Create a new file that will have the same hash."""
    file_path = context.storage_config.imports_path / "duplicate_file.csv"
    content = f"{VALID_CSV_HEADER}\n{VALID_CSV_ROW}"
    file_path.write_text(content)
    context.test_file = file_path
    context.expected_hash = file_hash


@given('the CSV processor will throw an exception')
def step_setup_processor_exception(context):
    """Set up the CSV processor to throw an exception."""
    context.processor_should_fail = True


@given('I have backup files with different ages')
def step_create_backup_files_with_ages(context):
    """Create backup files with different ages."""
    context.backup_files = []

    for row in context.table:
        filename = row['filename']
        age_days = int(row['age_days'])

        file_path = context.storage_config.backups_path / filename
        file_path.write_text("backup content")

        # Set file modification time
        age_timestamp = (datetime.now() - timedelta(days=age_days)).timestamp()
        os.utime(file_path, (age_timestamp, age_timestamp))

        context.backup_files.append((file_path, age_days))


@given('I have files distributed across storage directories')
def step_create_distributed_files(context):
    """Create files distributed across storage directories."""
    context.distributed_files = {}

    for row in context.table:
        directory = row['directory']
        file_count = int(row['file_count'])
        total_size_mb = float(row['total_size_mb'])

        # Calculate size per file
        size_per_file = int((total_size_mb * 1024 * 1024) / file_count) if file_count > 0 else 0

        # Get directory path
        dir_path = getattr(context.storage_config, f"{directory}_path")

        # Create files
        files = []
        for i in range(file_count):
            file_path = dir_path / f"file_{i}.csv"
            content = "x" * size_per_file  # Create file of specific size
            file_path.write_text(content)
            files.append(file_path)

        context.distributed_files[directory] = files


@given('I am logged in as user "{user_id}"')
def step_set_user_context(context, user_id):
    """Set the user context for tracking."""
    context.current_user_id = user_id


@given('the processed directory has restricted permissions')
def step_restrict_processed_directory_permissions(context):
    """Restrict permissions on the processed directory."""
    context.storage_config.processed_path.chmod(0o444)  # Read-only
    context.restricted_permissions = True


@given('I have CSV files with Unicode names')
def step_create_unicode_named_files(context):
    """Create CSV files with Unicode names."""
    context.unicode_files = []

    for row in context.table:
        filename = row['filename']
        file_path = context.storage_config.imports_path / filename
        content = f"{VALID_CSV_HEADER}\n{VALID_CSV_ROW}"
        file_path.write_text(content, encoding='utf-8')
        context.unicode_files.append(file_path)


@given('I have a CSV file that exceeds the size limit')
def step_create_oversized_file(context):
    """Create a CSV file that exceeds the size limit."""
    file_path = context.storage_config.imports_path / "oversized.csv"
    # Create a file larger than 100MB (the limit)
    large_content = "x" * (101 * 1024 * 1024)  # 101MB
    file_path.write_text(large_content)
    context.test_file = file_path


@given('I have CSV files with different encodings')
def step_create_files_with_different_encodings(context):
    """Create CSV files with different encodings."""
    context.encoding_files = []

    for row in context.table:
        filename = row['filename']
        encoding = row['encoding']

        file_path = context.storage_config.imports_path / filename
        content = f"{VALID_CSV_HEADER}\nFrançais,FR,Règlement Français,Loi Nationale,Autorité Française,2024-01-01,2024-01-01,Obligatoire,https://exemple.fr,Français,Portée française,Exigences françaises,Application française,Pénalités françaises,Éléments transfrontaliers,Protection des données françaises,Signalement d'incidents français,Gestion des risques française,Exigences tierces françaises,Obligations d'audit françaises,Exigences de certification françaises,Calendrier français,Alignement des normes françaises,Portée extraterritoriale française,Dispositions de refuge français,Dispositions sectorielles françaises,Dispositions technologiques françaises"

        with open(file_path, 'w', encoding=encoding) as f:
            f.write(content)

        context.encoding_files.append((file_path, encoding))


@given('I have a complete regulatory dataset in CSV format')
def step_create_complete_dataset(context):
    """Create a complete regulatory dataset."""
    file_path = context.storage_config.imports_path / "complete_dataset.csv"

    # Create dataset with 50 records from 5 countries
    content = VALID_CSV_HEADER + "\n"

    countries = [
        ("United States", "US"),
        ("Canada", "CA"),
        ("United Kingdom", "GB"),
        ("Germany", "DE"),
        ("France", "FR")
    ]

    for i in range(50):
        country_name, country_code = countries[i % 5]
        row = f"{country_name},{country_code},Regulation {i+1},Federal Law,Authority {i+1},2024-01-{(i%30)+1:02d},2024-01-{(i%30)+1:02d},Binding,https://example.com/{i+1},English,Scope {i+1},Requirements {i+1},Enforcement {i+1},Penalties {i+1},Cross-border {i+1},Data protection {i+1},Incident reporting {i+1},Risk management {i+1},Third party {i+1},Audit {i+1},Certification {i+1},Timeline {i+1},Standards {i+1},Extraterritorial {i+1},Safe harbor {i+1},Industry {i+1},Technology {i+1}"
        content += row + "\n"

    file_path.write_text(content)
    context.complete_dataset_file = file_path
    context.expected_record_count = 50
    context.expected_country_count = 5


@given('the dataset contains {record_count:d} regulation records from {country_count:d} countries')
def step_verify_dataset_structure(context, record_count, country_count):
    """Verify the dataset structure matches expectations."""
    context.expected_record_count = record_count
    context.expected_country_count = country_count


@given('I have multiple CSV files ready for import')
def step_create_concurrent_test_files(context):
    """Create multiple CSV files for concurrent processing test."""
    context.concurrent_files = []

    for i in range(5):
        file_path = context.storage_config.imports_path / f"concurrent_{i}.csv"
        content = f"{VALID_CSV_HEADER}\n{VALID_CSV_ROW.replace('Test Regulation', f'Test Regulation {i}')}"
        file_path.write_text(content)
        context.concurrent_files.append(file_path)


@given('I have a CSV file with mixed valid and invalid records')
def step_create_mixed_validity_file(context):
    """Create a CSV file with both valid and invalid records."""
    file_path = context.storage_config.imports_path / "mixed_validity.csv"

    content = VALID_CSV_HEADER + "\n"
    # Add valid record
    content += VALID_CSV_ROW + "\n"
    # Add invalid record (missing required fields)
    content += "Invalid,XX,,,,,,,,,,,,,,,,,,,,,,,,,\n"
    # Add another valid record
    content += VALID_CSV_ROW.replace("Test Regulation", "Test Regulation 2") + "\n"

    file_path.write_text(content)
    context.test_file = file_path
    context.expected_valid_records = 2
    context.expected_invalid_records = 1


# WHEN steps - Actions

@when('I discover import files')
def step_discover_import_files(context):
    """Discover import files in the directory."""
    context.discovered_files = context.importer.discover_import_files()


@when('I validate the file')
def step_validate_file(context):
    """Validate the test file."""
    context.validation_result, context.validation_errors = context.importer.validate_file(context.test_file)


@when('I import the single file')
def step_import_single_file(context):
    """Import a single file."""
    # Mock the CSV processor if needed
    if hasattr(context, 'processor_should_fail') and context.processor_should_fail:
        with patch('app.services.local_regulation_importer.RegulationCSVProcessor') as mock_processor_class:
            mock_processor = Mock()
            mock_processor_class.return_value = mock_processor
            mock_processor.import_csv_file.side_effect = Exception("Processing failed")
            context.import_result = context.importer.import_single_file(context.test_file, validate_first=False)
    else:
        with patch('app.services.local_regulation_importer.RegulationCSVProcessor') as mock_processor_class:
            mock_processor = Mock()
            mock_processor_class.return_value = mock_processor
            mock_processor.import_csv_file.return_value = RegulationCSVImportResult(
                batch_id="test_batch",
                total_records=1,
                successful_imports=1,
                failed_imports=0,
                updated_records=0,
                errors=[],
                warnings=[],
                processing_time=1.0
            )
            context.import_result = context.importer.import_single_file(context.test_file, validate_first=False)


@when('I import the single file with validation enabled')
def step_import_single_file_with_validation(context):
    """Import a single file with validation enabled."""
    context.import_result = context.importer.import_single_file(context.test_file, validate_first=True)


@when('I import all files')
def step_import_all_files(context):
    """Import all files in the directory."""
    with patch('app.services.local_regulation_importer.RegulationCSVProcessor') as mock_processor_class:
        mock_processor = Mock()
        mock_processor_class.return_value = mock_processor

        def mock_import_side_effect(file_path, **kwargs):
            # Check if file is valid based on content
            with open(file_path, 'r') as f:
                content = f.read()

            if VALID_CSV_HEADER in content and len(content.split('\n')) > 1:
                return RegulationCSVImportResult(
                    batch_id="test_batch",
                    total_records=1,
                    successful_imports=1,
                    failed_imports=0,
                    updated_records=0,
                    errors=[],
                    warnings=[],
                    processing_time=1.0
                )
            else:
                raise Exception("Invalid file format")

        mock_processor.import_csv_file.side_effect = mock_import_side_effect
        context.import_results = context.importer.import_all_files()


@when('I import the new file')
def step_import_duplicate_file(context):
    """Import a file that should be detected as duplicate."""
    # Mock the hash calculation to return the expected hash
    with patch.object(context.importer, 'calculate_file_hash', return_value=context.expected_hash):
        context.import_result = context.importer.import_single_file(context.test_file, validate_first=False)


@when('I create a backup of the file with reason "{reason}"')
def step_create_backup_file(context, reason):
    """Create a backup of the test file."""
    context.backup_path = context.importer.backup_file(context.test_file, reason)


@when('I clean up backups older than {days:d} days')
def step_cleanup_old_backups(context, days):
    """Clean up old backup files."""
    context.cleanup_count = context.importer.cleanup_old_backups(days_to_keep=days)


@when('I get storage statistics')
def step_get_storage_statistics(context):
    """Get storage statistics."""
    context.storage_stats = context.importer.get_storage_stats()


@when('I import the file with user tracking')
def step_import_with_user_tracking(context):
    """Import file with user tracking."""
    with patch('app.services.local_regulation_importer.RegulationCSVProcessor') as mock_processor_class:
        mock_processor = Mock()
        mock_processor_class.return_value = mock_processor
        mock_processor.import_csv_file.return_value = RegulationCSVImportResult(
            batch_id="test_batch",
            total_records=1,
            successful_imports=1,
            failed_imports=0,
            updated_records=0,
            errors=[],
            warnings=[],
            processing_time=1.0
        )
        context.import_result = context.importer.import_single_file(
            context.test_file,
            user_id=context.current_user_id,
            validate_first=False
        )


@when('I validate these files')
def step_validate_encoding_files(context):
    """Validate files with different encodings."""
    context.encoding_validation_results = []

    for file_path, encoding in context.encoding_files:
        is_valid, errors = context.importer.validate_file(file_path)
        context.encoding_validation_results.append((file_path, encoding, is_valid, errors))


@when('I place the file in the import directory')
def step_place_complete_dataset(context):
    """Place the complete dataset file in import directory (already done in given step)."""
    pass  # File already created in given step


@when('I run the complete import workflow')
def step_run_complete_workflow(context):
    """Run the complete import workflow."""
    with patch('app.services.local_regulation_importer.RegulationCSVProcessor') as mock_processor_class:
        mock_processor = Mock()
        mock_processor_class.return_value = mock_processor
        mock_processor.import_csv_file.return_value = RegulationCSVImportResult(
            batch_id="complete_workflow_batch",
            total_records=context.expected_record_count,
            successful_imports=context.expected_record_count,
            failed_imports=0,
            updated_records=0,
            errors=[],
            warnings=[],
            processing_time=5.0
        )
        context.workflow_results = context.importer.import_all_files(user_id="workflow_user")


@when('I attempt to import files concurrently')
def step_import_files_concurrently(context):
    """Attempt to import files concurrently."""
    import threading

    context.concurrent_results = []
    context.concurrent_errors = []

    def import_file(file_path):
        try:
            with patch('app.services.local_regulation_importer.RegulationCSVProcessor') as mock_processor_class:
                mock_processor = Mock()
                mock_processor_class.return_value = mock_processor
                mock_processor.import_csv_file.return_value = RegulationCSVImportResult(
                    batch_id=f"concurrent_batch_{file_path.name}",
                    total_records=1,
                    successful_imports=1,
                    failed_imports=0,
                    updated_records=0,
                    errors=[],
                    warnings=[],
                    processing_time=1.0
                )
                result = context.importer.import_single_file(file_path, validate_first=False)
                context.concurrent_results.append(result)
        except Exception as e:
            context.concurrent_errors.append(str(e))

    # Start concurrent imports
    threads = []
    for file_path in context.concurrent_files:
        thread = threading.Thread(target=import_file, args=(file_path,))
        threads.append(thread)
        thread.start()

    # Wait for all threads to complete
    for thread in threads:
        thread.join()


@when('I discover and import all files')
def step_discover_and_import_unicode_files(context):
    """Discover and import all Unicode-named files."""
    with patch('app.services.local_regulation_importer.RegulationCSVProcessor') as mock_processor_class:
        mock_processor = Mock()
        mock_processor_class.return_value = mock_processor
        mock_processor.import_csv_file.return_value = RegulationCSVImportResult(
            batch_id="unicode_batch",
            total_records=1,
            successful_imports=1,
            failed_imports=0,
            updated_records=0,
            errors=[],
            warnings=[],
            processing_time=1.0
        )
        context.unicode_import_results = context.importer.import_all_files()


@when('I import the file')
def step_import_mixed_validity_file(context):
    """Import file with mixed valid and invalid records."""
    with patch('app.services.local_regulation_importer.RegulationCSVProcessor') as mock_processor_class:
        mock_processor = Mock()
        mock_processor_class.return_value = mock_processor
        mock_processor.import_csv_file.return_value = RegulationCSVImportResult(
            batch_id="mixed_validity_batch",
            total_records=3,
            successful_imports=context.expected_valid_records,
            failed_imports=context.expected_invalid_records,
            updated_records=0,
            errors=[{"error": "Invalid record format", "row": 2}],
            warnings=[],
            processing_time=2.0
        )
        context.mixed_import_result = context.importer.import_single_file(context.test_file, validate_first=False)


# THEN steps - Assertions

@then('I should find {count:d} CSV files')
def step_verify_discovered_file_count(context, count):
    """Verify the number of discovered CSV files."""
    assert len(context.discovered_files) == count, f"Expected {count} files, found {len(context.discovered_files)}"


@then('the files should be sorted by name')
def step_verify_files_sorted(context):
    """Verify that discovered files are sorted by name."""
    file_names = [f.name for f in context.discovered_files]
    sorted_names = sorted(file_names)
    assert file_names == sorted_names, f"Files not sorted: {file_names} vs {sorted_names}"


@then('the validation should pass')
def step_verify_validation_passed(context):
    """Verify that file validation passed."""
    assert context.validation_result is True, f"Validation failed: {context.validation_errors}"


@then('there should be no validation errors')
def step_verify_no_validation_errors(context):
    """Verify that there are no validation errors."""
    assert len(context.validation_errors) == 0, f"Unexpected validation errors: {context.validation_errors}"


@then('the validation should fail')
def step_verify_validation_failed(context):
    """Verify that file validation failed."""
    assert context.validation_result is False, "Validation should have failed but passed"


@then('there should be validation errors about missing columns')
def step_verify_missing_columns_error(context):
    """Verify that validation errors mention missing columns."""
    error_text = ' '.join(context.validation_errors).lower()
    assert 'missing' in error_text and 'column' in error_text, f"Expected missing columns error, got: {context.validation_errors}"


@then('the import should succeed')
def step_verify_import_success(context):
    """Verify that the import succeeded."""
    assert context.import_result.successful_imports > 0, f"Import did not succeed: {context.import_result.errors}"
    assert context.import_result.failed_imports == 0, f"Import had failures: {context.import_result.errors}"


@then('the file should be moved to the processed directory')
def step_verify_file_moved_to_processed(context):
    """Verify that the file was moved to the processed directory."""
    assert not context.test_file.exists(), "Original file still exists"
    processed_files = list(context.storage_config.processed_path.glob("*"))
    assert len(processed_files) > 0, "No files found in processed directory"


@then('a backup should be created')
def step_verify_backup_created(context):
    """Verify that a backup was created."""
    backup_files = list(context.storage_config.backups_path.glob("*"))
    assert len(backup_files) > 0, "No backup files found"


@then('the import log should record the success')
def step_verify_import_log_success(context):
    """Verify that the import log records success."""
    import_logs = context.db.query(RegulationCSVImportLog).all()
    assert len(import_logs) > 0, "No import logs found"

    latest_log = import_logs[-1]
    assert latest_log.import_status in ['completed', 'completed_with_errors'], f"Unexpected status: {latest_log.import_status}"


@then('the import should fail validation')
def step_verify_import_validation_failure(context):
    """Verify that the import failed validation."""
    assert len(context.import_result.errors) > 0, "Import should have validation errors"
    assert context.import_result.successful_imports == 0, "Import should not have successful imports"


@then('the file should be moved to the failed directory')
def step_verify_file_moved_to_failed(context):
    """Verify that the file was moved to the failed directory."""
    assert not context.test_file.exists(), "Original file still exists"
    failed_files = list(context.storage_config.failed_path.glob("*"))
    assert len(failed_files) > 0, "No files found in failed directory"


@then('no backup should be created for processing')
def step_verify_no_processing_backup(context):
    """Verify that no backup was created during processing."""
    # Note: This is a bit tricky to test since backup might be created before validation
    # In practice, the backup creation happens before validation failure
    pass  # This step might need adjustment based on actual implementation


@then('the import log should record the validation failure')
def step_verify_import_log_validation_failure(context):
    """Verify that the import log records validation failure."""
    # For validation failures, no import log might be created, or it might record the failure
    # This depends on the implementation details
    pass  # Implementation-specific


@then('{success_count:d} files should be processed successfully')
def step_verify_successful_file_count(context, success_count):
    """Verify the number of successfully processed files."""
    successful_results = [r for r in context.import_results if r.successful_imports > 0]
    assert len(successful_results) == success_count, f"Expected {success_count} successful, got {len(successful_results)}"


@then('{fail_count:d} file should fail processing')
def step_verify_failed_file_count(context, fail_count):
    """Verify the number of failed file processing."""
    failed_results = [r for r in context.import_results if len(r.errors) > 0]
    assert len(failed_results) == fail_count, f"Expected {fail_count} failed, got {len(failed_results)}"


@then('all files should be moved from the import directory')
def step_verify_import_directory_empty(context):
    """Verify that all files were moved from the import directory."""
    remaining_files = list(context.storage_config.imports_path.glob("*.csv"))
    assert len(remaining_files) == 0, f"Files still in import directory: {[f.name for f in remaining_files]}"


@then('successful files should be in the processed directory')
def step_verify_successful_files_in_processed(context):
    """Verify that successful files are in the processed directory."""
    processed_files = list(context.storage_config.processed_path.glob("*"))
    successful_count = len([r for r in context.import_results if r.successful_imports > 0])
    assert len(processed_files) >= successful_count, f"Not enough files in processed directory: {len(processed_files)} < {successful_count}"


@then('failed files should be in the failed directory')
def step_verify_failed_files_in_failed(context):
    """Verify that failed files are in the failed directory."""
    failed_files = list(context.storage_config.failed_path.glob("*"))
    failed_count = len([r for r in context.import_results if len(r.errors) > 0])
    assert len(failed_files) >= failed_count, f"Not enough files in failed directory: {len(failed_files)} < {failed_count}"


@then('the import should detect the duplicate')
def step_verify_duplicate_detection(context):
    """Verify that the import detected a duplicate file."""
    assert len(context.import_result.warnings) > 0, "No warnings found for duplicate detection"
    warning_text = str(context.import_result.warnings[0]).lower()
    assert 'already processed' in warning_text or 'duplicate' in warning_text, f"Unexpected warning: {context.import_result.warnings[0]}"


@then('a warning should be logged about duplicate processing')
def step_verify_duplicate_warning_logged(context):
    """Verify that a warning was logged about duplicate processing."""
    assert len(context.import_result.warnings) > 0, "No warnings logged"
    warning_text = str(context.import_result.warnings).lower()
    assert 'processed' in warning_text, f"Warning doesn't mention processing: {context.import_result.warnings}"


@then('the import should handle the error gracefully')
def step_verify_error_handled_gracefully(context):
    """Verify that the import handled errors gracefully."""
    assert len(context.import_result.errors) > 0, "No errors recorded"
    assert context.import_result.successful_imports == 0, "Should not have successful imports on error"
    # Should not crash - if we get here, it was handled gracefully


@then('the error should be logged in the import result')
def step_verify_error_logged(context):
    """Verify that the error was logged in the import result."""
    assert len(context.import_result.errors) > 0, "No errors logged in import result"
    error_text = str(context.import_result.errors[0]).lower()
    assert 'processing failed' in error_text or 'exception' in error_text, f"Unexpected error: {context.import_result.errors[0]}"


@then('a backup file should be created in the backups directory')
def step_verify_backup_file_created(context):
    """Verify that a backup file was created."""
    assert context.backup_path.exists(), f"Backup file not found: {context.backup_path}"
    assert context.backup_path.parent == context.storage_config.backups_path, "Backup not in correct directory"


@then('the backup should contain the same content as the original')
def step_verify_backup_content_matches(context):
    """Verify that backup content matches original."""
    original_content = context.test_file.read_text()
    backup_content = context.backup_path.read_text()
    assert original_content == backup_content, "Backup content doesn't match original"


@then('the backup filename should include the reason and timestamp')
def step_verify_backup_filename_format(context):
    """Verify that backup filename includes reason and timestamp."""
    filename = context.backup_path.name
    assert 'test_backup' in filename, f"Backup filename doesn't include reason: {filename}"
    # Check for timestamp pattern (YYYYMMDD_HHMMSS)
    import re
    timestamp_pattern = r'\d{8}_\d{6}'
    assert re.search(timestamp_pattern, filename), f"Backup filename doesn't include timestamp: {filename}"


@then('{old_count:d} old backup files should be deleted')
def step_verify_old_backups_deleted(context, old_count):
    """Verify that old backup files were deleted."""
    assert context.cleanup_count == old_count, f"Expected {old_count} files cleaned, got {context.cleanup_count}"


@then('{recent_count:d} recent backup file should remain')
def step_verify_recent_backups_remain(context, recent_count):
    """Verify that recent backup files remain."""
    remaining_files = list(context.storage_config.backups_path.glob("*"))
    assert len(remaining_files) == recent_count, f"Expected {recent_count} remaining files, got {len(remaining_files)}"


@then('the cleanup count should be {expected_count:d}')
def step_verify_cleanup_count(context, expected_count):
    """Verify the cleanup count matches expected."""
    assert context.cleanup_count == expected_count, f"Expected cleanup count {expected_count}, got {context.cleanup_count}"


@then('the statistics should show correct file counts for each directory')
def step_verify_storage_stats_file_counts(context):
    """Verify that storage statistics show correct file counts."""
    for row in context.table:
        directory = row['directory']
        expected_count = int(row['file_count'])

        actual_count = context.storage_stats[directory]['file_count']
        assert actual_count == expected_count, f"Directory {directory}: expected {expected_count} files, got {actual_count}"


@then('the statistics should show correct total sizes for each directory')
def step_verify_storage_stats_sizes(context):
    """Verify that storage statistics show correct total sizes."""
    for row in context.table:
        directory = row['directory']
        expected_size = float(row['total_size_mb'])

        actual_size = context.storage_stats[directory]['total_size_mb']
        # Allow for small rounding differences
        assert abs(actual_size - expected_size) < 0.1, f"Directory {directory}: expected {expected_size}MB, got {actual_size}MB"


@then('the import log should record my user ID')
def step_verify_user_id_recorded(context):
    """Verify that the user ID was recorded in the import log."""
    import_logs = context.db.query(RegulationCSVImportLog).all()
    assert len(import_logs) > 0, "No import logs found"

    latest_log = import_logs[-1]
    # Note: The actual user ID recording depends on implementation
    # This is a placeholder for the actual verification
    assert context.current_user_id is not None, "User ID should be set"


@then('the import should succeed with user attribution')
def step_verify_import_success_with_user(context):
    """Verify that import succeeded with user attribution."""
    assert context.import_result.successful_imports > 0, "Import should have succeeded"
    # User attribution verification would depend on implementation details


@then('the import should handle the permission error')
def step_verify_permission_error_handled(context):
    """Verify that permission errors are handled gracefully."""
    # If we get here without crashing, the error was handled
    # The specific behavior depends on implementation
    assert True  # Placeholder - actual verification would check error handling


@then('an appropriate error message should be logged')
def step_verify_error_message_logged(context):
    """Verify that an appropriate error message was logged."""
    # This would typically check log files or error collections
    # Implementation depends on logging setup
    assert True  # Placeholder


@then('all Unicode-named files should be processed correctly')
def step_verify_unicode_files_processed(context):
    """Verify that Unicode-named files were processed correctly."""
    assert len(context.unicode_import_results) == len(context.unicode_files), "Not all Unicode files were processed"

    for result in context.unicode_import_results:
        assert result.successful_imports > 0, f"Unicode file import failed: {result.errors}"


@then('the files should be moved to appropriate directories')
def step_verify_unicode_files_moved(context):
    """Verify that Unicode files were moved to appropriate directories."""
    # Check that no files remain in imports
    remaining_files = list(context.storage_config.imports_path.glob("*"))
    assert len(remaining_files) == 0, f"Unicode files not moved: {[f.name for f in remaining_files]}"

    # Check that files are in processed directory
    processed_files = list(context.storage_config.processed_path.glob("*"))
    assert len(processed_files) >= len(context.unicode_files), "Not all Unicode files in processed directory"


@then('there should be an error about file size being too large')
def step_verify_file_size_error(context):
    """Verify that there's an error about file size."""
    error_text = ' '.join(context.validation_errors).lower()
    assert 'too large' in error_text or 'size' in error_text, f"Expected file size error, got: {context.validation_errors}"


@then('the validation should handle different encodings appropriately')
def step_verify_encoding_handling(context):
    """Verify that different encodings are handled appropriately."""
    # Check that validation didn't crash for any encoding
    assert len(context.encoding_validation_results) == len(context.encoding_files), "Not all encoding files were validated"

    for file_path, encoding, is_valid, errors in context.encoding_validation_results:
        # Should not crash (if we get here, it didn't crash)
        assert isinstance(is_valid, bool), f"Validation result should be boolean for {file_path}"
        assert isinstance(errors, list), f"Errors should be list for {file_path}"


@then('all {record_count:d} records should be imported successfully')
def step_verify_all_records_imported(context, record_count):
    """Verify that all records were imported successfully."""
    total_successful = sum(r.successful_imports for r in context.workflow_results)
    assert total_successful == record_count, f"Expected {record_count} successful imports, got {total_successful}"


@then('the file should be processed and archived')
def step_verify_file_processed_and_archived(context):
    """Verify that the file was processed and archived."""
    # Check that file is no longer in imports
    assert not context.complete_dataset_file.exists(), "Dataset file still in imports directory"

    # Check that file is in processed directory
    processed_files = list(context.storage_config.processed_path.glob("*"))
    assert len(processed_files) > 0, "No files in processed directory"

    # Check that backup was created
    backup_files = list(context.storage_config.backups_path.glob("*"))
    assert len(backup_files) > 0, "No backup files created"


@then('storage statistics should reflect the import')
def step_verify_storage_stats_reflect_import(context):
    """Verify that storage statistics reflect the import."""
    stats = context.importer.get_storage_stats()

    # Should have files in processed directory
    assert stats['processed']['file_count'] > 0, "No files in processed directory"

    # Should have backup files
    assert stats['backups']['file_count'] > 0, "No backup files"

    # Imports directory should be empty
    assert stats['imports']['file_count'] == 0, "Files still in imports directory"


@then('I can query the imported regulations from the database')
def step_verify_database_query(context):
    """Verify that imported regulations can be queried from database."""
    # This would require actual database integration
    # For now, we'll verify that the mock was called correctly
    assert len(context.workflow_results) > 0, "No workflow results"
    assert all(r.successful_imports > 0 for r in context.workflow_results), "Not all imports were successful"


@then('the system should handle concurrent access safely')
def step_verify_concurrent_access_safety(context):
    """Verify that concurrent access was handled safely."""
    # Check that all concurrent operations completed
    expected_results = len(context.concurrent_files)
    actual_results = len(context.concurrent_results)

    assert actual_results == expected_results, f"Expected {expected_results} results, got {actual_results}"
    assert len(context.concurrent_errors) == 0, f"Concurrent errors occurred: {context.concurrent_errors}"


@then('no data corruption should occur')
def step_verify_no_data_corruption(context):
    """Verify that no data corruption occurred."""
    # In a real scenario, this would check database integrity
    # For our mock scenario, we verify that all operations completed successfully
    for result in context.concurrent_results:
        assert result.successful_imports > 0, f"Concurrent import failed: {result.errors}"


@then('all files should be processed exactly once')
def step_verify_files_processed_once(context):
    """Verify that all files were processed exactly once."""
    # Check that no files remain in imports
    remaining_files = list(context.storage_config.imports_path.glob("*"))
    assert len(remaining_files) == 0, "Files still in imports directory"

    # Check that we have the expected number of results
    assert len(context.concurrent_results) == len(context.concurrent_files), "Not all files were processed"


@then('valid records should be imported successfully')
def step_verify_valid_records_imported(context):
    """Verify that valid records were imported successfully."""
    assert context.mixed_import_result.successful_imports == context.expected_valid_records, f"Expected {context.expected_valid_records} successful, got {context.mixed_import_result.successful_imports}"


@then('invalid records should be logged as errors')
def step_verify_invalid_records_logged(context):
    """Verify that invalid records were logged as errors."""
    assert context.mixed_import_result.failed_imports == context.expected_invalid_records, f"Expected {context.expected_invalid_records} failed, got {context.mixed_import_result.failed_imports}"
    assert len(context.mixed_import_result.errors) > 0, "No errors logged for invalid records"


@then('the import should complete with partial success')
def step_verify_partial_success(context):
    """Verify that import completed with partial success."""
    assert context.mixed_import_result.successful_imports > 0, "No successful imports"
    assert context.mixed_import_result.failed_imports > 0, "No failed imports"
    assert context.mixed_import_result.total_records > context.mixed_import_result.successful_imports, "Total should be greater than successful"


@then('detailed error information should be available')
def step_verify_detailed_error_info(context):
    """Verify that detailed error information is available."""
    assert len(context.mixed_import_result.errors) > 0, "No error information available"

    # Check that error contains useful information
    error = context.mixed_import_result.errors[0]
    assert 'error' in error, "Error should contain error message"
    # Could also check for row numbers, specific error types, etc.


# Cleanup function for after scenarios
def after_scenario(context, scenario):
    """Clean up after each scenario."""
    # Close database connection
    if hasattr(context, 'db'):
        context.db.close()

    # Clean up temporary directories
    if hasattr(context, 'cleanup_dirs'):
        import shutil
        for temp_dir in context.cleanup_dirs:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)

    # Restore permissions if they were changed
    if hasattr(context, 'restricted_permissions'):
        try:
            context.storage_config.processed_path.chmod(0o755)
        except:
            pass