"""
Behave environment configuration for RegulationGuru testing
"""
import os
import time
import subprocess
import requests
from pathlib import Path

def before_all(context):
    """Setup before all tests"""
    print("🚀 Setting up test environment...")
    
    # Set test configuration
    context.base_url = "http://localhost:8000"
    context.test_timeout = 30
    
    # Check if application is already running
    try:
        response = requests.get(f"{context.base_url}/api/v1/health", timeout=5)
        if response.status_code == 200:
            print("✅ Application already running")
            context.app_started_by_test = False
            return
    except requests.exceptions.RequestException:
        pass
    
    # Start the application for testing
    print("🔄 Starting application for testing...")
    try:
        # Start the application in the background
        context.app_process = subprocess.Popen(
            ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=Path(__file__).parent.parent
        )
        context.app_started_by_test = True
        
        # Wait for application to start
        max_attempts = 30
        for attempt in range(max_attempts):
            try:
                response = requests.get(f"{context.base_url}/api/v1/health", timeout=2)
                if response.status_code == 200:
                    print("✅ Application started successfully")
                    break
            except requests.exceptions.RequestException:
                time.sleep(1)
                if attempt == max_attempts - 1:
                    print("❌ Failed to start application")
                    raise Exception("Application failed to start within timeout")
                    
    except Exception as e:
        print(f"❌ Error starting application: {str(e)}")
        context.app_started_by_test = False

def after_all(context):
    """Cleanup after all tests"""
    print("🧹 Cleaning up test environment...")
    
    # Stop application if we started it
    if hasattr(context, 'app_started_by_test') and context.app_started_by_test:
        if hasattr(context, 'app_process'):
            print("🛑 Stopping application...")
            context.app_process.terminate()
            try:
                context.app_process.wait(timeout=10)
                print("✅ Application stopped successfully")
            except subprocess.TimeoutExpired:
                print("⚠️ Force killing application...")
                context.app_process.kill()
                context.app_process.wait()

def before_scenario(context, scenario):
    """Setup before each scenario"""
    context.scenario_name = scenario.name
    print(f"🎬 Starting scenario: {scenario.name}")

def after_scenario(context, scenario):
    """Cleanup after each scenario"""
    print(f"🎬 Completed scenario: {scenario.name} - Status: {scenario.status}")

    # Clean up any test data created during the scenario
    if hasattr(context, 'test_regulation_title'):
        # In a real implementation, this would clean up test data
        pass

    # Clean up local regulation importer test data
    if hasattr(context, 'db'):
        try:
            context.db.close()
        except:
            pass

    # Clean up temporary directories
    if hasattr(context, 'cleanup_dirs'):
        import shutil
        for temp_dir in context.cleanup_dirs:
            if os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                except:
                    pass

    # Restore permissions if they were changed
    if hasattr(context, 'restricted_permissions') and hasattr(context, 'storage_config'):
        try:
            context.storage_config.processed_path.chmod(0o755)
        except:
            pass

def before_step(context, step):
    """Setup before each step"""
    context.step_name = step.name

def after_step(context, step):
    """Cleanup after each step"""
    if step.status == "failed":
        print(f"❌ Step failed: {step.name}")
        if hasattr(context, 'last_response'):
            print(f"Last response status: {getattr(context.last_response, 'status_code', 'N/A')}")
