Feature: Local Regulation Import
  As a compliance officer
  I want to import regulation data from local CSV files
  So that I can maintain an up-to-date regulatory database

  Background:
    Given I have a local storage system configured
    And I have a database connection

  Scenario: Discover CSV files in import directory
    Given I have CSV files in the import directory:
      | filename                | content_type |
      | regulations_2024.csv    | valid        |
      | compliance_rules.csv    | valid        |
      | non_csv_file.txt        | text         |
    When I discover import files
    Then I should find 2 CSV files
    And the files should be sorted by name

  <PERSON><PERSON><PERSON>: Validate a valid CSV file
    Given I have a CSV file "valid_regulations.csv" with proper structure:
      | Country_Name | Country_Code | Document_Title | Document_Type | Issuing_Authority |
      | United States| US           | Test Regulation| Federal Law   | Test Authority    |
    When I validate the file
    Then the validation should pass
    And there should be no validation errors

  Sc<PERSON><PERSON>: Validate an invalid CSV file
    Given I have a CSV file "invalid_regulations.csv" with missing columns:
      | Name | Code |
      | US   | Test |
    When I validate the file
    Then the validation should fail
    And there should be validation errors about missing columns

  Sc<PERSON>rio: Import a single valid CSV file
    Given I have a valid CSV file "single_regulation.csv" in the import directory
    When I import the single file
    Then the import should succeed
    And the file should be moved to the processed directory
    And a backup should be created
    And the import log should record the success

  Scenario: Import a file that fails validation
    Given I have an invalid CSV file "bad_format.csv" in the import directory
    When I import the single file with validation enabled
    Then the import should fail validation
    And the file should be moved to the failed directory
    And no backup should be created for processing
    And the import log should record the validation failure

  Scenario: Import multiple files in batch
    Given I have multiple CSV files in the import directory:
      | filename           | validity |
      | regulations_1.csv  | valid    |
      | regulations_2.csv  | valid    |
      | regulations_3.csv  | invalid  |
    When I import all files
    Then 2 files should be processed successfully
    And 1 file should fail processing
    And all files should be moved from the import directory
    And successful files should be in the processed directory
    And failed files should be in the failed directory

  Scenario: Detect duplicate file by hash
    Given I have previously imported a file with hash "abc123def456"
    And I have a new file with the same hash "abc123def456"
    When I import the new file
    Then the import should detect the duplicate
    And the file should be moved to processed directory
    And a warning should be logged about duplicate processing

  Scenario: Handle file processing errors gracefully
    Given I have a valid CSV file "error_test.csv" in the import directory
    And the CSV processor will throw an exception
    When I import the single file
    Then the import should handle the error gracefully
    And the file should be moved to the failed directory
    And the error should be logged in the import result

  Scenario: Create and manage file backups
    Given I have a CSV file "backup_test.csv" in the import directory
    When I create a backup of the file with reason "test_backup"
    Then a backup file should be created in the backups directory
    And the backup should contain the same content as the original
    And the backup filename should include the reason and timestamp

  Scenario: Clean up old backup files
    Given I have backup files with different ages:
      | filename              | age_days |
      | old_backup_1.csv      | 35       |
      | old_backup_2.csv      | 45       |
      | recent_backup.csv     | 5        |
    When I clean up backups older than 30 days
    Then 2 old backup files should be deleted
    And 1 recent backup file should remain
    And the cleanup count should be 2

  Scenario: Get storage statistics
    Given I have files distributed across storage directories:
      | directory | file_count | total_size_mb |
      | imports   | 3          | 1.5           |
      | processed | 5          | 2.8           |
      | failed    | 1          | 0.3           |
      | backups   | 8          | 4.2           |
    When I get storage statistics
    Then the statistics should show correct file counts for each directory
    And the statistics should show correct total sizes for each directory

  Scenario: Import with user tracking
    Given I have a valid CSV file "user_tracked.csv" in the import directory
    And I am logged in as user "compliance_officer_123"
    When I import the file with user tracking
    Then the import log should record my user ID
    And the import should succeed with user attribution

  Scenario: Handle permission errors during file operations
    Given I have a CSV file "permission_test.csv" in the import directory
    And the processed directory has restricted permissions
    When I import the single file
    Then the import should handle the permission error
    And an appropriate error message should be logged

  Scenario: Process files with Unicode names
    Given I have CSV files with Unicode names:
      | filename              |
      | 规章制度_2024.csv      |
      | règlement_français.csv |
      | регламент_русский.csv  |
    When I discover and import all files
    Then all Unicode-named files should be processed correctly
    And the files should be moved to appropriate directories

  Scenario: Validate file size limits
    Given I have a CSV file that exceeds the size limit
    When I validate the file
    Then the validation should fail
    And there should be an error about file size being too large

  Scenario: Handle CSV files with different encodings
    Given I have CSV files with different encodings:
      | filename        | encoding |
      | utf8_file.csv   | utf-8    |
      | latin1_file.csv | latin-1  |
    When I validate these files
    Then the validation should handle different encodings appropriately

  Scenario: Import workflow end-to-end
    Given I have a complete regulatory dataset in CSV format
    And the dataset contains 50 regulation records from 5 countries
    When I place the file in the import directory
    And I run the complete import workflow
    Then all 50 records should be imported successfully
    And the file should be processed and archived
    And storage statistics should reflect the import
    And I can query the imported regulations from the database

  Scenario: Concurrent file processing safety
    Given I have multiple CSV files ready for import
    When I attempt to import files concurrently
    Then the system should handle concurrent access safely
    And no data corruption should occur
    And all files should be processed exactly once

  Scenario: Recovery from partial failures
    Given I have a CSV file with mixed valid and invalid records
    When I import the file
    Then valid records should be imported successfully
    And invalid records should be logged as errors
    And the import should complete with partial success
    And detailed error information should be available
