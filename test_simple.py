"""
Simple test to verify basic functionality without complex imports
"""
import pytest
import sys
import os

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_basic_imports():
    """Test that basic modules can be imported"""
    try:
        from app.db import models
        assert models is not None
        print("✅ Models import successful")
    except ImportError as e:
        pytest.skip(f"Models import failed: {e}")

def test_basic_database_models():
    """Test basic database model definitions"""
    try:
        from app.db.models import Country, Regulator, RegulationURL
        
        # Test that models have required attributes
        assert hasattr(Country, '__tablename__')
        assert hasattr(Regulator, '__tablename__')
        assert hasattr(RegulationURL, '__tablename__')
        
        print("✅ Database models structure verified")
    except ImportError as e:
        pytest.skip(f"Database models import failed: {e}")

def test_basic_schemas():
    """Test basic schema definitions"""
    try:
        from app.schemas import schemas
        assert schemas is not None
        print("✅ Schemas import successful")
    except ImportError as e:
        pytest.skip(f"Schemas import failed: {e}")

def test_basic_api_structure():
    """Test basic API structure"""
    try:
        import os
        api_dir = os.path.join(os.path.dirname(__file__), 'app', 'api')
        assert os.path.exists(api_dir), "API directory should exist"
        
        # Check for key API modules
        expected_modules = [
            'compliance.py',
            'countries',
            'regulators', 
            'regulations',
            'data_sources'
        ]
        
        for module in expected_modules:
            module_path = os.path.join(api_dir, module)
            if os.path.exists(module_path) or os.path.exists(module_path + '.py'):
                print(f"✅ Found API module: {module}")
            else:
                print(f"⚠️ Missing API module: {module}")
                
    except Exception as e:
        pytest.skip(f"API structure test failed: {e}")

def test_project_structure():
    """Test overall project structure"""
    expected_dirs = [
        'app',
        'tests', 
        'docs',
        'templates',
        'statics'
    ]
    
    for dir_name in expected_dirs:
        dir_path = os.path.join(os.path.dirname(__file__), dir_name)
        if os.path.exists(dir_path):
            print(f"✅ Found directory: {dir_name}")
        else:
            print(f"⚠️ Missing directory: {dir_name}")

def test_configuration_files():
    """Test that configuration files exist"""
    config_files = [
        'requirements.txt',
        'pytest.ini',
        '.coveragerc',
        'README.md'
    ]
    
    for file_name in config_files:
        file_path = os.path.join(os.path.dirname(__file__), file_name)
        if os.path.exists(file_path):
            print(f"✅ Found config file: {file_name}")
        else:
            print(f"⚠️ Missing config file: {file_name}")

def test_governance_module_structure():
    """Test governance module structure"""
    try:
        governance_dir = os.path.join(os.path.dirname(__file__), 'app', 'api', 'governance')
        if os.path.exists(governance_dir):
            print("✅ Governance module directory exists")
            
            # Check for governance files
            governance_files = ['router.py', 'risk.py', 'control.py']
            for file_name in governance_files:
                file_path = os.path.join(governance_dir, file_name)
                if os.path.exists(file_path):
                    print(f"✅ Found governance file: {file_name}")
                else:
                    print(f"⚠️ Missing governance file: {file_name}")
        else:
            print("⚠️ Governance module directory not found")
    except Exception as e:
        pytest.skip(f"Governance module test failed: {e}")

def test_data_sources_structure():
    """Test data sources module structure"""
    try:
        data_sources_dir = os.path.join(os.path.dirname(__file__), 'app', 'api', 'data_sources')
        if os.path.exists(data_sources_dir):
            print("✅ Data sources module directory exists")
            
            public_dir = os.path.join(data_sources_dir, 'public')
            if os.path.exists(public_dir):
                print("✅ Public data sources directory exists")
                
                # Check for public data source files
                public_files = ['sec_edgar.py', 'cfpb_complaints.py', 'finra_rules.py']
                for file_name in public_files:
                    file_path = os.path.join(public_dir, file_name)
                    if os.path.exists(file_path):
                        print(f"✅ Found public data source: {file_name}")
                    else:
                        print(f"⚠️ Missing public data source: {file_name}")
            else:
                print("⚠️ Public data sources directory not found")
        else:
            print("⚠️ Data sources module directory not found")
    except Exception as e:
        pytest.skip(f"Data sources module test failed: {e}")

def test_prd_documentation():
    """Test PRD documentation structure"""
    try:
        prd_dir = os.path.join(os.path.dirname(__file__), 'docs', 'prd')
        if os.path.exists(prd_dir):
            print("✅ PRD documentation directory exists")
            
            # Check for PRD files
            prd_files = [
                'compliance_evidence_repository.md',
                'compliance_calendar.md',
                'compliance_task_management.md',
                'ai_regulatory_chatbot.md'
            ]
            for file_name in prd_files:
                file_path = os.path.join(prd_dir, file_name)
                if os.path.exists(file_path):
                    print(f"✅ Found PRD: {file_name}")
                else:
                    print(f"⚠️ Missing PRD: {file_name}")
        else:
            print("⚠️ PRD documentation directory not found")
    except Exception as e:
        pytest.skip(f"PRD documentation test failed: {e}")

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
