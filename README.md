# Regulatory Compliance System

A comprehensive system for managing regulatory compliance, with features for impact assessment, compliance tracking, regulatory alerts, and trend analysis.

## Features

### API Endpoints
- **Compliance API**: Track compliance scores across regions and countries
- **Impact Assessment API**: Assess the impact of regulatory changes on your business
- **Regulatory Alerts API**: Get notifications of recent regulatory changes
- **Trend Analysis API**: Analyze trends in regulatory changes over time
- **Integration API**: Connect with external systems via SuperGlu integration
- **RESTful API**: Built with FastAPI
- **PostgreSQL Database**: With connection pooling
- **Soft Delete**: Records are marked as deleted instead of being permanently removed
- **Timestamps**: Automatic tracking of created_at and changed_on times
- **Type Safety**: Strong type hints throughout the codebase
- **Internationalization (i18n)**: Multi-language support using Babel with translations stored in the lang/ folder
- **Admin Interface**: Starlette-Admin integration under `/ui/manage` for easy database management
- **Multilingual Support**:  English (US/UK), Spanish, German, Afrikaans, Zulu

## Project Structure

The project is organized into the following directory structure:

```
.
├── app/                  # Main application package
│   ├── api/              # API endpoints and routers
│   ├── core/             # Core application functionality
│   ├── db/               # Database models and connection
│   ├── schemas/          # Pydantic schemas
│   ├── utils/            # Utility functions
│   ├── admin/            # Admin interface
│   ├── i18n/             # Internationalization
│   └── visualization/    # Data visualization components
├── docs/                 # Documentation
├── lang/                 # Translation files
├── migrations/           # Database migrations
├── scripts/              # Utility scripts
├── templates/            # HTML templates
├── tests/                 # Test suite
└── run.py                # Application entry point
```

To run the application:
```bash
python run.py
```

For deployment:
```bash
uvicorn app.main:app --host 0.0.0.0
```

## FastAPI Patterns

This application follows several FastAPI patterns:

1. **Module Organization**: Functionality is organized into specific modules
2. **Pydantic Models**: Data validation using Pydantic schemas
3. **Dependency Injection**: Database sessions and other dependencies are injected
4. **Path Operations**: Clearly defined API endpoints with documentation
5. **HTML Responses**: Some endpoints return HTML with embedded JavaScript

## Template Rendering

The application uses two approaches for rendering content:

1. **Server-Side Templates**: Using Jinja2 templates in the `templates/` directory
2. **Embedded HTML**: Some endpoints return HTML with embedded JavaScript directly 

## Coding Standards

- **PEP 8**: Follow Python style guide for code formatting
- **PEP 257**: Follow docstring conventions with structured docstrings
- **PEP 484**: Use type hints for all function parameters and return values
- **Strong Type Hinting**: Use explicit type annotations for improved code clarity and IDE support
- **Type Safety**: Enforced with static type checking tools

## Testing

This project includes a comprehensive test suite with:prehensive test suite to ensure code quality and reliability.

### Running Tests

```bash
# Run all tests
pytest

# Run tests with coverage report
pytest --cov=.

# Run specific test modules
pytest tests/test_main.py

# Run tests by marker
pytest -m ui
```

### Test Suite Components

- **Unit Tests**: Test individual functions and classes
  - API endpoints and logic (test_main.py, test_item_api.py)
  - Database models and operations
  - Utility functions (i18n, worldmap)

- **Integration Tests**: Test interactions between components
  - Database operations
  - API endpoint integrations

- **UI Tests**: Test front-end interactions (test_ui.py)
  - Uses selenium-headless for headless browser testing

- **Test Fixtures**: Common setup and teardown logic
  - Database fixtures
  - Client fixtures
  - Mock objects

### Test Coverage

The project aims for 95% test coverage with detailed tests for all functionality.
Coverage reports are generated automatically when running pytest with the --cov flag.

To generate detailed coverage reports, run:
```
python generate_coverage.py
```

This will create:
- Terminal report showing coverage percentage
- HTML report in coverage/html/ (open index.html in a browser)
- XML report in coverage/coverage.xml for CI integration

## Project Milestones

| Milestone | Status | Date | Description |
|-----------|--------|------|-------------|
| Initial Setup | ✅ | - | Project structure and core dependencies established |
| Database Schema | ✅ | - | PostgreSQL schema with models for regulatory data |
| API Development | ✅ | - | Core API endpoints for regulatory information |
| Internationalization | ✅ | - | Multi-language support using Babel |
| Admin Interface | ✅ | - | Admin dashboard for data management |
| Testing Framework | ✅ | - | Comprehensive test suite with >90% coverage |
| Server Running | ✅ | 2024-02-27 | Server successfully running with updated infrastructure |

## URL Processor

The URL Processor is a tool for analyzing regulatory URLs and storing them in a structured database. It extracts information about the country and regulatory authority responsible for each URL.

### Features:
- Extract domain and country information from URLs
- Identify regulatory authorities based on domain patterns
- Link URLs to known regulation categories
- Calculate confidence levels for URL-regulator associations
- Store all data in PostgreSQL with proper normalization

### Usage:
```
python import_urls.py <url_file_path>
```

### Database Schema:
- **Countries**: Stores country information
- **Regulators**: Stores regulatory authorities with country relationships
- **RegulationURLs**: Stores URLs with normalized relationships and confidence scores

## Database Configuration

### PostgreSQL Setup in Replit

1. Open a new tab in Replit and type "Database"
2. In the "Database" panel, click "create a database"
3. Once created, view the connection information in the "Secrets" tab
4. Update the `.env` file with your DATABASE_URL from the environment variables

The application will automatically use connection pooling for better performance if running on Replit's PostgreSQL service.

### Database Models

Our models are defined in `models.py` and include:
- Base SQLAlchemy declarative base
- `SoftDeleteMixin`: Provides soft delete functionality (is_deleted, deleted_at)
- `TimestampMixin`: Adds created_at and changed_on timestamps
- `Item`: Example model inheriting from both mixins

## Database Migrations with Alembic

This project uses Alembic for database migrations to manage schema changes. We've created a convenient migration management script in `migrate.py`.

### Using the Migration Script

```bash
# Create a new migration
python migrate.py create "Description of changes"

# Apply all pending migrations
python migrate.py upgrade

# Downgrade to previous migration
python migrate.py downgrade

# Show migration history
python migrate.py history

# Show current migration
python migrate.py current
```

### Using Workflows

For convenience, we've set up the following Replit workflows:
- **Alembic: Create Migration**: Generate a new migration
- **Alembic: Upgrade to Latest**: Apply all pending migrations
- **Alembic: Downgrade**: Revert to the previous migration
- **Alembic: Show History**: Display migration history
- **Alembic: Show Current**: Show current migration version

Access these workflows from the Run button's dropdown menu.

## API Endpoints

### Health Check
- `GET /health`: Check application and database health status
  - Returns health status, timestamp, and database connection status
  - Access logs stored in health_check_log.txt

### Core Endpoints
- `GET /`: Root endpoint returning a greeting message with i18n support
- `GET /languages`: Get information about available languages
- `GET /worldmap`: Interactive world map visualization with customizable center and zoom
  - Query parameters: lat, lon, zoom

### Items API
- `POST /items/`: Create a new item
- `GET /items/`: Retrieve a list of items with pagination and soft-delete filtering
  - Query parameters: skip, limit, include_deleted
- `GET /items/{item_id}`: Get a specific item by ID
- `PUT /items/{item_id}`: Update an existing item
- `DELETE /items/{item_id}`: Soft delete an item (or hard delete with query parameter)
  - Query parameters: hard_delete
- `POST /items/{item_id}/restore`: Restore a soft-deleted item

## Internationalization (i18n)

This project includes multi-language support using Babel:

1. Translation files are stored in the `lang/` directory
2. Supported languages: English (en), Spanish (es), French (fr), Afrikaans (af), German (de), Zulu (zu), English (UK) en-GB, English (US) en-US


### Managing Translations

Use the `translate.py` script to manage translations:

```bash
# Extract translatable strings to messages.pot
python translate.py extract

# Update language-specific .po files
python translate.py update

# Compile .po files to .mo binary files
python translate.py compile
```

### Using Translations in Code

To translate messages in endpoints:

```python
@app.get("/example")
def example(translator: Callable = Depends(get_translator)):
    return {"message": translator("Hello, this will be translated")}
```

### Changing Language

Language can be set using:
- Query parameter: `?lang=es`
- Cookie: `lang=es`
- Browser's Accept-Language header

## Entity Relationship Diagram

```mermaid
erDiagram
    Country {
        int id PK
        string name
        string code
        string region
        string subregion
        string flag_emoji
    }

    Regulator {
        int id PK
        string name
        string website
        int country_id FK
    }

    RegulatorySource {
        int id PK
        string name
        string url
        string description
        int collection_frequency
        datetime last_collection
        string last_error
        int country_id FK
        int regulator_id FK
    }

    RegulationURL {
        int id PK
        string url
        string title
        datetime publication_date
        string status
        int country_id FK
        int regulator_id FK
        int source_id FK
    }

    AlertSubscription {
        int id PK
        string email
        int frequency
        datetime created_at
        datetime last_notified_at
        boolean is_active
        int regulator_id FK
        int source_id FK
    }

    Document {
        int id PK
        string filename
        string content_type
        int content_length
        float confidence_score
        datetime created_at
    }

    DocumentSummary {
        int id PK
        int document_id FK
        string summary_text
        array key_requirements
        array deadlines
        array penalties
        datetime created_at
    }

    Country ||--o{ Regulator : "has many"
    Country ||--o{ RegulationURL : "has many"
    Country ||--o{ RegulatorySource : "has many"
    Regulator ||--o{ RegulationURL : "has many"
    Regulator ||--o{ RegulatorySource : "has many"
    Regulator ||--o{ AlertSubscription : "has many"
    RegulatorySource ||--o{ RegulationURL : "has many"
    RegulatorySource ||--o{ AlertSubscription : "has many"
    Document ||--o{ DocumentSummary : "has many"