#!/usr/bin/env python3
"""
PostgreSQL Integration Test Runner
Comprehensive testing with real PostgreSQL database - no mocks
"""
import os
import sys
import subprocess
import time
import json
import psycopg2
from datetime import datetime
from pathlib import Path

class PostgreSQLTestRunner:
    """Manages PostgreSQL integration testing lifecycle"""
    
    def __init__(self):
        self.test_db_url = "postgresql://postgres:postgres@localhost:5433/regulationguru_test"
        self.integration_db_url = "postgresql://postgres:postgres@localhost:5433/regulationguru_integration"
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'database_setup': {},
            'soft_delete_tests': {},
            'api_integration_tests': {},
            'performance_tests': {},
            'data_sources_tests': {},
            'compliance_tests': {},
            'summary': {}
        }
        
    def setup_environment(self):
        """Setup test environment variables"""
        print("🔧 Setting up test environment...")
        
        os.environ["TESTING"] = "true"
        os.environ["DATABASE_URL"] = self.test_db_url
        os.environ["TEST_DATABASE_URL"] = self.test_db_url
        os.environ["INTEGRATION_DATABASE_URL"] = self.integration_db_url
        os.environ["LOG_LEVEL"] = "INFO"
        os.environ["PYTHONPATH"] = str(Path.cwd())
        
        print("✅ Environment configured")
        
    def start_postgresql_container(self):
        """Start PostgreSQL test container"""
        print("🐘 Starting PostgreSQL test container...")
        
        try:
            # Check if container is already running
            check_cmd = ["docker", "ps", "--filter", "name=regulationguru-test-db", "--format", "{{.Names}}"]
            result = subprocess.run(check_cmd, capture_output=True, text=True)
            
            if "regulationguru-test-db" in result.stdout:
                print("✅ PostgreSQL container already running")
                return True
                
            # Start the test database container
            start_cmd = [
                "docker", "run", "-d",
                "--name", "regulationguru-test-db",
                "-e", "POSTGRES_USER=postgres",
                "-e", "POSTGRES_PASSWORD=postgres", 
                "-e", "POSTGRES_DB=regulationguru_test",
                "-p", "5433:5432",
                "postgres:14-alpine"
            ]
            
            subprocess.run(start_cmd, check=True)
            
            # Wait for PostgreSQL to be ready
            max_attempts = 30
            for attempt in range(max_attempts):
                try:
                    conn = psycopg2.connect(self.test_db_url)
                    conn.close()
                    print("✅ PostgreSQL container started and ready")
                    return True
                except psycopg2.OperationalError:
                    time.sleep(1)
                    if attempt == max_attempts - 1:
                        print("❌ PostgreSQL container failed to start")
                        return False
                        
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to start PostgreSQL container: {e}")
            return False
            
    def create_test_databases(self):
        """Create test databases"""
        print("🗄️ Creating test databases...")
        
        try:
            # Connect to default postgres database
            admin_url = "postgresql://postgres:postgres@localhost:5433/postgres"
            admin_conn = psycopg2.connect(admin_url)
            admin_conn.set_isolation_level(psycopg2.extensions.ISOLATION_LEVEL_AUTOCOMMIT)
            admin_cursor = admin_conn.cursor()
            
            # Create test databases
            databases = ["regulationguru_test", "regulationguru_integration"]
            
            for db_name in databases:
                admin_cursor.execute(
                    "SELECT 1 FROM pg_catalog.pg_database WHERE datname = %s", 
                    (db_name,)
                )
                
                if not admin_cursor.fetchone():
                    admin_cursor.execute(f'CREATE DATABASE "{db_name}"')
                    print(f"✅ Created database: {db_name}")
                else:
                    print(f"✅ Database already exists: {db_name}")
                    
            admin_cursor.close()
            admin_conn.close()
            
            self.results['database_setup'] = {
                'status': 'success',
                'databases_created': databases
            }
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to create test databases: {e}")
            self.results['database_setup'] = {
                'status': 'failed',
                'error': str(e)
            }
            return False
            
    def run_database_migrations(self):
        """Run database migrations"""
        print("🔄 Running database migrations...")
        
        try:
            # Run Alembic migrations
            migration_cmd = ["alembic", "upgrade", "head"]
            result = subprocess.run(
                migration_cmd, 
                capture_output=True, 
                text=True, 
                timeout=60,
                env=os.environ
            )
            
            if result.returncode == 0:
                print("✅ Database migrations completed")
                return True
            else:
                print(f"❌ Migration failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ Migration timed out")
            return False
        except Exception as e:
            print(f"❌ Migration error: {e}")
            return False
            
    def run_soft_delete_tests(self):
        """Run soft delete functionality tests"""
        print("🗑️ Running soft delete tests...")
        
        try:
            cmd = [
                "python", "-m", "pytest",
                "tests/integration/test_postgresql_soft_delete.py",
                "-v", "--tb=short",
                "--confcutdir=tests",
                "--junit-xml=test-reports/soft-delete-tests.xml",
                "--html=test-reports/soft-delete-tests.html",
                "--self-contained-html"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            self.results['soft_delete_tests'] = {
                'status': 'passed' if result.returncode == 0 else 'failed',
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
            if result.returncode == 0:
                print("✅ Soft delete tests passed")
            else:
                print("❌ Soft delete tests failed")
                
            return result.returncode == 0
            
        except subprocess.TimeoutExpired:
            print("❌ Soft delete tests timed out")
            self.results['soft_delete_tests'] = {'status': 'timeout'}
            return False
        except Exception as e:
            print(f"❌ Soft delete tests error: {e}")
            self.results['soft_delete_tests'] = {'status': 'error', 'error': str(e)}
            return False
            
    def run_api_integration_tests(self):
        """Run API integration tests"""
        print("🔗 Running API integration tests...")
        
        try:
            cmd = [
                "python", "-m", "pytest",
                "tests/integration/test_postgresql_api_integration.py",
                "-v", "--tb=short",
                "--confcutdir=tests",
                "--junit-xml=test-reports/api-integration-tests.xml",
                "--html=test-reports/api-integration-tests.html",
                "--self-contained-html"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            
            self.results['api_integration_tests'] = {
                'status': 'passed' if result.returncode == 0 else 'failed',
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
            if result.returncode == 0:
                print("✅ API integration tests passed")
            else:
                print("❌ API integration tests failed")
                
            return result.returncode == 0
            
        except subprocess.TimeoutExpired:
            print("❌ API integration tests timed out")
            self.results['api_integration_tests'] = {'status': 'timeout'}
            return False
        except Exception as e:
            print(f"❌ API integration tests error: {e}")
            self.results['api_integration_tests'] = {'status': 'error', 'error': str(e)}
            return False
            
    def run_performance_tests(self):
        """Run performance tests"""
        print("⚡ Running performance tests...")
        
        try:
            cmd = [
                "python", "-m", "pytest",
                "tests/integration/test_postgresql_api_integration.py::TestPerformanceIntegration",
                "-v", "--tb=short",
                "--confcutdir=tests",
                "--junit-xml=test-reports/performance-tests.xml"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            self.results['performance_tests'] = {
                'status': 'passed' if result.returncode == 0 else 'failed',
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
            if result.returncode == 0:
                print("✅ Performance tests passed")
            else:
                print("❌ Performance tests failed")
                
            return result.returncode == 0
            
        except Exception as e:
            print(f"❌ Performance tests error: {e}")
            self.results['performance_tests'] = {'status': 'error', 'error': str(e)}
            return False
            
    def cleanup_containers(self):
        """Cleanup test containers"""
        print("🧹 Cleaning up test containers...")
        
        try:
            # Stop and remove test container
            cleanup_cmd = ["docker", "rm", "-f", "regulationguru-test-db"]
            subprocess.run(cleanup_cmd, capture_output=True)
            print("✅ Test containers cleaned up")
            
        except Exception as e:
            print(f"⚠️ Cleanup warning: {e}")
            
    def generate_summary(self):
        """Generate test summary"""
        total_test_suites = 0
        passed_test_suites = 0
        failed_test_suites = 0
        
        test_categories = ['soft_delete_tests', 'api_integration_tests', 'performance_tests']
        
        for category in test_categories:
            if category in self.results:
                total_test_suites += 1
                status = self.results[category].get('status', 'unknown')
                if status == 'passed':
                    passed_test_suites += 1
                elif status in ['failed', 'error', 'timeout']:
                    failed_test_suites += 1
                    
        self.results['summary'] = {
            'total_test_suites': total_test_suites,
            'passed_suites': passed_test_suites,
            'failed_suites': failed_test_suites,
            'success_rate': (passed_test_suites / total_test_suites * 100) if total_test_suites > 0 else 0,
            'overall_status': 'PASSED' if failed_test_suites == 0 else 'FAILED'
        }
        
    def save_results(self):
        """Save test results"""
        os.makedirs('test-reports', exist_ok=True)
        with open('test-reports/postgresql-integration-results.json', 'w') as f:
            json.dump(self.results, f, indent=2)
            
    def print_summary(self):
        """Print test summary"""
        summary = self.results['summary']
        print("\n" + "="*70)
        print("🎯 POSTGRESQL INTEGRATION TEST RESULTS SUMMARY")
        print("="*70)
        print(f"📊 Total Test Suites: {summary['total_test_suites']}")
        print(f"✅ Passed Suites: {summary['passed_suites']}")
        print(f"❌ Failed Suites: {summary['failed_suites']}")
        print(f"📈 Success Rate: {summary['success_rate']:.1f}%")
        print(f"🏆 Overall Status: {summary['overall_status']}")
        
        print("\n📝 Detailed Results:")
        test_categories = {
            'database_setup': 'Database Setup',
            'soft_delete_tests': 'Soft Delete Tests',
            'api_integration_tests': 'API Integration Tests',
            'performance_tests': 'Performance Tests'
        }
        
        for category, name in test_categories.items():
            if category in self.results:
                status = self.results[category].get('status', 'unknown')
                emoji = "✅" if status == 'passed' or status == 'success' else "❌" if status in ['failed', 'error'] else "⏭️"
                print(f"  {emoji} {name}: {status.upper()}")
                
        print("="*70)

def main():
    """Main test runner function"""
    print("🚀 Starting PostgreSQL Integration Testing Suite")
    print("="*70)
    
    runner = PostgreSQLTestRunner()
    
    try:
        # Setup
        runner.setup_environment()
        
        if not runner.start_postgresql_container():
            print("❌ Failed to start PostgreSQL container")
            return 1
            
        if not runner.create_test_databases():
            print("❌ Failed to create test databases")
            return 1
            
        # Note: Skipping migrations for now as they may need to be set up
        # runner.run_database_migrations()
        
        # Run tests
        os.makedirs('test-reports', exist_ok=True)
        
        runner.run_soft_delete_tests()
        runner.run_api_integration_tests()
        runner.run_performance_tests()
        
        # Generate results
        runner.generate_summary()
        runner.save_results()
        runner.print_summary()
        
        # Return appropriate exit code
        return 0 if runner.results['summary']['overall_status'] == 'PASSED' else 1
        
    except KeyboardInterrupt:
        print("\n⚠️ Testing interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1
    finally:
        # Always cleanup
        runner.cleanup_containers()

if __name__ == "__main__":
    sys.exit(main())
