version: '3.8'

services:


  # PostgreSQL Database
  db:
    image: postgres:14-alpine
    container_name: regulationguru-db
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      POSTGRES_DB: ${POSTGRES_DB:-regulationguru}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - regulationguru
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and rate limiting
  redis:
    image: redis:7-alpine
    container_name: regulationguru-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
    networks:
      - regulationguru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Main API Application
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: regulationguru-api
    restart: unless-stopped
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@db:5432/${POSTGRES_DB:-regulationguru}
      REDIS_URL: redis://redis:6379/0
      SECRET_KEY: ${SECRET_KEY:-your-secret-key-here}
      ENVIRONMENT: ${ENVIRONMENT:-development}
      LOG_LEVEL: ${LOG_LEVEL:-info}
      ALLOWED_ORIGINS: ${ALLOWED_ORIGINS:-http://api.guru.localhost,http://docs.guru.localhost,http://admin.guru.localhost}
      GEMINI_API_KEY: ${GEMINI_API_KEY:-}
      GEMINI_API_RATE_LIMIT: 14
    volumes:
      - ./app:/app/app
      - ./templates:/app/templates
      - ./statics:/app/statics
      - ./docs:/app/docs
    networks:
      - traefik
      - regulationguru
    labels:
      # Enable Traefik
      - "traefik.enable=true"
      # API service routing
      - "traefik.http.routers.api.rule=Host(`api.guru.localhost`)"
      - "traefik.http.routers.api.service=api"
      - "traefik.http.services.api.loadbalancer.server.port=8000"
      # Health check configuration
      - "traefik.http.services.api.loadbalancer.healthcheck.path=/api/v1/health"
      - "traefik.http.services.api.loadbalancer.healthcheck.interval=30s"
      - "traefik.http.services.api.loadbalancer.healthcheck.timeout=10s"
      # Middleware configuration
      - "traefik.http.routers.api.middlewares=api-cors,api-ratelimit,api-headers"
      # CORS middleware
      - "traefik.http.middlewares.api-cors.headers.accesscontrolallowmethods=GET,POST,PUT,DELETE,OPTIONS,PATCH"
      - "traefik.http.middlewares.api-cors.headers.accesscontrolalloworiginlist=http://docs.guru.localhost,http://admin.guru.localhost"
      - "traefik.http.middlewares.api-cors.headers.accesscontrolallowheaders=Content-Type,Authorization,X-Requested-With"
      - "traefik.http.middlewares.api-cors.headers.accesscontrolallowcredentials=true"
      # Rate limiting middleware
      - "traefik.http.middlewares.api-ratelimit.ratelimit.burst=100"
      - "traefik.http.middlewares.api-ratelimit.ratelimit.average=50"
      # Security headers middleware
      - "traefik.http.middlewares.api-headers.headers.frameDeny=true"
      - "traefik.http.middlewares.api-headers.headers.browserXssFilter=true"
      - "traefik.http.middlewares.api-headers.headers.contentTypeNosniff=true"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Documentation Service (separate container for docs.guru.localhost)
  docs:
    image: nginx:alpine
    container_name: regulationguru-docs
    restart: unless-stopped
    volumes:
      - ./docs/_build/html:/usr/share/nginx/html:ro
      - ./nginx/docs.conf:/etc/nginx/conf.d/default.conf:ro
    networks:
      - traefik
    labels:
      # Enable Traefik
      - "traefik.enable=true"
      # Documentation service routing
      - "traefik.http.routers.docs.rule=Host(`docs.guru.localhost`)"
      - "traefik.http.routers.docs.service=docs"
      - "traefik.http.services.docs.loadbalancer.server.port=80"
      # Health check configuration
      - "traefik.http.services.docs.loadbalancer.healthcheck.path=/health"
      - "traefik.http.services.docs.loadbalancer.healthcheck.interval=30s"
      # Middleware configuration
      - "traefik.http.routers.docs.middlewares=docs-headers,docs-compress"
      # Compression middleware
      - "traefik.http.middlewares.docs-compress.compress=true"
      # Security headers for docs
      - "traefik.http.middlewares.docs-headers.headers.frameDeny=true"
      - "traefik.http.middlewares.docs-headers.headers.browserXssFilter=true"
      - "traefik.http.middlewares.docs-headers.headers.contentTypeNosniff=true"
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Admin Interface (separate container for admin.guru.localhost)
  admin:
    build:
      context: .
      dockerfile: Dockerfile.admin
    container_name: regulationguru-admin
    restart: unless-stopped
    depends_on:
      db:
        condition: service_healthy
    environment:
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres}@db:5432/${POSTGRES_DB:-regulationguru}
      SECRET_KEY: ${SECRET_KEY:-your-secret-key-here}
      ADMIN_USER: ${ADMIN_USER:-admin}
      ADMIN_PASSWORD: ${ADMIN_PASSWORD:-admin}
    networks:
      - traefik
      - regulationguru
    labels:
      # Enable Traefik
      - "traefik.enable=true"
      # Admin service routing
      - "traefik.http.routers.admin.rule=Host(`admin.guru.localhost`)"
      - "traefik.http.routers.admin.service=admin"
      - "traefik.http.services.admin.loadbalancer.server.port=8001"
      # Health check configuration
      - "traefik.http.services.admin.loadbalancer.healthcheck.path=/health"
      - "traefik.http.services.admin.loadbalancer.healthcheck.interval=30s"
      # Middleware configuration
      - "traefik.http.routers.admin.middlewares=admin-auth,admin-headers"
      # Basic auth middleware (for development)
      - "traefik.http.middlewares.admin-auth.basicauth.users=admin:$$2y$$10$$2b2cu/0S6PDcuCHCN6kuAOyxLgHPkdpKyBpKdAqvQqNFHsRHjcLI6"
      # Security headers for admin
      - "traefik.http.middlewares.admin-headers.headers.frameDeny=true"
      - "traefik.http.middlewares.admin-headers.headers.browserXssFilter=true"
      - "traefik.http.middlewares.admin-headers.headers.contentTypeNosniff=true"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  # External network for host Traefik
  traefik:
    external: true
    name: traefik-network

  # Internal network for backend services
  regulationguru:
    name: regulationguru
    driver: bridge

volumes:
  # PostgreSQL data volume
  postgres_data:
    name: regulationguru_postgres_data
    driver: local

  # Redis data volume
  redis_data:
    name: regulationguru_redis_data
    driver: local
