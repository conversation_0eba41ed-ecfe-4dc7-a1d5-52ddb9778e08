{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    # Python and core dependencies
    python311
    python311Packages.pip
    python311Packages.pytest
    python311Packages.pytest-cov
    python311Packages.pytest-xdist
    python311Packages.fastapi
    python311Packages.uvicorn
    python311Packages.httpx
    python311Packages.requests
    python311Packages.pydantic
    python311Packages.sqlalchemy
    python311Packages.psycopg2
    python311Packages.python-dotenv
    python311Packages.beautifulsoup4
    python311Packages.pandas
    python311Packages.jinja2
    python311Packages.babel
    python311Packages.spacy

    # Database
    postgresql_14

    # Browser automation for UI testing
    playwright
    playwright-driver
    geckodriver

    # Node.js for frontend tests
    nodejs_18
    yarn

    # System dependencies
    git
    libpq
    gcc
    gnumake

    # Additional tools
    psutil

    # Replit dependencies
    glibcLocales
    libxcrypt
  ];

  shellHook = ''
    echo "RegulationGuru development environment activated"
    echo "Python version: $(python --version)"
    echo "Node version: $(node --version)"
    echo "Playwright version: $(playwright --version 2>/dev/null || echo 'Not installed')"

    # Create virtual environment if it doesn't exist
    if [ ! -d ".venv" ]; then
      echo "Creating virtual environment..."
      python -m venv .venv
    fi

    # Activate virtual environment
    source .venv/bin/activate

    # Install Python dependencies if needed
    if [ ! -f ".venv/.dependencies-installed" ]; then
      echo "Installing Python dependencies..."
      pip install -r requirements.txt
      pip install fastapi sqlalchemy pytest pytest-cov pytest-xdist httpx spacy
      python -m spacy download en_core_web_sm
      touch .venv/.dependencies-installed
    fi

    # Install Playwright browsers if needed
    if [ ! -f ".venv/.playwright-installed" ]; then
      echo "Installing Playwright browsers..."
      python -m playwright install
      touch .venv/.playwright-installed
    fi

    # Set environment variables
    export PYTHONPATH=$PWD:$PYTHONPATH
    export DATABASE_URL="sqlite:///:memory:"
    export TESTING=True

    # Create test database if using PostgreSQL
    if [[ "$DATABASE_URL" == postgresql* ]]; then
      echo "Setting up PostgreSQL test database..."
      createdb -h localhost -U postgres regulationguru_test || echo "Database may already exist"
      export DATABASE_URL="postgresql://postgres:postgres@localhost:5432/regulationguru_test"
    fi

    echo "Integration test environment ready!"
    echo "To run integration tests, use one of the following commands:"
    echo "  python -m pytest tests/test_*integration*.py -v"
    echo "  python run_regulatory_tests.py -v"
    echo "  ./scripts/run_integration_tests.sh"
  '';
}
