# Core FastAPI dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
alembic==1.13.1

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Templates and static files
jinja2==3.1.2
aiofiles==23.2.1

# HTTP client
httpx==0.25.2
requests==2.31.0
aiohttp==3.9.1

# Data validation
pydantic==2.5.0
pydantic-settings==2.1.0

# Date/time handling
python-dateutil==2.8.2

# Environment variables
python-dotenv==1.0.0

# Basic utilities
itsdangerous==2.1.2
babel==2.14.0

# Admin interface
starlette-admin==0.13.2

# Monitoring
prometheus-client==0.19.0

# Data processing
pandas==2.1.4
numpy==1.25.2

# Web scraping
beautifulsoup4==4.12.2
lxml==4.9.3

# Visualization
plotly==5.17.0
folium==0.15.0

# PDF processing
reportlab==4.0.7
PyPDF2==3.0.1
pypdf==3.17.4

# NLP and text processing
spacy==3.7.2

# AI/ML
openai==1.3.8

# Network analysis
networkx==3.2.1

# Calendar processing
ics==0.7.2

# Image processing
Pillow==10.1.0

# Excel processing
openpyxl==3.1.2

# Email
email-validator==2.1.0

# Caching
redis==5.0.1

# Background tasks
celery==5.3.4

# File handling
pathlib2==2.3.7

# UUID
uuid==1.30

# Logging
structlog==23.2.0

# Configuration
pyyaml==6.0.1

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1

# Development
black==23.11.0
isort==5.12.0
