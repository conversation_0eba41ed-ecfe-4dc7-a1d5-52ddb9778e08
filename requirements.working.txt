# Core FastAPI dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
alembic==1.13.1

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Templates and static files
jinja2==3.1.2
aiofiles==23.2.1

# HTTP client
httpx==0.25.2
requests==2.31.0

# Data validation
pydantic==2.5.0
pydantic-settings==2.1.0

# Date/time handling
python-dateutil==2.8.2

# Environment variables
python-dotenv==1.0.0

# CORS
fastapi-cors==0.0.6

# Basic utilities
itsdangerous==2.1.2
babel==2.14.0

# Admin interface
starlette-admin==0.13.2

# Monitoring
prometheus-client==0.19.0

# Basic data processing (minimal versions)
pandas==2.1.4
numpy==1.25.2

# Web scraping (minimal)
beautifulsoup4==4.12.2
lxml==4.9.3

# Visualization (minimal)
plotly==5.17.0
folium==0.15.0

# PDF processing (minimal)
reportlab==4.0.7

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
