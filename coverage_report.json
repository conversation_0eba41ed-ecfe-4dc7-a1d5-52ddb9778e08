{"modules": {"schemas.py": 100, "app/__init__.py": 100, "app/admin/__init__.py": 100, "app/api/__init__.py": 100, "app/core/__init__.py": 100, "app/schemas/__init__.py": 100, "app/schemas/schemas.py": 100, "app/utils/__init__.py": 100, "app/visualization/__init__.py": 100, "app/admin/admin.py": 98, "models.py": 96, "app/db/models.py": 96, "database.py": 71, "main.py": 67, "app/db/database.py": 67, "app/i18n/__init__.py": 64, "regulations.py": 45, "app/visualization/worldmap.py": 43, "app/i18n/i18n.py": 37, "app/main.py": 36, "app/db/__init__.py": 33, "translate.py": 25, "scripts/translate.py": 25, "scripts/migrate.py": 24, "url_processor.py": 18, "generate_coverage.py": 0, "i18n.py": 0, "migrate.py": 0, "worldmap.py": 0, "analyze_confidence.py": 0, "analyze_regulatory_results.py": 0, "analyze_test_coverage.py": 0, "import_urls.py": 0, "run_tests_with_coverage.py": 0, "admin.py": 0, "app/api/regulations.py": 0, "app/utils/url_processor.py": 0, "restructure.py": 0, "run.py": 0, "update_imports.py": 0}, "summary": {"total_modules": 40, "high_coverage": 12, "medium_coverage": 4, "low_coverage": 24, "overall_percentage": 43.6}}