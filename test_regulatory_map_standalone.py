#!/usr/bin/env python3
"""Standalone unit tests for Enhanced Regulatory Map models."""
import sys
import os
sys.path.append('.')

from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.db.models import (
    Base, RegulatoryEntity, RegulatoryRelationship, RegulatoryComplianceStatus,
    RegulatoryView, RegulatoryAnnotation, EntityType, RelationshipType,
    ComplianceStatusEnum, ImplementationStatusEnum
)

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_regulatory_map_standalone.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def setup_database():
    """Set up test database."""
    Base.metadata.create_all(bind=engine)

def cleanup_database():
    """Clean up test database."""
    Base.metadata.drop_all(bind=engine)
    if os.path.exists("./test_regulatory_map_standalone.db"):
        os.remove("./test_regulatory_map_standalone.db")

def test_create_regulatory_entity():
    """Test creating a regulatory entity."""
    print("Testing regulatory entity creation...")
    
    db = TestingSessionLocal()
    try:
        entity = RegulatoryEntity(
            name="Test Regulation",
            description="A test regulation",
            type=EntityType.REGULATION,
            jurisdiction="US",
            status="active",
            version="1.0"
        )
        
        db.add(entity)
        db.commit()
        db.refresh(entity)
        
        assert entity.id is not None
        assert entity.name == "Test Regulation"
        assert entity.type == EntityType.REGULATION
        assert entity.jurisdiction == "US"
        assert entity.status == "active"
        assert entity.version == "1.0"
        assert entity.created_at is not None
        assert entity.is_deleted == False
        
        print("✓ Regulatory entity creation test passed")
        return entity
    finally:
        db.close()

def test_entity_hierarchy():
    """Test parent-child relationships in entities."""
    print("Testing entity hierarchy...")
    
    db = TestingSessionLocal()
    try:
        # Create parent entity
        parent = RegulatoryEntity(
            name="Parent Regulation",
            type=EntityType.REGULATION,
            jurisdiction="US"
        )
        db.add(parent)
        db.commit()
        db.refresh(parent)
        
        # Create child entity
        child = RegulatoryEntity(
            name="Child Requirement",
            type=EntityType.REQUIREMENT,
            parent_id=parent.id,
            jurisdiction="US"
        )
        db.add(child)
        db.commit()
        db.refresh(child)
        
        # Test relationships
        assert child.parent_id == parent.id
        assert child.parent == parent
        assert child in parent.children
        
        print("✓ Entity hierarchy test passed")
        return parent, child
    finally:
        db.close()

def test_regulatory_relationship():
    """Test creating a regulatory relationship."""
    print("Testing regulatory relationship...")
    
    db = TestingSessionLocal()
    try:
        # Create source and target entities
        source = RegulatoryEntity(
            name="Source Entity",
            type=EntityType.REGULATION,
            jurisdiction="US"
        )
        target = RegulatoryEntity(
            name="Target Entity",
            type=EntityType.REQUIREMENT,
            jurisdiction="US"
        )
        
        db.add(source)
        db.add(target)
        db.commit()
        db.refresh(source)
        db.refresh(target)
        
        # Create relationship
        relationship = RegulatoryRelationship(
            source_id=source.id,
            target_id=target.id,
            relationship_type=RelationshipType.CONTAINS,
            strength=0.8,
            description="Source contains target"
        )
        
        db.add(relationship)
        db.commit()
        db.refresh(relationship)
        
        assert relationship.id is not None
        assert relationship.source_id == source.id
        assert relationship.target_id == target.id
        assert relationship.relationship_type == RelationshipType.CONTAINS
        assert relationship.strength == 0.8
        assert relationship.source_entity == source
        assert relationship.target_entity == target
        
        print("✓ Regulatory relationship test passed")
        return relationship
    finally:
        db.close()

def test_compliance_status():
    """Test creating a compliance status record."""
    print("Testing compliance status...")
    
    db = TestingSessionLocal()
    try:
        # Create entity
        entity = RegulatoryEntity(
            name="Test Entity",
            type=EntityType.REGULATION,
            jurisdiction="US"
        )
        db.add(entity)
        db.commit()
        db.refresh(entity)
        
        # Create compliance status
        status = RegulatoryComplianceStatus(
            entity_id=entity.id,
            compliance_status=ComplianceStatusEnum.COMPLIANT,
            implementation_status=ImplementationStatusEnum.IMPLEMENTED,
            compliance_score=90.0,
            risk_score=10.0,
            assessment_date=datetime.utcnow(),
            assessed_by="Test Assessor",
            notes="Test compliance assessment"
        )
        
        db.add(status)
        db.commit()
        db.refresh(status)
        
        assert status.id is not None
        assert status.entity_id == entity.id
        assert status.compliance_status == ComplianceStatusEnum.COMPLIANT
        assert status.implementation_status == ImplementationStatusEnum.IMPLEMENTED
        assert status.compliance_score == 90.0
        assert status.risk_score == 10.0
        assert status.assessed_by == "Test Assessor"
        assert status.entity == entity
        
        print("✓ Compliance status test passed")
        return status
    finally:
        db.close()

def test_regulatory_view():
    """Test creating a regulatory view."""
    print("Testing regulatory view...")
    
    db = TestingSessionLocal()
    try:
        view = RegulatoryView(
            name="Test View",
            description="A test view",
            user_id="test-user-123",
            is_public=False,
            is_default=True,
            view_state={
                "filters": {"jurisdiction": "US"},
                "layout": "grid",
                "zoom": 1.0
            }
        )
        
        db.add(view)
        db.commit()
        db.refresh(view)
        
        assert view.id is not None
        assert view.name == "Test View"
        assert view.user_id == "test-user-123"
        assert view.is_public == False
        assert view.is_default == True
        assert view.view_state["filters"]["jurisdiction"] == "US"
        
        print("✓ Regulatory view test passed")
        return view
    finally:
        db.close()

def test_regulatory_annotation():
    """Test creating a regulatory annotation."""
    print("Testing regulatory annotation...")
    
    db = TestingSessionLocal()
    try:
        # Create entity
        entity = RegulatoryEntity(
            name="Test Entity",
            type=EntityType.REGULATION,
            jurisdiction="US"
        )
        db.add(entity)
        db.commit()
        db.refresh(entity)
        
        # Create annotation
        annotation = RegulatoryAnnotation(
            entity_id=entity.id,
            title="Test Annotation",
            content="This is a test annotation",
            annotation_type="interpretation",
            user_id="test-user-123",
            visibility="private"
        )
        
        db.add(annotation)
        db.commit()
        db.refresh(annotation)
        
        assert annotation.id is not None
        assert annotation.entity_id == entity.id
        assert annotation.title == "Test Annotation"
        assert annotation.content == "This is a test annotation"
        assert annotation.annotation_type == "interpretation"
        assert annotation.user_id == "test-user-123"
        assert annotation.visibility == "private"
        assert annotation.entity == entity
        
        print("✓ Regulatory annotation test passed")
        return annotation
    finally:
        db.close()

def test_soft_delete():
    """Test soft delete functionality."""
    print("Testing soft delete...")
    
    db = TestingSessionLocal()
    try:
        entity = RegulatoryEntity(
            name="Test Delete Entity",
            type=EntityType.CONTROL,
            jurisdiction="US"
        )
        
        db.add(entity)
        db.commit()
        db.refresh(entity)
        
        # Verify entity exists
        assert entity.is_deleted == False
        assert entity.deleted_at is None
        
        # Soft delete the entity
        entity.soft_delete()
        db.commit()
        
        # Verify soft delete
        assert entity.is_deleted == True
        assert entity.deleted_at is not None
        
        print("✓ Soft delete test passed")
        return entity
    finally:
        db.close()

def test_enum_values():
    """Test enum values."""
    print("Testing enum values...")
    
    # Test EntityType enum
    assert EntityType.REGULATION == "regulation"
    assert EntityType.REQUIREMENT == "requirement"
    assert EntityType.CONTROL == "control"
    assert EntityType.BUSINESS_PROCESS == "business_process"
    assert EntityType.SYSTEM == "system"
    assert EntityType.POLICY == "policy"
    assert EntityType.PROCEDURE == "procedure"
    
    # Test RelationshipType enum
    assert RelationshipType.CONTAINS == "contains"
    assert RelationshipType.REFERENCES == "references"
    assert RelationshipType.IMPLEMENTS == "implements"
    assert RelationshipType.IMPACTS == "impacts"
    assert RelationshipType.USES == "uses"
    assert RelationshipType.CONFLICTS == "conflicts"
    assert RelationshipType.SUPERSEDES == "supersedes"
    assert RelationshipType.DEPENDS_ON == "depends_on"
    assert RelationshipType.DERIVES_FROM == "derives_from"
    
    # Test ComplianceStatusEnum
    assert ComplianceStatusEnum.COMPLIANT == "compliant"
    assert ComplianceStatusEnum.AT_RISK == "at_risk"
    assert ComplianceStatusEnum.NON_COMPLIANT == "non_compliant"
    assert ComplianceStatusEnum.UNKNOWN == "unknown"
    assert ComplianceStatusEnum.NOT_APPLICABLE == "not_applicable"
    
    # Test ImplementationStatusEnum
    assert ImplementationStatusEnum.NOT_STARTED == "not_started"
    assert ImplementationStatusEnum.IN_PROGRESS == "in_progress"
    assert ImplementationStatusEnum.IMPLEMENTED == "implemented"
    assert ImplementationStatusEnum.VERIFIED == "verified"
    assert ImplementationStatusEnum.DECOMMISSIONED == "decommissioned"
    
    print("✓ Enum values test passed")

def main():
    """Run all tests."""
    print("🚀 Starting Enhanced Regulatory Map Tests")
    print("=" * 50)
    
    try:
        # Setup
        setup_database()
        
        # Run tests
        test_enum_values()
        test_create_regulatory_entity()
        test_entity_hierarchy()
        test_regulatory_relationship()
        test_compliance_status()
        test_regulatory_view()
        test_regulatory_annotation()
        test_soft_delete()
        
        print("=" * 50)
        print("🎉 All tests passed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        # Cleanup
        cleanup_database()
    
    return 0

if __name__ == "__main__":
    exit(main())
