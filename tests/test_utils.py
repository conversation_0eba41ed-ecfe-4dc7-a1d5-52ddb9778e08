"""
Tests for utility functions.
"""
import pytest
from unittest.mock import MagicMock, patch
import datetime

from app.utils.date_utils import format_date, parse_date, get_date_range
from app.utils.string_utils import sanitize_string, truncate_string, slugify
from app.utils.validation_utils import validate_email, validate_url, validate_phone
from app.utils.pagination_utils import paginate_query, get_pagination_params
from app.utils.gemini_api import GeminiAPI

def test_format_date():
    """Test the format_date function."""
    # Test with datetime object
    date = datetime.datetime(2023, 1, 1, 12, 0, 0)
    assert format_date(date) == "2023-01-01"
    assert format_date(date, format="%Y/%m/%d") == "2023/01/01"
    assert format_date(date, format="%B %d, %Y") == "January 01, 2023"
    
    # Test with date object
    date = datetime.date(2023, 1, 1)
    assert format_date(date) == "2023-01-01"
    
    # Test with string
    assert format_date("2023-01-01") == "2023-01-01"
    
    # Test with None
    assert format_date(None) == ""
    assert format_date(None, default="N/A") == "N/A"

def test_parse_date():
    """Test the parse_date function."""
    # Test with ISO format
    assert parse_date("2023-01-01") == datetime.date(2023, 1, 1)
    
    # Test with custom format
    assert parse_date("01/01/2023", format="%m/%d/%Y") == datetime.date(2023, 1, 1)
    
    # Test with datetime string
    assert parse_date("2023-01-01T12:00:00") == datetime.date(2023, 1, 1)
    
    # Test with invalid string
    with pytest.raises(ValueError):
        parse_date("invalid")
    
    # Test with None
    assert parse_date(None) is None

def test_get_date_range():
    """Test the get_date_range function."""
    # Test with start and end dates
    start_date = datetime.date(2023, 1, 1)
    end_date = datetime.date(2023, 1, 5)
    date_range = get_date_range(start_date, end_date)
    assert len(date_range) == 5
    assert date_range[0] == start_date
    assert date_range[-1] == end_date
    
    # Test with days parameter
    start_date = datetime.date(2023, 1, 1)
    date_range = get_date_range(start_date, days=5)
    assert len(date_range) == 5
    assert date_range[0] == start_date
    assert date_range[-1] == datetime.date(2023, 1, 5)
    
    # Test with negative days parameter
    start_date = datetime.date(2023, 1, 5)
    date_range = get_date_range(start_date, days=-5)
    assert len(date_range) == 5
    assert date_range[0] == datetime.date(2023, 1, 1)
    assert date_range[-1] == start_date

def test_sanitize_string():
    """Test the sanitize_string function."""
    # Test with normal string
    assert sanitize_string("Hello, World!") == "Hello, World!"
    
    # Test with HTML
    assert sanitize_string("<script>alert('XSS')</script>") == "alert('XSS')"
    
    # Test with None
    assert sanitize_string(None) == ""
    
    # Test with empty string
    assert sanitize_string("") == ""
    
    # Test with whitespace
    assert sanitize_string("  Hello  ") == "Hello"

def test_truncate_string():
    """Test the truncate_string function."""
    # Test with short string
    assert truncate_string("Hello", 10) == "Hello"
    
    # Test with long string
    assert truncate_string("Hello, World!", 5) == "Hello..."
    
    # Test with custom suffix
    assert truncate_string("Hello, World!", 5, suffix="...more") == "Hello...more"
    
    # Test with None
    assert truncate_string(None, 10) == ""
    
    # Test with empty string
    assert truncate_string("", 10) == ""

def test_slugify():
    """Test the slugify function."""
    # Test with normal string
    assert slugify("Hello, World!") == "hello-world"
    
    # Test with special characters
    assert slugify("Hello, World! & Special Characters") == "hello-world-special-characters"
    
    # Test with non-ASCII characters
    assert slugify("Héllö Wörld") == "hello-world"
    
    # Test with None
    assert slugify(None) == ""
    
    # Test with empty string
    assert slugify("") == ""

def test_validate_email():
    """Test the validate_email function."""
    # Test with valid email
    assert validate_email("<EMAIL>") is True
    
    # Test with invalid email
    assert validate_email("invalid") is False
    assert validate_email("invalid@") is False
    assert validate_email("@invalid") is False
    
    # Test with None
    assert validate_email(None) is False
    
    # Test with empty string
    assert validate_email("") is False

def test_validate_url():
    """Test the validate_url function."""
    # Test with valid URL
    assert validate_url("https://example.com") is True
    assert validate_url("http://example.com") is True
    
    # Test with invalid URL
    assert validate_url("invalid") is False
    assert validate_url("example.com") is False
    
    # Test with None
    assert validate_url(None) is False
    
    # Test with empty string
    assert validate_url("") is False

def test_validate_phone():
    """Test the validate_phone function."""
    # Test with valid phone number
    assert validate_phone("******-456-7890") is True
    assert validate_phone("************") is True
    assert validate_phone("(*************") is True
    
    # Test with invalid phone number
    assert validate_phone("invalid") is False
    assert validate_phone("123") is False
    
    # Test with None
    assert validate_phone(None) is False
    
    # Test with empty string
    assert validate_phone("") is False

def test_paginate_query():
    """Test the paginate_query function."""
    # Create a mock query
    mock_query = MagicMock()
    mock_query.offset.return_value = mock_query
    mock_query.limit.return_value = mock_query
    
    # Test with default parameters
    paginate_query(mock_query)
    mock_query.offset.assert_called_with(0)
    mock_query.limit.assert_called_with(100)
    
    # Test with custom parameters
    paginate_query(mock_query, skip=10, limit=50)
    mock_query.offset.assert_called_with(10)
    mock_query.limit.assert_called_with(50)
    
    # Test with invalid parameters
    paginate_query(mock_query, skip=-10, limit=0)
    mock_query.offset.assert_called_with(0)
    mock_query.limit.assert_called_with(100)

def test_get_pagination_params():
    """Test the get_pagination_params function."""
    # Test with default parameters
    skip, limit = get_pagination_params()
    assert skip == 0
    assert limit == 100
    
    # Test with custom parameters
    skip, limit = get_pagination_params(skip=10, limit=50)
    assert skip == 10
    assert limit == 50
    
    # Test with invalid parameters
    skip, limit = get_pagination_params(skip=-10, limit=0)
    assert skip == 0
    assert limit == 100
    
    # Test with limit exceeding maximum
    skip, limit = get_pagination_params(skip=10, limit=2000)
    assert skip == 10
    assert limit == 1000

def test_gemini_api():
    """Test the GeminiAPI class."""
    # Create a mock response
    mock_response = MagicMock()
    mock_response.json.return_value = {
        "candidates": [
            {
                "content": {
                    "parts": [
                        {
                            "text": "This is a test response"
                        }
                    ]
                }
            }
        ]
    }
    
    # Mock the requests.post function
    with patch("requests.post", return_value=mock_response):
        # Create the GeminiAPI instance
        api = GeminiAPI(api_key="test_key", rate_limit=14)
        
        # Test the generate_content method
        response = api.generate_content("This is a test prompt")
        assert response == "This is a test response"
        
        # Test rate limiting
        api.request_count = 14
        api.last_request_time = datetime.datetime.now()
        
        # Mock the time.sleep function
        with patch("time.sleep") as mock_sleep:
            api.generate_content("This is another test prompt")
            mock_sleep.assert_called_once()
