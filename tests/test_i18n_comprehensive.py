
import pytest
import os
from unittest.mock import patch, MagicMock
from app.i18n.i18n import (
    get_translator,
    get_locale,
    set_locale,
    get_available_languages,
    get_language_name
)

class TestI18nComprehensive:
    def test_get_locale(self):
        """Test getting the current locale."""
        # Should return a string
        locale = get_locale()
        assert isinstance(locale, str)
        # Default locale should be 'en' or similar
        assert locale.startswith('en') or locale == 'en'
    
    @patch('app.i18n.i18n.session')
    def test_set_locale(self, mock_session):
        """Test setting the locale."""
        # Setup mock for session
        mock_session.get.return_value = 'en'
        
        # Test setting a valid locale
        result = set_locale('fr')
        assert result == 'fr'
        mock_session.__setitem__.assert_called_once_with('locale', 'fr')
        
        # Reset mock
        mock_session.reset_mock()
        
        # Test setting an invalid locale
        result = set_locale('invalid-locale')
        # Should fall back to default
        assert result == 'en'
        mock_session.__setitem__.assert_called_once_with('locale', 'en')
    
    def test_get_available_languages(self):
        """Test retrieving available languages."""
        languages = get_available_languages()
        # Should have at least English
        assert 'en' in languages
        # All entries should be strings
        assert all(isinstance(lang, str) for lang in languages)
    
    def test_get_language_name(self):
        """Test getting human-readable language names."""
        # Test English
        assert get_language_name('en') == 'English'
        # Test French
        assert get_language_name('fr') == 'French'
        # Test fallback for unknown language
        assert get_language_name('xx') == 'xx'
    
    @patch('app.i18n.i18n.gettext.translation')
    def test_get_translator_with_existing_translation(self, mock_translation):
        """Test getting a translator for a language with existing translation."""
        # Setup mock
        mock_translator = MagicMock()
        mock_translation.return_value = mock_translator
        
        # Get translator for existing language
        translator = get_translator('fr')
        
        # Verify translation was attempted with correct parameters
        mock_translation.assert_called_once()
        assert translator == mock_translator
    
    @patch('app.i18n.i18n.gettext.translation')
    def test_get_translator_fallback(self, mock_translation):
        """Test fallback behavior when translation is not available."""
        # Setup mock to raise IOError (translation not found)
        mock_translation.side_effect = [IOError, MagicMock()]
        
        # Get translator with fallback
        translator = get_translator('nonexistent')
        
        # Should try first with requested locale, then with fallback
        assert mock_translation.call_count == 2
import pytest
import os
import sys
from unittest.mock import patch, MagicMock, mock_open
import builtins

# Add app directory to path if needed
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.i18n.i18n import (
    initialize_i18n,
    get_translator,
    translate_text,
    update_translations,
    detect_language,
    load_language_preferences,
    save_language_preferences,
    get_available_languages
)

@pytest.fixture
def mock_translator():
    """Fixture to create a mock translator."""
    translator = MagicMock()
    translator.gettext.side_effect = lambda text: f"Translated: {text}"
    return translator

def test_initialize_i18n():
    """Test initializing the i18n system."""
    with patch('app.i18n.i18n.gettext.translation') as mock_translation:
        # Setup mock
        mock_translator = MagicMock()
        mock_translation.return_value = mock_translator
        
        result = initialize_i18n('en')
        
        # Verify gettext.translation was called correctly
        mock_translation.assert_called_once()
        assert 'en' in str(mock_translation.call_args)
        assert result == mock_translator

def test_get_translator():
    """Test getting a translator for a specific language."""
    with patch('app.i18n.i18n.initialize_i18n') as mock_init:
        mock_translator = MagicMock()
        mock_init.return_value = mock_translator
        
        translator = get_translator('fr')
        
        # Verify initialize_i18n was called with the right language
        mock_init.assert_called_once_with('fr')
        assert translator == mock_translator

def test_translate_text(mock_translator):
    """Test text translation functionality."""
    with patch('app.i18n.i18n.get_translator', return_value=mock_translator):
        translated = translate_text('Hello world', 'de')
        
        # Verify the translator was used
        mock_translator.gettext.assert_called_once_with('Hello world')
        assert translated == "Translated: Hello world"

def test_translate_text_with_default():
    """Test translation with default language when translation fails."""
    # Create a mock translator that raises an exception
    error_translator = MagicMock()
    error_translator.gettext.side_effect = Exception("Translation failed")
    
    # Create a default translator
    default_translator = MagicMock()
    default_translator.gettext.return_value = "Default translation"
    
    with patch('app.i18n.i18n.get_translator', side_effect=[error_translator, default_translator]):
        translated = translate_text('Error text', 'invalid_lang')
        
        # Verify fallback to default translator
        assert translated == "Default translation"

def test_update_translations():
    """Test updating translation files."""
    with patch('subprocess.run') as mock_run:
        mock_process = MagicMock()
        mock_process.returncode = 0
        mock_run.return_value = mock_process
        
        result = update_translations('messages.pot', 'en')
        
        # Verify subprocess was called
        assert mock_run.call_count > 0
        assert result is True

def test_update_translations_error():
    """Test error handling in translation updates."""
    with patch('subprocess.run') as mock_run:
        mock_run.side_effect = Exception("Command failed")
        
        result = update_translations('messages.pot', 'en')
        
        # Verify subprocess was called and error was handled
        assert mock_run.call_count > 0
        assert result is False

def test_detect_language():
    """Test language detection from text."""
    with patch('langdetect.detect', return_value='es') as mock_detect:
        lang = detect_language("Hola mundo")
        
        # Verify langdetect was called
        mock_detect.assert_called_once_with("Hola mundo")
        assert lang == 'es'

def test_detect_language_error():
    """Test error handling in language detection."""
    with patch('langdetect.detect', side_effect=Exception("Detection failed")) as mock_detect:
        lang = detect_language("")
        
        # Verify fallback to default language
        mock_detect.assert_called_once_with("")
        assert lang == 'en'

def test_load_language_preferences():
    """Test loading language preferences from file."""
    mock_json_data = '{"user1": "en", "user2": "fr"}'
    
    with patch('builtins.open', mock_open(read_data=mock_json_data)):
        with patch('json.load', return_value={"user1": "en", "user2": "fr"}) as mock_json_load:
            prefs = load_language_preferences()
            
            # Verify file was opened and JSON loaded
            assert 'open' in str(builtins.open)
            mock_json_load.assert_called_once()
            assert prefs == {"user1": "en", "user2": "fr"}

def test_load_language_preferences_file_not_found():
    """Test loading language preferences when file doesn't exist."""
    with patch('builtins.open', side_effect=FileNotFoundError):
        prefs = load_language_preferences()
        
        # Verify empty dict is returned
        assert prefs == {}

def test_save_language_preferences():
    """Test saving language preferences to file."""
    prefs = {"user1": "en", "user2": "fr"}
    
    with patch('builtins.open', mock_open()) as mock_file:
        with patch('json.dump') as mock_json_dump:
            save_language_preferences(prefs)
            
            # Verify file was opened and JSON dumped
            mock_file.assert_called_once()
            mock_json_dump.assert_called_once_with(prefs, mock_file(), indent=2)

def test_get_available_languages():
    """Test getting list of available languages."""
    # Mock directory listing
    mock_dirs = ['en', 'fr', 'de', 'es']
    
    with patch('os.path.isdir', return_value=True):
        with patch('os.listdir', return_value=mock_dirs):
            languages = get_available_languages()
            
            # Verify correct languages returned
            assert set(languages) == set(mock_dirs)

def test_get_available_languages_with_filter():
    """Test filtering available languages."""
    # Mock directory listing with some non-directory items
    mock_items = ['en', 'fr', 'de', 'es', 'messages.pot', 'README.md']
    
    with patch('os.path.isdir', side_effect=lambda x: x.endswith(('en', 'fr', 'de', 'es'))):
        with patch('os.listdir', return_value=mock_items):
            languages = get_available_languages()
            
            # Verify only directories are returned
            assert set(languages) == {'en', 'fr', 'de', 'es'}
            assert 'messages.pot' not in languages
            assert 'README.md' not in languages
