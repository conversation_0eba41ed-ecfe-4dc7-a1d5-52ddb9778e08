
import pytest
from starlette.testclient import TestClient
from starlette.applications import Starlette

from app.admin.admin import get_admin_app

def test_admin_app_creation():
    """Test that the admin app is created correctly"""
    admin_app = get_admin_app()
    assert admin_app is not None
    assert isinstance(admin_app, Starlette)

def test_admin_routes():
    """Test that the admin routes are registered"""
    admin_app = get_admin_app()
    client = TestClient(admin_app)
    response = client.get("/")
    # Should redirect to /admin
    assert response.status_code == 307
    assert response.headers["location"] == "/admin"

def test_admin_views():
    """Test that the admin views are registered"""
    from app.admin.admin import admin
    # Check that we have at least the country, regulator, regulation URL, and item views
    assert len(admin._views) >= 4
