
"""
Benchmark tests for regulatory document parsing and relationship mapping.
These tests evaluate performance and accuracy of the regulatory analysis.
"""
import os
import pytest
import json
import time
from unittest.mock import patch, MagicMock
import tempfile

from app.utils.regulatory_parser import RegulatoryParser
from app.utils.relationship_mapper import RelationshipMapper


class TestRegulatoryBenchmarks:
    """Benchmark tests for regulatory functionality."""
    
    @pytest.fixture
    def sample_regulatory_text(self):
        """Provide sample regulatory text for testing."""
        return """
        Article 1. Scope
        This regulation applies to all organizations processing personal data.
        
        Article 2. Requirements
        2.1. Organizations shall implement appropriate security measures.
        2.2. Data breaches must be reported within 72 hours.
        
        Section 3. Penalties
        3.1. Violations may result in fines up to $10,000,000 or 2% of annual revenue.
        3.2. Serious violations may result in imprisonment of up to 5 years.
        """
    
    @pytest.fixture
    def large_regulatory_text(self):
        """Provide a larger sample regulatory text for performance testing."""
        # Create a more extensive sample with repeated sections
        base_text = """
        Article 1. Scope
        This regulation applies to all organizations processing personal data.
        
        Article 2. Requirements
        2.1. Organizations shall implement appropriate security measures.
        2.2. Data breaches must be reported within 72 hours.
        
        Section 3. Penalties
        3.1. Violations may result in fines up to $10,000,000 or 2% of annual revenue.
        3.2. Serious violations may result in imprisonment of up to 5 years.
        """
        
        # Repeat sections to create a larger document
        large_text = base_text
        for i in range(4, 15):
            large_text += f"""
            Article {i}. Additional Requirements
            {i}.1. Organizations shall maintain records of all processing activities.
            {i}.2. Organizations must implement data protection by design and default.
            {i}.3. Organizations shall conduct regular security assessments.
            
            Section {i+1}. Compliance
            {i+1}.1. Organizations must demonstrate compliance with this regulation.
            {i+1}.2. Organizations shall appoint a data protection officer.
            """
        
        return large_text
    
    def test_parser_performance(self, large_regulatory_text, tmp_path):
        """Test the performance of the regulatory parser with large documents."""
        parser = RegulatoryParser()
        
        # Create test PDF file path
        pdf_path = os.path.join(tmp_path, "large_regulation.pdf")
        with open(pdf_path, 'wb') as f:
            f.write(b'Dummy PDF content for large document')
        
        # Mock PDF extraction to return large text
        with patch.object(parser, 'extract_text_from_pdf', return_value=large_regulatory_text):
            # Measure parsing time
            start_time = time.time()
            result = parser.parse_document(pdf_path)
            end_time = time.time()
            
            # Log performance metrics
            parsing_time = end_time - start_time
            print(f"Parsing time: {parsing_time:.4f} seconds")
            print(f"Document sections: {len(result['sections']['articles']) + len(result['sections']['sections'])}")
            print(f"Requirements identified: {len(result['requirements'])}")
            
            # Performance assertions (adjust thresholds as needed)
            assert parsing_time < 5.0, "Parsing took too long"
            assert len(result["requirements"]) > 10, "Not enough requirements extracted"
            assert result["confidence_score"] > 0.7, "Confidence score too low"
    
    def test_relationship_mapper_performance(self, large_regulatory_text, tmp_path):
        """Test performance of relationship mapping with multiple documents."""
        parser = RegulatoryParser()
        mapper = RelationshipMapper()
        
        # Create multiple documents
        documents = []
        
        # Mock extraction for multiple documents
        with patch.object(parser, 'extract_text_from_pdf', return_value=large_regulatory_text):
            for i in range(5):
                pdf_path = os.path.join(tmp_path, f"regulation{i}.pdf")
                with open(pdf_path, 'wb') as f:
                    f.write(f"Dummy content for regulation {i}".encode())
                
                # Parse document
                doc = parser.parse_document(pdf_path)
                doc["id"] = f"doc{i}"
                doc["title"] = f"Regulation {i} - Data Protection Requirements"
                doc["file_name"] = os.path.basename(pdf_path)
                documents.append(doc)
        
        # Measure mapping time
        start_time = time.time()
        result = mapper.map_document_relationships(documents)
        end_time = time.time()
        
        # Log performance metrics
        mapping_time = end_time - start_time
        print(f"Relationship mapping time: {mapping_time:.4f} seconds")
        print(f"Documents mapped: {len(result['documents'])}")
        total_relationships = (
            len(result["hierarchical_relationships"]) + 
            len(result["cross_references"]) + 
            sum(len(mappings) for mappings in result["framework_mappings"].values())
        )
        print(f"Total relationships identified: {total_relationships}")
        
        # Performance assertions
        assert mapping_time < 10.0, "Mapping took too long"
        assert len(result["documents"]) == 5, "Not all documents were mapped"
    
    def test_mapping_accuracy(self, sample_regulatory_text, tmp_path):
        """Test the accuracy of framework mapping."""
        parser = RegulatoryParser()
        mapper = RelationshipMapper()
        
        # Create documents with specific keywords that should match frameworks
        documents = []
        
        base_texts = [
            # Document with ISO keywords
            """
            Article 1. Information Classification
            1.1. Organizations shall classify information based on sensitivity.
            1.2. Confidential information must be protected with encryption.
            
            Article 2. User Access Management
            2.1. User access rights shall be reviewed regularly.
            2.2. Authentication mechanisms must be implemented for all systems.
            """,
            
            # Document with NIST keywords
            """
            Article 1. System Monitoring
            1.1. Information systems shall be monitored for suspicious activity.
            1.2. Audit logs must be maintained for all access attempts.
            
            Article 2. Transmission Security
            2.1. All data transmissions shall be encrypted.
            2.2. Integrity of communications must be verified.
            """
        ]
        
        # Mock extraction for framework-specific documents
        for i, text in enumerate(base_texts):
            with patch.object(parser, 'extract_text_from_pdf', return_value=text):
                pdf_path = os.path.join(tmp_path, f"regulation{i}.pdf")
                with open(pdf_path, 'wb') as f:
                    f.write(f"Dummy content for regulation {i}".encode())
                
                # Parse document
                doc = parser.parse_document(pdf_path)
                doc["id"] = f"doc{i}"
                doc["title"] = f"Regulation {i}"
                doc["file_name"] = os.path.basename(pdf_path)
                documents.append(doc)
        
        # Map relationships
        result = mapper.map_document_relationships(documents)
        
        # Check framework mapping accuracy
        iso_mappings = result["framework_mappings"].get("ISO", [])
        nist_mappings = result["framework_mappings"].get("NIST", [])
        
        # Verify framework mappings
        assert any(mapping["control_id"] == "A.8.2" for mapping in iso_mappings), "Missing ISO classification mapping"
        assert any(mapping["control_id"] == "A.9.2" for mapping in iso_mappings), "Missing ISO access management mapping"
        assert any(mapping["control_id"] == "SI-4" for mapping in nist_mappings), "Missing NIST monitoring mapping"
        assert any(mapping["control_id"] == "SC-8" for mapping in nist_mappings), "Missing NIST transmission mapping"
    
    def test_incremental_mapping_performance(self, sample_regulatory_text, tmp_path):
        """Test performance of incremental relationship mapping."""
        parser = RegulatoryParser()
        mapper = RelationshipMapper()
        
        # Create initial set of documents
        documents = []
        
        # Mock extraction for initial documents
        with patch.object(parser, 'extract_text_from_pdf', return_value=sample_regulatory_text):
            for i in range(3):
                pdf_path = os.path.join(tmp_path, f"regulation{i}.pdf")
                with open(pdf_path, 'wb') as f:
                    f.write(f"Dummy content for regulation {i}".encode())
                
                # Parse document
                doc = parser.parse_document(pdf_path)
                doc["id"] = f"doc{i}"
                doc["title"] = f"Regulation {i}"
                doc["file_name"] = os.path.basename(pdf_path)
                documents.append(doc)
        
        # Initial mapping
        start_time = time.time()
        initial_result = mapper.map_document_relationships(documents)
        initial_mapping_time = time.time() - start_time
        
        # Add one more document
        with patch.object(parser, 'extract_text_from_pdf', return_value=sample_regulatory_text):
            pdf_path = os.path.join(tmp_path, "new_regulation.pdf")
            with open(pdf_path, 'wb') as f:
                f.write(b"Dummy content for new regulation")
            
            new_doc = parser.parse_document(pdf_path)
            new_doc["id"] = "new_doc"
            new_doc["title"] = "New Regulation with References"
            new_doc["file_name"] = os.path.basename(pdf_path)
            
            # Add a cross-reference to an existing document
            new_doc["sections"]["articles"].append(
                "Article 4: This regulation extends the requirements in Regulation 0."
            )
            
            documents.append(new_doc)
        
        # Incremental mapping
        start_time = time.time()
        updated_result = mapper.map_document_relationships(documents)
        incremental_mapping_time = time.time() - start_time
        
        # Compare performance
        print(f"Initial mapping time (3 docs): {initial_mapping_time:.4f} seconds")
        print(f"Incremental mapping time (4 docs): {incremental_mapping_time:.4f} seconds")
        
        # Verify results
        assert len(updated_result["documents"]) == 4
        assert len(updated_result["documents"]) > len(initial_result["documents"])
        
        # Check if cross-reference was identified
        cross_refs = [ref for ref in updated_result["cross_references"] 
                     if "new_doc" in (ref.get("source_id", ""), ref.get("target_id", ""))]
        assert len(cross_refs) > 0, "Cross-reference not identified"
