
import pytest
from fastapi.testclient import TestClient
from app.main import app
from app.api.regulations import (
    get_regulation_by_id,
    get_regulations_by_country,
    search_regulations
)
from unittest.mock import patch, MagicMock

client = TestClient(app)

class TestRegulationsAPI:
    @patch('app.api.regulations.get_regulation_by_id')
    def test_get_regulation_endpoint(self, mock_get_regulation):
        # Setup mock
        mock_regulation = {
            "id": 1,
            "title": "Test Regulation",
            "description": "This is a test regulation",
            "country_id": 1,
            "url": "https://example.com/regulation"
        }
        mock_get_regulation.return_value = mock_regulation
        
        # Test endpoint
        response = client.get("/api/v1/regulations/1")
        assert response.status_code == 200
        assert response.json() == mock_regulation
        mock_get_regulation.assert_called_once_with(1)
    
    @patch('app.api.regulations.get_regulation_by_id')
    def test_get_regulation_not_found(self, mock_get_regulation):
        # Setup mock for not found
        mock_get_regulation.return_value = None
        
        # Test endpoint with non-existent ID
        response = client.get("/api/v1/regulations/999")
        assert response.status_code == 404
        assert "detail" in response.json()
        mock_get_regulation.assert_called_once_with(999)
    
    @patch('app.api.regulations.get_regulations_by_country')
    def test_get_regulations_by_country_endpoint(self, mock_get_regs):
        # Setup mock
        mock_regulations = [
            {"id": 1, "title": "Regulation 1", "country_id": 1},
            {"id": 2, "title": "Regulation 2", "country_id": 1}
        ]
        mock_get_regs.return_value = mock_regulations
        
        # Test endpoint
        response = client.get("/api/v1/countries/1/regulations")
        assert response.status_code == 200
        assert response.json() == mock_regulations
        mock_get_regs.assert_called_once_with(1)
    
    @patch('app.api.regulations.search_regulations')
    def test_search_regulations_endpoint(self, mock_search):
        # Setup mock
        mock_results = [
            {"id": 1, "title": "Privacy Regulation", "score": 0.95},
            {"id": 3, "title": "Data Privacy Act", "score": 0.87}
        ]
        mock_search.return_value = mock_results
        
        # Test endpoint
        response = client.get("/api/v1/regulations/search?query=privacy")
        assert response.status_code == 200
        assert response.json() == mock_results
        mock_search.assert_called_once_with("privacy")
    
    @patch('app.api.regulations.search_regulations')
    def test_search_regulations_no_results(self, mock_search):
        # Setup mock for empty results
        mock_search.return_value = []
        
        # Test endpoint with query that yields no results
        response = client.get("/api/v1/regulations/search?query=nonexistent")
        assert response.status_code == 200
        assert response.json() == []
        mock_search.assert_called_once_with("nonexistent")
