
"""Integration tests for the health check endpoint."""
import json
import os
from datetime import datetime
from unittest.mock import patch, mock_open, MagicMock

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.exc import SQLAlchemyError

from main import app, get_db


@pytest.fixture
def client():
    """
    Create a test client for the FastAPI application.
    
    Returns:
        TestClient: The test client instance
    """
    return TestClient(app)


def test_health_check_integration(client):
    """
    Integration test for the health check endpoint.
    
    Args:
        client (TestClient): The test client fixture
    """
    # Make the request to both endpoints
    response = client.get("/health")
    response_api_v1 = client.get("/api/v1/health")
    
    # Both should return 200
    assert response.status_code == 200
    assert response_api_v1.status_code == 200
    
    # Both should return the same response
    assert response.json() == response_api_v1.json()
    
    # Check the structure of the response
    json_data = response.json()
    assert "status" in json_data
    assert "timestamp" in json_data
    assert "database" in json_data
    assert "test_coverage" in json_data
    
    # Check the values
    assert json_data["status"] == "healthy"
    assert isinstance(json_data["timestamp"], str)
    assert json_data["database"] in ["healthy", "unhealthy"] or "unhealthy:" in json_data["database"]


def test_health_check_with_coverage_data(client):
    """
    Test health check endpoint with mock coverage data.
    
    Args:
        client (TestClient): The test client fixture
    """
    # Create mock HTML content with coverage data
    mock_html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Coverage for main.py: 85%</title>
    </head>
    <body>
        <div class="stats">
            <p class="note">Generated by coverage.py</p>
        </div>
    </body>
    </html>
    """
    
    # Mock the os.path.exists to return True for coverage files
    with patch('os.path.exists', return_value=True):
        # Mock open to return our mock HTML content
        with patch('builtins.open', mock_open(read_data=mock_html_content)):
            # Make the request
            response = client.get("/health")
            
            # Check the response
            assert response.status_code == 200
            json_data = response.json()
            
            # Coverage data should be extracted
            assert "test_coverage" in json_data
            assert isinstance(json_data["test_coverage"], dict)
            assert "main.py" in json_data["test_coverage"]
            assert json_data["test_coverage"]["main.py"] == 85


def test_health_check_database_error(client):
    """
    Test health check endpoint when the database connection fails.
    
    Args:
        client (TestClient): The test client fixture
    """
    # Create a mock database session that raises an exception
    mock_db = MagicMock()
    mock_db.execute.side_effect = SQLAlchemyError("Database connection error")
    
    # Override the database dependency
    app.dependency_overrides[get_db] = lambda: mock_db
    
    try:
        # Make the request
        response = client.get("/health")
        
        # Should still return 200 even with DB error
        assert response.status_code == 200
        json_data = response.json()
        
        # Database status should indicate an error
        assert "database" in json_data
        assert json_data["database"].startswith("unhealthy")
        assert "Database connection error" in json_data["database"]
        
    finally:
        # Clean up the override
        app.dependency_overrides = {}


def test_health_check_log_creation(client):
    """
    Test that the health check endpoint creates a log file.
    
    Args:
        client (TestClient): The test client fixture
    """
    # Mock the file open operation to verify it's called correctly
    mock_file = MagicMock()
    
    with patch('builtins.open', mock_open()) as mock_file:
        # Make the request
        response = client.get("/health")
        
        # Check the response
        assert response.status_code == 200
        
        # Verify that the log file was opened for appending
        mock_file.assert_called_with("health_check_log.txt", "a")
        
        # Verify that something was written to the file
        mock_file().write.assert_called()
        
        # The write should include the current time and client IP
        write_args = mock_file().write.call_args[0][0]
        assert "[" in write_args  # Should have timestamp
        assert "IP:" in write_args  # Should have IP address
        assert "DB:" in write_args  # Should have DB status
