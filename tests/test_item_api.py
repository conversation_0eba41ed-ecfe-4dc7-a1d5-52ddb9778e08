
"""Test cases for the Item API endpoints."""
import json
from datetime import datetime
from unittest.mock import patch, MagicMock

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from main import app
from app.db import Item


@pytest.fixture
def client():
    """
    Create a test client for the FastAPI application.
    
    Returns:
        TestClient: The test client instance
    """
    return TestClient(app)


@pytest.fixture
def mock_db():
    """
    Create a mock database session.
    
    Returns:
        MagicMock: A mock database session
    """
    mock = MagicMock(spec=Session)
    
    # Setup query builder mocks
    query_mock = MagicMock()
    filter_mock = MagicMock()
    first_mock = MagicMock()
    all_mock = MagicMock()
    offset_mock = MagicMock()
    limit_mock = MagicMock()
    
    mock.query.return_value = query_mock
    query_mock.filter.return_value = filter_mock
    filter_mock.first.return_value = first_mock
    filter_mock.all.return_value = all_mock
    filter_mock.offset.return_value = offset_mock
    offset_mock.limit.return_value = limit_mock
    
    return mock


@pytest.fixture
def mock_db_dependency(mock_db):
    """
    Override the database dependency.
    
    Args:
        mock_db: The mock database fixture
        
    Yields:
        MagicMock: The mock database session
    """
    app.dependency_overrides = {}
    
    def override_get_db():
        yield mock_db
    
    app.dependency_overrides[app.dependency_overrides] = override_get_db
    yield mock_db
    app.dependency_overrides = {}


def test_create_item(client, mock_db):
    """
    Test creating a new item.
    
    Args:
        client (TestClient): The test client
        mock_db (MagicMock): The mock database session
    """
    # Prepare test data
    test_item = {
        "name": "Test Item",
        "description": "This is a test item"
    }
    
    # Mock the database session behavior
    mock_db_item = Item(
        id=1,
        name="Test Item",
        description="This is a test item",
        created_at=datetime.utcnow(),
        changed_on=datetime.utcnow(),
        is_deleted=False,
        deleted_at=None
    )
    
    with patch('main.get_db', return_value=mock_db):
        # Configure the mock to return our mocked item
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.side_effect = lambda x: setattr(x, 'id', 1)
        
        # Make the request
        response = client.post(
            "/items/",
            content=json.dumps(test_item),
            headers={"Content-Type": "application/json"}
        )
        
        # Assertions
        assert response.status_code == 200
        assert "id" in response.json()
        assert response.json()["name"] == test_item["name"]
        assert response.json()["description"] == test_item["description"]
        
        # Verify DB operations were called
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()


def test_read_items(client, mock_db):
    """
    Test retrieving a list of items.
    
    Args:
        client (TestClient): The test client
        mock_db (MagicMock): The mock database session
    """
    # Prepare test data
    mock_items = [
        Item(
            id=1,
            name="Item 1",
            description="Description 1",
            created_at=datetime.utcnow(),
            changed_on=datetime.utcnow(),
            is_deleted=False,
            deleted_at=None
        ),
        Item(
            id=2,
            name="Item 2",
            description="Description 2",
            created_at=datetime.utcnow(),
            changed_on=datetime.utcnow(),
            is_deleted=False,
            deleted_at=None
        )
    ]
    
    with patch('main.get_db', return_value=mock_db):
        # Configure mock to return our items
        query_mock = mock_db.query.return_value
        filter_mock = query_mock.filter.return_value
        offset_mock = filter_mock.offset.return_value
        limit_mock = offset_mock.limit.return_value
        limit_mock.all.return_value = mock_items
        
        # Make the request
        response = client.get("/items/")
        
        # Assertions
        assert response.status_code == 200
        assert isinstance(response.json(), list)
        assert len(response.json()) == 2
        assert response.json()[0]["id"] == 1
        assert response.json()[1]["id"] == 2
        assert response.json()[0]["name"] == "Item 1"
        assert response.json()[1]["name"] == "Item 2"
        
        # Verify query methods were called with correct parameters
        mock_db.query.assert_called_once()
        query_mock.filter.assert_called_once()
        filter_mock.offset.assert_called_once_with(0)
        offset_mock.limit.assert_called_once_with(100)
        limit_mock.all.assert_called_once()


def test_read_item(client, mock_db):
    """
    Test retrieving a specific item by ID.
    
    Args:
        client (TestClient): The test client
        mock_db (MagicMock): The mock database session
    """
    # Prepare test data
    mock_item = Item(
        id=1,
        name="Test Item",
        description="This is a test item",
        created_at=datetime.utcnow(),
        changed_on=datetime.utcnow(),
        is_deleted=False,
        deleted_at=None
    )
    
    with patch('main.get_db', return_value=mock_db):
        # Configure mock to return our item
        query_mock = mock_db.query.return_value
        filter_mock = query_mock.filter.return_value
        filter_mock.first.return_value = mock_item
        
        # Make the request
        response = client.get("/items/1")
        
        # Assertions
        assert response.status_code == 200
        assert response.json()["id"] == 1
        assert response.json()["name"] == "Test Item"
        assert response.json()["description"] == "This is a test item"
        
        # Verify query methods were called correctly
        mock_db.query.assert_called_once()
        query_mock.filter.assert_called_once()
        filter_mock.first.assert_called_once()


def test_read_item_not_found(client, mock_db):
    """
    Test retrieving a non-existent item.
    
    Args:
        client (TestClient): The test client
        mock_db (MagicMock): The mock database session
    """
    with patch('main.get_db', return_value=mock_db):
        # Configure mock to return None (item not found)
        query_mock = mock_db.query.return_value
        filter_mock = query_mock.filter.return_value
        filter_mock.first.return_value = None
        
        # Make the request
        response = client.get("/items/999")
        
        # Assertions
        assert response.status_code == 404
        assert "detail" in response.json()
        assert response.json()["detail"] == "Item not found"


def test_update_item(client, mock_db):
    """
    Test updating an existing item.
    
    Args:
        client (TestClient): The test client
        mock_db (MagicMock): The mock database session
    """
    # Prepare test data
    update_data = {
        "name": "Updated Item",
        "description": "Updated description"
    }
    
    mock_item = Item(
        id=1,
        name="Original Item",
        description="Original description",
        created_at=datetime.utcnow(),
        changed_on=datetime.utcnow(),
        is_deleted=False,
        deleted_at=None
    )
    
    with patch('main.get_db', return_value=mock_db):
        # Configure mock behavior
        query_mock = mock_db.query.return_value
        filter_mock = query_mock.filter.return_value
        filter_mock.first.return_value = mock_item
        
        # Make the request
        response = client.put(
            "/items/1",
            content=json.dumps(update_data),
            headers={"Content-Type": "application/json"}
        )
        
        # Assertions
        assert response.status_code == 200
        assert response.json()["id"] == 1
        assert response.json()["name"] == "Updated Item"
        assert response.json()["description"] == "Updated description"
        
        # Verify DB operations
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()


def test_delete_item_soft(client, mock_db):
    """
    Test soft deleting an item.
    
    Args:
        client (TestClient): The test client
        mock_db (MagicMock): The mock database session
    """
    mock_item = Item(
        id=1,
        name="Item to Delete",
        description="This item will be soft deleted",
        created_at=datetime.utcnow(),
        changed_on=datetime.utcnow(),
        is_deleted=False,
        deleted_at=None
    )
    
    with patch('main.get_db', return_value=mock_db):
        # Configure mock behavior
        query_mock = mock_db.query.return_value
        filter_mock = query_mock.filter.return_value
        filter_mock.first.return_value = mock_item
        
        # Make the request for soft delete
        response = client.delete("/items/1")
        
        # Assertions
        assert response.status_code == 200
        assert response.json()["id"] == 1
        assert mock_item.is_deleted is True
        assert mock_item.deleted_at is not None
        
        # Verify DB operations
        mock_db.commit.assert_called_once()


def test_delete_item_hard(client, mock_db):
    """
    Test hard deleting an item.
    
    Args:
        client (TestClient): The test client
        mock_db (MagicMock): The mock database session
    """
    mock_item = Item(
        id=1,
        name="Item to Delete",
        description="This item will be hard deleted",
        created_at=datetime.utcnow(),
        changed_on=datetime.utcnow(),
        is_deleted=False,
        deleted_at=None
    )
    
    with patch('main.get_db', return_value=mock_db):
        # Configure mock behavior
        query_mock = mock_db.query.return_value
        filter_mock = query_mock.filter.return_value
        filter_mock.first.return_value = mock_item
        
        # Make the request for hard delete
        response = client.delete("/items/1?hard_delete=true")
        
        # Assertions
        assert response.status_code == 200
        
        # Verify DB operations
        mock_db.delete.assert_called_once_with(mock_item)
        mock_db.commit.assert_called_once()


def test_restore_item(client, mock_db):
    """
    Test restoring a soft-deleted item.
    
    Args:
        client (TestClient): The test client
        mock_db (MagicMock): The mock database session
    """
    # Create a soft-deleted item
    mock_item = Item(
        id=1,
        name="Deleted Item",
        description="This item is soft deleted",
        created_at=datetime.utcnow(),
        changed_on=datetime.utcnow(),
        is_deleted=True,
        deleted_at=datetime.utcnow()
    )
    
    with patch('main.get_db', return_value=mock_db):
        # Configure mock behavior
        query_mock = mock_db.query.return_value
        filter_mock = query_mock.filter.return_value
        filter_mock.first.return_value = mock_item
        
        # Make the request to restore
        response = client.post("/items/1/restore")
        
        # Assertions
        assert response.status_code == 200
        assert response.json()["id"] == 1
        assert mock_item.is_deleted is False
        assert mock_item.deleted_at is None
        
        # Verify DB operations
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()
