"""
Tests for error handling.
"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import MagicMock, patch
from sqlalchemy.exc import SQLAlchemyError, IntegrityError

from app.main import app
from app.api.regulations.router import get_regulation

client = TestClient(app)

def test_404_not_found():
    """Test 404 Not Found error."""
    response = client.get("/api/v1/nonexistent-endpoint")
    assert response.status_code == 404
    assert "detail" in response.json()
    assert "Not Found" in response.json()["detail"]

def test_405_method_not_allowed():
    """Test 405 Method Not Allowed error."""
    response = client.put("/api/v1/health")
    assert response.status_code == 405
    assert "detail" in response.json()
    assert "Method Not Allowed" in response.json()["detail"]

def test_422_validation_error():
    """Test 422 Validation Error."""
    # Try to create a regulation with missing required fields
    response = client.post("/api/v1/regulations/", json={})
    assert response.status_code == 422
    assert "detail" in response.json()
    assert len(response.json()["detail"]) > 0
    
    # Check that the error message includes the missing fields
    error_fields = [error["loc"][1] for error in response.json()["detail"]]
    assert "title" in error_fields
    assert "status" in error_fields
    assert "reference_number" in error_fields
    assert "category_id" in error_fields

def test_database_error_handling():
    """Test handling of database errors."""
    # Mock the get_db dependency
    mock_db = MagicMock()
    mock_db.query.side_effect = SQLAlchemyError("Database error")
    
    # Mock the dependency override
    app.dependency_overrides[get_db] = lambda: mock_db
    
    try:
        # Make a request that would trigger a database query
        response = client.get("/api/v1/regulations/")
        
        # Check that a 500 error is returned
        assert response.status_code == 500
        assert "detail" in response.json()
        assert "Database error" in response.json()["detail"]
    finally:
        # Clean up the dependency override
        app.dependency_overrides.clear()

def test_integrity_error_handling():
    """Test handling of integrity errors."""
    # Create a mock regulation
    mock_regulation = MagicMock()
    mock_regulation.title = "Test Regulation"
    
    # Mock the get_db dependency
    mock_db = MagicMock()
    mock_db.query.return_value.filter.return_value.first.return_value = mock_regulation
    mock_db.commit.side_effect = IntegrityError("statement", "params", "orig")
    
    # Mock the dependency override
    app.dependency_overrides[get_db] = lambda: mock_db
    
    try:
        # Make a request that would trigger an integrity error
        response = client.put("/api/v1/regulations/1", json={"title": "Updated Title"})
        
        # Check that a 400 error is returned
        assert response.status_code == 400
        assert "detail" in response.json()
        assert "constraint violation" in response.json()["detail"]
    finally:
        # Clean up the dependency override
        app.dependency_overrides.clear()

def test_custom_exception_handler():
    """Test custom exception handler."""
    # Create a mock endpoint that raises a custom exception
    @app.get("/api/v1/test-custom-exception")
    def test_endpoint():
        from app.exceptions import RegulationGuruException
        raise RegulationGuruException("Custom error message")
    
    # Make a request to the endpoint
    response = client.get("/api/v1/test-custom-exception")
    
    # Check that the custom error is handled
    assert response.status_code == 500
    assert "detail" in response.json()
    assert "Custom error message" in response.json()["detail"]

def test_rate_limit_exceeded():
    """Test rate limit exceeded error."""
    # Mock the rate limiter to always return True (rate limited)
    with patch("app.middleware.rate_limiting_middleware.RateLimitingMiddleware.is_rate_limited", return_value=True):
        # Make a request
        response = client.get("/api/v1/regulations/")
        
        # Check that a 429 error is returned
        assert response.status_code == 429
        assert "detail" in response.json()
        assert "Rate limit exceeded" in response.json()["detail"]
        
        # Check that the rate limit headers are set
        assert "X-RateLimit-Limit" in response.headers
        assert "X-RateLimit-Remaining" in response.headers
        assert "X-RateLimit-Reset" in response.headers

def test_unauthorized_access():
    """Test unauthorized access error."""
    # Mock a protected endpoint
    @app.get("/api/v1/protected", dependencies=[Depends(get_current_user)])
    def protected_endpoint():
        return {"message": "You are authenticated"}
    
    # Make a request without authentication
    response = client.get("/api/v1/protected")
    
    # Check that a 401 error is returned
    assert response.status_code == 401
    assert "detail" in response.json()
    assert "Not authenticated" in response.json()["detail"]

def test_forbidden_access():
    """Test forbidden access error."""
    # Mock a protected endpoint that requires admin role
    @app.get("/api/v1/admin-only")
    def admin_only_endpoint(current_user: User = Depends(get_current_user)):
        if "admin" not in current_user.roles:
            raise HTTPException(status_code=403, detail="Not enough permissions")
        return {"message": "You are an admin"}
    
    # Mock the get_current_user dependency to return a non-admin user
    mock_user = MagicMock()
    mock_user.roles = ["user"]
    app.dependency_overrides[get_current_user] = lambda: mock_user
    
    try:
        # Make a request
        response = client.get("/api/v1/admin-only")
        
        # Check that a 403 error is returned
        assert response.status_code == 403
        assert "detail" in response.json()
        assert "Not enough permissions" in response.json()["detail"]
    finally:
        # Clean up the dependency override
        app.dependency_overrides.clear()
