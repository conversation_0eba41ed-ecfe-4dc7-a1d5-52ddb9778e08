
"""Tests for URL processing with regulations."""
import pytest
from unittest.mock import patch, MagicMock

from app.utils import process_url, extract_domain_and_tld
from app.api import regulations


def test_url_to_regulation_mapping():
    """Test mapping URLs to regulation categories."""
    # Privacy regulation URLs
    privacy_urls = [
        "https://www.gdpr-info.eu/art-5-gdpr/",
        "https://eur-lex.europa.eu/eli/reg/2016/679/oj",
        "https://www.mass.gov/regulations/201-CMR-1700-standards-for-the-protection-of-personal-information",
        "https://www.legislation.gov.uk/ukpga/2018/12/contents"
    ]
    
    for url in privacy_urls:
        result = process_url(url)
        assert result["category"] == "privacy_data_protection"
    
    # Cybersecurity regulation URLs
    cyber_urls = [
        "https://www.dfs.ny.gov/system/files/documents/2023/12/rf23_nycrr_part_500_amend02_20231101.pdf",
        "https://www.sec.gov/rules/interp/2018/33-10459.pdf",
        "https://www.ecb.europa.eu/paym/pdf/cons/cyberresilience/Cyber_resilience_oversight_expectations_for_financial_market_infrastructures.pdf"
    ]
    
    for url in cyber_urls:
        result = process_url(url)
        assert result["category"] == "cybersecurity"


def test_url_to_country_mapping():
    """Test mapping URLs to countries."""
    # US URLs
    us_urls = [
        "https://www.sec.gov/rules/interp/2018/33-10459.pdf",
        "https://www.mass.gov/regulations/201-CMR-1700",
        "https://www.dfs.ny.gov/system/files/documents/2023/12/rf23_nycrr_part_500_amend02_20231101.pdf"
    ]
    
    for url in us_urls:
        result = process_url(url)
        assert result["country"] == "United States"
    
    # EU URLs
    eu_urls = [
        "https://eur-lex.europa.eu/eli/reg/2016/679/oj",
        "https://eur-lex.europa.eu/legal-content/EN/TXT/?uri=CELEX%3A32022L2555",
        "https://ec.europa.eu/newsroom/dae/document.cfm?doc_id=72148"
    ]
    
    for url in eu_urls:
        result = process_url(url)
        assert result["country"] == "European Union"
    
    # UK URLs
    uk_urls = [
        "https://www.legislation.gov.uk/ukpga/2018/12/contents",
        "https://www.ncsc.gov.uk/files/Cyber-Essentials-Requirements-for-IT-infrastructure-2-2.pdf"
    ]
    
    for url in uk_urls:
        result = process_url(url)
        assert result["country"] == "United Kingdom"


def test_regulation_confidence_levels():
    """Test that confidence levels are assigned to URL processing results."""
    # Process a few URLs and check for confidence levels
    urls = [
        "https://www.gdpr-info.eu/art-5-gdpr/",
        "https://www.mass.gov/regulations/201-CMR-1700",
        "https://www.sec.gov/rules/interp/2018/33-10459.pdf"
    ]
    
    for url in urls:
        result = process_url(url)
        assert "confidence" in result
        assert 0.0 <= result["confidence"] <= 1.0


def test_batch_url_processing():
    """Test processing multiple URLs in a batch."""
    # Create a list of URLs
    urls = [
        "https://www.gdpr-info.eu/art-5-gdpr/",
        "https://www.mass.gov/regulations/201-CMR-1700",
        "https://www.sec.gov/rules/interp/2018/33-10459.pdf",
        "https://www.dfs.ny.gov/system/files/documents/2023/12/rf23_nycrr_part_500_amend02_20231101.pdf",
        "https://eur-lex.europa.eu/eli/reg/2016/679/oj"
    ]
    
    # Process each URL
    results = []
    for url in urls:
        results.append(process_url(url))
    
    # Check the results
    assert len(results) == len(urls)
    assert all("country" in result for result in results)
    assert all("regulator" in result for result in results)
    assert all("category" in result for result in results)
    assert all("url" in result for result in results)
    assert all("confidence" in result for result in results)


def test_unknown_urls():
    """Test processing URLs that don't match known patterns."""
    unknown_urls = [
        "https://example.com/some-page",
        "https://unknown-regulator.org/policy",
        "https://random-site.net/terms"
    ]
    
    for url in unknown_urls:
        result = process_url(url)
        
        # Should still have the basic fields
        assert "url" in result
        assert "domain" in result
        
        # But confidence should be low
        assert result["confidence"] < 0.5
        
        # Category might be "unknown" or a best guess
        if "category" in result and result["category"] != "unknown":
            assert result["confidence"] < 0.3  # Very low confidence for guesses


def test_integration_with_regulations_module():
    """Test integration between URL processor and regulations module."""
    # Mock the regulations module functions
    original_get_categories = regulations.get_categories
    
    try:
        # Create a mock that returns valid categories
        mock_categories = ["privacy_data_protection", "cybersecurity", "financial_regulations"]
        regulations.get_categories = MagicMock(return_value=mock_categories)
        
        # Process a URL
        url = "https://www.example.com/privacy-policy"
        result = process_url(url)
        
        # Verify the regulations module was used
        assert regulations.get_categories.called
        
        # Result should contain expected fields
        assert "url" in result
        assert "domain" in result
        assert "category" in result
    
    finally:
        # Restore the original function
        regulations.get_categories = original_get_categories
