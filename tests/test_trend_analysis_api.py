
"""
Tests for the regulatory trend analysis API endpoints.
"""
import pytest
from fastapi.testclient import TestClient
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

from app.main import app
from app.api import trend_analysis

client = TestClient(app)

class TestTrendAnalysisAPI:
    
    def test_analyze_trends(self):
        """Test trend analysis with valid parameters."""
        # Test data
        today = datetime.now()
        past_date = today - timedelta(days=90)
        
        analysis_request = {
            "start_date": past_date.isoformat(),
            "end_date": today.isoformat(),
            "regions": ["EU", "US"],
            "categories": ["Data Protection", "Privacy"]
        }
        
        # Make request
        response = client.post("/api/v1/trends/analyze", json=analysis_request)
        
        # Check response
        assert response.status_code == 200
        result = response.json()
        
        # Check structure
        assert "period" in result
        assert "previous_period" in result
        assert "overall_stats" in result
        assert "fastest_growing" in result
        assert "trends" in result
        
        # Check content
        assert result["period"]["days"] == 90
        assert len(result["trends"]) > 0
        
        # Check filtering
        for trend in result["trends"]:
            assert trend["region"] in ["EU", "US"]
            assert trend["category"] in ["Data Protection", "Privacy"]
    
    def test_analyze_trends_invalid_dates(self):
        """Test trend analysis with invalid date range."""
        # Test data with end date before start date
        today = datetime.now()
        future_date = today + timedelta(days=30)
        
        analysis_request = {
            "start_date": future_date.isoformat(),
            "end_date": today.isoformat(),
            "regions": ["EU", "US"],
            "categories": ["Data Protection", "Privacy"]
        }
        
        # Make request
        response = client.post("/api/v1/trends/analyze", json=analysis_request)
        
        # Check response
        assert response.status_code == 400
        assert "detail" in response.json()
        assert "end date" in response.json()["detail"].lower()
    
    def test_get_hot_topics_default(self):
        """Test getting hot topics with default parameters."""
        response = client.get("/api/v1/trends/hot-topics")
        
        # Check response
        assert response.status_code == 200
        result = response.json()
        
        # Check structure
        assert "period_days" in result
        assert "analysis_date" in result
        assert "hot_topics" in result
        
        # Check default period
        assert result["period_days"] == 90
        
        # Check hot topics
        assert len(result["hot_topics"]) > 0
        for topic in result["hot_topics"]:
            assert "topic" in topic
            assert "mentions" in topic
            assert "growth_rate" in topic
            assert "regions" in topic
            assert "sample_regulations" in topic
    
    def test_get_hot_topics_custom_period(self):
        """Test getting hot topics with custom period."""
        response = client.get("/api/v1/trends/hot-topics?days=180")
        
        # Check response
        assert response.status_code == 200
        result = response.json()
        
        # Check custom period
        assert result["period_days"] == 180
