
"""Test cases for the RegulationURL API endpoints."""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from main import app
from app.db import models


@pytest.fixture
def test_country(db: Session):
    """Create a test country."""
    country = models.Country(name="Test Country", code="TC")
    db.add(country)
    db.commit()
    db.refresh(country)
    yield country
    db.delete(country)
    db.commit()


@pytest.fixture
def test_regulator(db: Session, test_country):
    """Create a test regulator."""
    regulator = models.Regulator(
        name="Test Regulator",
        description="Test Description",
        type="Test Type",
        country_id=test_country.id
    )
    db.add(regulator)
    db.commit()
    db.refresh(regulator)
    yield regulator
    db.delete(regulator)
    db.commit()


@pytest.fixture
def test_regulation_url(db: Session, test_regulator):
    """Create a test regulation URL."""
    regulation_url = models.RegulationURL(
        url="https://example.com/regulation",
        domain="example.com",
        normalized_url="example.com/regulation",
        category="privacy",
        confidence_level=0.85,
        regulator_id=test_regulator.id
    )
    db.add(regulation_url)
    db.commit()
    db.refresh(regulation_url)
    yield regulation_url
    db.delete(regulation_url)
    db.commit()


def test_get_regulation_urls(client: TestClient, test_regulation_url):
    """Test getting list of regulation URLs."""
    response = client.get("/api/v1/regulations/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) >= 1


def test_get_regulation_url(client: TestClient, test_regulation_url):
    """Test getting a specific regulation URL by ID."""
    response = client.get(f"/api/v1/regulations/{test_regulation_url.id}")
    assert response.status_code == 200
    assert response.json()["id"] == test_regulation_url.id
    assert response.json()["url"] == test_regulation_url.url
    assert response.json()["regulator"]["id"] == test_regulation_url.regulator_id


def test_get_regulation_url_not_found(client: TestClient):
    """Test getting a non-existent regulation URL."""
    response = client.get("/api/v1/regulations/999999")
    assert response.status_code == 404
    assert "not found" in response.json()["detail"].lower()


def test_create_regulation_url(client: TestClient, test_regulator):
    """Test creating a new regulation URL."""
    data = {
        "url": "https://test.com/new_regulation",
        "domain": "test.com",
        "normalized_url": "test.com/new_regulation",
        "category": "security",
        "confidence_level": 0.75,
        "regulator_id": test_regulator.id
    }
    response = client.post("/api/v1/regulations/", json=data)
    assert response.status_code == 201
    assert response.json()["url"] == data["url"]
    assert response.json()["regulator"]["id"] == test_regulator.id
    
    # Clean up
    regulation_id = response.json()["id"]
    client.delete(f"/api/v1/regulations/{regulation_id}")


def test_create_regulation_url_invalid_regulator(client: TestClient):
    """Test creating a regulation URL with an invalid regulator ID."""
    data = {
        "url": "https://test.com/invalid_regulator",
        "domain": "test.com",
        "normalized_url": "test.com/invalid_regulator",
        "category": "security",
        "confidence_level": 0.75,
        "regulator_id": 999999
    }
    response = client.post("/api/v1/regulations/", json=data)
    assert response.status_code == 404
    assert "regulator not found" in response.json()["detail"].lower()


def test_update_regulation_url(client: TestClient, test_regulation_url):
    """Test updating a regulation URL."""
    data = {
        "url": "https://updated.com/regulation",
        "confidence_level": 0.95
    }
    response = client.put(f"/api/v1/regulations/{test_regulation_url.id}", json=data)
    assert response.status_code == 200
    assert response.json()["id"] == test_regulation_url.id
    assert response.json()["url"] == data["url"]
    assert response.json()["confidence_level"] == data["confidence_level"]
    # Fields not in the update should remain unchanged
    assert response.json()["domain"] == test_regulation_url.domain


def test_update_regulation_url_not_found(client: TestClient):
    """Test updating a non-existent regulation URL."""
    data = {"url": "https://updated.com/not_found"}
    response = client.put("/api/v1/regulations/999999", json=data)
    assert response.status_code == 404
    assert "not found" in response.json()["detail"].lower()


def test_delete_regulation_url(client: TestClient, db: Session, test_regulator):
    """Test deleting a regulation URL."""
    # Create a temporary regulation URL for this test
    regulation_url = models.RegulationURL(
        url="https://delete.com/regulation",
        domain="delete.com",
        regulator_id=test_regulator.id,
        confidence_level=0.7
    )
    db.add(regulation_url)
    db.commit()
    db.refresh(regulation_url)

    response = client.delete(f"/api/v1/regulations/{regulation_url.id}")
    assert response.status_code == 200

    # Verify it's deleted
    db_regulation = db.query(models.RegulationURL).filter(models.RegulationURL.id == regulation_url.id).first()
    assert db_regulation is None


def test_delete_regulation_url_not_found(client: TestClient):
    """Test deleting a non-existent regulation URL."""
    response = client.delete("/api/v1/regulations/999999")
    assert response.status_code == 404
    assert "not found" in response.json()["detail"].lower()


def test_filter_regulations_by_regulator(client: TestClient, test_regulation_url, test_regulator):
    """Test filtering regulation URLs by regulator ID."""
    response = client.get(f"/api/v1/regulations/?regulator_id={test_regulator.id}")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) >= 1
    
    # All results should have the specified regulator ID
    for item in response.json():
        assert item["regulator"]["id"] == test_regulator.id


def test_filter_regulations_by_category(client: TestClient, test_regulation_url):
    """Test filtering regulation URLs by category."""
    response = client.get(f"/api/v1/regulations/?category={test_regulation_url.category}")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    
    # All results should have the specified category
    for item in response.json():
        assert item["category"] == test_regulation_url.category


def test_filter_regulations_by_confidence(client: TestClient, test_regulation_url):
    """Test filtering regulation URLs by minimum confidence level."""
    min_confidence = test_regulation_url.confidence_level - 0.1
    response = client.get(f"/api/v1/regulations/?min_confidence={min_confidence}")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    
    # All results should have confidence level at least the minimum
    for item in response.json():
        assert item["confidence_level"] >= min_confidence


def test_search_regulations_by_domain(client: TestClient, test_regulation_url):
    """Test searching regulation URLs by domain substring."""
    response = client.get(f"/api/v1/regulations/search?query={test_regulation_url.domain}")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    
    # Results should contain the domain substring
    found = False
    for item in response.json():
        if test_regulation_url.domain in item["domain"]:
            found = True
            break
    assert found
