
"""Tests for PDF analyzer functionality."""
import os
import pytest
from unittest.mock import patch, MagicMock
import tempfile

from app.utils.pdf_analyzer import (
    extract_text_from_pdf,
    identify_regulatory_sections,
    extract_entities,
    analyze_pdf_document
)

@pytest.fixture
def sample_regulatory_text():
    """Provide sample regulatory text for testing."""
    return """
    Article 1. Scope
    This regulation applies to all organizations processing personal data.
    
    Article 2. Requirements
    2.1. Organizations shall implement appropriate security measures.
    2.2. Data breaches must be reported within 72 hours.
    
    Section 3. Penalties
    3.1. Violations may result in fines up to $10,000,000 or 2% of annual revenue.
    3.2. Serious violations may result in imprisonment of up to 5 years.
    """

@patch('app.utils.pdf_analyzer.PyPDF2.PdfReader')
def test_extract_text_from_pdf(mock_pdf_reader, tmp_path):
    """Test extracting text from PDF."""
    # Create mock PDF reader
    mock_reader = MagicMock()
    mock_page = MagicMock()
    mock_page.extract_text.return_value = "Sample PDF text"
    mock_reader.pages = [mock_page, mock_page]
    mock_pdf_reader.return_value = mock_reader
    
    # Create temp file
    pdf_path = os.path.join(tmp_path, "test.pdf")
    with open(pdf_path, 'wb') as f:
        f.write(b'Dummy PDF content')
    
    # Test function
    text = extract_text_from_pdf(pdf_path)
    
    # Verify extraction
    assert "Sample PDF text" in text
    assert mock_page.extract_text.call_count == 2

def test_identify_regulatory_sections(sample_regulatory_text):
    """Test identification of regulatory sections."""
    sections = identify_regulatory_sections(sample_regulatory_text)
    
    # Verify sections
    assert len(sections["articles"]) == 2
    assert "Article 1" in sections["articles"][0]
    assert "Article 2" in sections["articles"][1]
    assert len(sections["sections"]) == 1
    assert "Section 3" in sections["sections"][0]
    assert len(sections["requirements"]) >= 2
    assert any("implement" in req for req in sections["requirements"])
    assert any("reported within 72 hours" in req for req in sections["requirements"])
    assert len(sections["penalties"]) >= 2
    assert any("$10,000,000" in penalty for penalty in sections["penalties"])
    assert any("imprisonment" in penalty for penalty in sections["penalties"])

def test_extract_entities(sample_regulatory_text):
    """Test extraction of entities."""
    entities = extract_entities(sample_regulatory_text)
    
    # Verify entities
    assert "organizations" in entities
    assert any("personal data" in term for term in entities["regulatory_terms"])
    assert any("data protection" in term or "data" in term for term in entities["regulatory_terms"])
    assert any("$10,000,000" in money or "10,000,000" in money for money in entities["money"])
    assert any("2%" in percent for percent in entities["percent"])

@patch('app.utils.pdf_analyzer.extract_text_from_pdf')
def test_analyze_pdf_document(mock_extract_text, sample_regulatory_text, tmp_path):
    """Test full PDF document analysis."""
    # Mock text extraction
    mock_extract_text.return_value = sample_regulatory_text
    
    # Create temp file
    pdf_path = os.path.join(tmp_path, "test.pdf")
    with open(pdf_path, 'wb') as f:
        f.write(b'Dummy PDF content')
    
    # Test function
    result = analyze_pdf_document(pdf_path)
    
    # Verify result
    assert "regulatory_sections" in result
    assert "entities" in result
    assert "confidence_score" in result
    assert "summary" in result
    assert result["confidence_score"] > 0.5  # Should have high confidence
    assert "article" in result["summary"].lower()
    assert "requirements" in result["summary"].lower()
    assert "penalties" in result["summary"].lower()
