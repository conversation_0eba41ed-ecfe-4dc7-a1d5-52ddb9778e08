
import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
import json
import os
import sys

# Add root directory to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from app.main import app
from app.db.models import Regulator, Country
from app.schemas.schemas import RegulatorCreate, RegulatorUpdate

client = TestClient(app)

class TestRegulatorAPIComprehensive:
    @patch('app.db.database.get_db')
    def test_get_regulators(self, mock_get_db):
        """Test getting all regulators."""
        # Setup mock session
        mock_session = MagicMock()
        mock_get_db.return_value.__enter__.return_value = mock_session
        
        # Setup mock query results
        mock_regulators = [
            MagicMock(id=1, name="Financial Conduct Authority", country_id=1, 
                     to_dict=lambda: {"id": 1, "name": "Financial Conduct Authority", "country_id": 1}),
            MagicMock(id=2, name="Securities and Exchange Commission", country_id=2,
                     to_dict=lambda: {"id": 2, "name": "Securities and Exchange Commission", "country_id": 2})
        ]
        mock_session.query.return_value.all.return_value = mock_regulators
        
        # Make request
        response = client.get("/api/v1/regulators")
        
        # Check response
        assert response.status_code == 200
        assert len(response.json()) == 2
        assert response.json()[0]["name"] == "Financial Conduct Authority"
        assert response.json()[1]["name"] == "Securities and Exchange Commission"
    
    @patch('app.db.database.get_db')
    def test_get_regulator_by_id(self, mock_get_db):
        """Test getting a specific regulator by ID."""
        # Setup mock session
        mock_session = MagicMock()
        mock_get_db.return_value.__enter__.return_value = mock_session
        
        # Setup mock query result
        mock_regulator = MagicMock(id=1, name="Financial Conduct Authority", country_id=1)
        mock_regulator.to_dict.return_value = {
            "id": 1, 
            "name": "Financial Conduct Authority", 
            "country_id": 1,
            "url": "https://www.fca.org.uk"
        }
        mock_session.query.return_value.filter.return_value.first.return_value = mock_regulator
        
        # Make request
        response = client.get("/api/v1/regulators/1")
        
        # Check response
        assert response.status_code == 200
        assert response.json()["id"] == 1
        assert response.json()["name"] == "Financial Conduct Authority"
    
    @patch('app.db.database.get_db')
    def test_get_regulator_not_found(self, mock_get_db):
        """Test response when regulator is not found."""
        # Setup mock session
        mock_session = MagicMock()
        mock_get_db.return_value.__enter__.return_value = mock_session
        
        # Setup mock query result (not found)
        mock_session.query.return_value.filter.return_value.first.return_value = None
        
        # Make request
        response = client.get("/api/v1/regulators/999")
        
        # Check response
        assert response.status_code == 404
        assert "detail" in response.json()
        assert "not found" in response.json()["detail"].lower()
    
    @patch('app.db.database.get_db')
    def test_create_regulator(self, mock_get_db):
        """Test creating a new regulator."""
        # Setup mock session
        mock_session = MagicMock()
        mock_get_db.return_value.__enter__.return_value = mock_session
        
        # Setup country check
        mock_country = MagicMock(id=1, name="United Kingdom")
        mock_session.query.return_value.filter.return_value.first.return_value = mock_country
        
        # Setup regulator creation
        new_regulator_data = {
            "name": "New Regulatory Body",
            "country_id": 1,
            "url": "https://example.gov/regulator"
        }
        
        # Make request
        response = client.post("/api/v1/regulators", json=new_regulator_data)
        
        # Check response
        assert response.status_code == 201
        assert mock_session.add.called
        assert mock_session.commit.called
    
    @patch('app.db.database.get_db')
    def test_update_regulator(self, mock_get_db):
        """Test updating an existing regulator."""
        # Setup mock session
        mock_session = MagicMock()
        mock_get_db.return_value.__enter__.return_value = mock_session
        
        # Setup existing regulator
        mock_regulator = MagicMock(id=1, name="Old Name", country_id=1, url="https://old-url.com")
        mock_session.query.return_value.filter.return_value.first.return_value = mock_regulator
        
        # Update data
        update_data = {
            "name": "Updated Regulator Name"
        }
        
        # Make request
        response = client.patch("/api/v1/regulators/1", json=update_data)
        
        # Check response
        assert response.status_code == 200
        # Verify the attribute was updated
        assert mock_regulator.name == "Updated Regulator Name"
        assert mock_session.commit.called
    
    @patch('app.db.database.get_db')
    def test_delete_regulator(self, mock_get_db):
        """Test deleting a regulator."""
        # Setup mock session
        mock_session = MagicMock()
        mock_get_db.return_value.__enter__.return_value = mock_session
        
        # Setup existing regulator
        mock_regulator = MagicMock(id=1, name="Regulator to Delete", country_id=1)
        mock_session.query.return_value.filter.return_value.first.return_value = mock_regulator
        
        # Make delete request
        response = client.delete("/api/v1/regulators/1")
        
        # Check response
        assert response.status_code == 204
        assert mock_session.delete.called
        assert mock_session.commit.called
    
    @patch('app.db.database.get_db')
    def test_delete_regulator_not_found(self, mock_get_db):
        """Test attempting to delete a non-existent regulator."""
        # Setup mock session
        mock_session = MagicMock()
        mock_get_db.return_value.__enter__.return_value = mock_session
        
        # Setup non-existent regulator
        mock_session.query.return_value.filter.return_value.first.return_value = None
        
        # Make delete request
        response = client.delete("/api/v1/regulators/999")
        
        # Check response
        assert response.status_code == 404
        assert "detail" in response.json()
