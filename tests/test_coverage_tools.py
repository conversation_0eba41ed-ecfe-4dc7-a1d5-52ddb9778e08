
import pytest
import sys
import os
from unittest.mock import patch, MagicMock, mock_open

# Add scripts directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'scripts'))

from scripts.run_tests_with_coverage import (
    run_tests_with_coverage,
    get_coverage_percentage,
    parse_coverage_report
)

class TestCoverageTools:
    @patch('scripts.run_tests_with_coverage.subprocess.run')
    def test_run_tests_with_coverage(self, mock_run):
        # Setup mock
        mock_process = MagicMock()
        mock_process.returncode = 0
        mock_process.stdout = b"Ran 10 tests in 0.5s\n\nOK"
        mock_run.return_value = mock_process
        
        # Run the function
        result = run_tests_with_coverage()
        
        # Verify subprocess was called correctly
        assert mock_run.called
        # Verify return code
        assert result == 0
    
    @patch('scripts.run_tests_with_coverage.subprocess.run')
    def test_run_tests_with_coverage_failure(self, mock_run):
        # Setup mock for failure
        mock_process = MagicMock()
        mock_process.returncode = 1
        mock_process.stdout = b"Ran 10 tests in 0.5s\n\nFAILED (failures=1)"
        mock_run.return_value = mock_process
        
        # Run the function
        result = run_tests_with_coverage()
        
        # Verify subprocess was called
        assert mock_run.called
        # Verify return code
        assert result == 1
    
    def test_get_coverage_percentage(self):
        # Test with valid coverage report line
        report_line = "TOTAL                             123     45     78     0%   45-67, 89-123"
        result = get_coverage_percentage(report_line)
        assert result == 0
        
        # Test with another valid line
        report_line = "TOTAL                             123     23     100    81%"
        result = get_coverage_percentage(report_line)
        assert result == 81
        
        # Test with invalid line
        report_line = "Something else entirely"
        result = get_coverage_percentage(report_line)
        assert result is None
    
    @patch('builtins.open', new_callable=mock_open)
    def test_parse_coverage_report(self, mock_file):
        # Mock the coverage report file
        mock_file.return_value.__enter__.return_value.readlines.return_value = [
            "Name                 Stmts   Miss  Cover   Missing",
            "------------------------------------------------",
            "app/__init__.py         10      0   100%",
            "app/main.py             45     15    67%   45-67, 89-123",
            "models.py               25      1    96%   45",
            "------------------------------------------------",
            "TOTAL                   80     16    80%"
        ]
        
        # Call function
        result = parse_coverage_report()
        
        # Verify result
        assert isinstance(result, dict)
        assert result["total"] == 80
        assert result["modules"]["app/__init__.py"] == 100
        assert result["modules"]["app/main.py"] == 67
        assert result["modules"]["models.py"] == 96
