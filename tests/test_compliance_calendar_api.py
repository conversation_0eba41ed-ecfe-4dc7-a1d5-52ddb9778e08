
"""
Tests for the compliance calendar API endpoints.
"""
import pytest
from fastapi.testclient import TestClient
from datetime import datetime, timedelta

from app.main import app

client = TestClient(app)


class TestComplianceCalendarAPI:
    
    def test_get_calendar_events(self):
        """Test getting calendar events."""
        response = client.get("/api/v1/calendar/events")
        
        # Check response
        assert response.status_code == 200
        events = response.json()
        
        # Check content
        assert isinstance(events, list)
        assert len(events) > 0
        
        # Check event structure
        for event in events:
            assert "title" in event
            assert "start" in event
            assert "end" in event
            assert "backgroundColor" in event
            assert "extendedProps" in event
            assert "category" in event["extendedProps"]
            assert "jurisdiction" in event["extendedProps"]
            assert "description" in event["extendedProps"]
            assert "deadlineType" in event["extendedProps"]
            assert "action" in event["extendedProps"]
    
    def test_filter_events_by_category(self):
        """Test filtering events by category."""
        response = client.get("/api/v1/calendar/events?category=finance")
        
        # Check response
        assert response.status_code == 200
        events = response.json()
        
        # Check that all returned events have the specified category
        assert all(e["extendedProps"]["category"] == "finance" for e in events)
    
    def test_filter_events_by_jurisdiction(self):
        """Test filtering events by jurisdiction."""
        response = client.get("/api/v1/calendar/events?jurisdiction=United+States")
        
        # Check response
        assert response.status_code == 200
        events = response.json()
        
        # Check that all returned events have the specified jurisdiction
        assert all("United States" in e["extendedProps"]["jurisdiction"] for e in events)
