
"""
Tests for the scheduled digests API endpoints.
"""
import pytest
from fastapi.testclient import TestClient
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

from app.main import app
from app.api import scheduled_digests

client = TestClient(app)

class TestScheduledDigestsAPI:
    
    def test_get_quarterly_digest(self):
        """Test getting a quarterly digest."""
        # Test with default parameters
        response = client.get("/api/v1/digests/quarterly")
        
        # Check response
        assert response.status_code == 200
        digest = response.json()
        
        # Check digest structure
        assert "period" in digest
        assert "summary" in digest
        assert "changes" in digest
        assert "highlights" in digest
        
        # Check period structure
        period = digest["period"]
        assert "start_date" in period
        assert "end_date" in period
        assert "label" in period
        assert "previous_period" in period
        
        # Check summary structure
        summary = digest["summary"]
        assert "total_changes" in summary
        assert "by_importance" in summary
        assert "by_category" in summary
        assert "by_country" in summary
        assert "top_regulators" in summary
        
    def test_get_quarterly_digest_with_params(self):
        """Test getting a quarterly digest with specific parameters."""
        response = client.get("/api/v1/digests/quarterly?year=2023&quarter=2")
        
        # Check response
        assert response.status_code == 200
        digest = response.json()
        
        # Check period
        period = digest["period"]
        assert "Q2 2023" in period["label"]
        
        # Parse dates and verify they're in Q2 2023
        start_date = datetime.fromisoformat(period["start_date"].replace("Z", "+00:00"))
        end_date = datetime.fromisoformat(period["end_date"].replace("Z", "+00:00"))
        
        assert start_date.year == 2023
        assert start_date.month == 4  # April (start of Q2)
        assert start_date.day == 1
        
        assert end_date.year == 2023
        assert end_date.month == 6   # June (end of Q2)
        assert end_date.day == 30
        
    def test_get_monthly_digest(self):
        """Test getting a monthly digest."""
        # Test with default parameters
        response = client.get("/api/v1/digests/monthly")
        
        # Check response
        assert response.status_code == 200
        digest = response.json()
        
        # Check digest structure
        assert "period" in digest
        assert "summary" in digest
        assert "changes" in digest
        assert "highlights" in digest
        
    def test_get_quarterly_digest_html(self):
        """Test getting a quarterly digest in HTML format."""
        response = client.get("/api/v1/digests/quarterly/html")
        
        # Check response
        assert response.status_code == 200
        assert response.headers["content-type"] == "text/html; charset=utf-8"
        
        # Check content
        content = response.text
        assert "<html" in content
        assert "Quarterly Regulatory Digest" in content
        assert "Highlights" in content
        assert "All Changes" in content
