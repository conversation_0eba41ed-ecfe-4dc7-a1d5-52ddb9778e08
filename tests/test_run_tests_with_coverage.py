
import pytest
import os
import sys
from unittest.mock import patch, MagicMock
from pathlib import Path

# Import the module to test
from scripts.run_tests_with_coverage import run_tests_with_coverage

class TestRunTestsWithCoverage:
    
    @patch('subprocess.run')
    @patch('os.makedirs')
    def test_run_tests_with_coverage_success(self, mock_makedirs, mock_run):
        # Setup mocks
        mock_process = MagicMock()
        mock_process.returncode = 0
        mock_process.stdout = "TOTAL                             123     45     78    63%   45-67, 89-123"
        mock_process.stderr = ""
        mock_run.return_value = mock_process
        
        # Run the function
        result = run_tests_with_coverage()
        
        # Verify function behavior
        mock_makedirs.assert_called_once_with("coverage/html", exist_ok=True)
        mock_run.assert_called_once()
        assert result == 0
    
    @patch('subprocess.run')
    @patch('os.makedirs')
    def test_run_tests_with_coverage_failure(self, mock_makedirs, mock_run):
        # Setup mocks for failure
        mock_process = MagicMock()
        mock_process.returncode = 1
        mock_process.stdout = "Some test failures"
        mock_process.stderr = "Error details"
        mock_run.return_value = mock_process
        
        # Run the function
        result = run_tests_with_coverage()
        
        # Verify function behavior
        mock_makedirs.assert_called_once_with("coverage/html", exist_ok=True)
        mock_run.assert_called_once()
        assert result == 1
    
    @patch('subprocess.run')
    @patch('os.makedirs')
    @patch('os.path.exists')
    @patch('builtins.open', new_callable=MagicMock)
    @patch('webbrowser.open')
    @patch('json.dump')
    def test_run_tests_with_coverage_html_report(self, mock_json_dump, mock_webbrowser, 
                                                 mock_open, mock_exists, mock_makedirs, mock_run):
        # Setup mocks
        mock_process = MagicMock()
        mock_process.returncode = 0
        mock_process.stdout = "TOTAL                             123     45     78    63%   45-67, 89-123"
        mock_run.return_value = mock_process
        
        # Make Path.exists return True for the HTML file
        mock_exists.return_value = True
        
        # Run the function
        result = run_tests_with_coverage()
        
        # Verify function behavior
        mock_makedirs.assert_called_once_with("coverage/html", exist_ok=True)
        mock_run.assert_called_once()
        mock_webbrowser.assert_called_once()
        assert result == 0
    
    @patch('subprocess.run')
    @patch('os.makedirs')
    @patch('builtins.open', side_effect=Exception("File error"))
    def test_run_tests_with_coverage_error_handling(self, mock_open, mock_makedirs, mock_run):
        # Setup mocks
        mock_process = MagicMock()
        mock_process.returncode = 0
        mock_process.stdout = "TOTAL                             123     45     78    63%   45-67, 89-123"
        mock_run.return_value = mock_process
        
        # Run the function - should handle file opening error
        result = run_tests_with_coverage()
        
        # Verify function still completes
        assert result == 0
