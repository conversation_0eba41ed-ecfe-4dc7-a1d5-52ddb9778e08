"""
Basic unit tests to verify test framework
"""
import pytest
import sys
import os
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from fastapi.testclient import TestClient
from app.main import app
from app.db.models import Base
from app.db.database import engine

# Create all tables for testing
Base.metadata.create_all(bind=engine)


class TestBasicFunctionality:
    """Basic functionality tests"""
    
    def setup_method(self):
        """Set up test client"""
        self.client = TestClient(app)
    
    def test_app_exists(self):
        """Test that the app exists and is configured"""
        assert app is not None
        assert hasattr(app, 'title')
        assert app.title == "RegulationGuru API"
    
    def test_health_endpoint(self):
        """Test health endpoint"""
        response = self.client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert data["status"] == "healthy"
    
    def test_root_endpoint(self):
        """Test root endpoint"""
        response = self.client.get("/")
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
    
    def test_api_health_endpoint(self):
        """Test API health endpoint"""
        response = self.client.get("/api/v1/health")
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert data["status"] == "healthy"
    
    def test_openapi_docs(self):
        """Test OpenAPI documentation"""
        response = self.client.get("/docs")
        assert response.status_code == 200
    
    def test_enhanced_dashboard(self):
        """Test enhanced dashboard"""
        response = self.client.get("/enhanced-dashboard")
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
    
    def test_modern_dashboard(self):
        """Test modern dashboard"""
        response = self.client.get("/modern-dashboard")
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
    
    def test_interactive_dashboard(self):
        """Test interactive dashboard"""
        response = self.client.get("/interactive-dashboard")
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
    
    def test_404_handling(self):
        """Test 404 error handling"""
        response = self.client.get("/nonexistent-endpoint")
        assert response.status_code == 404
    
    def test_method_not_allowed(self):
        """Test method not allowed handling"""
        response = self.client.post("/health")
        assert response.status_code == 405


class TestAPIEndpoints:
    """Test API endpoints"""
    
    def setup_method(self):
        """Set up test client"""
        self.client = TestClient(app)
    
    def test_countries_endpoint(self):
        """Test countries endpoint"""
        response = self.client.get("/api/v1/countries")
        # May not be implemented, so accept 404
        assert response.status_code in [200, 404]
    
    def test_regulators_endpoint(self):
        """Test regulators endpoint"""
        response = self.client.get("/api/v1/regulators")
        assert response.status_code in [200, 404]
    
    def test_regulation_urls_endpoint(self):
        """Test regulation URLs endpoint"""
        response = self.client.get("/api/v1/regulation-urls")
        assert response.status_code in [200, 404]
    
    def test_api_versioning(self):
        """Test API versioning"""
        response = self.client.get("/api/v1/health")
        assert response.status_code == 200
        
        # Test that v1 is in the path
        assert "/api/v1/" in str(response.url)


class TestStaticFiles:
    """Test static file serving"""
    
    def setup_method(self):
        """Set up test client"""
        self.client = TestClient(app)
    
    def test_css_files(self):
        """Test CSS file serving"""
        css_files = [
            "/static/css/main.css",
            "/static/css/enhanced-ui.css",
            "/static/css/advanced-ui.css"
        ]
        
        for css_file in css_files:
            response = self.client.get(css_file)
            # Files may or may not exist
            assert response.status_code in [200, 404]
    
    def test_js_files(self):
        """Test JavaScript file serving"""
        js_files = [
            "/static/js/main.js",
            "/static/js/enhanced-ui.js",
            "/static/js/modern-dashboard.js"
        ]
        
        for js_file in js_files:
            response = self.client.get(js_file)
            assert response.status_code in [200, 404]


class TestResponseFormats:
    """Test response formats"""
    
    def setup_method(self):
        """Set up test client"""
        self.client = TestClient(app)
    
    def test_json_responses(self):
        """Test JSON responses"""
        response = self.client.get("/api/v1/health")
        assert response.status_code == 200
        assert "application/json" in response.headers["content-type"]
        
        # Should be valid JSON
        data = response.json()
        assert isinstance(data, dict)
    
    def test_html_responses(self):
        """Test HTML responses"""
        response = self.client.get("/")
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
        
        # Should contain HTML
        content = response.text
        assert "<html" in content.lower()
    
    def test_content_length(self):
        """Test content length headers"""
        response = self.client.get("/health")
        assert response.status_code == 200
        
        # Should have content
        assert len(response.content) > 0


class TestErrorHandling:
    """Test error handling"""
    
    def setup_method(self):
        """Set up test client"""
        self.client = TestClient(app)
    
    def test_invalid_json(self):
        """Test invalid JSON handling"""
        response = self.client.post(
            "/api/v1/countries",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        # Should handle invalid JSON gracefully
        assert response.status_code in [400, 404, 405, 422]
    
    def test_large_request(self):
        """Test large request handling"""
        large_data = {"data": "x" * 1000}
        response = self.client.post("/api/v1/countries", json=large_data)
        # Should handle large requests
        assert response.status_code in [200, 400, 404, 405, 413, 422]
    
    def test_invalid_headers(self):
        """Test invalid headers handling"""
        response = self.client.get(
            "/health",
            headers={"Invalid-Header": "invalid\nvalue"}
        )
        # Should handle gracefully
        assert response.status_code in [200, 400]


if __name__ == "__main__":
    pytest.main([__file__])
