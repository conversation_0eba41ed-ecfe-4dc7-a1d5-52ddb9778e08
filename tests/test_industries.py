"""
Tests for the industries API.
"""
import pytest
from fastapi.testclient import TestClient

from app.main import app

client = TestClient(app)

def test_get_industries(client, test_data):
    """Test getting a list of industries."""
    response = client.get("/api/v1/industries/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) > 0
    assert response.json()[0]["name"] == test_data["industry"].name

def test_get_industries_with_search(client, test_data):
    """Test getting industries with search filter."""
    response = client.get("/api/v1/industries/?search=Test")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) > 0
    assert response.json()[0]["name"] == test_data["industry"].name

def test_get_industry(client, test_data):
    """Test getting a specific industry by ID."""
    response = client.get(f"/api/v1/industries/{test_data['industry'].id}")
    assert response.status_code == 200
    assert response.json()["name"] == test_data["industry"].name
    assert response.json()["description"] == test_data["industry"].description
    assert response.json()["sector"] == test_data["industry"].sector

def test_get_industry_not_found(client):
    """Test getting a non-existent industry."""
    response = client.get("/api/v1/industries/999")
    assert response.status_code == 404
    assert response.json()["detail"] == "Industry not found"

def test_create_industry(client):
    """Test creating a new industry."""
    industry_data = {
        "name": "New Test Industry",
        "description": "New test industry description",
        "sector": "Technology"
    }
    response = client.post("/api/v1/industries/", json=industry_data)
    assert response.status_code == 201
    assert response.json()["name"] == industry_data["name"]
    assert response.json()["description"] == industry_data["description"]
    assert response.json()["sector"] == industry_data["sector"]

def test_create_industry_duplicate_name(client, test_data):
    """Test creating an industry with a duplicate name."""
    industry_data = {
        "name": test_data["industry"].name,
        "description": "Duplicate industry description",
        "sector": "Technology"
    }
    response = client.post("/api/v1/industries/", json=industry_data)
    assert response.status_code == 400
    assert "already exists" in response.json()["detail"]

def test_update_industry(client, test_data):
    """Test updating an industry."""
    industry_data = {
        "name": "Updated Test Industry",
        "description": "Updated test industry description",
        "sector": "Healthcare"
    }
    response = client.put(f"/api/v1/industries/{test_data['industry'].id}", json=industry_data)
    assert response.status_code == 200
    assert response.json()["name"] == industry_data["name"]
    assert response.json()["description"] == industry_data["description"]
    assert response.json()["sector"] == industry_data["sector"]

def test_update_industry_not_found(client):
    """Test updating a non-existent industry."""
    industry_data = {
        "name": "Updated Test Industry",
        "description": "Updated test industry description",
        "sector": "Healthcare"
    }
    response = client.put("/api/v1/industries/999", json=industry_data)
    assert response.status_code == 404
    assert response.json()["detail"] == "Industry not found"

def test_update_industry_duplicate_name(client, test_data):
    """Test updating an industry with a duplicate name."""
    # Create a new industry first
    new_industry_data = {
        "name": "Another Test Industry",
        "description": "Another test industry description",
        "sector": "Technology"
    }
    new_industry_response = client.post("/api/v1/industries/", json=new_industry_data)
    assert new_industry_response.status_code == 201
    new_industry_id = new_industry_response.json()["id"]
    
    # Try to update the new industry with the name of the existing industry
    update_data = {
        "name": test_data["industry"].name,
        "description": "Updated description",
        "sector": "Healthcare"
    }
    response = client.put(f"/api/v1/industries/{new_industry_id}", json=update_data)
    assert response.status_code == 400
    assert "already exists" in response.json()["detail"]

def test_delete_industry(client):
    """Test deleting an industry."""
    # Create a new industry first
    industry_data = {
        "name": "Industry to Delete",
        "description": "Industry to be deleted",
        "sector": "Technology"
    }
    create_response = client.post("/api/v1/industries/", json=industry_data)
    assert create_response.status_code == 201
    industry_id = create_response.json()["id"]
    
    # Delete the industry
    response = client.delete(f"/api/v1/industries/{industry_id}")
    assert response.status_code == 204
    
    # Verify the industry is deleted
    get_response = client.get(f"/api/v1/industries/{industry_id}")
    assert get_response.status_code == 404

def test_delete_industry_not_found(client):
    """Test deleting a non-existent industry."""
    response = client.delete("/api/v1/industries/999")
    assert response.status_code == 404
    assert response.json()["detail"] == "Industry not found"

def test_delete_industry_with_regulations(client, test_data):
    """Test deleting an industry that has associated regulations."""
    response = client.delete(f"/api/v1/industries/{test_data['industry'].id}")
    assert response.status_code == 400
    assert "Cannot delete industry that is associated with" in response.json()["detail"]

def test_get_regulations_by_industry(client, test_data):
    """Test getting regulations associated with an industry."""
    response = client.get(f"/api/v1/industries/{test_data['industry'].id}/regulations")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) > 0
    assert response.json()[0]["title"] == test_data["regulation"].title
