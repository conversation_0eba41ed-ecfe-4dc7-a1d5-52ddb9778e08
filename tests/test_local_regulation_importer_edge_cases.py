"""
Edge case and error scenario tests for LocalRegulationImporter.

Tests cover error conditions, edge cases, and failure scenarios
to ensure robust error handling and recovery.
"""

import os
import tempfile
import shutil
import pytest
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

import pandas as pd
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.db.models import Base
from app.db.models.regulations_csv import RegulationCSVRecord, RegulationCSVImportLog
from app.services.local_regulation_importer import LocalRegulationImporter, LocalStorageConfig
from app.schemas.regulations_csv import RegulationCSVImportResult


class TestErrorHandling:
    """Test error handling scenarios."""
    
    @pytest.fixture
    def temp_storage(self):
        """Create temporary storage for testing."""
        temp_dir = tempfile.mkdtemp()
        config = LocalStorageConfig(temp_dir)
        yield config
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def mock_db(self):
        """Create mock database session."""
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        SessionLocal = sessionmaker(bind=engine)
        return SessionLocal()
    
    @pytest.fixture
    def importer(self, mock_db, temp_storage):
        """Create LocalRegulationImporter instance for testing."""
        return LocalRegulationImporter(mock_db, temp_storage)
    
    def test_validate_file_permission_denied(self, importer, temp_storage):
        """Test validation when file permissions are denied."""
        file_path = temp_storage.imports_path / "permission_test.csv"
        file_path.write_text("test content")
        
        # Mock permission error
        with patch('pandas.read_csv', side_effect=PermissionError("Permission denied")):
            is_valid, errors = importer.validate_file(file_path)
            assert not is_valid
            assert any("Permission denied" in error for error in errors)
    
    def test_validate_file_corrupted_csv(self, importer, temp_storage):
        """Test validation with corrupted CSV file."""
        file_path = temp_storage.imports_path / "corrupted.csv"
        # Create file with binary content that looks like CSV but isn't
        with open(file_path, 'wb') as f:
            f.write(b'\x00\x01\x02\x03Country_Name,Country_Code\n\x04\x05\x06\x07')
        
        is_valid, errors = importer.validate_file(file_path)
        assert not is_valid
        assert len(errors) > 0
    
    def test_validate_file_extremely_large(self, importer, temp_storage):
        """Test validation with file exceeding size limit."""
        file_path = temp_storage.imports_path / "large.csv"
        
        # Mock file size to be over limit
        with patch.object(Path, 'stat') as mock_stat:
            mock_stat.return_value.st_size = 200 * 1024 * 1024  # 200MB
            
            is_valid, errors = importer.validate_file(file_path)
            assert not is_valid
            assert any("too large" in error.lower() for error in errors)
    
    def test_backup_file_permission_error(self, importer, temp_storage):
        """Test backup when backup directory is not writable."""
        file_path = temp_storage.imports_path / "test.csv"
        file_path.write_text("test content")
        
        # Make backup directory read-only
        temp_storage.backups_path.chmod(0o444)
        
        try:
            with pytest.raises(PermissionError):
                importer.backup_file(file_path)
        finally:
            # Restore permissions for cleanup
            temp_storage.backups_path.chmod(0o755)
    
    def test_move_file_destination_exists(self, importer, temp_storage):
        """Test moving file when destination already exists."""
        source_file = temp_storage.imports_path / "source.csv"
        source_file.write_text("source content")
        
        # Create file with same timestamp in processed directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        existing_file = temp_storage.processed_path / f"source_{timestamp}.csv"
        existing_file.write_text("existing content")
        
        # This should still work as the timestamp will be slightly different
        new_path = importer.move_file_to_processed(source_file, success=True)
        assert new_path.exists()
        assert not source_file.exists()
    
    def test_calculate_file_hash_missing_file(self, importer, temp_storage):
        """Test hash calculation on missing file."""
        missing_file = temp_storage.imports_path / "missing.csv"
        
        with pytest.raises(FileNotFoundError):
            importer.calculate_file_hash(missing_file)
    
    @patch('app.services.local_regulation_importer.RegulationCSVProcessor')
    def test_import_single_file_processor_exception(self, mock_processor_class, importer, temp_storage):
        """Test import when CSV processor raises exception."""
        # Setup mock to raise exception
        mock_processor = Mock()
        mock_processor_class.return_value = mock_processor
        mock_processor.import_csv_file.side_effect = Exception("Processing failed")
        
        # Create valid test file
        file_path = temp_storage.imports_path / "test.csv"
        csv_content = """Country_Name,Country_Code,Document_Title,Document_Type,Issuing_Authority,Publication_Date,Effective_Date,Legal_Status,Document_URL,Language,Scope_Application,Key_Compliance_Requirements,Enforcement_Mechanisms,Penalties,Cross_Border_Elements,Data_Protection_Provisions,Incident_Reporting_Requirements,Risk_Management_Mandates,Third_Party_Requirements,Audit_Obligations,Certification_Requirements,Implementation_Timeline,International_Standards_Alignment,Extraterritorial_Reach,Safe_Harbor_Provisions,Industry_Specific_Provisions,Technology_Specific_Provisions
US,US,Test,Federal Law,Authority,2024-01-01,2024-01-01,Binding,http://test.com,English,scope,requirements,enforcement,penalties,cross-border,data protection,incident reporting,risk management,third party,audit,certification,timeline,standards,extraterritorial,safe harbor,industry,technology"""
        file_path.write_text(csv_content)
        
        result = importer.import_single_file(file_path, validate_first=False)
        
        # Should return error result
        assert result.successful_imports == 0
        assert result.failed_imports == 0
        assert len(result.errors) > 0
        assert "Processing failed" in str(result.errors[0])
        
        # File should be moved to failed directory
        assert not file_path.exists()
        assert len(list(temp_storage.failed_path.glob("*"))) == 1
    
    def test_import_single_file_move_failure(self, importer, temp_storage):
        """Test import when file move operation fails."""
        file_path = temp_storage.imports_path / "test.csv"
        file_path.write_text("invalid csv content")
        
        # Make processed directory read-only to cause move failure
        temp_storage.failed_path.chmod(0o444)
        
        try:
            result = importer.import_single_file(file_path)
            # Should still return error result even if move fails
            assert len(result.errors) > 0
        finally:
            # Restore permissions
            temp_storage.failed_path.chmod(0o755)
    
    def test_cleanup_old_backups_permission_error(self, importer, temp_storage):
        """Test cleanup when backup files cannot be deleted."""
        # Create backup file
        backup_file = temp_storage.backups_path / "old_backup.csv"
        backup_file.write_text("content")
        
        # Make file read-only
        backup_file.chmod(0o444)
        
        # Make the file appear old
        old_time = (datetime.now().timestamp() - 35 * 24 * 60 * 60)
        os.utime(backup_file, (old_time, old_time))
        
        try:
            # Should handle permission error gracefully
            cleaned_count = importer.cleanup_old_backups(days_to_keep=30)
            assert cleaned_count == 0  # File couldn't be deleted
            assert backup_file.exists()
        finally:
            # Restore permissions for cleanup
            backup_file.chmod(0o644)


class TestEdgeCases:
    """Test edge cases and boundary conditions."""
    
    @pytest.fixture
    def temp_storage(self):
        """Create temporary storage for testing."""
        temp_dir = tempfile.mkdtemp()
        config = LocalStorageConfig(temp_dir)
        yield config
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def mock_db(self):
        """Create mock database session."""
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        SessionLocal = sessionmaker(bind=engine)
        return SessionLocal()
    
    @pytest.fixture
    def importer(self, mock_db, temp_storage):
        """Create LocalRegulationImporter instance for testing."""
        return LocalRegulationImporter(mock_db, temp_storage)
    
    def test_discover_files_with_unicode_names(self, importer, temp_storage):
        """Test file discovery with Unicode filenames."""
        # Create files with Unicode names
        unicode_file1 = temp_storage.imports_path / "规章制度_2024.csv"
        unicode_file2 = temp_storage.imports_path / "règlement_français.csv"
        unicode_file3 = temp_storage.imports_path / "регламент_русский.csv"
        
        for file_path in [unicode_file1, unicode_file2, unicode_file3]:
            file_path.write_text("test,content\nvalue1,value2")
        
        files = importer.discover_import_files()
        assert len(files) == 3
        
        # Verify all files are discovered correctly
        file_names = [f.name for f in files]
        assert "规章制度_2024.csv" in file_names
        assert "règlement_français.csv" in file_names
        assert "регламент_русский.csv" in file_names
    
    def test_validate_file_with_bom(self, importer, temp_storage):
        """Test validation of CSV file with BOM (Byte Order Mark)."""
        file_path = temp_storage.imports_path / "bom_test.csv"
        
        # Create CSV with BOM
        csv_content = """Country_Name,Country_Code,Document_Title,Document_Type,Issuing_Authority,Publication_Date,Effective_Date,Legal_Status,Document_URL,Language,Scope_Application,Key_Compliance_Requirements,Enforcement_Mechanisms,Penalties,Cross_Border_Elements,Data_Protection_Provisions,Incident_Reporting_Requirements,Risk_Management_Mandates,Third_Party_Requirements,Audit_Obligations,Certification_Requirements,Implementation_Timeline,International_Standards_Alignment,Extraterritorial_Reach,Safe_Harbor_Provisions,Industry_Specific_Provisions,Technology_Specific_Provisions
US,US,Test,Federal Law,Authority,2024-01-01,2024-01-01,Binding,http://test.com,English,scope,requirements,enforcement,penalties,cross-border,data protection,incident reporting,risk management,third party,audit,certification,timeline,standards,extraterritorial,safe harbor,industry,technology"""
        
        with open(file_path, 'wb') as f:
            f.write('\ufeff'.encode('utf-8'))  # BOM
            f.write(csv_content.encode('utf-8'))
        
        is_valid, errors = importer.validate_file(file_path)
        assert is_valid  # Should handle BOM correctly
    
    def test_validate_file_different_encodings(self, importer, temp_storage):
        """Test validation of files with different encodings."""
        # Test with Latin-1 encoding
        file_path = temp_storage.imports_path / "latin1_test.csv"
        content = "Country_Name,Country_Code\nFrançais,FR"
        
        with open(file_path, 'w', encoding='latin-1') as f:
            f.write(content)
        
        # Should handle encoding gracefully (pandas usually auto-detects)
        is_valid, errors = importer.validate_file(file_path)
        # May or may not be valid depending on pandas behavior, but shouldn't crash
        assert isinstance(is_valid, bool)
        assert isinstance(errors, list)
    
    def test_import_duplicate_file_hash(self, importer, temp_storage, mock_db):
        """Test importing file that has already been processed (same hash)."""
        # Create existing import log
        existing_log = RegulationCSVImportLog(
            batch_id="existing_batch",
            file_name="existing.csv",
            file_hash="abc123def456",
            import_status="completed",
            total_records=1,
            successful_imports=1,
            failed_imports=0,
            updated_records=0
        )
        mock_db.add(existing_log)
        mock_db.commit()
        
        # Create test file
        file_path = temp_storage.imports_path / "duplicate.csv"
        csv_content = """Country_Name,Country_Code,Document_Title,Document_Type,Issuing_Authority,Publication_Date,Effective_Date,Legal_Status,Document_URL,Language,Scope_Application,Key_Compliance_Requirements,Enforcement_Mechanisms,Penalties,Cross_Border_Elements,Data_Protection_Provisions,Incident_Reporting_Requirements,Risk_Management_Mandates,Third_Party_Requirements,Audit_Obligations,Certification_Requirements,Implementation_Timeline,International_Standards_Alignment,Extraterritorial_Reach,Safe_Harbor_Provisions,Industry_Specific_Provisions,Technology_Specific_Provisions
US,US,Test,Federal Law,Authority,2024-01-01,2024-01-01,Binding,http://test.com,English,scope,requirements,enforcement,penalties,cross-border,data protection,incident reporting,risk management,third party,audit,certification,timeline,standards,extraterritorial,safe harbor,industry,technology"""
        file_path.write_text(csv_content)
        
        # Mock hash calculation to return existing hash
        with patch.object(importer, 'calculate_file_hash', return_value="abc123def456"):
            result = importer.import_single_file(file_path, validate_first=False)
        
        # Should detect duplicate and skip processing
        assert result.successful_imports == 0
        assert result.failed_imports == 0
        assert len(result.warnings) > 0
        assert "already processed" in str(result.warnings[0])
        
        # File should be moved to processed (not failed)
        assert not file_path.exists()
        assert len(list(temp_storage.processed_path.glob("*"))) == 1
    
    def test_get_storage_stats_with_subdirectories(self, importer, temp_storage):
        """Test storage stats calculation with subdirectories."""
        # Create files and subdirectories
        (temp_storage.imports_path / "file1.csv").write_text("content1")
        (temp_storage.imports_path / "subdir").mkdir()
        (temp_storage.imports_path / "subdir" / "file2.csv").write_text("content2")
        
        stats = importer.get_storage_stats()
        
        # Should only count files, not subdirectories
        assert stats["imports"]["file_count"] == 1  # Only file1.csv
        assert stats["imports"]["total_size_mb"] > 0
    
    def test_cleanup_old_backups_boundary_conditions(self, importer, temp_storage):
        """Test cleanup with boundary conditions (exactly at cutoff time)."""
        # Create file exactly at cutoff time
        cutoff_file = temp_storage.backups_path / "cutoff_test.csv"
        cutoff_file.write_text("content")
        
        # Set file time to exactly 30 days ago
        cutoff_time = datetime.now().timestamp() - (30 * 24 * 60 * 60)
        os.utime(cutoff_file, (cutoff_time, cutoff_time))
        
        # Should not delete file at exactly cutoff time
        cleaned_count = importer.cleanup_old_backups(days_to_keep=30)
        assert cleaned_count == 0
        assert cutoff_file.exists()
        
        # But should delete if slightly older
        older_time = cutoff_time - 1
        os.utime(cutoff_file, (older_time, older_time))
        
        cleaned_count = importer.cleanup_old_backups(days_to_keep=30)
        assert cleaned_count == 1
        assert not cutoff_file.exists()
    
    def test_import_all_files_mixed_success_failure(self, importer, temp_storage):
        """Test importing multiple files with mixed success and failure."""
        # Create valid file
        valid_file = temp_storage.imports_path / "valid.csv"
        valid_content = """Country_Name,Country_Code,Document_Title,Document_Type,Issuing_Authority,Publication_Date,Effective_Date,Legal_Status,Document_URL,Language,Scope_Application,Key_Compliance_Requirements,Enforcement_Mechanisms,Penalties,Cross_Border_Elements,Data_Protection_Provisions,Incident_Reporting_Requirements,Risk_Management_Mandates,Third_Party_Requirements,Audit_Obligations,Certification_Requirements,Implementation_Timeline,International_Standards_Alignment,Extraterritorial_Reach,Safe_Harbor_Provisions,Industry_Specific_Provisions,Technology_Specific_Provisions
US,US,Test,Federal Law,Authority,2024-01-01,2024-01-01,Binding,http://test.com,English,scope,requirements,enforcement,penalties,cross-border,data protection,incident reporting,risk management,third party,audit,certification,timeline,standards,extraterritorial,safe harbor,industry,technology"""
        valid_file.write_text(valid_content)
        
        # Create invalid file
        invalid_file = temp_storage.imports_path / "invalid.csv"
        invalid_file.write_text("invalid,content")
        
        # Mock processor for valid file
        with patch('app.services.local_regulation_importer.RegulationCSVProcessor') as mock_processor_class:
            mock_processor = Mock()
            mock_processor_class.return_value = mock_processor
            
            def side_effect(file_path, **kwargs):
                if "valid.csv" in file_path:
                    return RegulationCSVImportResult(
                        batch_id="valid_batch",
                        total_records=1,
                        successful_imports=1,
                        failed_imports=0,
                        updated_records=0,
                        errors=[],
                        warnings=[],
                        processing_time=1.0
                    )
                else:
                    raise Exception("Invalid file")
            
            mock_processor.import_csv_file.side_effect = side_effect
            
            results = importer.import_all_files()
        
        assert len(results) == 2
        
        # One should succeed, one should fail
        success_results = [r for r in results if r.successful_imports > 0]
        error_results = [r for r in results if len(r.errors) > 0]
        
        assert len(success_results) == 1
        assert len(error_results) == 1
        
        # Check file movements
        assert len(list(temp_storage.processed_path.glob("*"))) == 1  # Valid file
        assert len(list(temp_storage.failed_path.glob("*"))) == 1     # Invalid file
