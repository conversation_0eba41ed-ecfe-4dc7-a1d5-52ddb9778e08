
"""UI tests using selenium-headless."""
import pytest
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By


@pytest.fixture
def selenium_driver():
    """
    Create a headless Chrome driver for UI testing.
    
    Yields:
        webdriver.Chrome: The configured Chrome webdriver
    """
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    driver = webdriver.Chrome(options=chrome_options)
    yield driver
    driver.quit()


@pytest.mark.ui
def test_ui_example(selenium_driver):
    """
    Example UI test using selenium-headless.
    
    Args:
        selenium_driver (webdriver.Chrome): The selenium driver fixture
    """
    # This test is a placeholder - in a real app, you would test your UI
    # after the API server is running
    selenium_driver.get("https://replit.com")
    assert "Replit" in selenium_driver.title
