
"""Test cases for the database module."""
import pytest
from unittest.mock import patch, MagicMock
import os
from sqlalchemy.exc import SQLAlchemyError

from app.db import database


def test_get_db():
    """Test the get_db dependency with context manager pattern."""
    # Setup mock Session
    mock_db = MagicMock()
    
    with patch('database.SessionLocal', return_value=mock_db):
        # Get a generator from get_db()
        db_generator = database.get_db()
        
        # Get the db session from the generator
        db = next(db_generator)
        
        # Verify we got the mock session
        assert db == mock_db
        
        try:
            # Trying to advance the generator should raise StopIteration
            # This simulates the closing of the db session
            next(db_generator)
            pytest.fail("Generator should have been exhausted")
        except StopIteration:
            # This is expected
            pass
        
        # Verify close was called when generator completed
        mock_db.close.assert_called_once()


def test_get_db_with_exception():
    """Test the get_db dependency when an exception occurs."""
    # Setup mock Session
    mock_db = MagicMock()
    
    with patch('database.SessionLocal', return_value=mock_db):
        # Get a generator from get_db()
        db_generator = database.get_db()
        
        # Get the db session from the generator
        db = next(db_generator)
        
        # Simulate an exception in the route handler
        try:
            # Send an exception to the generator
            db_generator.throw(Exception("Test exception"))
            pytest.fail("Generator should have raised an exception")
        except Exception as e:
            # Verify we got our test exception
            assert str(e) == "Test exception"
        
        # Verify close was called even though an exception occurred
        mock_db.close.assert_called_once()


def test_engine_creation():
    """Test that the engine is created with the correct URL."""
    with patch('database.create_engine') as mock_create_engine:
        # Setup mock engine
        mock_engine = MagicMock()
        mock_create_engine.return_value = mock_engine
        
        # Import the module again to trigger engine creation
        with patch.dict(os.environ, {'DATABASE_URL': 'sqlite:///test_custom.db'}):
            # Force reload of the module
            import importlib
            importlib.reload(database)
            
            # Check that create_engine was called with the correct URL
            mock_create_engine.assert_called_once()
            args, kwargs = mock_create_engine.call_args
            assert 'sqlite:///test_custom.db' in args[0]
