
"""
Tests for the regulatory benchmarks API endpoints.
"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

from app.main import app

client = TestClient(app)

class TestBenchmarksAPI:
    
    def test_get_global_benchmarks(self):
        """Test getting global benchmarks."""
        response = client.get("/api/v1/benchmarks/global")
        
        # Check response
        assert response.status_code == 200
        result = response.json()
        
        # Check structure
        assert "benchmarks" in result
        assert "regional_averages" in result
        assert "global_average" in result
        
        # Check content types
        assert isinstance(result["benchmarks"], list)
        assert isinstance(result["regional_averages"], list)
        assert isinstance(result["global_average"], (int, float))
        
        # Check filtering
        response_europe = client.get("/api/v1/benchmarks/global?region=Europe")
        assert response_europe.status_code == 200
        europe_data = response_europe.json()
        assert all(country["region"] == "Europe" for country in europe_data["benchmarks"])
    
    def test_compare_countries(self):
        """Test comparing countries."""
        response = client.get("/api/v1/benchmarks/compare?countries=US&countries=UK")
        
        # Check response
        assert response.status_code == 200
        result = response.json()
        
        # Check structure
        assert "countries" in result
        assert "categories" in result
        assert "comparison_date" in result
        
        # Check content
        assert len(result["countries"]) == 2
        assert any(country["code"] == "US" for country in result["countries"])
        assert any(country["code"] == "UK" for country in result["countries"])
        
        # Check highlights field exists
        for country in result["countries"]:
            assert "highlights" in country
            assert "data_protection" in country["highlights"]
        
        # Test with invalid country
        response_invalid = client.get("/api/v1/benchmarks/compare?countries=INVALID")
        assert response_invalid.status_code == 404
