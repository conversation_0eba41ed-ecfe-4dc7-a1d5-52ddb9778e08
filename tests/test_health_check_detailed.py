
"""Detailed test cases for the health check endpoint."""
import json
import os
from unittest.mock import patch, MagicMock

import pytest
from fastapi.testclient import TestClient

from main import app


@pytest.fixture
def client():
    return TestClient(app)


def test_health_check_basic(client):
    """Test basic health check response."""
    response = client.get("/health")
    assert response.status_code == 200
    assert "status" in response.json()
    assert response.json()["status"] == "healthy"
    assert "timestamp" in response.json()
    assert "database" in response.json()
    assert "test_coverage" in response.json()


@patch("main.get_db")
def test_health_check_database_healthy(mock_get_db, client):
    """Test health check with healthy database."""
    # Mock the database session
    mock_session = MagicMock()
    mock_session.execute.return_value = True
    mock_get_db.return_value.__enter__.return_value = mock_session
    
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json()["database"]["status"] == "connected"
    assert "version" in response.json()["database"]


@patch("main.get_db")
def test_health_check_database_unhealthy(mock_get_db, client):
    """Test health check with unhealthy database."""
    # Mock database error
    mock_get_db.return_value.__enter__.side_effect = Exception("Database error")
    
    response = client.get("/health")
    assert response.status_code == 200  # Still returns 200 even with DB issue
    assert response.json()["database"]["status"] == "error"
    assert "error" in response.json()["database"]


@patch("os.path.exists")
@patch("builtins.open")
def test_health_check_coverage_data(mock_open, mock_exists, client):
    """Test health check with coverage data."""
    # Mock coverage data file
    mock_exists.return_value = True
    mock_file = MagicMock()
    mock_file.__enter__.return_value.read.return_value = json.dumps({
        "main.py": 85,
        "database.py": 90,
        "models.py": 95
    })
    mock_open.return_value = mock_file
    
    response = client.get("/health")
    assert response.status_code == 200
    assert "main.py" in response.json()["test_coverage"]
    assert "database.py" in response.json()["test_coverage"]
    assert "models.py" in response.json()["test_coverage"]
    assert "overall" in response.json()["test_coverage"]
    assert response.json()["test_coverage"]["overall"] == 90.0  # (85+90+95)/3


@patch("os.path.exists")
def test_health_check_no_coverage_data(mock_exists, client):
    """Test health check without coverage data."""
    # Mock coverage data file doesn't exist
    mock_exists.return_value = False
    
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json()["test_coverage"] == {}


@patch("os.walk")
@patch("os.path.join")
@patch("os.path.exists")
@patch("builtins.open")
def test_health_check_html_coverage_parsing(mock_open, mock_exists, mock_join, mock_walk, client):
    """Test health check HTML coverage parsing."""
    # Mock directory walk
    mock_walk.return_value = [("/coverage/html", [], ["main_py.html", "database_py.html"])]
    mock_exists.return_value = True
    mock_join.return_value = "coverage/html/main_py.html"
    
    # Mock file content with coverage percentage
    mock_file = MagicMock()
    mock_file.__enter__.return_value.read.return_value = '<title>Coverage for main.py: 75%</title>'
    mock_open.return_value = mock_file
    
    response = client.get("/health")
    assert response.status_code == 200
    assert "main.py" in response.json()["test_coverage"]
    assert response.json()["test_coverage"]["main.py"] == 75
