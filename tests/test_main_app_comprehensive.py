
import pytest
from unittest.mock import patch, MagicMock
import sys
import os
from fastapi.testclient import TestClient

# Add app directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.main import app

client = TestClient(app)

class TestMainAppComprehensive:
    """Comprehensive tests for the main FastAPI application."""

    def test_root_endpoint(self):
        """Test the root endpoint returns the correct response."""
        response = client.get("/")
        assert response.status_code == 200
        assert "Regulatory Compliance" in response.text
    
    def test_api_docs_endpoints(self):
        """Test that API documentation endpoints are accessible."""
        # Test Swagger UI
        response = client.get("/docs")
        assert response.status_code == 200
        assert "swagger" in response.text.lower()
        
        # Test ReDoc
        response = client.get("/redoc")
        assert response.status_code == 200
        assert "redoc" in response.text.lower()
    
    def test_static_files(self):
        """Test that static files are being served correctly."""
        # Test a static file (assuming there's a CSS file in the statics directory)
        response = client.get("/static/css/styles.css")
        if response.status_code == 200:
            assert "text/css" in response.headers.get("content-type", "")
        
    def test_404_handling(self):
        """Test that 404 errors are handled correctly."""
        response = client.get("/nonexistent_path")
        assert response.status_code == 404
    
    @patch("app.main.get_db")
    def test_database_integration(self, mock_get_db):
        """Test database integration with the main app."""
        # Mock database session
        mock_session = MagicMock()
        mock_get_db.return_value.__enter__.return_value = mock_session
        
        # Test an endpoint that uses the database
        # This will depend on what endpoints you have in your app
        response = client.get("/health")
        assert response.status_code == 200
    
    def test_cors_configuration(self):
        """Test CORS configuration by checking headers on options request."""
        response = client.options(
            "/",
            headers={"Origin": "http://testclient.com", "Access-Control-Request-Method": "GET"}
        )
        
        # Check if CORS headers are present
        assert "access-control-allow-origin" in response.headers
        
    @pytest.mark.parametrize("accept_language, expected_text", [
        ("en", "English"),
        ("fr", "French"),
        ("es", "Spanish"),
        ("de", "German"),
    ])
    def test_i18n_integration(self, accept_language, expected_text):
        """Test i18n integration with different Accept-Language headers."""
        # This is a placeholder test that assumes your app has i18n integration
        # The actual implementation will depend on how your app handles i18n
        
        # Skip if test would fail due to missing translation
        pytest.skip("Placeholder test for i18n integration")
        
        response = client.get("/", headers={"Accept-Language": accept_language})
        assert response.status_code == 200
        
        # Check for expected translated text
        # This will need to be adapted based on how your app actually implements i18n
        if expected_text == "English":
            assert "Regulatory Compliance" in response.text
