
import pytest
from unittest.mock import patch, MagicMock
import os
import sys

# Add app directory to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from app.utils.url_processor import (
    extract_regulatory_content,
    process_url_for_regulations,
    analyze_regulation_confidence,
    extract_regulation_entities,
    normalize_regulatory_text
)

class TestURLRegulationsComprehensive:
    @patch('app.utils.url_processor.requests.get')
    def test_extract_regulatory_content_success(self, mock_get):
        """Test successful extraction of regulatory content from URL."""
        # Setup mock
        mock_response = MagicMock()
        mock_response.text = """
        <html>
        <body>
        <h1>Privacy Regulation</h1>
        <p>Section 1. Data Protection Requirements</p>
        <p>Organizations must protect user data by implementing security measures.</p>
        </body>
        </html>
        """
        mock_response.status_code = 200
        mock_get.return_value = mock_response
        
        # Test function
        content = extract_regulatory_content("https://example.gov/regulation")
        
        # Verify content was extracted
        assert content is not None
        assert "Privacy Regulation" in content
        assert "Data Protection Requirements" in content
    
    @patch('app.utils.url_processor.requests.get')
    def test_extract_regulatory_content_error(self, mock_get):
        """Test error handling in regulatory content extraction."""
        # Setup mock to raise exception
        mock_get.side_effect = Exception("Connection error")
        
        # Test function with error
        content = extract_regulatory_content("https://example.gov/regulation")
        
        # Verify appropriate error handling
        assert content is None
    
    @patch('app.utils.url_processor.extract_regulatory_content')
    @patch('app.utils.url_processor.extract_regulation_entities')
    def test_process_url_for_regulations(self, mock_extract_entities, mock_extract_content):
        """Test processing URL for regulations."""
        # Setup mocks
        mock_extract_content.return_value = "This regulation requires data protection measures."
        mock_extract_entities.return_value = {
            "requirements": ["data protection measures"],
            "entities": ["organizations", "users"],
            "penalties": []
        }
        
        # Test function
        result = process_url_for_regulations("https://example.gov/regulation")
        
        # Verify result
        assert result is not None
        assert "requirements" in result
        assert "entities" in result
        assert "penalties" in result
        assert "data protection measures" in result["requirements"]
    
    def test_analyze_regulation_confidence(self):
        """Test confidence analysis for regulation extraction."""
        # Test with strong regulatory indicators
        text = "Article 5. Organizations shall implement security measures to protect personal data."
        confidence = analyze_regulation_confidence(text)
        assert confidence > 0.7  # High confidence expected
        
        # Test with weak regulatory indicators
        text = "The website has a contact form."
        confidence = analyze_regulation_confidence(text)
        assert confidence < 0.3  # Low confidence expected
    
    def test_extract_regulation_entities(self):
        """Test extraction of regulatory entities."""
        # Test with regulatory text
        text = """
        Article 1. Scope
        This regulation applies to all organizations processing personal data.
        
        Article 2. Penalties
        Violations may result in fines up to $10,000,000 or 2% of annual revenue.
        """
        
        entities = extract_regulation_entities(text)
        
        # Verify extraction
        assert "organizations" in entities["entities"]
        assert "personal data" in entities["entities"]
        assert any("fine" in penalty.lower() for penalty in entities["penalties"])
    
    def test_normalize_regulatory_text(self):
        """Test normalization of regulatory text."""
        # Test with various formats
        text = """
        ARTICLE 1 - Scope
        
        1.1. This applies to all data controllers.
        
        SECTION 2 - Requirements
        
        2.1. Data must be protected.
        """
        
        normalized = normalize_regulatory_text(text)
        
        # Verify normalization
        assert "article 1" in normalized.lower()
        assert "section 2" in normalized.lower()
        assert normalized.count("\n") < text.count("\n")  # Should have fewer newlines
