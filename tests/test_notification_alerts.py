"""
Unit tests for the notification and alerts functionality.
"""
import unittest
from unittest.mock import patch, MagicMock, call
import datetime
import json

# Import the modules to be tested
# These imports will need to be updated based on the actual implementation
from app.regulatory_change.notifications import (
    NotificationManager,
    NotificationTemplate,
    Notification,
    NotificationChannel,
    AlertLevel,
    EscalationRule,
    NotificationPreference,
    NotificationDigest
)
from app.regulatory_change.workflow import WorkflowInstance, TaskAssignment


class TestNotificationAlerts(unittest.TestCase):
    """Test cases for notification and alerts functionality."""

    def setUp(self):
        """Set up test fixtures."""
        # Create notification manager
        self.notification_manager = NotificationManager()
        
        # Create mock email service
        self.mock_email_service = MagicMock()
        self.notification_manager.email_service = self.mock_email_service
        
        # Create mock SMS service
        self.mock_sms_service = MagicMock()
        self.notification_manager.sms_service = self.mock_sms_service
        
        # Create mock in-app notification service
        self.mock_inapp_service = MagicMock()
        self.notification_manager.inapp_service = self.mock_inapp_service
        
        # Create sample users
        self.user1 = MagicMock()
        self.user1.id = "user123"
        self.user1.name = "Test User"
        self.user1.email = "<EMAIL>"
        self.user1.phone = "+1234567890"
        
        self.user2 = MagicMock()
        self.user2.id = "user456"
        self.user2.name = "Manager User"
        self.user2.email = "<EMAIL>"
        self.user2.phone = "+1987654321"
        
        # Create sample workflow instance
        self.workflow_instance = MagicMock(spec=WorkflowInstance)
        self.workflow_instance.id = "workflow123"
        self.workflow_instance.name = "Test Workflow"
        
        # Create sample task assignment
        self.task_assignment = MagicMock(spec=TaskAssignment)
        self.task_assignment.id = "task123"
        self.task_assignment.task.name = "Test Task"
        self.task_assignment.assignee = self.user1

    def test_create_notification_template(self):
        """Test creation of notification templates."""
        # Create notification template
        template = NotificationTemplate(
            name="Task Assignment Template",
            subject="New Task Assigned: {task_name}",
            body="You have been assigned a new task: {task_name}. Please complete it by {due_date}.",
            template_type="EMAIL",
            created_by=self.user1,
            created_at=datetime.datetime.now()
        )
        
        # Assertions
        self.assertEqual(template.name, "Task Assignment Template")
        self.assertEqual(template.subject, "New Task Assigned: {task_name}")
        self.assertEqual(template.template_type, "EMAIL")
        
        # Test template rendering
        context = {
            "task_name": "Security Review",
            "due_date": "2023-01-15"
        }
        
        rendered_subject = template.render_subject(context)
        rendered_body = template.render_body(context)
        
        self.assertEqual(rendered_subject, "New Task Assigned: Security Review")
        self.assertEqual(rendered_body, "You have been assigned a new task: Security Review. Please complete it by 2023-01-15.")

    def test_send_notification(self):
        """Test sending notifications through different channels."""
        # Create notification
        notification = Notification(
            recipient=self.user1,
            subject="Task Deadline Approaching",
            body="Your task 'Security Review' is due in 2 days.",
            notification_type="TASK_REMINDER",
            related_object_id="task123",
            related_object_type="TaskAssignment",
            created_at=datetime.datetime.now()
        )
        
        # Send notification via email
        self.notification_manager.send_notification(
            notification=notification,
            channel=NotificationChannel.EMAIL
        )
        
        # Assertions for email
        self.mock_email_service.send_email.assert_called_once_with(
            recipient_email=self.user1.email,
            subject="Task Deadline Approaching",
            body="Your task 'Security Review' is due in 2 days.",
            recipient_name=self.user1.name
        )
        
        # Reset mock
        self.mock_email_service.reset_mock()
        
        # Send notification via SMS
        self.notification_manager.send_notification(
            notification=notification,
            channel=NotificationChannel.SMS
        )
        
        # Assertions for SMS
        self.mock_sms_service.send_sms.assert_called_once_with(
            recipient_phone=self.user1.phone,
            message="Task Deadline Approaching: Your task 'Security Review' is due in 2 days."
        )
        
        # Reset mock
        self.mock_sms_service.reset_mock()
        
        # Send notification via in-app
        self.notification_manager.send_notification(
            notification=notification,
            channel=NotificationChannel.IN_APP
        )
        
        # Assertions for in-app
        self.mock_inapp_service.send_notification.assert_called_once_with(
            recipient_id=self.user1.id,
            subject="Task Deadline Approaching",
            body="Your task 'Security Review' is due in 2 days.",
            notification_type="TASK_REMINDER",
            related_object_id="task123",
            related_object_type="TaskAssignment"
        )

    def test_notification_preferences(self):
        """Test user notification preferences."""
        # Create notification preferences for user
        preferences = NotificationPreference(
            user=self.user1,
            created_at=datetime.datetime.now()
        )
        
        # Set channel preferences
        preferences.set_channel_preference(
            notification_type="TASK_ASSIGNMENT",
            channels=[NotificationChannel.EMAIL, NotificationChannel.IN_APP]
        )
        
        preferences.set_channel_preference(
            notification_type="TASK_REMINDER",
            channels=[NotificationChannel.EMAIL, NotificationChannel.SMS, NotificationChannel.IN_APP]
        )
        
        preferences.set_channel_preference(
            notification_type="APPROVAL_REQUEST",
            channels=[NotificationChannel.EMAIL, NotificationChannel.IN_APP]
        )
        
        # Set digest preferences
        preferences.set_digest_preference(
            digest_enabled=True,
            digest_frequency="DAILY",
            digest_time="18:00",
            notification_types=["STATUS_UPDATE", "COMMENT"]
        )
        
        # Assertions
        self.assertEqual(len(preferences.channel_preferences), 3)
        
        # Check channel preferences
        task_assignment_channels = preferences.get_channels_for_type("TASK_ASSIGNMENT")
        self.assertEqual(len(task_assignment_channels), 2)
        self.assertIn(NotificationChannel.EMAIL, task_assignment_channels)
        self.assertIn(NotificationChannel.IN_APP, task_assignment_channels)
        
        task_reminder_channels = preferences.get_channels_for_type("TASK_REMINDER")
        self.assertEqual(len(task_reminder_channels), 3)
        self.assertIn(NotificationChannel.SMS, task_reminder_channels)
        
        # Check digest preferences
        self.assertTrue(preferences.digest_enabled)
        self.assertEqual(preferences.digest_frequency, "DAILY")
        self.assertEqual(preferences.digest_time, "18:00")
        self.assertIn("STATUS_UPDATE", preferences.digest_notification_types)
        self.assertIn("COMMENT", preferences.digest_notification_types)
        
        # Test if notification should be digested
        self.assertTrue(preferences.should_digest("STATUS_UPDATE"))
        self.assertTrue(preferences.should_digest("COMMENT"))
        self.assertFalse(preferences.should_digest("TASK_ASSIGNMENT"))
        self.assertFalse(preferences.should_digest("TASK_REMINDER"))

    def test_alert_levels(self):
        """Test alert levels for notifications."""
        # Create notifications with different alert levels
        info_notification = Notification(
            recipient=self.user1,
            subject="Information Update",
            body="Regulatory change workflow has been updated.",
            notification_type="STATUS_UPDATE",
            alert_level=AlertLevel.INFO,
            related_object_id="workflow123",
            related_object_type="WorkflowInstance",
            created_at=datetime.datetime.now()
        )
        
        warning_notification = Notification(
            recipient=self.user1,
            subject="Approaching Deadline",
            body="Task deadline is approaching in 3 days.",
            notification_type="TASK_REMINDER",
            alert_level=AlertLevel.WARNING,
            related_object_id="task123",
            related_object_type="TaskAssignment",
            created_at=datetime.datetime.now()
        )
        
        critical_notification = Notification(
            recipient=self.user1,
            subject="Missed Deadline",
            body="Task deadline has been missed.",
            notification_type="TASK_REMINDER",
            alert_level=AlertLevel.CRITICAL,
            related_object_id="task123",
            related_object_type="TaskAssignment",
            created_at=datetime.datetime.now()
        )
        
        # Send notifications with different alert levels
        with patch.object(self.notification_manager, 'get_channels_for_notification') as mock_get_channels:
            # Configure mock to return all channels for all notifications
            mock_get_channels.return_value = [
                NotificationChannel.EMAIL,
                NotificationChannel.SMS,
                NotificationChannel.IN_APP
            ]
            
            # Send notifications
            self.notification_manager.send_notification_with_alert_level(info_notification)
            self.notification_manager.send_notification_with_alert_level(warning_notification)
            self.notification_manager.send_notification_with_alert_level(critical_notification)
        
        # Assertions for email service
        email_calls = self.mock_email_service.send_email.call_args_list
        self.assertEqual(len(email_calls), 3)
        
        # Assertions for SMS service - should only be called for WARNING and CRITICAL
        sms_calls = self.mock_sms_service.send_sms.call_args_list
        self.assertEqual(len(sms_calls), 2)  # Only warning and critical
        
        # Assertions for in-app service
        inapp_calls = self.mock_inapp_service.send_notification.call_args_list
        self.assertEqual(len(inapp_calls), 3)
        
        # Verify critical notification has priority flag
        critical_inapp_call = inapp_calls[2]
        self.assertEqual(critical_inapp_call[1].get('alert_level'), AlertLevel.CRITICAL)

    def test_escalation_rules(self):
        """Test notification escalation rules."""
        # Create escalation rule
        escalation_rule = EscalationRule(
            name="Missed Deadline Escalation",
            notification_type="TASK_REMINDER",
            condition={
                "status": "OVERDUE",
                "days_overdue": 1
            },
            escalation_level=1,
            escalation_recipients=[self.user2],  # Escalate to manager
            created_by=self.user1,
            created_at=datetime.datetime.now()
        )
        
        # Add escalation rule to notification manager
        self.notification_manager.add_escalation_rule(escalation_rule)
        
        # Create overdue task notification
        overdue_notification = Notification(
            recipient=self.user1,
            subject="Task Overdue",
            body="Your task 'Security Review' is overdue by 2 days.",
            notification_type="TASK_REMINDER",
            alert_level=AlertLevel.CRITICAL,
            related_object_id="task123",
            related_object_type="TaskAssignment",
            metadata={
                "status": "OVERDUE",
                "days_overdue": 2
            },
            created_at=datetime.datetime.now()
        )
        
        # Process notification with escalation
        self.notification_manager.process_notification_with_escalation(overdue_notification)
        
        # Assertions - original notification should be sent
        self.mock_email_service.send_email.assert_any_call(
            recipient_email=self.user1.email,
            subject="Task Overdue",
            body="Your task 'Security Review' is overdue by 2 days.",
            recipient_name=self.user1.name
        )
        
        # Escalation notification should also be sent to manager
        self.mock_email_service.send_email.assert_any_call(
            recipient_email=self.user2.email,
            subject="ESCALATION: Task Overdue",
            body=unittest.mock.ANY,  # Don't check exact content
            recipient_name=self.user2.name
        )
        
        # Check total number of emails sent (original + escalation)
        self.assertEqual(self.mock_email_service.send_email.call_count, 2)

    def test_notification_digest(self):
        """Test notification digest functionality."""
        # Create notification digest
        digest = NotificationDigest(
            recipient=self.user1,
            digest_type="DAILY",
            created_at=datetime.datetime.now()
        )
        
        # Add notifications to digest
        digest.add_notification(
            subject="Status Update 1",
            body="Workflow status has been updated to 'In Progress'.",
            notification_type="STATUS_UPDATE",
            related_object_id="workflow123",
            related_object_type="WorkflowInstance",
            created_at=datetime.datetime.now() - datetime.timedelta(hours=5)
        )
        
        digest.add_notification(
            subject="Status Update 2",
            body="Task status has been updated to 'In Progress'.",
            notification_type="STATUS_UPDATE",
            related_object_id="task123",
            related_object_type="TaskAssignment",
            created_at=datetime.datetime.now() - datetime.timedelta(hours=3)
        )
        
        digest.add_notification(
            subject="New Comment",
            body="A new comment has been added to the task.",
            notification_type="COMMENT",
            related_object_id="task123",
            related_object_type="TaskAssignment",
            created_at=datetime.datetime.now() - datetime.timedelta(hours=1)
        )
        
        # Generate digest content
        digest_subject, digest_body = digest.generate_digest_content()
        
        # Assertions
        self.assertEqual(len(digest.notifications), 3)
        self.assertIn("Daily Digest", digest_subject)
        
        # Send digest
        self.notification_manager.send_digest(digest)
        
        # Assertions - digest should be sent via email
        self.mock_email_service.send_email.assert_called_once_with(
            recipient_email=self.user1.email,
            subject=digest_subject,
            body=digest_body,
            recipient_name=self.user1.name
        )

    def test_notification_delivery_tracking(self):
        """Test tracking of notification delivery status."""
        # Create notification
        notification = Notification(
            recipient=self.user1,
            subject="Task Deadline Approaching",
            body="Your task 'Security Review' is due in 2 days.",
            notification_type="TASK_REMINDER",
            related_object_id="task123",
            related_object_type="TaskAssignment",
            created_at=datetime.datetime.now()
        )
        
        # Mock successful email delivery
        self.mock_email_service.send_email.return_value = {
            "success": True,
            "message_id": "email123",
            "timestamp": datetime.datetime.now()
        }
        
        # Send notification via email
        delivery_status = self.notification_manager.send_notification(
            notification=notification,
            channel=NotificationChannel.EMAIL,
            track_delivery=True
        )
        
        # Assertions for successful delivery
        self.assertTrue(delivery_status.success)
        self.assertEqual(delivery_status.channel, NotificationChannel.EMAIL)
        self.assertIsNotNone(delivery_status.delivery_id)
        self.assertIsNotNone(delivery_status.delivered_at)
        
        # Reset mock and configure for failure
        self.mock_email_service.reset_mock()
        self.mock_email_service.send_email.return_value = {
            "success": False,
            "error": "Invalid email address",
            "timestamp": datetime.datetime.now()
        }
        
        # Send notification with failure
        delivery_status = self.notification_manager.send_notification(
            notification=notification,
            channel=NotificationChannel.EMAIL,
            track_delivery=True
        )
        
        # Assertions for failed delivery
        self.assertFalse(delivery_status.success)
        self.assertEqual(delivery_status.channel, NotificationChannel.EMAIL)
        self.assertIsNone(delivery_status.delivery_id)
        self.assertIsNotNone(delivery_status.error_message)


if __name__ == '__main__':
    unittest.main()
