"""
Unit tests for the implementation tracking functionality.
"""
import unittest
from unittest.mock import patch, MagicMock
import datetime
import json

# Import the modules to be tested
# These imports will need to be updated based on the actual implementation
from app.regulatory_change.implementation import (
    ImplementationPlan,
    ImplementationTask,
    ImplementationMilestone,
    ResourceAllocation,
    EvidenceItem,
    TestCase,
    CostTracking
)
from app.regulatory_change.detection import RegulatoryChange
from app.regulatory_change.workflow import WorkflowInstance


class TestImplementationTracking(unittest.TestCase):
    """Test cases for implementation tracking functionality."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a sample regulatory change
        self.regulatory_change = RegulatoryChange(
            source="Test Source",
            title="Updated Security Requirements",
            description="Financial institutions shall implement new security measures by January 1, 2023.",
            change_type="AMENDMENT",
            detected_date=datetime.datetime.now()
        )
        
        # Create a sample workflow instance
        self.workflow_instance = MagicMock(spec=WorkflowInstance)
        self.workflow_instance.id = "workflow123"
        self.workflow_instance.regulatory_change = self.regulatory_change
        
        # Create a sample user
        self.user = MagicMock()
        self.user.id = "user123"
        self.user.name = "Test User"
        self.user.role = "Implementation Manager"
        
        # Create another sample user
        self.user2 = MagicMock()
        self.user2.id = "user456"
        self.user2.name = "Another User"
        self.user2.role = "IT Specialist"

    def test_create_implementation_plan(self):
        """Test creation of an implementation plan."""
        # Create implementation plan
        plan = ImplementationPlan(
            workflow_instance=self.workflow_instance,
            name="Security Requirements Implementation",
            description="Plan to implement new security measures",
            created_by=self.user,
            created_at=datetime.datetime.now(),
            target_completion_date=datetime.datetime(2023, 1, 1)
        )
        
        # Assertions
        self.assertEqual(plan.workflow_instance, self.workflow_instance)
        self.assertEqual(plan.name, "Security Requirements Implementation")
        self.assertEqual(plan.created_by, self.user)
        self.assertEqual(plan.status, "DRAFT")
        self.assertEqual(plan.target_completion_date, datetime.datetime(2023, 1, 1))

    def test_add_milestones(self):
        """Test adding milestones to an implementation plan."""
        # Create implementation plan
        plan = ImplementationPlan(
            workflow_instance=self.workflow_instance,
            name="Security Requirements Implementation",
            description="Plan to implement new security measures",
            created_by=self.user,
            created_at=datetime.datetime.now(),
            target_completion_date=datetime.datetime(2023, 1, 1)
        )
        
        # Add milestones
        milestone1 = plan.add_milestone(
            name="Requirements Gathering",
            description="Gather detailed requirements for security measures",
            due_date=datetime.datetime(2022, 7, 15),
            order=1
        )
        
        milestone2 = plan.add_milestone(
            name="System Design",
            description="Design system changes for new security measures",
            due_date=datetime.datetime(2022, 9, 1),
            order=2
        )
        
        milestone3 = plan.add_milestone(
            name="Implementation",
            description="Implement system changes",
            due_date=datetime.datetime(2022, 11, 15),
            order=3
        )
        
        milestone4 = plan.add_milestone(
            name="Testing",
            description="Test implemented changes",
            due_date=datetime.datetime(2022, 12, 15),
            order=4
        )
        
        # Assertions
        self.assertEqual(len(plan.milestones), 4)
        self.assertEqual(plan.milestones[0].name, "Requirements Gathering")
        self.assertEqual(plan.milestones[1].name, "System Design")
        self.assertEqual(plan.milestones[2].name, "Implementation")
        self.assertEqual(plan.milestones[3].name, "Testing")
        
        # Test milestone ordering
        ordered_milestones = plan.get_ordered_milestones()
        self.assertEqual(ordered_milestones[0].name, "Requirements Gathering")
        self.assertEqual(ordered_milestones[3].name, "Testing")

    def test_add_tasks(self):
        """Test adding tasks to milestones."""
        # Create implementation plan
        plan = ImplementationPlan(
            workflow_instance=self.workflow_instance,
            name="Security Requirements Implementation",
            description="Plan to implement new security measures",
            created_by=self.user,
            created_at=datetime.datetime.now(),
            target_completion_date=datetime.datetime(2023, 1, 1)
        )
        
        # Add milestone
        milestone = plan.add_milestone(
            name="System Design",
            description="Design system changes for new security measures",
            due_date=datetime.datetime(2022, 9, 1),
            order=1
        )
        
        # Add tasks to milestone
        task1 = milestone.add_task(
            name="Review Current Architecture",
            description="Review current system architecture",
            estimated_effort=16.0,  # hours
            assignee=self.user2
        )
        
        task2 = milestone.add_task(
            name="Design Encryption Changes",
            description="Design changes to encryption system",
            estimated_effort=24.0,  # hours
            assignee=self.user2
        )
        
        task3 = milestone.add_task(
            name="Design Authentication Changes",
            description="Design changes to authentication system",
            estimated_effort=20.0,  # hours
            assignee=self.user2
        )
        
        # Assertions
        self.assertEqual(len(milestone.tasks), 3)
        self.assertEqual(milestone.tasks[0].name, "Review Current Architecture")
        self.assertEqual(milestone.tasks[1].name, "Design Encryption Changes")
        self.assertEqual(milestone.tasks[2].name, "Design Authentication Changes")
        
        # Test total effort calculation
        total_effort = milestone.calculate_total_effort()
        self.assertEqual(total_effort, 60.0)  # 16 + 24 + 20 = 60 hours

    def test_task_dependencies(self):
        """Test task dependencies."""
        # Create implementation plan
        plan = ImplementationPlan(
            workflow_instance=self.workflow_instance,
            name="Security Requirements Implementation",
            description="Plan to implement new security measures",
            created_by=self.user,
            created_at=datetime.datetime.now(),
            target_completion_date=datetime.datetime(2023, 1, 1)
        )
        
        # Add milestone
        milestone = plan.add_milestone(
            name="Implementation",
            description="Implement system changes",
            due_date=datetime.datetime(2022, 11, 15),
            order=1
        )
        
        # Add tasks to milestone
        task1 = milestone.add_task(
            name="Update Database Schema",
            description="Update database schema for new security features",
            estimated_effort=8.0,  # hours
            assignee=self.user2
        )
        
        task2 = milestone.add_task(
            name="Implement Encryption Module",
            description="Implement new encryption module",
            estimated_effort=24.0,  # hours
            assignee=self.user2
        )
        
        task3 = milestone.add_task(
            name="Implement Authentication Changes",
            description="Implement changes to authentication system",
            estimated_effort=20.0,  # hours
            assignee=self.user2
        )
        
        # Add dependencies
        plan.add_task_dependency(task2, task1)  # Encryption depends on DB schema
        plan.add_task_dependency(task3, task1)  # Authentication depends on DB schema
        
        # Assertions
        self.assertEqual(len(task1.dependent_tasks), 2)
        self.assertIn(task2, task1.dependent_tasks)
        self.assertIn(task3, task1.dependent_tasks)
        
        self.assertEqual(len(task2.prerequisite_tasks), 1)
        self.assertIn(task1, task2.prerequisite_tasks)
        
        self.assertEqual(len(task3.prerequisite_tasks), 1)
        self.assertIn(task1, task3.prerequisite_tasks)
        
        # Test task readiness
        self.assertTrue(task1.is_ready())  # No prerequisites
        self.assertFalse(task2.is_ready())  # Prerequisite not completed
        self.assertFalse(task3.is_ready())  # Prerequisite not completed
        
        # Complete task1
        task1.status = "COMPLETED"
        task1.completed_at = datetime.datetime.now()
        task1.completed_by = self.user2
        
        # Recheck readiness
        self.assertTrue(task2.is_ready())  # Prerequisite now completed
        self.assertTrue(task3.is_ready())  # Prerequisite now completed

    def test_resource_allocation(self):
        """Test resource allocation for implementation tasks."""
        # Create implementation plan
        plan = ImplementationPlan(
            workflow_instance=self.workflow_instance,
            name="Security Requirements Implementation",
            description="Plan to implement new security measures",
            created_by=self.user,
            created_at=datetime.datetime.now(),
            target_completion_date=datetime.datetime(2023, 1, 1)
        )
        
        # Add milestone
        milestone = plan.add_milestone(
            name="Implementation",
            description="Implement system changes",
            due_date=datetime.datetime(2022, 11, 15),
            order=1
        )
        
        # Add task to milestone
        task = milestone.add_task(
            name="Implement Encryption Module",
            description="Implement new encryption module",
            estimated_effort=40.0,  # hours
            assignee=self.user2
        )
        
        # Create resource allocation
        allocation = ResourceAllocation(
            task=task,
            resource_type="STAFF",
            resource_name="Senior Developer",
            allocation_percentage=50,  # Half-time allocation
            start_date=datetime.datetime(2022, 10, 1),
            end_date=datetime.datetime(2022, 10, 15),
            allocated_by=self.user,
            allocated_at=datetime.datetime.now()
        )
        
        # Add allocation to task
        task.add_resource_allocation(allocation)
        
        # Assertions
        self.assertEqual(len(task.resource_allocations), 1)
        self.assertEqual(task.resource_allocations[0].resource_name, "Senior Developer")
        self.assertEqual(task.resource_allocations[0].allocation_percentage, 50)
        
        # Test allocation duration
        duration_days = (allocation.end_date - allocation.start_date).days
        self.assertEqual(duration_days, 14)  # Oct 1 to Oct 15 = 14 days
        
        # Test effective hours calculation (assuming 8-hour workdays)
        effective_hours = allocation.calculate_effective_hours(hours_per_day=8)
        # 14 days * 8 hours/day * 50% allocation = 56 hours
        self.assertEqual(effective_hours, 56)

    def test_evidence_collection(self):
        """Test evidence collection for implementation tasks."""
        # Create implementation plan
        plan = ImplementationPlan(
            workflow_instance=self.workflow_instance,
            name="Security Requirements Implementation",
            description="Plan to implement new security measures",
            created_by=self.user,
            created_at=datetime.datetime.now(),
            target_completion_date=datetime.datetime(2023, 1, 1)
        )
        
        # Add milestone
        milestone = plan.add_milestone(
            name="Implementation",
            description="Implement system changes",
            due_date=datetime.datetime(2022, 11, 15),
            order=1
        )
        
        # Add task to milestone
        task = milestone.add_task(
            name="Implement Encryption Module",
            description="Implement new encryption module",
            estimated_effort=40.0,  # hours
            assignee=self.user2
        )
        
        # Add evidence to task
        evidence1 = task.add_evidence(
            title="Code Review Documentation",
            description="Documentation of code review for encryption module",
            evidence_type="DOCUMENT",
            file_reference="code_review_encryption.pdf",
            submitted_by=self.user2,
            submitted_at=datetime.datetime.now()
        )
        
        evidence2 = task.add_evidence(
            title="Test Results",
            description="Test results for encryption module",
            evidence_type="TEST_RESULTS",
            file_reference="encryption_test_results.xlsx",
            submitted_by=self.user2,
            submitted_at=datetime.datetime.now()
        )
        
        # Assertions
        self.assertEqual(len(task.evidence_items), 2)
        self.assertEqual(task.evidence_items[0].title, "Code Review Documentation")
        self.assertEqual(task.evidence_items[1].title, "Test Results")
        
        # Test evidence approval
        approved_evidence = evidence1.approve(
            approved_by=self.user,
            approval_date=datetime.datetime.now(),
            comments="Evidence is complete and satisfactory"
        )
        
        self.assertEqual(approved_evidence.status, "APPROVED")
        self.assertEqual(approved_evidence.approval_comments, "Evidence is complete and satisfactory")
        self.assertEqual(approved_evidence.approved_by, self.user)

    def test_testing_and_validation(self):
        """Test testing and validation functionality."""
        # Create implementation plan
        plan = ImplementationPlan(
            workflow_instance=self.workflow_instance,
            name="Security Requirements Implementation",
            description="Plan to implement new security measures",
            created_by=self.user,
            created_at=datetime.datetime.now(),
            target_completion_date=datetime.datetime(2023, 1, 1)
        )
        
        # Add milestone
        milestone = plan.add_milestone(
            name="Testing",
            description="Test implemented changes",
            due_date=datetime.datetime(2022, 12, 15),
            order=1
        )
        
        # Add task to milestone
        task = milestone.add_task(
            name="Test Encryption Module",
            description="Perform testing of encryption module",
            estimated_effort=16.0,  # hours
            assignee=self.user2
        )
        
        # Create test cases
        test_case1 = TestCase(
            task=task,
            name="Encryption Performance Test",
            description="Test performance of encryption operations",
            expected_result="Encryption operations complete within 100ms",
            created_by=self.user2,
            created_at=datetime.datetime.now()
        )
        
        test_case2 = TestCase(
            task=task,
            name="Encryption Security Test",
            description="Test security of encryption implementation",
            expected_result="Encryption meets FIPS 140-2 requirements",
            created_by=self.user2,
            created_at=datetime.datetime.now()
        )
        
        # Add test cases to task
        task.add_test_case(test_case1)
        task.add_test_case(test_case2)
        
        # Execute test cases
        test_case1.execute(
            executed_by=self.user2,
            execution_date=datetime.datetime.now(),
            actual_result="Encryption operations completed in 85ms",
            status="PASSED",
            notes="Performance is within acceptable limits"
        )
        
        test_case2.execute(
            executed_by=self.user2,
            execution_date=datetime.datetime.now(),
            actual_result="Encryption meets FIPS 140-2 requirements",
            status="PASSED",
            notes="All security requirements satisfied"
        )
        
        # Assertions
        self.assertEqual(len(task.test_cases), 2)
        self.assertEqual(task.test_cases[0].name, "Encryption Performance Test")
        self.assertEqual(task.test_cases[1].name, "Encryption Security Test")
        
        self.assertEqual(task.test_cases[0].status, "PASSED")
        self.assertEqual(task.test_cases[1].status, "PASSED")
        
        # Test overall test status
        test_status = task.get_test_status()
        self.assertEqual(test_status, "PASSED")  # All tests passed

    def test_cost_tracking(self):
        """Test cost tracking functionality."""
        # Create implementation plan
        plan = ImplementationPlan(
            workflow_instance=self.workflow_instance,
            name="Security Requirements Implementation",
            description="Plan to implement new security measures",
            created_by=self.user,
            created_at=datetime.datetime.now(),
            target_completion_date=datetime.datetime(2023, 1, 1)
        )
        
        # Add milestone
        milestone = plan.add_milestone(
            name="Implementation",
            description="Implement system changes",
            due_date=datetime.datetime(2022, 11, 15),
            order=1
        )
        
        # Add task to milestone
        task = milestone.add_task(
            name="Implement Encryption Module",
            description="Implement new encryption module",
            estimated_effort=40.0,  # hours
            assignee=self.user2
        )
        
        # Create cost tracking
        cost_tracking = CostTracking(
            implementation_plan=plan,
            created_by=self.user,
            created_at=datetime.datetime.now()
        )
        
        # Add budget items
        cost_tracking.add_budget_item(
            category="LABOR",
            description="Developer time",
            amount=20000.00,
            notes="Based on 200 hours at $100/hour"
        )
        
        cost_tracking.add_budget_item(
            category="SOFTWARE",
            description="Encryption library license",
            amount=5000.00,
            notes="Annual license fee"
        )
        
        cost_tracking.add_budget_item(
            category="HARDWARE",
            description="Security hardware module",
            amount=15000.00,
            notes="One-time purchase"
        )
        
        # Add actual costs
        cost_tracking.add_actual_cost(
            category="LABOR",
            description="Developer time",
            amount=18500.00,
            date_incurred=datetime.datetime.now(),
            recorded_by=self.user
        )
        
        cost_tracking.add_actual_cost(
            category="SOFTWARE",
            description="Encryption library license",
            amount=5000.00,
            date_incurred=datetime.datetime.now(),
            recorded_by=self.user
        )
        
        cost_tracking.add_actual_cost(
            category="HARDWARE",
            description="Security hardware module",
            amount=16200.00,
            date_incurred=datetime.datetime.now(),
            recorded_by=self.user
        )
        
        # Assertions
        self.assertEqual(len(cost_tracking.budget_items), 3)
        self.assertEqual(len(cost_tracking.actual_costs), 3)
        
        # Test budget calculations
        total_budget = cost_tracking.calculate_total_budget()
        self.assertEqual(total_budget, 40000.00)  # 20000 + 5000 + 15000
        
        total_actual = cost_tracking.calculate_total_actual_cost()
        self.assertEqual(total_actual, 39700.00)  # 18500 + 5000 + 16200
        
        variance = cost_tracking.calculate_variance()
        self.assertEqual(variance, 300.00)  # 40000 - 39700
        
        variance_percentage = cost_tracking.calculate_variance_percentage()
        self.assertAlmostEqual(variance_percentage, 0.75, places=2)  # (300 / 40000) * 100 = 0.75%


if __name__ == '__main__':
    unittest.main()
