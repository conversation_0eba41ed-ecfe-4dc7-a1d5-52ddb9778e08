"""
Tests for the compliance requirements API.
"""
import pytest
from fastapi.testclient import TestClient
from datetime import date

from app.main import app

client = TestClient(app)

def test_get_compliance_requirements(client, test_data):
    """Test getting a list of compliance requirements."""
    response = client.get("/api/v1/compliance-requirements/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) > 0
    assert response.json()[0]["text"] == test_data["requirement"].text

def test_get_compliance_requirements_with_search(client, test_data):
    """Test getting compliance requirements with search filter."""
    response = client.get("/api/v1/compliance-requirements/?search=Test")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) > 0
    assert response.json()[0]["text"] == test_data["requirement"].text

def test_get_compliance_requirements_with_status_filter(client, test_data):
    """Test getting compliance requirements with status filter."""
    response = client.get(f"/api/v1/compliance-requirements/?status={test_data['requirement'].status}")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) > 0
    assert response.json()[0]["status"] == test_data["requirement"].status

def test_get_compliance_requirements_with_priority_filter(client, test_data):
    """Test getting compliance requirements with priority filter."""
    response = client.get(f"/api/v1/compliance-requirements/?priority={test_data['requirement'].priority}")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) > 0
    assert response.json()[0]["priority"] == test_data["requirement"].priority

def test_get_compliance_requirements_with_regulation_filter(client, test_data):
    """Test getting compliance requirements with regulation filter."""
    response = client.get(f"/api/v1/compliance-requirements/?regulation_id={test_data['regulation'].id}")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) > 0
    assert response.json()[0]["regulation_id"] == test_data["regulation"].id

def test_get_compliance_requirement(client, test_data):
    """Test getting a specific compliance requirement by ID."""
    response = client.get(f"/api/v1/compliance-requirements/{test_data['requirement'].id}")
    assert response.status_code == 200
    assert response.json()["text"] == test_data["requirement"].text
    assert response.json()["section"] == test_data["requirement"].section
    assert response.json()["priority"] == test_data["requirement"].priority
    assert response.json()["status"] == test_data["requirement"].status
    assert response.json()["regulation_id"] == test_data["regulation"].id

def test_get_compliance_requirement_not_found(client):
    """Test getting a non-existent compliance requirement."""
    response = client.get("/api/v1/compliance-requirements/999")
    assert response.status_code == 404
    assert response.json()["detail"] == "Compliance requirement not found"

def test_create_compliance_requirement(client, test_data):
    """Test creating a new compliance requirement."""
    requirement_data = {
        "text": "New Test Requirement",
        "section": "Section 2",
        "priority": "Medium",
        "status": "Not Started",
        "regulation_id": test_data["regulation"].id,
        "due_date": str(date.today())
    }
    response = client.post("/api/v1/compliance-requirements/", json=requirement_data)
    assert response.status_code == 201
    assert response.json()["text"] == requirement_data["text"]
    assert response.json()["section"] == requirement_data["section"]
    assert response.json()["priority"] == requirement_data["priority"]
    assert response.json()["status"] == requirement_data["status"]
    assert response.json()["regulation_id"] == requirement_data["regulation_id"]
    assert response.json()["due_date"] == requirement_data["due_date"]

def test_create_compliance_requirement_invalid_regulation(client):
    """Test creating a compliance requirement with an invalid regulation."""
    requirement_data = {
        "text": "New Test Requirement",
        "section": "Section 2",
        "priority": "Medium",
        "status": "Not Started",
        "regulation_id": 999
    }
    response = client.post("/api/v1/compliance-requirements/", json=requirement_data)
    assert response.status_code == 400
    assert "Regulation not found" in response.json()["detail"]

def test_update_compliance_requirement(client, test_data):
    """Test updating a compliance requirement."""
    requirement_data = {
        "text": "Updated Test Requirement",
        "section": "Updated Section",
        "priority": "Low",
        "status": "Completed",
        "due_date": str(date.today())
    }
    response = client.put(f"/api/v1/compliance-requirements/{test_data['requirement'].id}", json=requirement_data)
    assert response.status_code == 200
    assert response.json()["text"] == requirement_data["text"]
    assert response.json()["section"] == requirement_data["section"]
    assert response.json()["priority"] == requirement_data["priority"]
    assert response.json()["status"] == requirement_data["status"]
    assert response.json()["due_date"] == requirement_data["due_date"]

def test_update_compliance_requirement_not_found(client):
    """Test updating a non-existent compliance requirement."""
    requirement_data = {
        "text": "Updated Test Requirement",
        "section": "Updated Section",
        "priority": "Low",
        "status": "Completed"
    }
    response = client.put("/api/v1/compliance-requirements/999", json=requirement_data)
    assert response.status_code == 404
    assert response.json()["detail"] == "Compliance requirement not found"

def test_update_compliance_requirement_invalid_regulation(client, test_data):
    """Test updating a compliance requirement with an invalid regulation."""
    requirement_data = {
        "regulation_id": 999
    }
    response = client.put(f"/api/v1/compliance-requirements/{test_data['requirement'].id}", json=requirement_data)
    assert response.status_code == 400
    assert "Regulation not found" in response.json()["detail"]

def test_delete_compliance_requirement(client, test_data):
    """Test deleting a compliance requirement."""
    # Create a new requirement first
    requirement_data = {
        "text": "Requirement to Delete",
        "section": "Section to Delete",
        "priority": "Medium",
        "status": "Not Started",
        "regulation_id": test_data["regulation"].id
    }
    create_response = client.post("/api/v1/compliance-requirements/", json=requirement_data)
    assert create_response.status_code == 201
    requirement_id = create_response.json()["id"]
    
    # Delete the requirement
    response = client.delete(f"/api/v1/compliance-requirements/{requirement_id}")
    assert response.status_code == 204
    
    # Verify the requirement is deleted
    get_response = client.get(f"/api/v1/compliance-requirements/{requirement_id}")
    assert get_response.status_code == 404

def test_delete_compliance_requirement_not_found(client):
    """Test deleting a non-existent compliance requirement."""
    response = client.delete("/api/v1/compliance-requirements/999")
    assert response.status_code == 404
    assert response.json()["detail"] == "Compliance requirement not found"
