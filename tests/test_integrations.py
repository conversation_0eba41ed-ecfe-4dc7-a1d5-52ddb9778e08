
"""
Tests for integration APIs, specifically SuperGlu integration.
"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

from app.main import app

client = TestClient(app)

def test_list_available_integrations():
    """Test the endpoint that lists available integrations."""
    response = client.get("/api/v1/integrations/")
    assert response.status_code == 200
    data = response.json()
    assert "available_integrations" in data
    assert any(integration["name"] == "SuperGlu" for integration in data["available_integrations"])

def test_superglu_status():
    """Test the SuperGlu status endpoint."""
    response = client.get("/api/v1/integrations/superglu/status")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "SuperGlu integration is operational" in data["message"]

def test_superglu_sync_entity():
    """Test the entity synchronization endpoint."""
    response = client.post("/api/v1/integrations/superglu/sync/regulations?sync_all=false")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "synced_count" in data["data"]

def test_superglu_sync_invalid_entity():
    """Test sync with an invalid entity type."""
    response = client.post("/api/v1/integrations/superglu/sync/invalid_type")
    assert response.status_code == 400
    
def test_superglu_get_field_mappings():
    """Test retrieving field mappings."""
    response = client.get("/api/v1/integrations/superglu/mappings")
    assert response.status_code == 200
    data = response.json()
    assert "regulation" in data
    assert "compliance_task" in data
