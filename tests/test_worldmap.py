
"""Test cases for the worldmap functionality."""
from unittest.mock import patch, MagicMock

import pytest
import folium
from fastapi.testclient import TestClient

from main import app
from app.visualization import worldmap


@pytest.fixture
def client():
    """
    Create a test client for the FastAPI application.
    
    Returns:
        TestClient: The test client instance
    """
    return TestClient(app)


def test_create_world_map():
    """Test creating a world map with default parameters."""
    map_obj = worldmap.create_world_map()
    
    # Test that a folium Map object is returned
    assert isinstance(map_obj, folium.Map)
    
    # Test default values
    assert map_obj.location == [0, 0]
    assert map_obj.zoom_start == 2


def test_create_world_map_custom_params():
    """Test creating a world map with custom parameters."""
    # Test with custom center and zoom
    map_obj = worldmap.create_world_map(center=(40.7128, -74.0060), zoom_start=10)
    
    # Verify custom values
    assert map_obj.location == [40.7128, -74.0060]
    assert map_obj.zoom_start == 10


def test_get_map_html():
    """Test getting HTML from a map object."""
    # Create a mock map object
    mock_map = MagicMock()
    mock_map._repr_html_.return_value = "<div>Map HTML</div>"
    
    # Get HTML from mock map
    html = worldmap.get_map_html(mock_map)
    
    # Verify HTML was retrieved
    assert html == "<div>Map HTML</div>"
    mock_map._repr_html_.assert_called_once()


def test_worldmap_endpoint(client):
    """
    Test the worldmap endpoint.
    
    Args:
        client (TestClient): The test client
    """
    # Mock the map creation and HTML generation
    with patch('worldmap.create_world_map') as mock_create_map, \
         patch('worldmap.get_map_html') as mock_get_html:
        
        # Setup mocks
        mock_map = MagicMock()
        mock_create_map.return_value = mock_map
        mock_get_html.return_value = "<div>Test Map</div>"
        
        # Make the request with default parameters
        response = client.get("/worldmap")
        
        # Assertions
        assert response.status_code == 200
        assert response.text == "<div>Test Map</div>"
        mock_create_map.assert_called_once_with(center=(0.0, 0.0), zoom_start=2)
        mock_get_html.assert_called_once_with(mock_map)
        
        # Reset mocks
        mock_create_map.reset_mock()
        mock_get_html.reset_mock()
        
        # Test with custom parameters
        response = client.get("/worldmap?lat=40.7128&lon=-74.0060&zoom=12")
        
        # Assertions
        assert response.status_code == 200
        mock_create_map.assert_called_once_with(center=(40.7128, -74.0060), zoom_start=12)
import pytest
from app.visualization import create_world_map, get_map_html, add_markers, add_country_highlights

def test_create_world_map():
    """Test world map creation with different parameters."""
    # Test with default parameters
    map_obj = create_world_map()
    assert map_obj is not None
    
    # Test with custom parameters
    custom_map = create_world_map(center=(42.0, -71.0), zoom_start=10)
    assert custom_map is not None
    
def test_get_map_html():
    """Test HTML generation from map object."""
    map_obj = create_world_map()
    html = get_map_html(map_obj)
    assert isinstance(html, str)
    assert "<html" in html
    assert "<div id='map'" in html
    
def test_add_markers():
    """Test adding markers to the map."""
    map_obj = create_world_map()
    locations = [
        {"lat": 40.7128, "lng": -74.0060, "popup": "New York"},
        {"lat": 34.0522, "lng": -118.2437, "popup": "Los Angeles"}
    ]
    result_map = add_markers(map_obj, locations)
    assert result_map is not None
    # The map object should be modified in-place
    assert result_map is map_obj
    
def test_add_country_highlights():
    """Test adding country highlights to the map."""
    map_obj = create_world_map()
    countries = [
        {"code": "US", "style": {"fillColor": "#ff0000", "weight": 2}},
        {"code": "CA", "style": {"fillColor": "#00ff00", "weight": 1}}
    ]
    result_map = add_country_highlights(map_obj, countries)
    assert result_map is not None
    # The map object should be modified in-place
    assert result_map is map_obj
"""Test cases for the worldmap module."""
import pytest
from unittest.mock import patch, MagicMock

from app.visualization import worldmap


def test_create_world_map():
    """Test creating a world map with default parameters."""
    with patch('folium.Map') as mock_map:
        # Setup mock
        mock_map_instance = MagicMock()
        mock_map.return_value = mock_map_instance
        
        # Call the function
        result = worldmap.create_world_map()
        
        # Assertions
        assert result == mock_map_instance
        mock_map.assert_called_once()
        
        # Get the call arguments
        args, kwargs = mock_map.call_args
        
        # Check default values
        assert kwargs['center'] == (0, 0)
        assert kwargs['zoom_start'] == 2
        assert kwargs['tiles'] == 'OpenStreetMap'


def test_create_world_map_with_custom_params():
    """Test creating a world map with custom parameters."""
    with patch('folium.Map') as mock_map:
        # Setup mock
        mock_map_instance = MagicMock()
        mock_map.return_value = mock_map_instance
        
        # Call the function with custom params
        custom_center = (40.7128, -74.0060)  # New York coordinates
        custom_zoom = 10
        custom_tiles = 'Stamen Terrain'
        
        result = worldmap.create_world_map(
            center=custom_center,
            zoom_start=custom_zoom,
            tiles=custom_tiles
        )
        
        # Assertions
        assert result == mock_map_instance
        
        # Get the call arguments
        args, kwargs = mock_map.call_args
        
        # Check custom values
        assert kwargs['center'] == custom_center
        assert kwargs['zoom_start'] == custom_zoom
        assert kwargs['tiles'] == custom_tiles


def test_get_map_html():
    """Test getting HTML representation of a map."""
    # Create a mock map object
    mock_map = MagicMock()
    mock_map._repr_html_.return_value = '<div>Map HTML</div>'
    
    # Get the HTML
    html = worldmap.get_map_html(mock_map)
    
    # Assertions
    assert html == '<div>Map HTML</div>'
    mock_map._repr_html_.assert_called_once()


def test_add_marker():
    """Test adding a marker to the map."""
    with patch('folium.Map') as mock_map_class, \
         patch('folium.Marker') as mock_marker_class:
        
        # Setup mocks
        mock_map = MagicMock()
        mock_map_class.return_value = mock_map
        
        mock_marker = MagicMock()
        mock_marker_class.return_value = mock_marker
        
        # Create a map
        map_obj = worldmap.create_world_map()
        
        # Add a marker
        location = (51.5074, -0.1278)  # London coordinates
        popup = "London"
        
        worldmap.add_marker(map_obj, location, popup)
        
        # Assertions
        mock_marker_class.assert_called_once()
        args, kwargs = mock_marker_class.call_args
        assert kwargs['location'] == location
        assert kwargs['popup'] == popup
        
        # Check that the marker was added to the map
        mock_marker.add_to.assert_called_once_with(mock_map)
