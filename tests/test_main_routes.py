
"""Test cases for main application routes including documentation endpoints."""
import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import patch, MagicMock

from main import app


@pytest.fixture
def client():
    """
    Create a test client for the FastAPI application.
    
    Returns:
        TestClient: The test client instance
    """
    return TestClient(app)


def test_root_endpoint(client):
    """
    Test the root endpoint returns the dashboard correctly.
    
    Args:
        client (TestClient): The test client fixture
    """
    response = client.get("/")
    assert response.status_code == 200
    assert "text/html" in response.headers["content-type"]
    assert "RegulationGuru" in response.text
    assert "Welcome to RegulationGuru" in response.text


def test_api_docs_endpoints(client):
    """
    Test the API documentation endpoints are accessible and functioning.
    
    Args:
        client (TestClient): The test client fixture
    """
    # Test Swagger UI docs endpoint
    response = client.get("/docs")
    assert response.status_code == 200
    assert "text/html" in response.headers["content-type"]
    assert "swagger" in response.text.lower() or "openapi" in response.text.lower()
    
    # Test ReDoc endpoint
    response = client.get("/redoc")
    assert response.status_code == 200
    assert "text/html" in response.headers["content-type"]
    assert "redoc" in response.text.lower()
    
    # Test OpenAPI JSON schema
    response = client.get("/openapi.json")
    assert response.status_code == 200
    assert "application/json" in response.headers["content-type"]
    json_data = response.json()
    assert "openapi" in json_data
    assert "paths" in json_data
    assert "components" in json_data


def test_api_v1_docs_redirect(client):
    """
    Test that /api/v1/docs redirects to /docs.
    
    Args:
        client (TestClient): The test client fixture
    """
    response = client.get("/api/v1/docs", allow_redirects=False)
    assert response.status_code == 307  # Temporary redirect
    assert response.headers["location"] == "/docs"
    
    # Follow the redirect
    response = client.get("/api/v1/docs", allow_redirects=True)
    assert response.status_code == 200
    assert "text/html" in response.headers["content-type"]


def test_health_check(client):
    """
    Test the health check endpoints.
    
    Args:
        client (TestClient): The test client fixture
    """
    # Test the root health endpoint
    response = client.get("/health")
    assert response.status_code == 200
    
    # Test the API v1 health endpoint
    response_v1 = client.get("/api/v1/health")
    assert response_v1.status_code == 200
    
    # Verify both return the same structure
    assert "status" in response.json()
    assert "timestamp" in response.json()
    assert "database" in response.json()
    assert response.json()["status"] == "healthy"


def test_api_v1_redirect(client):
    """
    Test that /api/v1 redirects to the docs.
    
    Args:
        client (TestClient): The test client fixture
    """
    response = client.get("/api/v1", allow_redirects=False)
    assert response.status_code in [301, 302, 307, 308]  # Any redirect status code
    
    # Follow the redirect
    response = client.get("/api/v1", allow_redirects=True)
    assert response.status_code == 200


def test_static_files_endpoint(client):
    """
    Test that static files can be accessed.
    
    Args:
        client (TestClient): The test client fixture
    """
    # Try to access a CSS file
    response = client.get("/static/css/main.css")
    # If the file exists, it should return 200, otherwise 404
    assert response.status_code in [200, 404]
    if response.status_code == 200:
        assert "text/css" in response.headers["content-type"]


def test_nonexistent_route(client):
    """
    Test that a 404 response is returned for nonexistent routes.
    
    Args:
        client (TestClient): The test client fixture
    """
    response = client.get("/nonexistent_route")
    assert response.status_code == 404


def test_dashboard_with_language_query(client):
    """
    Test that the dashboard can handle language query parameters.
    
    Args:
        client (TestClient): The test client fixture
    """
    response = client.get("/?lang=de")
    assert response.status_code == 200
    assert "text/html" in response.headers["content-type"]
    
    # Check that the language cookie is set
    cookies = response.cookies
    assert "lang" in cookies
    assert cookies["lang"] == "de"


def test_regulatory_dashboard(client):
    """
    Test the regulatory dashboard endpoint.
    
    Args:
        client (TestClient): The test client fixture
    """
    response = client.get("/regulatory_dashboard")
    assert response.status_code == 200
    assert "text/html" in response.headers["content-type"]


def test_languages_endpoint(client):
    """
    Test the languages API endpoint.
    
    Args:
        client (TestClient): The test client fixture
    """
    response = client.get("/api/v1/languages")
    assert response.status_code == 200
    json_data = response.json()
    assert "current_language" in json_data
    assert "available_languages" in json_data
    assert "en_US" in json_data["available_languages"]


def test_swagger_ui_middleware(client):
    """
    Test that the SwaggerUI middleware is functioning.
    
    Args:
        client (TestClient): The test client fixture
    """
    # This test specifically looks for the dark mode CSS injection in the Swagger UI
    response = client.get("/docs")
    assert response.status_code == 200
    
    # The SwaggerUI middleware should inject some CSS for dark mode
    assert "swagger-ui" in response.text
    # This could be brittle if the middleware changes how it injects CSS
    # So we'll just check for basic indicators
    assert "html" in response.text
    assert "head" in response.text
    assert "body" in response.text
