"""Tests for the regulator management API endpoints."""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from main import app
from app.db import models


@pytest.fixture
def test_country(db: Session):
    """Create a test country."""
    country = models.Country(name="Test Country", code="TC")
    db.add(country)
    db.commit()
    db.refresh(country)
    yield country
    db.delete(country)
    db.commit()


@pytest.fixture
def test_regulator(db: Session, test_country):
    """Create a test regulator."""
    regulator = models.Regulator(
        name="Test Regulator",
        description="Test Description",
        type="Test Type",
        website="https://example.com",
        country_id=test_country.id
    )
    db.add(regulator)
    db.commit()
    db.refresh(regulator)
    yield regulator
    db.delete(regulator)
    db.commit()


def test_get_regulators(client: TestClient, test_regulator):
    """Test getting list of regulators."""
    response = client.get("/api/v1/regulators/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) >= 1


def test_get_regulator(client: TestClient, test_regulator):
    """Test getting a specific regulator by ID."""
    response = client.get(f"/api/v1/regulators/{test_regulator.id}")
    assert response.status_code == 200
    assert response.json()["id"] == test_regulator.id
    assert response.json()["name"] == test_regulator.name


def test_get_regulator_not_found(client: TestClient):
    """Test getting a non-existent regulator."""
    response = client.get("/api/v1/regulators/999999")
    assert response.status_code == 404
    assert "not found" in response.json()["detail"].lower()


def test_create_regulator(client: TestClient, test_country):
    """Test creating a new regulator."""
    data = {
        "name": "New Regulator",
        "description": "New Description",
        "type": "New Type",
        "website": "https://new-example.com",
        "country_id": test_country.id
    }
    response = client.post("/api/v1/regulators/", json=data)
    assert response.status_code == 201
    assert response.json()["name"] == data["name"]
    assert response.json()["country"]["id"] == test_country.id


def test_create_regulator_invalid_country(client: TestClient):
    """Test creating a regulator with invalid country ID."""
    data = {
        "name": "New Regulator",
        "description": "New Description",
        "type": "New Type",
        "website": "https://new-example.com",
        "country_id": 999999
    }
    response = client.post("/api/v1/regulators/", json=data)
    assert response.status_code == 404
    assert "country not found" in response.json()["detail"].lower()


def test_update_regulator(client: TestClient, test_regulator):
    """Test updating a regulator."""
    data = {
        "name": "Updated Regulator",
        "description": "Updated Description"
    }
    response = client.put(f"/api/v1/regulators/{test_regulator.id}", json=data)
    assert response.status_code == 200
    assert response.json()["name"] == data["name"]
    assert response.json()["description"] == data["description"]
    # Fields not in the update should remain unchanged
    assert response.json()["type"] == test_regulator.type


def test_update_regulator_not_found(client: TestClient):
    """Test updating a non-existent regulator."""
    data = {"name": "Updated Regulator"}
    response = client.put("/api/v1/regulators/999999", json=data)
    assert response.status_code == 404
    assert "not found" in response.json()["detail"].lower()


def test_delete_regulator(client: TestClient, db: Session, test_country):
    """Test deleting a regulator."""
    # Create a temporary regulator for this test
    regulator = models.Regulator(
        name="Regulator To Delete",
        country_id=test_country.id
    )
    db.add(regulator)
    db.commit()
    db.refresh(regulator)

    response = client.delete(f"/api/v1/regulators/{regulator.id}")
    assert response.status_code == 200

    # Verify it's deleted
    db_regulator = db.query(models.Regulator).filter(models.Regulator.id == regulator.id).first()
    assert db_regulator is None


def test_delete_regulator_not_found(client: TestClient):
    """Test deleting a non-existent regulator."""
    response = client.delete("/api/v1/regulators/999999")
    assert response.status_code == 404
    assert "not found" in response.json()["detail"].lower()


def test_delete_regulator_with_regulations(client: TestClient, db: Session, test_regulator):
    """Test deleting a regulator with associated regulations."""
    # Create a regulation URL associated with the test regulator
    regulation = models.RegulationURL(
        url="https://example.com/regulation",
        domain="example.com",
        regulator_id=test_regulator.id,
        confidence_level=0.9
    )
    db.add(regulation)
    db.commit()

    response = client.delete(f"/api/v1/regulators/{test_regulator.id}")
    assert response.status_code == 400
    assert "associated regulations" in response.json()["detail"].lower()

    # Clean up
    db.delete(regulation)
    db.commit()
import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

from app.main import app

client = TestClient(app)

def test_get_regulator_success():
    """Test successful retrieval of a regulator"""
    # Mock response data
    test_regulator = {
        "id": 1, 
        "name": "Regulator A", 
        "country": "USA"
    }
    
    with patch("main.regulators", {1: test_regulator}):
        with patch("main.countries", {"USA": "United States of America"}):
            response = client.get("/api/v1/regulators/1")
            assert response.status_code == 200
            assert response.json() == {
                "regulator": test_regulator,
                "country_name": "United States of America"
            }

def test_get_regulator_unknown_country():
    """Test retrieval of a regulator with unknown country"""
    # Mock response data
    test_regulator = {
        "id": 1, 
        "name": "Regulator A", 
        "country": "XYZ"
    }
    
    with patch("main.regulators", {1: test_regulator}):
        with patch("main.countries", {}):
            response = client.get("/api/v1/regulators/1")
            assert response.status_code == 200
            assert response.json() == {
                "regulator": test_regulator,
                "country_name": "Unknown"
            }

def test_get_regulator_not_found():
    """Test retrieval of a non-existent regulator"""
    with patch("main.regulators", {}):
        response = client.get("/api/v1/regulators/999")
        assert response.status_code == 200
        assert "detail" in response.json()
        assert response.json()["detail"] == "Regulator not found"

def test_list_regulators():
    """Test listing all regulators"""
    test_regulators = {
        1: {"id": 1, "name": "Regulator A", "country": "USA"},
        2: {"id": 2, "name": "Regulator B", "country": "Canada"}
    }
    
    with patch("main.regulators", test_regulators):
        response = client.get("/api/v1/regulators/")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 2
