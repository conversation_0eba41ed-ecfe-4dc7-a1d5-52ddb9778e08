
"""Tests for the SwaggerUI middleware."""
import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import <PERSON><PERSON><PERSON>, patch

from main import app
from app.middleware.swagger_ui import SwaggerUIMiddleware


@pytest.fixture
def client():
    """Create a test client for the FastAPI application."""
    return TestClient(app)


def test_swagger_ui_middleware_injection(client):
    """Test that the SwaggerUI middleware injects content into the response."""
    response = client.get("/docs")
    assert response.status_code == 200
    
    # The middleware should modify the response body to inject CSS
    assert "swagger-ui" in response.text
    
    # Look for indicators of the dark mode CSS that the middleware injects
    # These checks will depend on what your middleware actually does
    # Modify these assertions based on your actual implementation
    assert "<style" in response.text
    
    # If your middleware adds a dark mode toggle, check for that
    # This is just an example, adjust based on your actual implementation
    dark_mode_indicators = [
        "dark-mode", "dark_mode", "theme-switch", "toggle-theme"
    ]
    found_indicator = any(indicator in response.text.lower() for indicator in dark_mode_indicators)
    assert found_indicator, "No dark mode indicators found in the response"


def test_middleware_only_modifies_swagger_ui():
    """Test that the middleware only modifies Swagger UI responses and not others."""
    # Create a mock response for Swagger UI
    swagger_response = MagicMock()
    swagger_response.headers = {"content-type": "text/html"}
    swagger_response.url.path = "/docs"
    swagger_response.body = b"<html><head></head><body><div id='swagger-ui'></div></body></html>"
    
    # Create a mock response for a regular endpoint
    regular_response = MagicMock()
    regular_response.headers = {"content-type": "text/html"}
    regular_response.url.path = "/regular-page"
    regular_response.body = b"<html><head></head><body><div>Regular content</div></body></html>"
    
    # Create a middleware instance
    middleware = SwaggerUIMiddleware(app=None)
    
    # Mock the dispatch method to return our responses
    middleware.dispatch = MagicMock()
    
    # Test with Swagger UI response
    with patch.object(middleware, 'dispatch', return_value=swagger_response):
        request = MagicMock()
        request.url.path = "/docs"
        modified_response = middleware.process_response(request, swagger_response)
        # The body should be modified for Swagger UI
        assert modified_response.body != swagger_response.body
    
    # Test with regular response
    with patch.object(middleware, 'dispatch', return_value=regular_response):
        request = MagicMock()
        request.url.path = "/regular-page"
        modified_response = middleware.process_response(request, regular_response)
        # The body should remain unchanged for regular responses
        assert modified_response.body == regular_response.body
