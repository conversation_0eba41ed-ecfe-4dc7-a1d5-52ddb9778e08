
import pytest
from unittest.mock import patch, MagicMock
import sys
import os
from fastapi.testclient import TestClient
import datetime
import json

# Add app directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.main import app
from app.db.models import Country, Regulator, RegulationURL, RegulationText

client = TestClient(app)

class TestAPIIntegration:
    """Integration tests for API endpoints."""

    @pytest.fixture
    def setup_test_data(self, db_session):
        """Set up test data for API integration tests."""
        # Create test countries
        country1 = Country(name="United States", code="US", region="North America")
        country2 = Country(name="United Kingdom", code="UK", region="Europe")
        db_session.add_all([country1, country2])
        db_session.flush()
        
        # Create test regulators
        reg1 = Regulator(name="FTC", country_id=country1.id, website="https://ftc.gov")
        reg2 = Regulator(name="ICO", country_id=country2.id, website="https://ico.org.uk")
        db_session.add_all([reg1, reg2])
        db_session.flush()
        
        # Create test regulation URLs
        url1 = RegulationURL(
            url="https://ftc.gov/privacy",
            country_id=country1.id,
            regulator_id=reg1.id,
            title="Privacy Rule",
            date_added=datetime.datetime.now()
        )
        url2 = RegulationURL(
            url="https://ico.org.uk/gdpr",
            country_id=country2.id,
            regulator_id=reg2.id,
            title="GDPR Guidance",
            date_added=datetime.datetime.now()
        )
        db_session.add_all([url1, url2])
        db_session.flush()
        
        # Create test regulation texts
        text1 = RegulationText(
            url_id=url1.id,
            text="This is a test privacy regulation text",
            language="en",
            date_processed=datetime.datetime.now()
        )
        text2 = RegulationText(
            url_id=url2.id,
            text="This is a test GDPR guidance text",
            language="en",
            date_processed=datetime.datetime.now()
        )
        db_session.add_all([text1, text2])
        db_session.commit()
        
        # Return IDs for use in tests
        return {
            "countries": [country1.id, country2.id],
            "regulators": [reg1.id, reg2.id],
            "urls": [url1.id, url2.id],
            "texts": [text1.id, text2.id]
        }

    def test_get_countries(self, setup_test_data):
        """Test GET /api/v1/countries endpoint."""
        response = client.get("/api/v1/countries")
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 2
        
        # Check if test countries are in the response
        country_names = [country["name"] for country in data]
        assert "United States" in country_names
        assert "United Kingdom" in country_names

    def test_get_country_by_id(self, setup_test_data):
        """Test GET /api/v1/countries/{country_id} endpoint."""
        country_id = setup_test_data["countries"][0]
        
        response = client.get(f"/api/v1/countries/{country_id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data["id"] == country_id
        assert data["name"] == "United States"
        assert data["code"] == "US"

    def test_get_regulators(self, setup_test_data):
        """Test GET /api/v1/regulators endpoint."""
        response = client.get("/api/v1/regulators")
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 2
        
        # Check if test regulators are in the response
        regulator_names = [reg["name"] for reg in data]
        assert "FTC" in regulator_names
        assert "ICO" in regulator_names

    def test_get_regulator_by_id(self, setup_test_data):
        """Test GET /api/v1/regulators/{regulator_id} endpoint."""
        regulator_id = setup_test_data["regulators"][0]
        
        response = client.get(f"/api/v1/regulators/{regulator_id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data["id"] == regulator_id
        assert data["name"] == "FTC"
        assert data["website"] == "https://ftc.gov"

    def test_get_regulation_urls(self, setup_test_data):
        """Test GET /api/v1/regulation-urls endpoint."""
        response = client.get("/api/v1/regulation-urls")
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 2
        
        # Check if test URLs are in the response
        url_titles = [url["title"] for url in data]
        assert "Privacy Rule" in url_titles
        assert "GDPR Guidance" in url_titles

    def test_get_regulation_url_by_id(self, setup_test_data):
        """Test GET /api/v1/regulation-urls/{url_id} endpoint."""
        url_id = setup_test_data["urls"][0]
        
        response = client.get(f"/api/v1/regulation-urls/{url_id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data["id"] == url_id
        assert data["title"] == "Privacy Rule"
        assert data["url"] == "https://ftc.gov/privacy"

    def test_get_regulation_text_by_url_id(self, setup_test_data):
        """Test GET /api/v1/regulation-urls/{url_id}/text endpoint."""
        url_id = setup_test_data["urls"][0]
        
        response = client.get(f"/api/v1/regulation-urls/{url_id}/text")
        assert response.status_code == 200
        
        data = response.json()
        assert data["url_id"] == url_id
        assert "This is a test privacy regulation text" in data["text"]

    def test_search_regulations(self, setup_test_data):
        """Test GET /api/v1/regulations/search endpoint."""
        # Search for "privacy"
        response = client.get("/api/v1/regulations/search?query=privacy")
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list)
        # At least one result should contain "privacy"
        assert any("privacy" in url.get("text", "").lower() for url in data)
        
        # Search for "GDPR"
        response = client.get("/api/v1/regulations/search?query=GDPR")
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list)
        # At least one result should contain "GDPR"
        assert any("GDPR" in url.get("title", "").upper() for url in data)

    def test_health_check(self):
        """Test GET /health endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert data["status"] == "ok"
        assert "database" in data
