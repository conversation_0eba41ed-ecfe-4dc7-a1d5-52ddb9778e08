"""
Integration tests for the Regulatory Change Workflow Automation feature.
"""
import unittest
import os
import datetime
import json
from unittest.mock import patch

# Import the modules to be tested
# These imports will need to be updated based on the actual implementation
from app import create_app, db
from app.regulatory_change.models import (
    RegulatoryChange,
    WorkflowDefinition,
    WorkflowInstance,
    WorkflowStage,
    WorkflowTask,
    ImpactAssessment,
    ImplementationPlan,
    ImplementationMilestone,
    ImplementationTask
)
from app.auth.models import User, Role


class TestRegulatoryChangeIntegration(unittest.TestCase):
    """Integration tests for Regulatory Change Workflow Automation."""

    @classmethod
    def setUpClass(cls):
        """Set up test database and application."""
        # Configure test environment
        os.environ['FLASK_ENV'] = 'testing'
        os.environ['DATABASE_URL'] = 'sqlite:///:memory:'
        
        # Create application
        cls.app = create_app()
        cls.app_context = cls.app.app_context()
        cls.app_context.push()
        
        # Create database tables
        db.create_all()
        
        # Create test client
        cls.client = cls.app.test_client()
        
        # Create test user and roles
        admin_role = Role(name='Admin', description='Administrator')
        compliance_role = Role(name='Compliance', description='Compliance Officer')
        db.session.add(admin_role)
        db.session.add(compliance_role)
        
        test_user = User(
            username='testuser',
            email='<EMAIL>',
            password='password123',
            first_name='Test',
            last_name='User'
        )
        test_user.roles.append(admin_role)
        test_user.roles.append(compliance_role)
        db.session.add(test_user)
        
        # Create workflow definition
        workflow_def = WorkflowDefinition(
            name='Standard Regulatory Change Workflow',
            description='Standard workflow for regulatory changes'
        )
        db.session.add(workflow_def)
        
        # Create workflow stages
        stage1 = WorkflowStage(
            name='Initial Assessment',
            description='Initial assessment of the regulatory change',
            order=1,
            workflow_definition=workflow_def
        )
        
        stage2 = WorkflowStage(
            name='Impact Analysis',
            description='Detailed impact analysis of the change',
            order=2,
            workflow_definition=workflow_def
        )
        
        stage3 = WorkflowStage(
            name='Implementation Planning',
            description='Planning for implementation of the change',
            order=3,
            workflow_definition=workflow_def
        )
        
        stage4 = WorkflowStage(
            name='Implementation',
            description='Implementation of the change',
            order=4,
            workflow_definition=workflow_def
        )
        
        stage5 = WorkflowStage(
            name='Verification',
            description='Verification of compliance',
            order=5,
            workflow_definition=workflow_def
        )
        
        db.session.add_all([stage1, stage2, stage3, stage4, stage5])
        
        # Create tasks for stages
        task1 = WorkflowTask(
            name='Review Regulatory Change',
            description='Review the details of the regulatory change',
            estimated_effort=2.0,
            stage=stage1
        )
        
        task2 = WorkflowTask(
            name='Determine Applicability',
            description='Determine if the change applies to the organization',
            estimated_effort=1.5,
            stage=stage1
        )
        
        db.session.add_all([task1, task2])
        
        # Commit changes
        db.session.commit()
        
        # Store IDs for later use
        cls.user_id = test_user.id
        cls.workflow_def_id = workflow_def.id

    @classmethod
    def tearDownClass(cls):
        """Clean up test database."""
        db.session.remove()
        db.drop_all()
        cls.app_context.pop()

    def setUp(self):
        """Set up test case."""
        # Log in test user
        response = self.client.post(
            '/api/v1/auth/login',
            json={
                'username': 'testuser',
                'password': 'password123'
            }
        )
        data = json.loads(response.data)
        self.token = data['token']
        self.headers = {
            'Authorization': f'Bearer {self.token}',
            'Content-Type': 'application/json'
        }

    def test_end_to_end_workflow(self):
        """Test end-to-end regulatory change workflow process."""
        # Step 1: Create a regulatory change
        reg_change_data = {
            'title': 'New Security Requirements',
            'description': 'Financial institutions shall implement new security measures by January 1, 2023.',
            'source': 'Test Regulatory Authority',
            'change_type': 'NEW_REGULATION',
            'jurisdiction': 'US',
            'industry_sector': 'Banking',
            'business_function': 'IT',
            'impact_level': 'Medium',
            'compliance_deadline': '2023-01-01'
        }
        
        response = self.client.post(
            '/api/v1/regulatory-changes',
            headers=self.headers,
            json=reg_change_data
        )
        
        self.assertEqual(response.status_code, 201)
        reg_change = json.loads(response.data)
        reg_change_id = reg_change['id']
        
        # Step 2: Create a workflow instance
        workflow_data = {
            'workflow_definition_id': self.workflow_def_id,
            'regulatory_change_id': reg_change_id
        }
        
        response = self.client.post(
            '/api/v1/workflow-instances',
            headers=self.headers,
            json=workflow_data
        )
        
        self.assertEqual(response.status_code, 201)
        workflow = json.loads(response.data)
        workflow_id = workflow['id']
        
        # Verify workflow is in initial stage
        self.assertEqual(workflow['current_stage'], 'Initial Assessment')
        self.assertEqual(workflow['status'], 'IN_PROGRESS')
        
        # Step 3: Assign tasks
        # Get tasks for current stage
        response = self.client.get(
            f'/api/v1/workflow-instances/{workflow_id}/tasks',
            headers=self.headers
        )
        
        self.assertEqual(response.status_code, 200)
        tasks = json.loads(response.data)
        self.assertEqual(len(tasks), 2)
        
        # Assign tasks to user
        for task in tasks:
            assignment_data = {
                'assignee_id': self.user_id
            }
            
            response = self.client.post(
                f'/api/v1/workflow-instances/tasks/{task["id"]}/assign',
                headers=self.headers,
                json=assignment_data
            )
            
            self.assertEqual(response.status_code, 200)
        
        # Step 4: Complete tasks
        for task in tasks:
            completion_data = {
                'completion_notes': f'Completed task: {task["name"]}',
                'actual_effort': task['estimated_effort'] + 0.5  # Slightly more than estimated
            }
            
            response = self.client.post(
                f'/api/v1/workflow-instances/tasks/{task["id"]}/complete',
                headers=self.headers,
                json=completion_data
            )
            
            self.assertEqual(response.status_code, 200)
        
        # Step 5: Progress workflow to next stage
        response = self.client.post(
            f'/api/v1/workflow-instances/{workflow_id}/progress',
            headers=self.headers
        )
        
        self.assertEqual(response.status_code, 200)
        updated_workflow = json.loads(response.data)
        self.assertEqual(updated_workflow['current_stage'], 'Impact Analysis')
        
        # Step 6: Create impact assessment
        assessment_data = {
            'workflow_instance_id': workflow_id,
            'regulatory_change_id': reg_change_id,
            'template_id': 'standard'  # Using standard template
        }
        
        response = self.client.post(
            '/api/v1/impact-assessments',
            headers=self.headers,
            json=assessment_data
        )
        
        self.assertEqual(response.status_code, 201)
        assessment = json.loads(response.data)
        assessment_id = assessment['id']
        
        # Step 7: Update impact assessment with data
        assessment_update_data = {
            'data': {
                'Regulatory Overview': {
                    'summary': 'New security requirements for financial institutions',
                    'effective_date': '2023-01-01'
                },
                'Business Impact': {
                    'affected_departments': ['IT', 'Security', 'Operations'],
                    'impact_description': 'Need to update security protocols and systems'
                },
                'Risk Assessment': {
                    'compliance_risk': 'High',
                    'operational_risk': 'Medium',
                    'financial_risk': 'Medium',
                    'overall_risk': 'High'
                },
                'Gap Analysis': {
                    'identified_gaps': [
                        {
                            'category': 'System',
                            'description': 'Current encryption does not meet new requirements',
                            'severity': 'High'
                        },
                        {
                            'category': 'Policy',
                            'description': 'Security policy needs updating',
                            'severity': 'Medium'
                        }
                    ]
                }
            },
            'status': 'COMPLETED'
        }
        
        response = self.client.put(
            f'/api/v1/impact-assessments/{assessment_id}',
            headers=self.headers,
            json=assessment_update_data
        )
        
        self.assertEqual(response.status_code, 200)
        updated_assessment = json.loads(response.data)
        self.assertEqual(updated_assessment['status'], 'COMPLETED')
        
        # Step 8: Progress workflow to implementation planning
        response = self.client.post(
            f'/api/v1/workflow-instances/{workflow_id}/progress',
            headers=self.headers
        )
        
        self.assertEqual(response.status_code, 200)
        updated_workflow = json.loads(response.data)
        self.assertEqual(updated_workflow['current_stage'], 'Implementation Planning')
        
        # Step 9: Create implementation plan
        plan_data = {
            'workflow_instance_id': workflow_id,
            'name': 'Security Requirements Implementation',
            'description': 'Plan to implement new security measures',
            'target_completion_date': '2022-12-15'
        }
        
        response = self.client.post(
            '/api/v1/implementation-plans',
            headers=self.headers,
            json=plan_data
        )
        
        self.assertEqual(response.status_code, 201)
        plan = json.loads(response.data)
        plan_id = plan['id']
        
        # Step 10: Add milestones to implementation plan
        milestones = [
            {
                'name': 'Requirements Gathering',
                'description': 'Gather detailed requirements for security measures',
                'due_date': '2022-07-15',
                'order': 1
            },
            {
                'name': 'System Design',
                'description': 'Design system changes for new security measures',
                'due_date': '2022-09-01',
                'order': 2
            },
            {
                'name': 'Implementation',
                'description': 'Implement system changes',
                'due_date': '2022-11-15',
                'order': 3
            },
            {
                'name': 'Testing',
                'description': 'Test implemented changes',
                'due_date': '2022-12-01',
                'order': 4
            }
        ]
        
        milestone_ids = []
        for milestone_data in milestones:
            response = self.client.post(
                f'/api/v1/implementation-plans/{plan_id}/milestones',
                headers=self.headers,
                json=milestone_data
            )
            
            self.assertEqual(response.status_code, 201)
            milestone = json.loads(response.data)
            milestone_ids.append(milestone['id'])
        
        # Step 11: Add tasks to milestones
        tasks_by_milestone = {
            0: [  # Requirements Gathering
                {
                    'name': 'Review Regulatory Text',
                    'description': 'Review detailed regulatory text',
                    'estimated_effort': 8.0,
                    'assignee_id': self.user_id
                },
                {
                    'name': 'Interview Stakeholders',
                    'description': 'Interview key stakeholders',
                    'estimated_effort': 16.0,
                    'assignee_id': self.user_id
                }
            ],
            1: [  # System Design
                {
                    'name': 'Review Current Architecture',
                    'description': 'Review current system architecture',
                    'estimated_effort': 16.0,
                    'assignee_id': self.user_id
                },
                {
                    'name': 'Design Encryption Changes',
                    'description': 'Design changes to encryption system',
                    'estimated_effort': 24.0,
                    'assignee_id': self.user_id
                }
            ]
        }
        
        task_ids = []
        for milestone_index, tasks in tasks_by_milestone.items():
            milestone_id = milestone_ids[milestone_index]
            for task_data in tasks:
                response = self.client.post(
                    f'/api/v1/implementation-plans/milestones/{milestone_id}/tasks',
                    headers=self.headers,
                    json=task_data
                )
                
                self.assertEqual(response.status_code, 201)
                task = json.loads(response.data)
                task_ids.append(task['id'])
        
        # Step 12: Progress workflow to implementation stage
        response = self.client.post(
            f'/api/v1/workflow-instances/{workflow_id}/progress',
            headers=self.headers
        )
        
        self.assertEqual(response.status_code, 200)
        updated_workflow = json.loads(response.data)
        self.assertEqual(updated_workflow['current_stage'], 'Implementation')
        
        # Step 13: Update task status
        for task_id in task_ids:
            status_data = {
                'status': 'COMPLETED',
                'completion_notes': 'Task completed successfully',
                'actual_effort': 18.5
            }
            
            response = self.client.put(
                f'/api/v1/implementation-plans/tasks/{task_id}/status',
                headers=self.headers,
                json=status_data
            )
            
            self.assertEqual(response.status_code, 200)
            updated_task = json.loads(response.data)
            self.assertEqual(updated_task['status'], 'COMPLETED')
        
        # Step 14: Add evidence to implementation
        evidence_data = {
            'title': 'Implementation Evidence',
            'description': 'Evidence of implementation completion',
            'evidence_type': 'DOCUMENT',
            'file_reference': 'implementation_evidence.pdf'
        }
        
        response = self.client.post(
            f'/api/v1/implementation-plans/{plan_id}/evidence',
            headers=self.headers,
            json=evidence_data
        )
        
        self.assertEqual(response.status_code, 201)
        
        # Step 15: Progress workflow to verification stage
        response = self.client.post(
            f'/api/v1/workflow-instances/{workflow_id}/progress',
            headers=self.headers
        )
        
        self.assertEqual(response.status_code, 200)
        updated_workflow = json.loads(response.data)
        self.assertEqual(updated_workflow['current_stage'], 'Verification')
        
        # Step 16: Complete verification and workflow
        verification_data = {
            'verification_notes': 'All requirements have been implemented and verified',
            'compliance_status': 'COMPLIANT'
        }
        
        response = self.client.post(
            f'/api/v1/workflow-instances/{workflow_id}/verify',
            headers=self.headers,
            json=verification_data
        )
        
        self.assertEqual(response.status_code, 200)
        
        # Step 17: Check final workflow status
        response = self.client.get(
            f'/api/v1/workflow-instances/{workflow_id}',
            headers=self.headers
        )
        
        self.assertEqual(response.status_code, 200)
        final_workflow = json.loads(response.data)
        self.assertEqual(final_workflow['status'], 'COMPLETED')
        
        # Step 18: Generate compliance report
        report_data = {
            'workflow_instance_id': workflow_id,
            'report_type': 'COMPLIANCE_SUMMARY'
        }
        
        response = self.client.post(
            '/api/v1/reports',
            headers=self.headers,
            json=report_data
        )
        
        self.assertEqual(response.status_code, 201)
        report = json.loads(response.data)
        self.assertIn('report_url', report)
        
        # Verify database state
        with self.app.app_context():
            # Check regulatory change
            reg_change_db = RegulatoryChange.query.get(reg_change_id)
            self.assertEqual(reg_change_db.title, 'New Security Requirements')
            
            # Check workflow instance
            workflow_db = WorkflowInstance.query.get(workflow_id)
            self.assertEqual(workflow_db.status, 'COMPLETED')
            
            # Check impact assessment
            assessment_db = ImpactAssessment.query.get(assessment_id)
            self.assertEqual(assessment_db.status, 'COMPLETED')
            
            # Check implementation plan
            plan_db = ImplementationPlan.query.get(plan_id)
            self.assertEqual(len(plan_db.milestones), 4)
            
            # Check tasks
            tasks_db = ImplementationTask.query.filter_by(
                milestone_id=milestone_ids[0]
            ).all()
            self.assertEqual(len(tasks_db), 2)
            self.assertEqual(tasks_db[0].status, 'COMPLETED')


if __name__ == '__main__':
    unittest.main()
