import pytest
import os
import sys
import json

# Check if the file exists
def test_compliance_reporting_dashboard_file_exists():
    """Test that the ComplianceReportingDashboard component file exists."""
    file_path = os.path.join(os.path.dirname(__file__), '../../frontend/src/components/compliance-reporting/ComplianceReportingDashboard.jsx')
    assert os.path.exists(file_path), f"File does not exist: {file_path}"

def test_compliance_reporting_dashboard_index_exists():
    """Test that the index.js file exists."""
    file_path = os.path.join(os.path.dirname(__file__), '../../frontend/src/components/compliance-reporting/index.js')
    assert os.path.exists(file_path), f"File does not exist: {file_path}"

def test_compliance_reporting_dashboard_content():
    """Test that the ComplianceReportingDashboard component has the expected content."""
    file_path = os.path.join(os.path.dirname(__file__), '../../frontend/src/components/compliance-reporting/ComplianceReportingDashboard.jsx')
    with open(file_path, 'r') as f:
        content = f.read()

    # Check for key elements in the component
    assert 'ComplianceReportingDashboard' in content, "Component name not found in file"
    assert 'import React' in content, "React import not found"
    assert 'export default ComplianceReportingDashboard' in content, "Component not exported"
    assert 'Recharts' in content, "Recharts not imported"

def test_compliance_reporting_dashboard_index_content():
    """Test that the index.js file exports the component correctly."""
    file_path = os.path.join(os.path.dirname(__file__), '../../frontend/src/components/compliance-reporting/index.js')
    with open(file_path, 'r') as f:
        content = f.read()

    # Check for export in index.js
    assert 'import ComplianceReportingDashboard' in content, "Component import not found in index.js"
    assert 'export {' in content and 'ComplianceReportingDashboard' in content, "Component not exported from index.js"
