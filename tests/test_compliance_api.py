
"""
Comprehensive tests for the compliance API endpoints.
"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

from app.main import app
from app.api import compliance
from app.utils.regulatory_analysis import (
    calculate_compliance_scores,
    identify_critical_gaps,
    generate_recommendations
)

client = TestClient(app)

class TestComplianceAPI:
    
    def test_get_compliance_summary(self):
        """Test getting compliance summary."""
        response = client.get("/api/v1/compliance/summary")
        
        # Check response
        assert response.status_code == 200
        result = response.json()
        
        # Check structure
        assert "overall_score" in result
        assert "regional_scores" in result
        assert "critical_gaps" in result
        assert "recommendations" in result
        assert "recent_changes" in result
        
        # Check content types
        assert isinstance(result["overall_score"], (int, float))
        assert isinstance(result["regional_scores"], list)
        assert isinstance(result["critical_gaps"], list)
        assert isinstance(result["recommendations"], list)
        assert isinstance(result["recent_changes"], list)
    
    @patch('app.utils.regulatory_analysis.calculate_regional_averages')
    def test_get_compliance_summary_with_mock(self, mock_regional_averages):
        """Test compliance summary with mocked utility functions."""
        # Mock the calculation
        mock_regional_averages.return_value = [
            {"region": "Europe", "score": 85, "country_count": 5},
            {"region": "Americas", "score": 78, "country_count": 2},
            {"region": "Asia", "score": 70, "country_count": 1}
        ]
        
        response = client.get("/api/v1/compliance/summary")
        
        # Check that mock was called
        mock_regional_averages.assert_called_once()
        
        # Check response uses mock data
        assert response.status_code == 200
        result = response.json()
        regional_scores = result["regional_scores"]
        
        # Verify mock data is used
        assert any(r["region"] == "Europe" and r["score"] == 85 for r in regional_scores)
        assert any(r["region"] == "Americas" and r["score"] == 78 for r in regional_scores)
        assert any(r["region"] == "Asia" and r["score"] == 70 for r in regional_scores)
    
    def test_get_country_compliance_all(self):
        """Test getting compliance data for all countries."""
        response = client.get("/api/v1/compliance/countries")
        
        # Check response
        assert response.status_code == 200
        countries = response.json()
        
        # Check structure
        assert isinstance(countries, list)
        assert len(countries) > 0
        
        # Check country data structure
        for country in countries:
            assert "country" in country
            assert "code" in country
            assert "data_protection_score" in country
            assert "breach_notification_score" in country
            assert "penalties_score" in country
            assert "overall_compliance" in country
            assert "region" in country
    
    def test_get_country_compliance_specific(self):
        """Test getting compliance data for a specific country."""
        response = client.get("/api/v1/compliance/countries?country_code=US")
        
        # Check response
        assert response.status_code == 200
        countries = response.json()
        
        # Check filtering
        assert len(countries) == 1
        assert countries[0]["code"] == "US"
        
        # Check data
        assert "data_protection_score" in countries[0]
        assert "breach_notification_score" in countries[0]
        assert "penalties_score" in countries[0]
        assert "overall_compliance" in countries[0]
    
    def test_get_country_compliance_not_found(self):
        """Test getting compliance data for a non-existent country."""
        response = client.get("/api/v1/compliance/countries?country_code=ZZ")
        
        # Check response for not found
        assert response.status_code == 404
        assert "detail" in response.json()
        assert "not found" in response.json()["detail"].lower()
