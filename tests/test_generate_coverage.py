
import pytest
import os
import sys
from unittest.mock import patch, MagicMock
from pathlib import Path

# Add scripts directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'scripts'))

from scripts.generate_coverage import run_command, main

class TestGenerateCoverage:
    
    @patch('subprocess.run')
    def test_run_command_success(self, mock_run):
        # Setup mock
        mock_process = MagicMock()
        mock_process.returncode = 0
        mock_run.return_value = mock_process
        
        # Run the function
        result = run_command("echo 'test'")
        
        # Verify subprocess was called correctly
        mock_run.assert_called_once_with("echo 'test'", shell=True, check=True)
        assert result == 0
    
    @patch('subprocess.run')
    def test_run_command_failure(self, mock_run):
        # Setup mock for failure
        mock_run.side_effect = Exception("Command failed")
        
        # Run the function
        result = run_command("invalid_command")
        
        # Verify subprocess was called and error handling worked
        mock_run.assert_called_once()
        assert result != 0
    
    @patch('os.makedirs')
    @patch('scripts.generate_coverage.run_command')
    @patch('pathlib.Path.exists')
    @patch('builtins.print')
    def test_main_success(self, mock_print, mock_exists, mock_run, mock_makedirs):
        # Setup mocks
        mock_run.return_value = 0
        mock_exists.return_value = True
        
        # Run the function
        result = main()
        
        # Verify function calls
        mock_makedirs.assert_called_once_with("coverage", exist_ok=True)
        mock_run.assert_called_once()
        mock_print.assert_any_call("HTML coverage report generated at: coverage/html/index.html")
        mock_print.assert_any_call("XML coverage report generated at: coverage/coverage.xml")
        assert result == 0
    
    @patch('os.makedirs')
    @patch('scripts.generate_coverage.run_command')
    @patch('pathlib.Path.exists')
    @patch('builtins.print')
    def test_main_tests_failed(self, mock_print, mock_exists, mock_run, mock_makedirs):
        # Setup mocks
        mock_run.return_value = 1
        mock_exists.return_value = True
        
        # Run the function
        result = main()
        
        # Verify function calls
        mock_makedirs.assert_called_once_with("coverage", exist_ok=True)
        mock_run.assert_called_once()
        mock_print.assert_any_call("Tests failed, but still generating coverage reports")
        assert result == 1
    
    @patch('os.makedirs')
    @patch('scripts.generate_coverage.run_command')
    @patch('pathlib.Path.exists')
    @patch('builtins.print')
    def test_main_reports_missing(self, mock_print, mock_exists, mock_run, mock_makedirs):
        # Setup mocks
        mock_run.return_value = 0
        mock_exists.return_value = False
        
        # Run the function
        result = main()
        
        # Verify function calls
        mock_makedirs.assert_called_once_with("coverage", exist_ok=True)
        mock_run.assert_called_once()
        mock_print.assert_any_call("HTML coverage report generation failed")
        mock_print.assert_any_call("XML coverage report generation failed")
        assert result == 0
