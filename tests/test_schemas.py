
"""Test cases for the schemas module."""
import pytest
from datetime import datetime
from pydantic import ValidationError

from app.schemas import schemas


def test_item_schema():
    """Test the Item schema validation and fields."""
    # Test creating a valid item
    item_data = {
        "id": 1,
        "name": "Test Item",
        "description": "A test item for validation",
        "price": 19.99,
        "is_active": True,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
        "is_deleted": False,
        "deleted_at": None
    }
    
    item = schemas.Item(**item_data)
    
    # Check that the fields match
    assert item.id == item_data["id"]
    assert item.name == item_data["name"]
    assert item.description == item_data["description"]
    assert item.price == item_data["price"]
    assert item.is_active == item_data["is_active"]
    assert item.is_deleted == item_data["is_deleted"]
    assert item.deleted_at == item_data["deleted_at"]


def test_item_create_schema():
    """Test the ItemCreate schema validation."""
    # Test creating a valid item
    item_data = {
        "name": "New Item",
        "description": "A newly created item",
        "price": 29.99,
        "is_active": True
    }
    
    item = schemas.ItemCreate(**item_data)
    
    # Check that the fields match
    assert item.name == item_data["name"]
    assert item.description == item_data["description"]
    assert item.price == item_data["price"]
    assert item.is_active == item_data["is_active"]
    
    # Test with missing required field
    with pytest.raises(ValidationError):
        schemas.ItemCreate(description="Missing name field", price=10.0)


def test_country_schema():
    """Test the Country schema validation."""
    country_data = {
        "id": 1,
        "name": "United States",
        "code": "US",
        "region": "North America"
    }
    
    country = schemas.Country(**country_data)
    
    # Check that the fields match
    assert country.id == country_data["id"]
    assert country.name == country_data["name"]
    assert country.code == country_data["code"]
    assert country.region == country_data["region"]


def test_regulator_schema():
    """Test the Regulator schema validation."""
    regulator_data = {
        "id": 1,
        "name": "Securities and Exchange Commission",
        "country_id": 1,
        "type": "Financial",
        "url": "https://www.sec.gov"
    }
    
    regulator = schemas.Regulator(**regulator_data)
    
    # Check that the fields match
    assert regulator.id == regulator_data["id"]
    assert regulator.name == regulator_data["name"]
    assert regulator.country_id == regulator_data["country_id"]
    assert regulator.type == regulator_data["type"]
    assert regulator.url == regulator_data["url"]


def test_regulator_with_country_schema():
    """Test the RegulatorWithCountry schema validation."""
    country_data = {
        "id": 1,
        "name": "United States",
        "code": "US",
        "region": "North America"
    }
    
    regulator_data = {
        "id": 1,
        "name": "Securities and Exchange Commission",
        "country_id": 1,
        "type": "Financial",
        "url": "https://www.sec.gov",
        "country": country_data
    }
    
    regulator = schemas.RegulatorWithCountry(**regulator_data)
    
    # Check that the fields match
    assert regulator.id == regulator_data["id"]
    assert regulator.name == regulator_data["name"]
    assert regulator.country_id == regulator_data["country_id"]
    assert regulator.country.id == country_data["id"]
    assert regulator.country.name == country_data["name"]


def test_regulation_url_schema():
    """Test the RegulationURL schema validation."""
    regulation_data = {
        "id": 1,
        "url": "https://www.sec.gov/rules/final/2023.htm",
        "title": "Final Rules 2023",
        "regulator_id": 1,
        "category": "Financial Regulation",
        "confidence_level": 0.95
    }
    
    regulation = schemas.RegulationURL(**regulation_data)
    
    # Check that the fields match
    assert regulation.id == regulation_data["id"]
    assert regulation.url == regulation_data["url"]
    assert regulation.title == regulation_data["title"]
    assert regulation.regulator_id == regulation_data["regulator_id"]
    assert regulation.category == regulation_data["category"]
    assert regulation.confidence_level == regulation_data["confidence_level"]
    
    # Test confidence level validation
    with pytest.raises(ValidationError):
        schemas.RegulationURL(
            id=1,
            url="https://example.com",
            title="Example",
            regulator_id=1,
            confidence_level=1.5  # Should be between 0 and 1
        )
