
import pytest
from fastapi.testclient import Test<PERSON>lient
import os
import sys
from unittest.mock import patch, MagicMock

# Add root directory to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from app.main import app, startup, shutdown

client = TestClient(app)

class TestMainIntegration:
    def test_api_root(self):
        """Test the API root endpoint."""
        response = client.get("/api/v1")
        assert response.status_code == 200
        assert "version" in response.json()
        assert "endpoints" in response.json()
    
    def test_documentation_available(self):
        """Test that API documentation is available."""
        response = client.get("/docs")
        assert response.status_code == 200
        assert "swagger" in response.text.lower()
        
        response = client.get("/redoc")
        assert response.status_code == 200
        assert "redoc" in response.text.lower()
    
    def test_static_files_served(self):
        """Test that static files are properly served."""
        # This assumes you have a static file in your project
        response = client.get("/static/css/main.css")
        if response.status_code == 200:
            assert "text/css" in response.headers["content-type"]
    
    @patch('app.main.init_db')
    def test_startup_event(self, mock_init_db):
        """Test the startup event handler."""
        # Call the startup event handler
        startup()
        
        # Verify the database was initialized
        mock_init_db.assert_called_once()
    
    @patch('app.main.close_db_connections')
    def test_shutdown_event(self, mock_close_db):
        """Test the shutdown event handler."""
        # Call the shutdown event handler
        shutdown()
        
        # Verify database connections were closed
        mock_close_db.assert_called_once()
    
    def test_cors_middleware(self):
        """Test CORS middleware functionality."""
        # Make a preflight request
        headers = {
            "Access-Control-Request-Method": "POST",
            "Origin": "http://testclient.example.com",
        }
        response = client.options("/api/v1/countries", headers=headers)
        
        # Check CORS headers
        assert response.status_code == 200
        assert "access-control-allow-origin" in response.headers
        assert "access-control-allow-methods" in response.headers
        assert "POST" in response.headers["access-control-allow-methods"]
    
    @patch('app.main.get_translator')
    def test_i18n_integration(self, mock_get_translator):
        """Test internationalization integration."""
        # Setup mock translator
        mock_translator = MagicMock()
        mock_translator.gettext.return_value = "Translated text"
        mock_get_translator.return_value = mock_translator
        
        # Make request with Accept-Language header
        response = client.get(
            "/api/v1/translated-content",
            headers={"Accept-Language": "fr"}
        )
        
        # Check response
        if response.status_code == 200:
            assert "translated" in response.text.lower()
