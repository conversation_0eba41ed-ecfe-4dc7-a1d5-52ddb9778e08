
"""Test cases for main.py endpoints."""
from fastapi.testclient import TestClient
import pytest

from main import app


@pytest.fixture
def client() -> TestClient:
    """
    Create a test client for the FastAPI application.
    
    Returns:
        TestClient: The test client instance
    """
    return TestClient(app)


def test_read_root(client: TestClient) -> None:
    """
    Test the root endpoint returns the expected response.
    
    Args:
        client (TestClient): The test client fixture
    """
    response = client.get("/")
    assert response.status_code == 200
    assert response.json() == {"Hello": "World"}


def test_health_check(client: TestClient) -> None:
    """
    Test the health endpoint returns the correct structure and status.
    
    Args:
        client (TestClient): The test client fixture
    """
    response = client.get("/health")
    assert response.status_code == 200
    json_data = response.json()
    
    # Check keys and types
    assert "status" in json_data
    assert "timestamp" in json_data
    assert "database" in json_data
    assert json_data["status"] == "healthy"
    
    # Test versioned endpoint too
    response_v1 = client.get("/api/v1/health")
    assert response_v1.status_code == 200
    assert response_v1.json() == json_data


def test_api_docs_endpoints(client: TestClient) -> None:
    """
    Test the API documentation endpoints are accessible.
    
    Args:
        client (TestClient): The test client fixture
    """
    # Test docs endpoint
    response = client.get("/docs")
    assert response.status_code == 200
    assert "text/html" in response.headers["content-type"]
    
    # Test redoc endpoint
    response = client.get("/redoc")
    assert response.status_code == 200
    assert "text/html" in response.headers["content-type"]
    
    # Test OpenAPI JSON
    response = client.get("/openapi.json")
    assert response.status_code == 200
    assert response.headers["content-type"] == "application/json"
    assert "openapi" in response.json()
    assert "paths" in response.json()
    assert "components" in response.json()


def test_read_item(client: TestClient) -> None:
    """
    Test the item endpoint returns the expected response with an ID.
    
    Args:
        client (TestClient): The test client fixture
    """
    response = client.get("/items/42")
    assert response.status_code == 200
    assert response.json() == {"item_id": 42, "q": None}


def test_read_item_with_query(client: TestClient) -> None:
    """
    Test the item endpoint returns the expected response with an ID and query.
    
    Args:
        client (TestClient): The test client fixture
    """
    response = client.get("/items/42?q=test")
    assert response.status_code == 200
    assert response.json() == {"item_id": 42, "q": "test"}
