"""
Tests for the caching middleware.
"""
import pytest
from unittest.mock import <PERSON><PERSON><PERSON>, patch
import json
import hashlib
from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from starlette.testclient import TestClient

from app.middleware.caching_middleware import CachingMiddleware

@pytest.fixture
def mock_redis():
    """Mock Redis client."""
    mock_client = MagicMock()
    mock_client.ping.return_value = True
    mock_client.get.return_value = None
    mock_client.setex.return_value = True
    return mock_client

@pytest.fixture
def app_with_middleware(mock_redis):
    """Create a FastAPI app with the caching middleware."""
    with patch("app.middleware.caching_middleware.get_redis_client", return_value=mock_redis):
        app = FastAPI()
        app.add_middleware(
            CachingMiddleware,
            ttl=60,
            exclude_paths=["/api/v1/health", "/api/v1/excluded"],
            exclude_methods=["POST", "PUT", "DELETE"],
            vary_by_headers=["accept-language"],
            vary_by_query_params=["filter"]
        )
        
        @app.get("/api/v1/test")
        def test_endpoint():
            return {"message": "test"}
        
        @app.get("/api/v1/health")
        def health_endpoint():
            return {"status": "healthy"}
        
        @app.get("/api/v1/excluded")
        def excluded_endpoint():
            return {"message": "excluded"}
        
        @app.post("/api/v1/test")
        def test_post_endpoint():
            return {"message": "test post"}
        
        yield app

@pytest.fixture
def client(app_with_middleware):
    """Create a test client for the app."""
    return TestClient(app_with_middleware)

def test_caching_middleware_get_request(client, mock_redis):
    """Test the caching middleware with a GET request."""
    # Make a request
    response = client.get("/api/v1/test")
    
    # Check that the response is correct
    assert response.status_code == 200
    assert response.json() == {"message": "test"}
    
    # Check that the response was cached
    mock_redis.setex.assert_called_once()
    cache_key = mock_redis.setex.call_args[0][0]
    assert mock_redis.setex.call_args[0][1] == 60
    cached_data = json.loads(mock_redis.setex.call_args[0][2])
    assert cached_data["content"] == '{"message":"test"}'
    assert cached_data["status_code"] == 200
    
    # Reset the mock
    mock_redis.setex.reset_mock()
    
    # Set up the mock to return a cached response
    mock_redis.get.return_value = json.dumps({
        "content": '{"message":"cached"}',
        "status_code": 200,
        "headers": {"content-type": "application/json"},
        "media_type": "application/json"
    })
    
    # Make the same request again
    response = client.get("/api/v1/test")
    
    # Check that the cached response was returned
    assert response.status_code == 200
    assert response.json() == {"message": "cached"}
    
    # Check that the response was not cached again
    mock_redis.setex.assert_not_called()

def test_caching_middleware_excluded_path(client, mock_redis):
    """Test the caching middleware with an excluded path."""
    # Make a request to an excluded path
    response = client.get("/api/v1/health")
    
    # Check that the response is correct
    assert response.status_code == 200
    assert response.json() == {"status": "healthy"}
    
    # Check that the response was not cached
    mock_redis.setex.assert_not_called()

def test_caching_middleware_excluded_method(client, mock_redis):
    """Test the caching middleware with an excluded method."""
    # Make a POST request
    response = client.post("/api/v1/test")
    
    # Check that the response is correct
    assert response.status_code == 200
    assert response.json() == {"message": "test post"}
    
    # Check that the response was not cached
    mock_redis.setex.assert_not_called()

def test_caching_middleware_vary_by_query_params(client, mock_redis):
    """Test the caching middleware with varying query parameters."""
    # Make a request with a query parameter
    response = client.get("/api/v1/test?filter=value1")
    
    # Check that the response is correct
    assert response.status_code == 200
    assert response.json() == {"message": "test"}
    
    # Check that the response was cached
    mock_redis.setex.assert_called_once()
    cache_key1 = mock_redis.setex.call_args[0][0]
    
    # Reset the mock
    mock_redis.setex.reset_mock()
    mock_redis.get.return_value = None
    
    # Make a request with a different query parameter
    response = client.get("/api/v1/test?filter=value2")
    
    # Check that the response was cached with a different key
    mock_redis.setex.assert_called_once()
    cache_key2 = mock_redis.setex.call_args[0][0]
    assert cache_key1 != cache_key2

def test_caching_middleware_vary_by_headers(client, mock_redis):
    """Test the caching middleware with varying headers."""
    # Make a request with a header
    response = client.get("/api/v1/test", headers={"accept-language": "en"})
    
    # Check that the response is correct
    assert response.status_code == 200
    assert response.json() == {"message": "test"}
    
    # Check that the response was cached
    mock_redis.setex.assert_called_once()
    cache_key1 = mock_redis.setex.call_args[0][0]
    
    # Reset the mock
    mock_redis.setex.reset_mock()
    mock_redis.get.return_value = None
    
    # Make a request with a different header
    response = client.get("/api/v1/test", headers={"accept-language": "fr"})
    
    # Check that the response was cached with a different key
    mock_redis.setex.assert_called_once()
    cache_key2 = mock_redis.setex.call_args[0][0]
    assert cache_key1 != cache_key2

def test_caching_middleware_redis_error(client, mock_redis):
    """Test the caching middleware with Redis errors."""
    # Make Redis.get raise an exception
    mock_redis.get.side_effect = Exception("Redis error")
    
    # Make a request
    response = client.get("/api/v1/test")
    
    # Check that the response is correct
    assert response.status_code == 200
    assert response.json() == {"message": "test"}
    
    # Check that the middleware tried to cache the response
    mock_redis.setex.assert_called_once()
    
    # Make Redis.setex raise an exception
    mock_redis.setex.side_effect = Exception("Redis error")
    mock_redis.setex.reset_mock()
    
    # Make another request
    response = client.get("/api/v1/test")
    
    # Check that the response is correct
    assert response.status_code == 200
    assert response.json() == {"message": "test"}
    
    # Check that the middleware tried to cache the response
    mock_redis.setex.assert_called_once()
