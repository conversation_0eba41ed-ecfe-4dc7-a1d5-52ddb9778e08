
import pytest
from unittest.mock import patch, MagicMock
import sys
import os

# Add app directory to path if needed
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.visualization.worldmap import (
    generate_world_map,
    add_country_data,
    create_choropleth,
    save_map
)

def test_generate_world_map():
    """Test that a world map can be generated."""
    with patch('app.visualization.worldmap.folium.Map') as mock_map:
        mock_map_instance = MagicMock()
        mock_map.return_value = mock_map_instance
        
        result = generate_world_map()
        
        # Verify map was created with expected parameters
        mock_map.assert_called_once()
        assert result == mock_map_instance

def test_add_country_data():
    """Test adding country data to the map."""
    mock_map = MagicMock()
    country_data = {
        'USA': {'compliance_score': 85, 'color': 'green'},
        'Canada': {'compliance_score': 92, 'color': 'green'},
        'Mexico': {'compliance_score': 78, 'color': 'yellow'}
    }
    
    with patch('app.visualization.worldmap.add_marker') as mock_add_marker:
        add_country_data(mock_map, country_data)
        
        # Should call add_marker for each country
        assert mock_add_marker.call_count == len(country_data)

def test_create_choropleth():
    """Test creating a choropleth map."""
    mock_map = MagicMock()
    geo_data = {'type': 'FeatureCollection', 'features': []}
    data = {'USA': 85, 'Canada': 92}
    
    with patch('app.visualization.worldmap.folium.Choropleth') as mock_choropleth:
        create_choropleth(mock_map, geo_data, data)
        
        # Verify choropleth was created and added to map
        mock_choropleth.assert_called_once()
        mock_map.add_child.assert_called_once()

def test_save_map():
    """Test saving the map to a file."""
    mock_map = MagicMock()
    filename = 'test_map.html'
    
    with patch('builtins.open', mock_open()) as mock_file:
        save_map(mock_map, filename)
        
        # Verify map was saved
        mock_map.save.assert_called_once_with(filename)
