"""
Tests for middleware components.
"""
import pytest
from fastapi.testclient import <PERSON><PERSON>lient
from unittest.mock import MagicMock, patch

from app.main import app
from app.middleware.logging_middleware import LoggingMiddleware
from app.middleware.rate_limiting_middleware import RateLimitingMiddleware
from app.middleware.locale_middleware import LocaleMiddleware

client = TestClient(app)

def test_logging_middleware():
    """Test the logging middleware."""
    # Create a mock request
    mock_request = MagicMock()
    mock_request.method = "GET"
    mock_request.url.path = "/api/v1/test"
    mock_request.client.host = "127.0.0.1"
    
    # Create a mock response
    mock_response = MagicMock()
    mock_response.status_code = 200
    
    # Create a mock call_next function
    async def mock_call_next(request):
        return mock_response
    
    # Create the middleware
    middleware = LoggingMiddleware()
    
    # Mock the logger
    with patch.object(middleware, "logger") as mock_logger:
        # Call the middleware
        import asyncio
        response = asyncio.run(middleware.dispatch(mock_request, mock_call_next))
        
        # Check that the logger was called
        mock_logger.info.assert_called_once()
        
        # Check that the response was returned
        assert response == mock_response

def test_rate_limiting_middleware():
    """Test the rate limiting middleware."""
    # Create a mock request
    mock_request = MagicMock()
    mock_request.method = "GET"
    mock_request.url.path = "/api/v1/test"
    mock_request.client.host = "127.0.0.1"
    
    # Create a mock response
    mock_response = MagicMock()
    mock_response.status_code = 200
    
    # Create a mock call_next function
    async def mock_call_next(request):
        return mock_response
    
    # Create the middleware
    middleware = RateLimitingMiddleware()
    
    # Mock the rate limiter
    with patch.object(middleware, "rate_limiter") as mock_rate_limiter:
        # Set up the mock rate limiter
        mock_rate_limiter.is_rate_limited.return_value = False
        
        # Call the middleware
        import asyncio
        response = asyncio.run(middleware.dispatch(mock_request, mock_call_next))
        
        # Check that the rate limiter was called
        mock_rate_limiter.is_rate_limited.assert_called_once()
        
        # Check that the response was returned
        assert response == mock_response
        
        # Test rate limiting
        mock_rate_limiter.is_rate_limited.return_value = True
        
        # Call the middleware again
        response = asyncio.run(middleware.dispatch(mock_request, mock_call_next))
        
        # Check that a 429 response was returned
        assert response.status_code == 429

def test_locale_middleware():
    """Test the locale middleware."""
    # Test with no language parameter
    response = client.get("/")
    assert response.status_code == 200
    assert "lang" not in response.cookies
    
    # Test with valid language parameter
    response = client.get("/?lang=es")
    assert response.status_code == 200
    assert "lang" in response.cookies
    assert response.cookies["lang"] == "es"
    
    # Test with invalid language parameter
    response = client.get("/?lang=invalid")
    assert response.status_code == 200
    assert "lang" not in response.cookies
    
    # Test with language cookie
    client.cookies.set("lang", "fr")
    response = client.get("/")
    assert response.status_code == 200
    assert "lang" in response.cookies
    assert response.cookies["lang"] == "fr"
    
    # Test overriding language cookie with parameter
    client.cookies.set("lang", "fr")
    response = client.get("/?lang=de")
    assert response.status_code == 200
    assert "lang" in response.cookies
    assert response.cookies["lang"] == "de"
