
"""Test cases for the Country API endpoints."""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from main import app
from app.db import models


@pytest.fixture
def test_country(db: Session):
    """Create a test country."""
    country = models.Country(name="Test Country", code="TC")
    db.add(country)
    db.commit()
    db.refresh(country)
    yield country
    db.delete(country)
    db.commit()


def test_get_countries(client: TestClient, test_country):
    """Test getting list of countries."""
    response = client.get("/api/v1/countries/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) >= 1
    
    # Verify the test country is in the response
    country_ids = [country["id"] for country in response.json()]
    assert test_country.id in country_ids


def test_get_country(client: TestClient, test_country):
    """Test getting a specific country by ID."""
    response = client.get(f"/api/v1/countries/{test_country.id}")
    assert response.status_code == 200
    assert response.json()["id"] == test_country.id
    assert response.json()["name"] == test_country.name
    assert response.json()["code"] == test_country.code


def test_get_country_not_found(client: TestClient):
    """Test getting a non-existent country."""
    response = client.get("/api/v1/countries/999999")
    assert response.status_code == 404
    assert "not found" in response.json()["detail"].lower()


def test_create_country(client: TestClient):
    """Test creating a new country."""
    data = {
        "name": "New Test Country",
        "code": "NT"
    }
    response = client.post("/api/v1/countries/", json=data)
    assert response.status_code == 201
    assert response.json()["name"] == data["name"]
    assert response.json()["code"] == data["code"]
    
    # Clean up
    country_id = response.json()["id"]
    client.delete(f"/api/v1/countries/{country_id}")


def test_update_country(client: TestClient, test_country):
    """Test updating a country."""
    data = {
        "name": "Updated Country Name"
    }
    response = client.put(f"/api/v1/countries/{test_country.id}", json=data)
    assert response.status_code == 200
    assert response.json()["id"] == test_country.id
    assert response.json()["name"] == data["name"]
    # The code should remain unchanged
    assert response.json()["code"] == test_country.code


def test_update_country_not_found(client: TestClient):
    """Test updating a non-existent country."""
    data = {"name": "Updated Country"}
    response = client.put("/api/v1/countries/999999", json=data)
    assert response.status_code == 404
    assert "not found" in response.json()["detail"].lower()


def test_delete_country(client: TestClient, db: Session):
    """Test deleting a country."""
    # Create a temporary country for this test
    country = models.Country(name="Country To Delete", code="CD")
    db.add(country)
    db.commit()
    db.refresh(country)

    response = client.delete(f"/api/v1/countries/{country.id}")
    assert response.status_code == 200

    # Verify it's deleted
    db_country = db.query(models.Country).filter(models.Country.id == country.id).first()
    assert db_country is None


def test_delete_country_not_found(client: TestClient):
    """Test deleting a non-existent country."""
    response = client.delete("/api/v1/countries/999999")
    assert response.status_code == 404
    assert "not found" in response.json()["detail"].lower()


def test_delete_country_with_regulators(client: TestClient, db: Session, test_country):
    """Test deleting a country with associated regulators."""
    # Create a regulator associated with the test country
    regulator = models.Regulator(
        name="Test Regulator",
        country_id=test_country.id
    )
    db.add(regulator)
    db.commit()

    response = client.delete(f"/api/v1/countries/{test_country.id}")
    assert response.status_code == 400
    assert "associated regulators" in response.json()["detail"].lower()

    # Clean up
    db.delete(regulator)
    db.commit()
