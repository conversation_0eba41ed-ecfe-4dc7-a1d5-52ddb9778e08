
import pytest
from unittest.mock import patch, MagicMock
import sys
import os
import glob
import json

# Add app directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.i18n.i18n import (
    setup_translation,
    get_translator,
    translate,
    get_available_languages,
    get_language_name
)


class TestTranslationsComprehensive:
    """Comprehensive tests for the translations system."""
    
    @patch('app.i18n.i18n.gettext.translation')
    def test_setup_translation(self, mock_translation):
        """Test setting up translation with various locales."""
        # Mock translation objects
        mock_en = MagicMock()
        mock_fr = MagicMock()
        mock_es = MagicMock()
        
        # Configure mock to return different translation objects for different locales
        def side_effect(domain, localedir, languages, fallback):
            if 'en' in languages:
                return mock_en
            elif 'fr' in languages:
                return mock_fr
            elif 'es' in languages:
                return mock_es
            else:
                return MagicMock()  # Default fallback
                
        mock_translation.side_effect = side_effect
        
        # Test with English
        translator = setup_translation('en')
        assert translator is mock_en
        
        # Test with French
        translator = setup_translation('fr')
        assert translator is mock_fr
        
        # Test with Spanish
        translator = setup_translation('es')
        assert translator is mock_es
        
        # Test with unsupported language (should use fallback)
        translator = setup_translation('xyz')
        assert translator is not None
        
        # Verify calls to gettext.translation
        assert mock_translation.call_count == 4
    
    @patch('app.i18n.i18n.get_translator')
    def test_translate_function(self, mock_get_translator):
        """Test the translate function with various scenarios."""
        # Mock translator object
        mock_translator = MagicMock()
        mock_translator.gettext.side_effect = lambda text: f"TRANSLATED[{text}]"
        mock_get_translator.return_value = mock_translator
        
        # Test basic translation
        result = translate("Hello", "en")
        assert result == "TRANSLATED[Hello]"
        
        # Test with formatting
        result = translate("Hello {name}", "en", name="World")
        assert result == "TRANSLATED[Hello {name}]"
        mock_translator.gettext.assert_called_with("Hello {name}")
        
        # Test with missing translator
        mock_get_translator.return_value = None
        result = translate("Fallback text", "unknown")
        assert result == "Fallback text"
    
    def test_get_available_languages(self):
        """Test retrieving available languages."""
        # Mock the directory structure
        with patch('glob.glob') as mock_glob:
            mock_glob.return_value = [
                '/path/to/lang/en/LC_MESSAGES',
                '/path/to/lang/fr/LC_MESSAGES',
                '/path/to/lang/es/LC_MESSAGES',
                '/path/to/lang/de/LC_MESSAGES'
            ]
            
            languages = get_available_languages()
            assert len(languages) == 4
            assert 'en' in languages
            assert 'fr' in languages
            assert 'es' in languages
            assert 'de' in languages
    
    @patch('app.i18n.i18n.get_language_name')
    def test_get_language_name_function(self, mock_get_language_name):
        """Test the get_language_name function."""
        # Configure the mock
        mock_get_language_name.side_effect = lambda code: {
            'en': 'English',
            'fr': 'French',
            'es': 'Spanish',
            'de': 'German',
            'unknown': 'Unknown'
        }.get(code, 'Unknown')
        
        # Test valid language codes
        assert get_language_name('en') == 'English'
        assert get_language_name('fr') == 'French'
        assert get_language_name('es') == 'Spanish'
        assert get_language_name('de') == 'German'
        
        # Test unknown language code
        assert get_language_name('xyz') == 'Unknown'
