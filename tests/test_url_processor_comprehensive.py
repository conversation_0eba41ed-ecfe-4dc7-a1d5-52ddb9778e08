
import pytest
from unittest.mock import patch, MagicMock
import sys
import os

# Add app directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.utils.url_processor import (
    extract_text_from_url,
    clean_text,
    get_domain_from_url,
    is_valid_url,
    normalize_url,
    get_content_type,
    extract_domain_and_path
)

class TestURLProcessorComprehensive:
    """Comprehensive tests for URL Processor module."""

    def test_clean_text_with_various_inputs(self):
        """Test clean_text function with various inputs."""
        # Test with HTML content
        html_text = "<html><body><p>Test content</p></body></html>"
        cleaned = clean_text(html_text)
        assert "<html>" not in cleaned
        assert "Test content" in cleaned
        
        # Test with extra whitespace
        whitespace_text = "  Multiple    spaces  \n\n and \t tabs  "
        cleaned = clean_text(whitespace_text)
        assert "  Multiple" not in cleaned
        assert "Multiple spaces and tabs" in cleaned
        
        # Test with special characters
        special_chars = "Text with special chars: & < > ' \" © ®"
        cleaned = clean_text(special_chars)
        assert "Text with special chars:" in cleaned

    def test_is_valid_url_comprehensive(self):
        """Test is_valid_url with various inputs."""
        # Valid URLs
        assert is_valid_url("https://example.com") is True
        assert is_valid_url("http://subdomain.example.co.uk/path?query=1") is True
        assert is_valid_url("https://example.com/path/to/page.html") is True
        
        # Invalid URLs
        assert is_valid_url("not_a_url") is False
        assert is_valid_url("http:/example.com") is False
        assert is_valid_url("https://.com") is False
        assert is_valid_url("") is False
        assert is_valid_url(None) is False

    def test_normalize_url_comprehensive(self):
        """Test normalize_url with various inputs."""
        # Basic normalization
        assert normalize_url("http://EXAMPLE.com") == "http://example.com"
        assert normalize_url("https://example.com/") == "https://example.com"
        
        # With paths and fragments
        assert normalize_url("https://example.com/path/") == "https://example.com/path"
        assert normalize_url("https://example.com/path#fragment") == "https://example.com/path"
        assert normalize_url("https://example.com/path?query=1") == "https://example.com/path?query=1"
        
        # Invalid URLs
        assert normalize_url("not_a_url") == "not_a_url"
        assert normalize_url("") == ""

    @patch('app.utils.url_processor.requests.head')
    def test_get_content_type(self, mock_head):
        """Test get_content_type function."""
        # Setup mock for HTML
        mock_response_html = MagicMock()
        mock_response_html.headers = {'Content-Type': 'text/html; charset=utf-8'}
        
        # Setup mock for PDF
        mock_response_pdf = MagicMock()
        mock_response_pdf.headers = {'Content-Type': 'application/pdf'}
        
        # Setup mock for error
        mock_error_response = MagicMock()
        mock_error_response.raise_for_status.side_effect = Exception("Connection error")
        
        # Test with HTML content
        mock_head.return_value = mock_response_html
        content_type = get_content_type("https://example.com")
        assert content_type == "text/html"
        
        # Test with PDF content
        mock_head.return_value = mock_response_pdf
        content_type = get_content_type("https://example.com/document.pdf")
        assert content_type == "application/pdf"
        
        # Test with error
        mock_head.side_effect = Exception("Connection error")
        content_type = get_content_type("https://nonexistent.example")
        assert content_type == "unknown"

    def test_extract_domain_and_path(self):
        """Test extract_domain_and_path function."""
        # Basic URL
        domain, path = extract_domain_and_path("https://example.com/path/to/page")
        assert domain == "example.com"
        assert path == "/path/to/page"
        
        # URL with query parameters
        domain, path = extract_domain_and_path("https://example.com/search?q=test")
        assert domain == "example.com"
        assert path == "/search?q=test"
        
        # URL with subdomain
        domain, path = extract_domain_and_path("https://sub.example.com/")
        assert domain == "sub.example.com"
        assert path == "/"
        
        # Invalid URL
        domain, path = extract_domain_and_path("not_a_url")
        assert domain == ""
        assert path == ""

    @patch('app.utils.url_processor.requests.get')
    def test_extract_text_from_url_comprehensive(self, mock_get):
        """Test extract_text_from_url function comprehensively."""
        # Setup mock for HTML
        mock_response_html = MagicMock()
        mock_response_html.headers = {'Content-Type': 'text/html; charset=utf-8'}
        mock_response_html.text = "<html><body><p>Test content</p></body></html>"
        mock_response_html.raise_for_status = MagicMock()
        
        # Setup mock for PDF
        mock_response_pdf = MagicMock()
        mock_response_pdf.headers = {'Content-Type': 'application/pdf'}
        mock_response_pdf.content = b"%PDF-1.4 mock PDF content"
        mock_response_pdf.raise_for_status = MagicMock()
        
        # Test with HTML content
        mock_get.return_value = mock_response_html
        text = extract_text_from_url("https://example.com")
        assert "Test content" in text
        
        # Test with PDF content (assuming textract would be mocked separately)
        mock_get.return_value = mock_response_pdf
        with patch('app.utils.url_processor.extract_text_from_pdf', return_value="PDF content"):
            text = extract_text_from_url("https://example.com/document.pdf")
            assert "PDF content" in text
        
        # Test with network error
        mock_get.side_effect = Exception("Network error")
        text = extract_text_from_url("https://nonexistent.example")
        assert "Error: Network error" in text
        
        # Test with invalid URL
        text = extract_text_from_url("not_a_url")
        assert "Error: Invalid URL" in text
