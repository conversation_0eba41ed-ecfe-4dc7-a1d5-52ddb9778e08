"""Test cases for the health check endpoint."""
import json
from unittest.mock import patch, MagicMock

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.exc import SQLAlchemyError

from main import app


@pytest.fixture
def client():
    """
    Create a test client for the FastAPI application.

    Returns:
        TestClient: The test client instance
    """
    return TestClient(app)


def test_health_check_success(client):
    """
    Test health check endpoint when database is healthy.

    Args:
        client (TestClient): The test client
    """
    # Mock the database execution to succeed
    with patch('main.get_db') as mock_get_db:
        mock_db = MagicMock()
        mock_db.execute.return_value = True
        mock_get_db.return_value = mock_db

        # Make the request to both endpoints
        response = client.get("/health")
        response_api_v1 = client.get("/api/v1/health")

        # Ensure both endpoints return the same response
        assert response.json() == response_api_v1.json()

        # Assertions
        assert response.status_code == 200
        assert "status" in response.json()
        assert "timestamp" in response.json()
        assert "database" in response.json()
        assert response.json()["status"] == "healthy"
        assert response.json()["database"] == "healthy"
        # Test coverage might or might not be present
        # depending on if reports have been generated
        assert "test_coverage" in response.json()


def test_health_check_database_failure(client):
    """
    Test health check endpoint when database connection fails.

    Args:
        client (TestClient): The test client
    """
    # Mock the database execution to fail
    with patch('main.get_db') as mock_get_db:
        mock_db = MagicMock()
        mock_db.execute.side_effect = SQLAlchemyError("Database connection error")
        mock_get_db.return_value = mock_db

        # Make the request to both endpoints
        response = client.get("/health")
        response_api_v1 = client.get("/api/v1/health")

        # Ensure both endpoints return the same response
        assert response.json() == response_api_v1.json()

        # Assertions
        assert response.status_code == 200
        assert "status" in response.json()
        assert "timestamp" in response.json()
        assert "database" in response.json()
        assert response.json()["status"] == "healthy"
        assert "unhealthy" in response.json()["database"]
        assert "Database connection error" in response.json()["database"]
        # Test coverage might or might not be present
        # depending on if reports have been generated
        assert "test_coverage" in response.json()


def test_health_check_logging(client, tmp_path):
    """
    Test that health check endpoint logs accesses.

    Args:
        client (TestClient): The test client
        tmp_path: Pytest fixture for temporary directory
    """
    # Create temporary log file
    log_file = tmp_path / "health_check_log.txt"

    # Mock the database execution and file handling
    with patch('main.get_db') as mock_get_db, \
         patch('builtins.open') as mock_open, \
         patch('main.print') as mock_print:

        # Setup mocks
        mock_db = MagicMock()
        mock_db.execute.return_value = True
        mock_get_db.return_value = mock_db

        mock_file = MagicMock()
        mock_open.return_value.__enter__.return_value = mock_file

        # Make the request to both endpoints
        response = client.get("/health")
        response_api_v1 = client.get("/api/v1/health")

        # Ensure both endpoints return the same response
        assert response.json() == response_api_v1.json()

        # Assertions
        assert response.status_code == 200

        # Verify logging occurred
        mock_print.assert_called_once()
        mock_open.assert_called_once()
        mock_file.write.assert_called_once()

        # Check log message format
        log_message = mock_print.call_args[0][0]
        assert "Health check accessed by" in log_message
        assert "Database status: healthy" in log_message
        assert "test_coverage" in response.json()