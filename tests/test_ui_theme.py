"""Tests for UI theme functionality."""
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_dashboard_loads_with_theme_toggle():
    """Test that the dashboard loads and contains theme toggle elements."""
    response = client.get("/")
    assert response.status_code == 200

    # Check for theme toggle elements
    assert 'id="theme-toggle"' in response.text
    assert 'class="theme-switch"' in response.text

    # Check for Material Design elements
    assert 'class="material-icons"' in response.text

    # Check for Roboto font
    assert "font-family: 'Roboto'" in response.text

def test_dashboard_dark_mode_styles():
    """Test that dark mode CSS styles are present in the dashboard."""
    response = client.get("/")
    assert response.status_code == 200

    # Check for dark mode styles
    assert "body.dark-mode" in response.text
    assert "background-color: #121212" in response.text

def test_bootstrap_integration():
    """Test that Bootstrap is properly integrated in the dashboard."""
    response = client.get("/")
    assert response.status_code == 200

    # Check for Bootstrap elements
    assert 'class="container' in response.text
    assert 'class="row"' in response.text
    assert 'class="col-md-' in response.text
    assert 'class="navbar' in response.text

    # Check for Bootstrap JS
    assert 'bootstrap.bundle.min.js' in response.text

def test_health_endpoint_can_be_loaded_via_ajax(client):
    """Test that the health endpoint can be loaded via AJAX."""
    response = client.get("/health")
    assert response.status_code == 200
    assert "status" in response.json()
    assert "database" in response.json()
    assert "timestamp" in response.json()

def test_worldmap_endpoint_returns_html(client):
    """Test that the worldmap endpoint returns HTML."""
    response = client.get("/api/v1/worldmap?lat=20&lon=0&zoom=2")
    assert response.status_code == 200
    assert response.headers["content-type"] == "text/html; charset=utf-8"
    assert "<div" in response.text

def test_dashboard_has_proper_links(client):
    """Test that the dashboard has proper links."""
    response = client.get("/")
    assert response.status_code == 200
    assert "/docs" in response.text
    assert "/redoc" in response.text
    assert "/openapi.json" in response.text
    assert "/ui/manage" in response.text

def test_dashboard_has_health_check_button(client):
    """Test that the dashboard has a health check button."""
    response = client.get("/")
    assert response.status_code == 200
    assert "healthCheckBtn" in response.text
    assert "Health Check" in response.text