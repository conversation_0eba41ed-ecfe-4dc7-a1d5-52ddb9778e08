
"""Tests for the enhanced regulatory document parser."""
import os
import pytest
from unittest.mock import patch, MagicMock
import tempfile

from app.utils.regulatory_parser import RegulatoryParser

@pytest.fixture
def sample_regulatory_text():
    """Provide sample regulatory text for testing."""
    return """
    Article 1. Scope
    This regulation applies to all organizations processing personal data.
    
    Article 2. Requirements
    2.1. Organizations shall implement appropriate security measures.
    2.2. Data breaches must be reported within 72 hours.
    
    Section 3. Penalties
    3.1. Violations may result in fines up to $10,000,000 or 2% of annual revenue.
    3.2. Serious violations may result in imprisonment of up to 5 years.
    """

@pytest.fixture
def regulatory_parser():
    """Initialize a RegulatoryParser instance for testing."""
    return RegulatoryParser()

@patch('app.utils.regulatory_parser.PyPDF2.PdfReader')
def test_extract_text_from_pdf(mock_pdf_reader, regulatory_parser, tmp_path):
    """Test extracting text from PDF."""
    # Create mock PDF reader
    mock_reader = MagicMock()
    mock_page = MagicMock()
    mock_page.extract_text.return_value = "Sample PDF text"
    mock_reader.pages = [mock_page, mock_page]
    mock_pdf_reader.return_value = mock_reader
    
    # Create temp file
    pdf_path = os.path.join(tmp_path, "test.pdf")
    with open(pdf_path, 'wb') as f:
        f.write(b'Dummy PDF content')
    
    # Test function
    text = regulatory_parser.extract_text_from_pdf(pdf_path)
    
    # Verify extraction
    assert "Sample PDF text" in text
    assert mock_page.extract_text.call_count == 2

def test_identify_regulatory_sections(regulatory_parser, sample_regulatory_text):
    """Test identification of regulatory sections."""
    sections = regulatory_parser.identify_regulatory_sections(sample_regulatory_text)
    
    # Verify sections
    assert len(sections["articles"]) == 2
    assert "Article 1" in sections["articles"][0]
    assert "Article 2" in sections["articles"][1]
    assert len(sections["sections"]) == 1
    assert "Section 3" in sections["sections"][0]

def test_extract_entities(regulatory_parser, sample_regulatory_text):
    """Test extraction of entities."""
    entities = regulatory_parser.extract_entities(sample_regulatory_text)
    
    # Verify entities extracted correctly
    assert any("data" in term for term in entities["regulatory_terms"])
    
    # Check for monetary values if supported by the current spaCy model
    if regulatory_parser.nlp:
        # Note: exact entity extraction depends on the spaCy model
        # So we're testing for presence of the key rather than exact content
        assert "monetary_values" in entities
        assert "percentages" in entities

def test_extract_requirements(regulatory_parser, sample_regulatory_text):
    """Test extraction of requirements."""
    requirements = regulatory_parser.extract_requirements(sample_regulatory_text)
    
    # Verify requirements
    assert len(requirements) >= 2
    assert any("implement" in req["text"] for req in requirements)
    assert any("reported within 72 hours" in req["text"] for req in requirements)
    
    # Check priority classifications
    assert any(req["priority"] == "high" for req in requirements)

def test_extract_penalties(regulatory_parser, sample_regulatory_text):
    """Test extraction of penalties."""
    penalties = regulatory_parser.extract_penalties(sample_regulatory_text)
    
    # Verify penalties
    assert len(penalties) >= 1
    assert any("$10,000,000" in penalty["text"] for penalty in penalties)
    
    # Check severity and type classifications
    if penalties:
        assert "severity" in penalties[0]
        assert "type" in penalties[0]

def test_calculate_regulatory_confidence(regulatory_parser, sample_regulatory_text):
    """Test calculation of regulatory confidence score."""
    sections = regulatory_parser.identify_regulatory_sections(sample_regulatory_text)
    entities = regulatory_parser.extract_entities(sample_regulatory_text)
    
    confidence = regulatory_parser.calculate_regulatory_confidence(
        sample_regulatory_text, sections, entities
    )
    
    # Verify confidence is reasonable for a regulatory document
    assert confidence > 0.5
    assert confidence <= 1.0

def test_generate_summary(regulatory_parser, sample_regulatory_text):
    """Test generation of regulatory document summary."""
    sections = regulatory_parser.identify_regulatory_sections(sample_regulatory_text)
    entities = regulatory_parser.extract_entities(sample_regulatory_text)
    requirements = regulatory_parser.extract_requirements(sample_regulatory_text)
    
    summary = regulatory_parser.generate_summary(
        sample_regulatory_text, sections, entities, requirements
    )
    
    # Verify summary contains key information
    assert "sections" in summary.lower()
    assert "articles" in summary.lower()
