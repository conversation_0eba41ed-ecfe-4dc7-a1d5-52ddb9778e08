"""
Unit tests for the regulatory change detection functionality.
"""
import unittest
from unittest.mock import patch, MagicMock
import datetime
import json

# Import the modules to be tested
# These imports will need to be updated based on the actual implementation
from app.regulatory_change.detection import (
    RegulatoryChangeDetector,
    RegulatorySource,
    RegulatoryChange,
    ChangeClassifier
)


class TestRegulatoryChangeDetection(unittest.TestCase):
    """Test cases for regulatory change detection functionality."""

    def setUp(self):
        """Set up test fixtures."""
        self.detector = RegulatoryChangeDetector()
        
        # Mock regulatory source
        self.mock_source = MagicMock(spec=RegulatorySource)
        self.mock_source.name = "Test Regulatory Source"
        self.mock_source.url = "https://test-regulatory-source.gov"
        
        # Sample regulatory document content
        self.sample_content = """
        REGULATORY DOCUMENT
        
        Section 1. Scope
        This regulation applies to all financial institutions.
        
        Section 2. Requirements
        Financial institutions shall implement new security measures by January 1, 2023.
        
        Section 3. Penalties
        Non-compliance may result in fines up to $1,000,000.
        """
        
        # Sample previous version for comparison
        self.previous_content = """
        REGULATORY DOCUMENT
        
        Section 1. Scope
        This regulation applies to all financial institutions.
        
        Section 2. Requirements
        Financial institutions shall implement security measures by January 1, 2022.
        
        Section 3. Penalties
        Non-compliance may result in fines up to $500,000.
        """

    def test_detect_new_regulation(self):
        """Test detection of a completely new regulation."""
        # Mock a new regulation with no previous version
        self.mock_source.get_document.return_value = self.sample_content
        self.mock_source.has_previous_version.return_value = False
        
        changes = self.detector.detect_changes(self.mock_source)
        
        # Assertions
        self.assertEqual(len(changes), 1)
        self.assertEqual(changes[0].change_type, "NEW_REGULATION")
        self.assertEqual(changes[0].source, self.mock_source.name)
        self.assertIsNotNone(changes[0].detected_date)

    def test_detect_amended_regulation(self):
        """Test detection of changes in an existing regulation."""
        # Mock an existing regulation with a previous version
        self.mock_source.get_document.return_value = self.sample_content
        self.mock_source.has_previous_version.return_value = True
        self.mock_source.get_previous_version.return_value = self.previous_content
        
        changes = self.detector.detect_changes(self.mock_source)
        
        # Assertions
        self.assertGreaterEqual(len(changes), 2)  # At least 2 changes (deadline and penalty)
        
        # Check for deadline change
        deadline_change = next((c for c in changes if "2023" in c.description), None)
        self.assertIsNotNone(deadline_change)
        self.assertEqual(deadline_change.change_type, "AMENDMENT")
        
        # Check for penalty change
        penalty_change = next((c for c in changes if "$1,000,000" in c.description), None)
        self.assertIsNotNone(penalty_change)
        self.assertEqual(penalty_change.change_type, "AMENDMENT")

    def test_detect_repealed_regulation(self):
        """Test detection of a repealed regulation."""
        # Mock a repealed regulation
        self.mock_source.get_document.return_value = "This regulation has been repealed effective March 15, 2023."
        self.mock_source.has_previous_version.return_value = True
        self.mock_source.get_previous_version.return_value = self.sample_content
        
        changes = self.detector.detect_changes(self.mock_source)
        
        # Assertions
        self.assertEqual(len(changes), 1)
        self.assertEqual(changes[0].change_type, "REPEAL")
        self.assertIn("repealed", changes[0].description.lower())

    def test_change_classification(self):
        """Test classification of detected changes."""
        # Create a sample change
        change = RegulatoryChange(
            source="Test Source",
            title="Updated Security Requirements",
            description="Financial institutions shall implement new security measures by January 1, 2023.",
            change_type="AMENDMENT",
            detected_date=datetime.datetime.now()
        )
        
        classifier = ChangeClassifier()
        classified_change = classifier.classify(change)
        
        # Assertions
        self.assertIsNotNone(classified_change.jurisdiction)
        self.assertIsNotNone(classified_change.industry_sector)
        self.assertIsNotNone(classified_change.business_function)
        self.assertIsNotNone(classified_change.impact_level)
        self.assertIsNotNone(classified_change.compliance_deadline)

    def test_duplicate_detection(self):
        """Test detection of duplicate regulatory changes."""
        # Create two similar changes
        change1 = RegulatoryChange(
            source="Source A",
            title="New Security Requirements",
            description="Implement new security measures by January 1, 2023.",
            change_type="NEW_REGULATION",
            detected_date=datetime.datetime.now()
        )
        
        change2 = RegulatoryChange(
            source="Source B",
            title="Security Measures Update",
            description="Organizations must implement new security measures by Jan 1, 2023.",
            change_type="NEW_REGULATION",
            detected_date=datetime.datetime.now()
        )
        
        # Test duplicate detection
        duplicates = self.detector.detect_duplicates([change1, change2])
        
        # Assertions
        self.assertEqual(len(duplicates), 1)
        self.assertEqual(len(duplicates[0]), 2)  # Group of 2 duplicates
        self.assertIn(change1, duplicates[0])
        self.assertIn(change2, duplicates[0])

    def test_relevance_screening(self):
        """Test relevance screening of regulatory changes."""
        # Create a sample change
        change = RegulatoryChange(
            source="Test Source",
            title="Updated Security Requirements for Banks",
            description="Banks shall implement new security measures by January 1, 2023.",
            change_type="AMENDMENT",
            detected_date=datetime.datetime.now()
        )
        
        # Configure business profile for a bank
        business_profile = {
            "industry_sectors": ["Banking", "Financial Services"],
            "jurisdictions": ["US", "EU"],
            "business_functions": ["Security", "Compliance", "IT"]
        }
        
        # Test relevance screening
        relevance_score = self.detector.screen_relevance(change, business_profile)
        
        # Assertions
        self.assertGreaterEqual(relevance_score, 0.7)  # High relevance expected


if __name__ == '__main__':
    unittest.main()
