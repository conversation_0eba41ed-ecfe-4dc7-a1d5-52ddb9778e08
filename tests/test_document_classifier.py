
"""Tests for document classifier functionality."""
import os
import pytest
from unittest.mock import patch, MagicMock

from app.utils.document_classifier import (
    classify_document,
    extract_country_data,
    extract_regulation_data,
    extract_regulator_data,
    suggest_mappings,
    process_pdf_for_mapping
)

@pytest.fixture
def sample_country_text():
    """Provide sample country profile text for testing."""
    return """
    Country Profile: New Zealand
    
    New Zealand is an island country in the southwestern Pacific Ocean. The country's geography comprises two main landmasses—the North Island and the South Island—and around 600 smaller islands.
    
    Capital: Wellington
    Official Language: English, Māori, NZ Sign Language
    Government: Unitary parliamentary constitutional monarchy
    Population: 5.1 million (2021 estimate)
    GDP: $212 billion (2021 estimate)
    Currency: New Zealand Dollar (NZD)
    ISO Code: NZ
    Region: Oceania
    """

@pytest.fixture
def sample_regulation_text():
    """Provide sample regulatory text for testing."""
    return """
    Personal Data Protection Act 2021
    
    An Act to establish a data protection framework for the collection, use, disclosure, and care of personal data.
    
    Enacted by the Parliament of Singapore on 15 March 2021.
    
    Part I - Preliminary
    
    1. This Act may be cited as the Personal Data Protection Act 2021.
    
    2. In this Act, unless the context otherwise requires—
    "Commission" means the Personal Data Protection Commission;
    "personal data" means data, whether true or not, about an individual;
    
    Part II - Protection of Personal Data
    
    3. Organizations shall implement reasonable security arrangements to protect personal data in its possession or under its control.
    
    4. An organization must not collect, use or disclose personal data about an individual unless—
    (a) the individual gives, or is deemed to have given, his consent under this Act; or
    (b) the collection, use or disclosure is required or authorized under this Act or any other written law.
    
    5. Organizations that fail to comply with this Act shall be liable for a financial penalty not exceeding $1,000,000.
    """

@pytest.fixture
def sample_regulator_text():
    """Provide sample regulator profile text for testing."""
    return """
    Personal Data Protection Commission (PDPC)
    
    The Personal Data Protection Commission (PDPC) is Singapore's main authority in matters relating to personal data protection.
    
    Established in 2013, the PDPC administers the Personal Data Protection Act 2012 (PDPA), which aims to govern the collection, use and disclosure of personal data by organizations.
    
    The Commission is empowered to:
    - Conduct investigations into breaches of the PDPA
    - Issue directions to organizations to ensure compliance
    - Impose financial penalties for non-compliance
    - Publish guidelines and advisory guidelines
    
    Website: www.pdpc.gov.sg
    
    The PDPC works closely with organizations to promote good personal data protection practices and compliance with the PDPA.
    """

def test_classify_document_country(sample_country_text):
    """Test classification of country profile document."""
    classification = classify_document(sample_country_text)
    
    # Verify classification
    assert classification["country_profile"] > 0.5
    assert classification["country_profile"] > classification["regulation"]
    assert classification["country_profile"] > classification["regulator_profile"]

def test_classify_document_regulation(sample_regulation_text):
    """Test classification of regulation document."""
    classification = classify_document(sample_regulation_text)
    
    # Verify classification
    assert classification["regulation"] > 0.5
    assert classification["regulation"] > classification["country_profile"]
    assert classification["regulation"] > classification["regulator_profile"]

def test_classify_document_regulator(sample_regulator_text):
    """Test classification of regulator profile document."""
    classification = classify_document(sample_regulator_text)
    
    # Verify classification
    assert classification["regulator_profile"] > 0.5
    assert classification["regulator_profile"] > classification["country_profile"]
    assert classification["regulator_profile"] > classification["regulation"]

def test_extract_country_data(sample_country_text):
    """Test extraction of country data."""
    countries = extract_country_data(sample_country_text)
    
    # Verify country extraction
    assert len(countries) >= 1
    assert any(c["name"] == "New Zealand" for c in countries)
    
    # Find New Zealand in results
    nz = next(c for c in countries if c["name"] == "New Zealand")
    assert nz["code"] == "NZ"
    assert nz["region"] == "Oceania"
    assert nz["confidence"] > 0.3

def test_extract_regulation_data(sample_regulation_text):
    """Test extraction of regulation data."""
    regulations = extract_regulation_data(sample_regulation_text)
    
    # Verify regulation extraction
    assert len(regulations) >= 1
    assert any(r["title"] == "Personal Data Protection Act" for r in regulations)
    
    # Find PDPA in results
    pdpa = next(r for r in regulations if "Personal Data Protection Act" in r["title"])
    assert "2021" in pdpa["date"]
    assert "Singapore" in pdpa["country"]
    assert len(pdpa["requirements"]) >= 1
    assert pdpa["confidence"] > 0.3

def test_extract_regulator_data(sample_regulator_text):
    """Test extraction of regulator data."""
    regulators = extract_regulator_data(sample_regulator_text)
    
    # Verify regulator extraction
    assert len(regulators) >= 1
    assert any("Personal Data Protection Commission" in r["name"] for r in regulators)
    
    # Find PDPC in results
    pdpc = next(r for r in regulators if "Personal Data Protection Commission" in r["name"])
    assert "Singapore" in pdpc["country"]
    assert "www.pdpc.gov.sg" in pdpc["website"]
    assert len(pdpc["powers"]) >= 1
    assert pdpc["confidence"] > 0.3

@patch('app.utils.document_classifier.process_pdf_for_mapping')
def test_process_pdf_for_mapping(mock_process, tmp_path):
    """Test processing PDF for mapping."""
    # Create mock PDF results
    mock_result = {
        "classification": {
            "country_profile": 0.8,
            "regulation": 0.2,
            "regulator_profile": 0.1
        },
        "dominant_type": "country_profile",
        "confidence": 0.8,
        "suggested_mappings": {
            "countries": [{"id": 1, "name": "New Zealand", "code": "NZ", "confidence": 0.9}]
        },
        "new_entities": {
            "countries": [{"name": "Australia", "code": "AU", "confidence": 0.7}]
        }
    }
    mock_process.return_value = mock_result
    
    # Create dummy DB session
    db_session = MagicMock()
    
    # Create dummy file
    pdf_path = os.path.join(tmp_path, "test.pdf")
    with open(pdf_path, 'wb') as f:
        f.write(b'Dummy PDF content')
    
    # Test function
    result = process_pdf_for_mapping(pdf_path, db_session)
    
    # Verify result
    assert result["dominant_type"] == "country_profile"
    assert result["confidence"] == 0.8
    assert "countries" in result["suggested_mappings"]
    assert "countries" in result["new_entities"]
