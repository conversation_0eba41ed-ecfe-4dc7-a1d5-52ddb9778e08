
import pytest
import os
from unittest.mock import patch, MagicMock

from app.i18n import get_locale, get_translator

def test_get_locale_default():
    """Test get_locale returns default when LANG not set"""
    with patch.dict(os.environ, {}, clear=True):
        assert get_locale() == "en_US"

def test_get_locale_custom():
    """Test get_locale returns environment variable value"""
    with patch.dict(os.environ, {"LANG": "fr_FR"}, clear=True):
        assert get_locale() == "fr_FR"

def test_get_translator_fallback():
    """Test translator fallback behavior"""
    # This test checks that the translator falls back to default when locale doesn't exist
    mock_translation = MagicMock()
    mock_translation.gettext.return_value = "translated text"
    
    with patch("gettext.translation", return_value=mock_translation) as mock_gettext:
        translator = get_translator()
        result = translator("test string")
        
        assert result == "translated text"
        mock_gettext.assert_called_once()

def test_translator_usage():
    """Test actual usage of translator"""
    with patch("app.i18n.i18n.get_locale", return_value="en_US"):
        translator = get_translator()
        # Even if the actual translation fails, the function should not raise an exception
        result = translator("Welcome")
        assert isinstance(result, str)
