
import pytest
import os
import sys
import tempfile
import json
from unittest.mock import patch, mock_open, MagicMock

# Add scripts directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'scripts'))

from scripts.analyze_test_coverage import (
    get_coverage_from_html,
    analyze_coverage,
    print_report
)

class TestAnalyzeTestCoverage:
    
    @patch('builtins.open', new_callable=mock_open)
    def test_get_coverage_from_html(self, mock_file):
        # Mock HTML content
        html_content = '<title>Coverage for app/main.py: 75%</title>'
        mock_file.return_value.__enter__.return_value.read.return_value = html_content
        
        # Test function
        module_name, coverage_pct = get_coverage_from_html("fake_path.html")
        
        # Assert result
        assert module_name == "app/main.py"
        assert coverage_pct == 75
    
    @patch('builtins.open', new_callable=mock_open)
    def test_get_coverage_from_html_no_match(self, mock_file):
        # Mock HTML content with no coverage info
        html_content = '<title>Invalid title</title>'
        mock_file.return_value.__enter__.return_value.read.return_value = html_content
        
        # Test function
        module_name, coverage_pct = get_coverage_from_html("fake_path.html")
        
        # Assert result
        assert module_name is None
        assert coverage_pct == 0
    
    @patch('os.path.exists')
    @patch('glob.glob')
    @patch('scripts.analyze_test_coverage.get_coverage_from_html')
    def test_analyze_coverage(self, mock_get_coverage, mock_glob, mock_exists):
        # Setup mocks
        mock_exists.return_value = True
        mock_glob.return_value = [
            "coverage/html/main_py.html", 
            "coverage/html/models_py.html",
            "coverage/html/z_de1a740d5dc98ffd_migrate_py.html"
        ]
        mock_get_coverage.side_effect = [
            ("main.py", 80), 
            ("models.py", 90), 
            ("migrate.py", 24)
        ]
        
        # Run function
        result = analyze_coverage()
        
        # Assert results
        assert "modules" in result
        assert result["modules"]["main.py"] == 80
        assert result["modules"]["models.py"] == 90
        assert result["modules"]["migrate.py"] == 24
        assert "summary" in result
        assert result["summary"]["total_modules"] == 3
        assert result["summary"]["high_coverage"] == 2
        assert result["summary"]["medium_coverage"] == 0
        assert result["summary"]["low_coverage"] == 1
        assert result["summary"]["overall_percentage"] == 64.7
    
    @patch('os.path.exists')
    def test_analyze_coverage_no_directory(self, mock_exists):
        # Setup mock
        mock_exists.return_value = False
        
        # Run function
        result = analyze_coverage()
        
        # Assert result
        assert "error" in result
        assert result["error"] == "Coverage directory not found. Run tests with coverage first."
    
    @patch('os.path.exists')
    @patch('glob.glob')
    def test_analyze_coverage_no_files(self, mock_glob, mock_exists):
        # Setup mocks
        mock_exists.return_value = True
        mock_glob.return_value = []
        
        # Run function
        result = analyze_coverage()
        
        # Assert result
        assert "error" in result
        assert result["error"] == "No coverage files found"
    
    @patch('builtins.print')
    def test_print_report(self, mock_print):
        # Create a sample results dictionary
        results = {
            "modules": {
                "main.py": 67,
                "models.py": 96,
                "migrate.py": 24
            },
            "summary": {
                "total_modules": 3,
                "high_coverage": 1,
                "medium_coverage": 1,
                "low_coverage": 1,
                "overall_percentage": 62.3
            }
        }
        
        # Run function
        print_report(results)
        
        # Verify print was called multiple times
        assert mock_print.call_count > 5
    
    @patch('builtins.print')
    def test_print_report_error(self, mock_print):
        # Create a sample results dictionary with error
        results = {
            "error": "Coverage directory not found"
        }
        
        # Run function
        print_report(results)
        
        # Verify print was called with error
        mock_print.assert_called_once_with("Error: Coverage directory not found")
