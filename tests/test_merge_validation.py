"""
Test suite for validating system functionality after branch merges.
This module provides comprehensive testing to ensure each merge maintains system integrity.
"""

import pytest
import requests
import time
import os
from typing import List, Dict, Any

# Configuration
BASE_URL = os.getenv("BASE_URL", "http://localhost:8000")
TIMEOUT = 30


class TestMergeValidation:
    """Comprehensive test suite for post-merge validation"""

    def test_health_endpoint(self):
        """Test basic application health endpoint"""
        response = requests.get(f"{BASE_URL}/health", timeout=TIMEOUT)
        assert response.status_code == 200, f"Health endpoint failed: {response.status_code}"
        
        # Check response content
        data = response.json()
        assert "status" in data, "Health response missing status field"
        assert data["status"] == "healthy", f"Application not healthy: {data}"

    def test_database_health(self):
        """Test database connectivity and health"""
        response = requests.get(f"{BASE_URL}/health/db", timeout=TIMEOUT)
        assert response.status_code == 200, f"Database health check failed: {response.status_code}"

    def test_redis_health(self):
        """Test Redis connectivity and health"""
        response = requests.get(f"{BASE_URL}/health/redis", timeout=TIMEOUT)
        assert response.status_code == 200, f"Redis health check failed: {response.status_code}"

    @pytest.mark.parametrize("endpoint", [
        "/api/v1/regulations",
        "/api/v1/countries", 
        "/api/v1/calendar",
        "/api/v1/governance",
        "/api/v1/ai"
    ])
    def test_api_endpoints_accessible(self, endpoint: str):
        """Test that all main API endpoints are accessible"""
        response = requests.get(f"{BASE_URL}{endpoint}", timeout=TIMEOUT)
        # Accept 200 (success), 401 (unauthorized), or 403 (forbidden) as valid responses
        # These indicate the endpoint exists and is responding
        assert response.status_code in [200, 401, 403], \
            f"Endpoint {endpoint} returned unexpected status: {response.status_code}"

    def test_api_documentation_accessible(self):
        """Test that API documentation endpoints are accessible"""
        endpoints = ["/docs", "/redoc"]
        
        for endpoint in endpoints:
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=TIMEOUT)
            assert response.status_code == 200, \
                f"API documentation endpoint {endpoint} failed: {response.status_code}"

    def test_admin_interface_accessible(self):
        """Test that admin interface is accessible"""
        response = requests.get(f"{BASE_URL}/ui/manage", timeout=TIMEOUT)
        # Admin interface might require authentication, so accept redirects too
        assert response.status_code in [200, 302, 401, 403], \
            f"Admin interface failed: {response.status_code}"

    def test_static_files_accessible(self):
        """Test that static files are being served"""
        # Test common static file paths
        static_paths = ["/static/", "/favicon.ico"]
        
        for path in static_paths:
            response = requests.get(f"{BASE_URL}{path}", timeout=TIMEOUT)
            # Static files might not exist, but should not return 500 errors
            assert response.status_code != 500, \
                f"Static file path {path} returned server error: {response.status_code}"

    def test_response_times(self):
        """Test that response times are reasonable"""
        endpoints = [
            "/health",
            "/api/v1/regulations",
            "/api/v1/countries"
        ]
        
        for endpoint in endpoints:
            start_time = time.time()
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=TIMEOUT)
            end_time = time.time()
            
            response_time = end_time - start_time
            
            # Response should be under 5 seconds for basic endpoints
            assert response_time < 5.0, \
                f"Endpoint {endpoint} too slow: {response_time:.2f}s"

    def test_cors_headers(self):
        """Test that CORS headers are properly configured"""
        response = requests.options(f"{BASE_URL}/api/v1/regulations", timeout=TIMEOUT)
        
        # Should handle OPTIONS requests for CORS
        assert response.status_code in [200, 204, 405], \
            f"CORS preflight failed: {response.status_code}"

    def test_error_handling(self):
        """Test that error handling works correctly"""
        # Test 404 handling
        response = requests.get(f"{BASE_URL}/nonexistent-endpoint", timeout=TIMEOUT)
        assert response.status_code == 404, \
            f"404 handling failed: {response.status_code}"

    def test_security_headers(self):
        """Test that basic security headers are present"""
        response = requests.get(f"{BASE_URL}/health", timeout=TIMEOUT)
        
        # Check for basic security headers
        headers = response.headers
        
        # These are recommended security headers
        security_headers = [
            "X-Content-Type-Options",
            "X-Frame-Options", 
            "X-XSS-Protection"
        ]
        
        missing_headers = []
        for header in security_headers:
            if header not in headers:
                missing_headers.append(header)
        
        # Log missing headers but don't fail the test (they're recommendations)
        if missing_headers:
            print(f"Missing recommended security headers: {missing_headers}")


class TestDatabaseIntegrity:
    """Test database operations and integrity after merges"""

    def test_database_connection(self):
        """Test basic database connectivity"""
        try:
            from app.database import engine
            with engine.connect() as conn:
                result = conn.execute("SELECT 1").scalar()
                assert result == 1, "Database query failed"
        except ImportError:
            pytest.skip("Database module not available")
        except Exception as e:
            pytest.fail(f"Database connection failed: {e}")

    def test_database_tables_exist(self):
        """Test that expected database tables exist"""
        try:
            from app.database import engine
            with engine.connect() as conn:
                # Check if main tables exist
                tables_query = """
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                """
                result = conn.execute(tables_query)
                tables = [row[0] for row in result]
                
                # Should have at least some basic tables
                assert len(tables) > 0, "No tables found in database"
                
                # Log available tables for debugging
                print(f"Available tables: {tables}")
                
        except ImportError:
            pytest.skip("Database module not available")
        except Exception as e:
            pytest.fail(f"Database table check failed: {e}")


class TestPerformanceBaseline:
    """Test performance metrics to establish baselines"""

    def test_concurrent_requests(self):
        """Test handling of concurrent requests"""
        import concurrent.futures
        import threading
        
        def make_request():
            response = requests.get(f"{BASE_URL}/health", timeout=TIMEOUT)
            return response.status_code == 200
        
        # Test 10 concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(10)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # All requests should succeed
        success_rate = sum(results) / len(results)
        assert success_rate >= 0.9, f"Concurrent request success rate too low: {success_rate}"

    def test_memory_usage_stable(self):
        """Test that memory usage remains stable under load"""
        # Make multiple requests to check for memory leaks
        for _ in range(20):
            response = requests.get(f"{BASE_URL}/health", timeout=TIMEOUT)
            assert response.status_code == 200
        
        # If we get here without timeouts or errors, memory usage is likely stable
        assert True


if __name__ == "__main__":
    # Run tests when executed directly
    pytest.main([__file__, "-v"])
