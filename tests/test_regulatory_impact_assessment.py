"""
Unit tests for the impact assessment functionality.
"""
import unittest
from unittest.mock import patch, MagicMock
import datetime
import json

# Import the modules to be tested
# These imports will need to be updated based on the actual implementation
from app.regulatory_change.impact import (
    ImpactAssessment,
    ImpactAssessmentTemplate,
    RiskAssessment,
    GapAnalysis,
    CostBenefitAnalysis,
    ImplementationComplexity,
    BusinessImpact
)
from app.regulatory_change.detection import RegulatoryChange


class TestRegulatoryImpactAssessment(unittest.TestCase):
    """Test cases for impact assessment functionality."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a sample regulatory change
        self.regulatory_change = RegulatoryChange(
            source="Test Source",
            title="Updated Security Requirements",
            description="Financial institutions shall implement new security measures by January 1, 2023.",
            change_type="AMENDMENT",
            detected_date=datetime.datetime.now()
        )
        
        # Create a sample user
        self.user = MagicMock()
        self.user.id = "user123"
        self.user.name = "Test User"
        self.user.role = "Compliance Officer"
        
        # Create a sample impact assessment template
        self.template = ImpactAssessmentTemplate(
            name="Standard Impact Assessment",
            description="Standard template for regulatory impact assessment",
            sections=[
                {
                    "name": "Regulatory Overview",
                    "description": "Overview of the regulatory change",
                    "fields": [
                        {
                            "name": "summary",
                            "label": "Summary",
                            "type": "text",
                            "required": True
                        },
                        {
                            "name": "effective_date",
                            "label": "Effective Date",
                            "type": "date",
                            "required": True
                        }
                    ]
                },
                {
                    "name": "Business Impact",
                    "description": "Impact on business functions",
                    "fields": [
                        {
                            "name": "affected_departments",
                            "label": "Affected Departments",
                            "type": "multi_select",
                            "options": ["Legal", "Compliance", "IT", "Operations", "Finance"],
                            "required": True
                        },
                        {
                            "name": "impact_description",
                            "label": "Impact Description",
                            "type": "text",
                            "required": True
                        }
                    ]
                }
            ]
        )

    def test_create_impact_assessment(self):
        """Test creation of an impact assessment."""
        # Create impact assessment
        assessment = ImpactAssessment(
            regulatory_change=self.regulatory_change,
            template=self.template,
            created_by=self.user,
            created_at=datetime.datetime.now()
        )
        
        # Assertions
        self.assertEqual(assessment.regulatory_change, self.regulatory_change)
        self.assertEqual(assessment.template, self.template)
        self.assertEqual(assessment.created_by, self.user)
        self.assertIsNotNone(assessment.created_at)
        self.assertEqual(assessment.status, "DRAFT")

    def test_update_assessment_data(self):
        """Test updating assessment data."""
        # Create impact assessment
        assessment = ImpactAssessment(
            regulatory_change=self.regulatory_change,
            template=self.template,
            created_by=self.user,
            created_at=datetime.datetime.now()
        )
        
        # Update assessment data
        assessment_data = {
            "Regulatory Overview": {
                "summary": "New security requirements for financial institutions",
                "effective_date": "2023-01-01"
            },
            "Business Impact": {
                "affected_departments": ["Compliance", "IT", "Operations"],
                "impact_description": "Need to update security protocols and systems"
            }
        }
        
        updated_assessment = assessment.update_data(assessment_data, self.user)
        
        # Assertions
        self.assertEqual(updated_assessment.data, assessment_data)
        self.assertEqual(updated_assessment.last_updated_by, self.user)
        self.assertIsNotNone(updated_assessment.last_updated_at)

    def test_assessment_validation(self):
        """Test validation of assessment data against template."""
        # Create impact assessment
        assessment = ImpactAssessment(
            regulatory_change=self.regulatory_change,
            template=self.template,
            created_by=self.user,
            created_at=datetime.datetime.now()
        )
        
        # Valid assessment data
        valid_data = {
            "Regulatory Overview": {
                "summary": "New security requirements for financial institutions",
                "effective_date": "2023-01-01"
            },
            "Business Impact": {
                "affected_departments": ["Compliance", "IT"],
                "impact_description": "Need to update security protocols"
            }
        }
        
        # Invalid assessment data (missing required field)
        invalid_data = {
            "Regulatory Overview": {
                "summary": "New security requirements for financial institutions"
                # Missing effective_date
            },
            "Business Impact": {
                "affected_departments": ["Compliance", "IT"],
                "impact_description": "Need to update security protocols"
            }
        }
        
        # Validate data
        valid_result = assessment.validate(valid_data)
        invalid_result = assessment.validate(invalid_data)
        
        # Assertions
        self.assertTrue(valid_result.is_valid)
        self.assertFalse(invalid_result.is_valid)
        self.assertIn("effective_date", invalid_result.errors[0])

    def test_risk_assessment(self):
        """Test risk assessment functionality."""
        # Create risk assessment
        risk_assessment = RiskAssessment(
            regulatory_change=self.regulatory_change,
            assessed_by=self.user,
            assessed_at=datetime.datetime.now()
        )
        
        # Add risk scores
        risk_assessment.add_risk_score(
            category="Compliance Risk",
            likelihood=4,  # High likelihood
            impact=5,      # Critical impact
            description="High risk of non-compliance with significant penalties"
        )
        
        risk_assessment.add_risk_score(
            category="Operational Risk",
            likelihood=3,  # Medium likelihood
            impact=3,      # Medium impact
            description="Moderate operational disruption during implementation"
        )
        
        risk_assessment.add_risk_score(
            category="Financial Risk",
            likelihood=2,  # Low likelihood
            impact=4,      # High impact
            description="Potential financial impact from implementation costs"
        )
        
        # Calculate overall risk
        overall_risk = risk_assessment.calculate_overall_risk()
        
        # Assertions
        self.assertEqual(len(risk_assessment.risk_scores), 3)
        self.assertEqual(risk_assessment.risk_scores[0].category, "Compliance Risk")
        self.assertEqual(risk_assessment.risk_scores[0].risk_level, "Critical")  # 4x5=20 (Critical)
        self.assertEqual(risk_assessment.risk_scores[1].risk_level, "Medium")    # 3x3=9 (Medium)
        self.assertEqual(risk_assessment.risk_scores[2].risk_level, "High")      # 2x4=8 (High)
        self.assertEqual(overall_risk, "High")  # Highest risk level is Critical

    def test_gap_analysis(self):
        """Test gap analysis functionality."""
        # Create gap analysis
        gap_analysis = GapAnalysis(
            regulatory_change=self.regulatory_change,
            analyzed_by=self.user,
            analyzed_at=datetime.datetime.now()
        )
        
        # Add identified gaps
        gap_analysis.add_gap(
            category="Policy",
            description="Current security policy does not address new requirements",
            severity="High",
            remediation_plan="Update security policy to include new requirements"
        )
        
        gap_analysis.add_gap(
            category="System",
            description="Current systems do not support required encryption level",
            severity="Critical",
            remediation_plan="Upgrade encryption capabilities in affected systems"
        )
        
        gap_analysis.add_gap(
            category="Training",
            description="Staff not trained on new security protocols",
            severity="Medium",
            remediation_plan="Develop and deliver training program"
        )
        
        # Assertions
        self.assertEqual(len(gap_analysis.gaps), 3)
        self.assertEqual(gap_analysis.gaps[0].category, "Policy")
        self.assertEqual(gap_analysis.gaps[1].severity, "Critical")
        self.assertEqual(gap_analysis.gaps[2].category, "Training")
        
        # Test gap prioritization
        prioritized_gaps = gap_analysis.prioritize_gaps()
        self.assertEqual(prioritized_gaps[0].severity, "Critical")
        self.assertEqual(prioritized_gaps[1].severity, "High")
        self.assertEqual(prioritized_gaps[2].severity, "Medium")

    def test_cost_benefit_analysis(self):
        """Test cost-benefit analysis functionality."""
        # Create cost-benefit analysis
        cba = CostBenefitAnalysis(
            regulatory_change=self.regulatory_change,
            analyzed_by=self.user,
            analyzed_at=datetime.datetime.now()
        )
        
        # Add costs
        cba.add_cost(
            category="Implementation",
            description="System upgrades",
            amount=250000.00,
            recurring=False
        )
        
        cba.add_cost(
            category="Implementation",
            description="Staff training",
            amount=50000.00,
            recurring=False
        )
        
        cba.add_cost(
            category="Ongoing",
            description="Additional security monitoring",
            amount=75000.00,
            recurring=True,
            recurrence_period="Annual"
        )
        
        # Add benefits
        cba.add_benefit(
            category="Risk Reduction",
            description="Reduced risk of security breaches",
            amount=500000.00,  # Estimated value of risk reduction
            recurring=True,
            recurrence_period="Annual"
        )
        
        cba.add_benefit(
            category="Operational",
            description="Improved operational efficiency",
            amount=100000.00,
            recurring=True,
            recurrence_period="Annual"
        )
        
        # Calculate ROI
        roi_1year = cba.calculate_roi(years=1)
        roi_3year = cba.calculate_roi(years=3)
        
        # Assertions
        self.assertEqual(len(cba.costs), 3)
        self.assertEqual(len(cba.benefits), 2)
        
        # First year ROI calculation:
        # Costs: 250000 + 50000 + 75000 = 375000
        # Benefits: 500000 + 100000 = 600000
        # ROI = (600000 - 375000) / 375000 = 0.6 = 60%
        self.assertAlmostEqual(roi_1year, 0.6, places=1)
        
        # 3-year ROI calculation:
        # Costs: 250000 + 50000 + (75000 * 3) = 525000
        # Benefits: (500000 + 100000) * 3 = 1800000
        # ROI = (1800000 - 525000) / 525000 = 2.43 = 243%
        self.assertAlmostEqual(roi_3year, 2.43, places=2)

    def test_implementation_complexity(self):
        """Test implementation complexity assessment."""
        # Create implementation complexity assessment
        complexity = ImplementationComplexity(
            regulatory_change=self.regulatory_change,
            assessed_by=self.user,
            assessed_at=datetime.datetime.now()
        )
        
        # Add complexity factors
        complexity.add_factor(
            category="Technical",
            description="System changes required",
            complexity_level=4,  # High
            weight=3  # Important factor
        )
        
        complexity.add_factor(
            category="Organizational",
            description="Process changes required",
            complexity_level=3,  # Medium
            weight=2  # Moderately important
        )
        
        complexity.add_factor(
            category="Timeline",
            description="Tight deadline",
            complexity_level=5,  # Very High
            weight=4  # Very important
        )
        
        # Calculate overall complexity
        overall_complexity = complexity.calculate_overall_complexity()
        
        # Assertions
        self.assertEqual(len(complexity.factors), 3)
        
        # Weighted average calculation:
        # (4*3 + 3*2 + 5*4) / (3+2+4) = (12 + 6 + 20) / 9 = 38/9 = 4.22
        # This rounds to 4, which is "High" complexity
        self.assertAlmostEqual(overall_complexity.score, 4.22, places=2)
        self.assertEqual(overall_complexity.level, "High")

    def test_business_impact_assessment(self):
        """Test business impact assessment functionality."""
        # Create business impact assessment
        impact = BusinessImpact(
            regulatory_change=self.regulatory_change,
            assessed_by=self.user,
            assessed_at=datetime.datetime.now()
        )
        
        # Add impacted areas
        impact.add_impacted_area(
            department="IT",
            description="Need to upgrade security systems",
            impact_level="High",
            affected_systems=["Authentication System", "Data Storage"],
            affected_processes=["User Authentication", "Data Encryption"]
        )
        
        impact.add_impacted_area(
            department="Compliance",
            description="Need to update compliance procedures",
            impact_level="Medium",
            affected_systems=[],
            affected_processes=["Compliance Monitoring", "Audit Procedures"]
        )
        
        impact.add_impacted_area(
            department="Operations",
            description="Need to adjust operational procedures",
            impact_level="Low",
            affected_systems=["Operations Portal"],
            affected_processes=["Customer Onboarding"]
        )
        
        # Generate impact heat map
        heat_map = impact.generate_heat_map()
        
        # Assertions
        self.assertEqual(len(impact.impacted_areas), 3)
        self.assertEqual(impact.impacted_areas[0].department, "IT")
        self.assertEqual(impact.impacted_areas[0].impact_level, "High")
        self.assertEqual(len(impact.impacted_areas[0].affected_systems), 2)
        
        # Check heat map
        self.assertEqual(heat_map["IT"], "High")
        self.assertEqual(heat_map["Compliance"], "Medium")
        self.assertEqual(heat_map["Operations"], "Low")
        
        # Test most impacted areas
        most_impacted = impact.get_most_impacted_areas()
        self.assertEqual(most_impacted[0].department, "IT")


if __name__ == '__main__':
    unittest.main()
