
import pytest
from fastapi.testclient import TestClient
import json
import os
from unittest.mock import patch, MagicMock

from app.main import app

client = TestClient(app)

@pytest.fixture
def mock_file():
    class MockFile:
        def __init__(self):
            self.filename = "test_document.pdf"
            self.content_type = "application/pdf"
            self.file = MagicMock()
            
        def read(self):
            return b"test content"
    
    return MockFile()

def test_analyze_document_endpoint(mock_file):
    with patch('app.api.document_import.process_document') as mock_process:
        mock_process.return_value = "job123"
        
        response = client.post(
            "/document-import/analyze",
            files={"file": ("test_document.pdf", b"test content", "application/pdf")}
        )
        
        assert response.status_code == 200
        assert "job_id" in response.json()
        mock_process.assert_called_once()

def test_get_document_status_completed():
    with patch('app.api.document_import.get_job_status') as mock_status:
        mock_status.return_value = {"status": "completed", "result": {"key": "value"}}
        
        response = client.get("/document-import/status/job123")
        
        assert response.status_code == 200
        assert response.json()["status"] == "completed"
        assert "result" in response.json()
        mock_status.assert_called_once_with("job123")

def test_get_document_status_processing():
    with patch('app.api.document_import.get_job_status') as mock_status:
        mock_status.return_value = {"status": "processing"}
        
        response = client.get("/document-import/status/job123")
        
        assert response.status_code == 200
        assert response.json()["status"] == "processing"
        mock_status.assert_called_once_with("job123")

def test_get_document_status_failed():
    with patch('app.api.document_import.get_job_status') as mock_status:
        mock_status.return_value = {"status": "failed", "error": "Error message"}
        
        response = client.get("/document-import/status/job123")
        
        assert response.status_code == 200
        assert response.json()["status"] == "failed"
        assert response.json()["error"] == "Error message"
        mock_status.assert_called_once_with("job123")

def test_document_import_invalid_file():
    response = client.post(
        "/document-import/analyze",
        files={"file": ("test.txt", b"not a valid document", "text/plain")}
    )
    
    # Depending on your API implementation, this could be 400 or 422
    assert response.status_code in [400, 422]
    assert "error" in response.json() or "detail" in response.json()
