"""Unit tests for Enhanced Regulatory Map models."""
import pytest
from datetime import datetime
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.db.models import (
    Base, RegulatoryEntity, RegulatoryRelationship, RegulatoryComplianceStatus,
    RegulatoryView, RegulatoryAnnotation, EntityType, RelationshipType,
    ComplianceStatusEnum, ImplementationStatusEnum
)

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_regulatory_map_models.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="module")
def setup_database():
    """Set up test database."""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def db_session():
    """Create a database session for testing."""
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()


class TestRegulatoryEntityModel:
    """Test cases for RegulatoryEntity model."""
    
    def test_create_regulatory_entity(self, setup_database, db_session):
        """Test creating a regulatory entity."""
        entity = RegulatoryEntity(
            name="Test Regulation",
            description="A test regulation",
            type=EntityType.REGULATION,
            jurisdiction="US",
            status="active",
            version="1.0"
        )
        
        db_session.add(entity)
        db_session.commit()
        db_session.refresh(entity)
        
        assert entity.id is not None
        assert entity.name == "Test Regulation"
        assert entity.type == EntityType.REGULATION
        assert entity.jurisdiction == "US"
        assert entity.status == "active"
        assert entity.version == "1.0"
        assert entity.created_at is not None
        assert entity.is_deleted == False
    
    def test_entity_hierarchy(self, setup_database, db_session):
        """Test parent-child relationships in entities."""
        # Create parent entity
        parent = RegulatoryEntity(
            name="Parent Regulation",
            type=EntityType.REGULATION,
            jurisdiction="US"
        )
        db_session.add(parent)
        db_session.commit()
        db_session.refresh(parent)
        
        # Create child entity
        child = RegulatoryEntity(
            name="Child Requirement",
            type=EntityType.REQUIREMENT,
            parent_id=parent.id,
            jurisdiction="US"
        )
        db_session.add(child)
        db_session.commit()
        db_session.refresh(child)
        
        # Test relationships
        assert child.parent_id == parent.id
        assert child.parent == parent
        assert child in parent.children
    
    def test_soft_delete(self, setup_database, db_session):
        """Test soft delete functionality."""
        entity = RegulatoryEntity(
            name="Test Delete Entity",
            type=EntityType.CONTROL,
            jurisdiction="US"
        )
        
        db_session.add(entity)
        db_session.commit()
        db_session.refresh(entity)
        
        # Verify entity exists
        assert entity.is_deleted == False
        assert entity.deleted_at is None
        
        # Soft delete the entity
        entity.soft_delete()
        db_session.commit()
        
        # Verify soft delete
        assert entity.is_deleted == True
        assert entity.deleted_at is not None


class TestRegulatoryRelationshipModel:
    """Test cases for RegulatoryRelationship model."""
    
    def test_create_relationship(self, setup_database, db_session):
        """Test creating a regulatory relationship."""
        # Create source and target entities
        source = RegulatoryEntity(
            name="Source Entity",
            type=EntityType.REGULATION,
            jurisdiction="US"
        )
        target = RegulatoryEntity(
            name="Target Entity",
            type=EntityType.REQUIREMENT,
            jurisdiction="US"
        )
        
        db_session.add(source)
        db_session.add(target)
        db_session.commit()
        db_session.refresh(source)
        db_session.refresh(target)
        
        # Create relationship
        relationship = RegulatoryRelationship(
            source_id=source.id,
            target_id=target.id,
            relationship_type=RelationshipType.CONTAINS,
            strength=0.8,
            description="Source contains target"
        )
        
        db_session.add(relationship)
        db_session.commit()
        db_session.refresh(relationship)
        
        assert relationship.id is not None
        assert relationship.source_id == source.id
        assert relationship.target_id == target.id
        assert relationship.relationship_type == RelationshipType.CONTAINS
        assert relationship.strength == 0.8
        assert relationship.source_entity == source
        assert relationship.target_entity == target


class TestRegulatoryComplianceStatusModel:
    """Test cases for RegulatoryComplianceStatus model."""
    
    def test_create_compliance_status(self, setup_database, db_session):
        """Test creating a compliance status record."""
        # Create entity
        entity = RegulatoryEntity(
            name="Test Entity",
            type=EntityType.REGULATION,
            jurisdiction="US"
        )
        db_session.add(entity)
        db_session.commit()
        db_session.refresh(entity)
        
        # Create compliance status
        status = RegulatoryComplianceStatus(
            entity_id=entity.id,
            compliance_status=ComplianceStatusEnum.COMPLIANT,
            implementation_status=ImplementationStatusEnum.IMPLEMENTED,
            compliance_score=90.0,
            risk_score=10.0,
            assessment_date=datetime.utcnow(),
            assessed_by="Test Assessor",
            notes="Test compliance assessment"
        )
        
        db_session.add(status)
        db_session.commit()
        db_session.refresh(status)
        
        assert status.id is not None
        assert status.entity_id == entity.id
        assert status.compliance_status == ComplianceStatusEnum.COMPLIANT
        assert status.implementation_status == ImplementationStatusEnum.IMPLEMENTED
        assert status.compliance_score == 90.0
        assert status.risk_score == 10.0
        assert status.assessed_by == "Test Assessor"
        assert status.entity == entity


class TestRegulatoryViewModel:
    """Test cases for RegulatoryView model."""
    
    def test_create_view(self, setup_database, db_session):
        """Test creating a regulatory view."""
        view = RegulatoryView(
            name="Test View",
            description="A test view",
            user_id="test-user-123",
            is_public=False,
            is_default=True,
            view_state={
                "filters": {"jurisdiction": "US"},
                "layout": "grid",
                "zoom": 1.0
            }
        )
        
        db_session.add(view)
        db_session.commit()
        db_session.refresh(view)
        
        assert view.id is not None
        assert view.name == "Test View"
        assert view.user_id == "test-user-123"
        assert view.is_public == False
        assert view.is_default == True
        assert view.view_state["filters"]["jurisdiction"] == "US"


class TestRegulatoryAnnotationModel:
    """Test cases for RegulatoryAnnotation model."""
    
    def test_create_annotation(self, setup_database, db_session):
        """Test creating a regulatory annotation."""
        # Create entity
        entity = RegulatoryEntity(
            name="Test Entity",
            type=EntityType.REGULATION,
            jurisdiction="US"
        )
        db_session.add(entity)
        db_session.commit()
        db_session.refresh(entity)
        
        # Create annotation
        annotation = RegulatoryAnnotation(
            entity_id=entity.id,
            title="Test Annotation",
            content="This is a test annotation",
            annotation_type="interpretation",
            user_id="test-user-123",
            visibility="private"
        )
        
        db_session.add(annotation)
        db_session.commit()
        db_session.refresh(annotation)
        
        assert annotation.id is not None
        assert annotation.entity_id == entity.id
        assert annotation.title == "Test Annotation"
        assert annotation.content == "This is a test annotation"
        assert annotation.annotation_type == "interpretation"
        assert annotation.user_id == "test-user-123"
        assert annotation.visibility == "private"
        assert annotation.entity == entity


class TestEnumValues:
    """Test cases for enum values."""
    
    def test_entity_type_enum(self):
        """Test EntityType enum values."""
        assert EntityType.REGULATION == "regulation"
        assert EntityType.REQUIREMENT == "requirement"
        assert EntityType.CONTROL == "control"
        assert EntityType.BUSINESS_PROCESS == "business_process"
        assert EntityType.SYSTEM == "system"
        assert EntityType.POLICY == "policy"
        assert EntityType.PROCEDURE == "procedure"
    
    def test_relationship_type_enum(self):
        """Test RelationshipType enum values."""
        assert RelationshipType.CONTAINS == "contains"
        assert RelationshipType.REFERENCES == "references"
        assert RelationshipType.IMPLEMENTS == "implements"
        assert RelationshipType.IMPACTS == "impacts"
        assert RelationshipType.USES == "uses"
        assert RelationshipType.CONFLICTS == "conflicts"
        assert RelationshipType.SUPERSEDES == "supersedes"
        assert RelationshipType.DEPENDS_ON == "depends_on"
        assert RelationshipType.DERIVES_FROM == "derives_from"
    
    def test_compliance_status_enum(self):
        """Test ComplianceStatusEnum values."""
        assert ComplianceStatusEnum.COMPLIANT == "compliant"
        assert ComplianceStatusEnum.AT_RISK == "at_risk"
        assert ComplianceStatusEnum.NON_COMPLIANT == "non_compliant"
        assert ComplianceStatusEnum.UNKNOWN == "unknown"
        assert ComplianceStatusEnum.NOT_APPLICABLE == "not_applicable"
    
    def test_implementation_status_enum(self):
        """Test ImplementationStatusEnum values."""
        assert ImplementationStatusEnum.NOT_STARTED == "not_started"
        assert ImplementationStatusEnum.IN_PROGRESS == "in_progress"
        assert ImplementationStatusEnum.IMPLEMENTED == "implemented"
        assert ImplementationStatusEnum.VERIFIED == "verified"
        assert ImplementationStatusEnum.DECOMMISSIONED == "decommissioned"
