
"""Comprehensive tests for the health check endpoint."""
import pytest
from fastapi.testclient import TestClient
import json
from unittest.mock import patch, MagicMock

from app.main import app

client = TestClient(app)

def test_health_check_basic():
    """Test the basic health check endpoint"""
    with patch('app.main.get_db') as mock_get_db:
        mock_db = MagicMock()
        mock_db.execute.return_value = True
        mock_get_db.return_value = mock_db
        
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert data["database"] == "healthy"
        assert "test_coverage" in data

def test_health_check_api_v1():
    """Test the API v1 health check endpoint"""
    with patch('app.main.get_db') as mock_get_db:
        mock_db = MagicMock()
        mock_db.execute.return_value = True
        mock_get_db.return_value = mock_db
        
        response = client.get("/api/v1/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert data["database"] == "healthy"
        assert "test_coverage" in data

def test_health_check_db_failure():
    """Test health check when database connection fails"""
    with patch('app.main.get_db') as mock_get_db:
        mock_db = MagicMock()
        mock_db.execute.side_effect = Exception("Database error")
        mock_get_db.return_value = mock_db
        
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "unhealthy" in data["database"]
        assert "Database error" in data["database"]

def test_health_check_with_coverage():
    """Test health check with test coverage data"""
    with patch('app.main.get_db') as mock_get_db, \
         patch('os.path.exists', return_value=True), \
         patch('builtins.open'), \
         patch('re.search') as mock_re_search:
        
        # Mock database
        mock_db = MagicMock()
        mock_db.execute.return_value = True
        mock_get_db.return_value = mock_db
        
        # Mock coverage extraction
        mock_re_search.return_value.group.return_value = "85"
        
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert "test_coverage" in data
