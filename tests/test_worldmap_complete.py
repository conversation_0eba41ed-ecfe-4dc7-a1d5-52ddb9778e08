
"""Comprehensive test cases for the worldmap module."""
import pytest
from unittest.mock import patch, MagicMock
import folium

from app.visualization import worldmap


def test_create_world_map_default_params():
    """Test creating a world map with default parameters."""
    map_obj = worldmap.create_world_map()
    
    # Test that a folium Map object is returned
    assert isinstance(map_obj, folium.Map)
    
    # Test default values
    assert map_obj.location == [0, 0]
    assert map_obj.zoom_start == 2


def test_create_world_map_custom_params():
    """Test creating a world map with custom parameters."""
    # Test with custom center and zoom
    map_obj = worldmap.create_world_map(center=(40.7128, -74.0060), zoom_start=10, tiles="Stamen Terrain")
    
    # Verify custom values
    assert map_obj.location == [40.7128, -74.0060]
    assert map_obj.zoom_start == 10
    assert map_obj.options['tiles'] == "Stamen Terrain"


def test_get_map_html():
    """Test getting HTML representation of a map."""
    # Create a map
    map_obj = worldmap.create_world_map()
    
    # Get HTML from map
    html = worldmap.get_map_html(map_obj)
    
    # Verify HTML was generated
    assert isinstance(html, str)
    assert "<html" in html.lower()
    assert "leaflet" in html.lower()


@patch('folium.Map')
@patch('folium.Marker')
def test_add_marker(mock_marker, mock_map):
    """Test adding a marker to a map."""
    # Setup mocks
    mock_map_instance = MagicMock()
    mock_map.return_value = mock_map_instance
    
    mock_marker_instance = MagicMock()
    mock_marker.return_value = mock_marker_instance
    
    # Create map and add marker
    map_obj = worldmap.create_world_map()
    location = (51.5074, -0.1278)  # London
    popup = "London"
    
    worldmap.add_marker(map_obj, location, popup)
    
    # Verify marker was created and added to map
    mock_marker.assert_called_once()
    args, kwargs = mock_marker.call_args
    assert kwargs['location'] == location
    assert kwargs['popup'] == popup
    mock_marker_instance.add_to.assert_called_once_with(mock_map_instance)


@patch('folium.Choropleth')
def test_add_country_highlights(mock_choropleth):
    """Test adding country highlights to the map."""
    # Setup mocks
    mock_map = MagicMock()
    mock_choropleth_instance = MagicMock()
    mock_choropleth.return_value = mock_choropleth_instance
    
    # Test data
    countries = {
        'USA': 95,
        'CAN': 87,
        'GBR': 92
    }
    
    # Call function
    worldmap.add_country_highlights(mock_map, countries)
    
    # Verify choropleth was created and added to map
    mock_choropleth.assert_called_once()
    mock_map.add_child.assert_called_once_with(mock_choropleth_instance)


def test_map_endpoint_integration():
    """Test the integration with FastAPI endpoint."""
    from fastapi.testclient import TestClient
    from app.main import app
    
    client = TestClient(app)
    
    # Test with default parameters
    response = client.get("/api/v1/worldmap")
    assert response.status_code == 200
    assert "<html" in response.text.lower()
    
    # Test with custom parameters
    response = client.get("/api/v1/worldmap?lat=40.7128&lon=-74.0060&zoom=10")
    assert response.status_code == 200
    assert "<html" in response.text.lower()
