import pytest
from unittest.mock import patch, MagicMock
from app.api.app.api.pdf_analysis import *

# TODO: Add fixtures here as needed
"""Tests for PDF analysis API."""
import os
import pytest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
import json
import tempfile

from app.main import app
from app.api.pdf_analysis import analysis_results, run_pdf_analysis

client = TestClient(app)

@pytest.fixture
def sample_pdf_content():
    """Sample PDF content for testing."""
    return b"Sample PDF content for testing"

@pytest.fixture
def mock_pdf_analysis_result():
    """Mock PDF analysis result."""
    return {
        "regulatory_sections": {
            "articles": ["Article 1. Scope"],
            "sections": ["Section 3. Penalties"],
            "requirements": ["Organizations shall implement appropriate security measures."],
            "penalties": ["Violations may result in fines up to $10,000,000."]
        },
        "entities": {
            "organizations": ["organizations"],
            "regulatory_terms": ["personal data", "security measures"],
            "money": ["$10,000,000"],
            "percent": ["2%"],
            "dates": []
        },
        "confidence_score": 0.85,
        "summary": "The document contains 1 article. There are 1 requirement identified. The document specifies 1 penalty/enforcement measure.",
        "text_length": 500,
        "document_path": "/tmp/test.pdf"
    }

@patch('app.api.pdf_analysis.analyze_pdf_document')
def test_analyze_pdf_endpoint(mock_analyze, sample_pdf_content, mock_pdf_analysis_result):
    """Test the analyze PDF endpoint."""
    # Setup mock
    mock_analyze.return_value = mock_pdf_analysis_result
    
    # Create temp file
    with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp:
        tmp.write(sample_pdf_content)
        tmp_path = tmp.name
    
    try:
        # Test with file
        with open(tmp_path, 'rb') as f:
            response = client.post(
                "/pdf/analyze",
                files={"document": ("test.pdf", f, "application/pdf")},
                data={"country": "US"}
            )
        
        # Check response
        assert response.status_code == 200
        data = response.json()
        assert "job_id" in data
        assert data["status"] == "processing"
        
        # Simulate job completion
        job_id = data["job_id"]
        run_pdf_analysis(job_id, tmp_path, cleanup=False)
        
        # Check status endpoint
        status_response = client.get(f"/pdf/status/{job_id}")
        assert status_response.status_code == 200
        status_data = status_response.json()
        
        assert status_data["status"] == "completed"
        assert "result" in status_data
        assert status_data["result"]["confidence_score"] == 0.85
        
        # Test non-existent job
        non_existent_response = client.get("/pdf/status/nonexistentjob123")
        assert non_existent_response.status_code == 404
        
    finally:
        # Clean up
        if os.path.exists(tmp_path):
            os.unlink(tmp_path)

@patch('app.api.pdf_analysis.analyze_pdf_document')
def test_analyze_pdf_invalid_file(mock_analyze, sample_pdf_content):
    """Test analyze PDF with invalid file type."""
    # Create temp file (not a PDF)
    with tempfile.NamedTemporaryFile(suffix=".txt", delete=False) as tmp:
        tmp.write(b"This is not a PDF file")
        tmp_path = tmp.name
    
    try:
        # Test with non-PDF file
        with open(tmp_path, 'rb') as f:
            response = client.post(
                "/pdf/analyze",
                files={"document": ("test.txt", f, "text/plain")},
                data={"country": "US"}
            )
        
        # Check response for error
        assert response.status_code == 400
        data = response.json()
        assert "detail" in data
        assert "Invalid file type" in data["detail"]
        
    finally:
        # Clean up
        if os.path.exists(tmp_path):
            os.unlink(tmp_path)

def test_pdf_analysis_exception_handling(mock_pdf_analysis_result):
    """Test exception handling in PDF analysis job."""
    # Create a job ID and setup initial status
    job_id = "test_exception_job"
    analysis_results[job_id] = {
        "status": "processing",
        "filename": "test.pdf"
    }
    
    # Run with a non-existent file to trigger exception
    run_pdf_analysis(job_id, "/nonexistent/path/to/file.pdf", cleanup=False)
    
    # Check result
    assert analysis_results[job_id]["status"] == "failed"
    assert "error" in analysis_results[job_id]
