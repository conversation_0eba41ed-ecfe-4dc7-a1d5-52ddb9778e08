"""
Tests for the regulator management API router.
"""
import unittest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.main import app
from app.db.models import Regulator, Country
from app.schemas.schemas import RegulatorCreate, RegulatorUpdate

client = TestClient(app)

class TestRegulatorRouter(unittest.TestCase):
    """Tests for the regulator management API router."""

    def setUp(self):
        """Set up test data."""
        self.test_regulator_data = {
            "name": "Test Regulator",
            "description": "Test Description",
            "type": "Test Type",
            "website": "https://www.testregulator.com",
            "country_id": 1
        }
        
        self.test_regulator_update_data = {
            "name": "Updated Test Regulator",
            "description": "Updated Description"
        }

    @patch('app.api.regulators.router.get_db')
    def test_get_regulators(self, mock_get_db):
        """Test getting a list of regulators."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock query result
        mock_regulator = MagicMock()
        mock_regulator.id = 1
        mock_regulator.name = "Test Regulator"
        mock_regulator.description = "Test Description"
        mock_regulator.type = "Test Type"
        mock_regulator.website = "https://www.testregulator.com"
        mock_regulator.country_id = 1
        mock_regulator.created_at = "2023-01-01T00:00:00"
        mock_regulator.changed_on = "2023-01-01T00:00:00"
        
        # Create mock country for the relationship
        mock_country = MagicMock()
        mock_country.id = 1
        mock_country.name = "Test Country"
        mock_country.code = "TC"
        
        # Set up the relationship
        mock_regulator.country = mock_country
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [mock_regulator]
        
        # Make request
        response = client.get("/api/v1/regulators/")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response.json(), list)
        self.assertEqual(len(response.json()), 1)
        self.assertEqual(response.json()[0]["name"], "Test Regulator")
        self.assertEqual(response.json()[0]["country_id"], 1)
        
        # Verify query was called correctly
        mock_db.query.assert_called_once_with(Regulator)
        mock_query.offset.assert_called_once_with(0)
        mock_query.limit.assert_called_once_with(100)

    @patch('app.api.regulators.router.get_db')
    def test_get_regulators_with_filters(self, mock_get_db):
        """Test getting regulators with filters."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = []
        
        # Make request with filters
        response = client.get("/api/v1/regulators/?country_id=1&type=Financial")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        
        # Verify filter was called twice (once for each filter)
        self.assertEqual(mock_query.filter.call_count, 2)

    @patch('app.api.regulators.router.get_db')
    def test_get_regulator(self, mock_get_db):
        """Test getting a specific regulator by ID."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock query result
        mock_regulator = MagicMock()
        mock_regulator.id = 1
        mock_regulator.name = "Test Regulator"
        mock_regulator.description = "Test Description"
        mock_regulator.type = "Test Type"
        mock_regulator.website = "https://www.testregulator.com"
        mock_regulator.country_id = 1
        mock_regulator.created_at = "2023-01-01T00:00:00"
        mock_regulator.changed_on = "2023-01-01T00:00:00"
        
        # Create mock country for the relationship
        mock_country = MagicMock()
        mock_country.id = 1
        mock_country.name = "Test Country"
        mock_country.code = "TC"
        
        # Set up the relationship
        mock_regulator.country = mock_country
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_regulator
        
        # Make request
        response = client.get("/api/v1/regulators/1")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["name"], "Test Regulator")
        self.assertEqual(response.json()["country_id"], 1)
        
        # Verify query was called correctly
        mock_db.query.assert_called_once_with(Regulator)
        mock_query.filter.assert_called_once()
        mock_query.first.assert_called_once()

    @patch('app.api.regulators.router.get_db')
    def test_get_regulator_not_found(self, mock_get_db):
        """Test getting a non-existent regulator."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain to return None
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        
        # Make request
        response = client.get("/api/v1/regulators/999")
        
        # Check response
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.json()["detail"], "Regulator not found")

    @patch('app.api.regulators.router.get_db')
    def test_create_regulator(self, mock_get_db):
        """Test creating a new regulator."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Mock the country query
        mock_country = MagicMock()
        mock_country.id = 1
        mock_country.name = "Test Country"
        
        # Set up the mock query chain for checking existing country
        mock_country_query = MagicMock()
        mock_db.query.side_effect = [mock_country_query, mock_country_query]
        mock_country_query.filter.return_value = mock_country_query
        mock_country_query.first.side_effect = [mock_country, None]  # First return country, then no existing regulator
        
        # Create mock regulator object that will be returned
        mock_regulator = MagicMock()
        mock_regulator.id = 1
        mock_regulator.name = self.test_regulator_data["name"]
        mock_regulator.description = self.test_regulator_data["description"]
        mock_regulator.type = self.test_regulator_data["type"]
        mock_regulator.website = self.test_regulator_data["website"]
        mock_regulator.country_id = self.test_regulator_data["country_id"]
        mock_regulator.created_at = "2023-01-01T00:00:00"
        mock_regulator.changed_on = "2023-01-01T00:00:00"
        mock_regulator.country = mock_country
        
        # Mock the add, commit, and refresh methods
        mock_db.add = MagicMock()
        mock_db.commit = MagicMock()
        mock_db.refresh = MagicMock()
        
        # Make the Regulator constructor return our mock
        with patch('app.api.regulators.router.Regulator', return_value=mock_regulator):
            # Make request
            response = client.post("/api/v1/regulators/", json=self.test_regulator_data)
            
            # Check response
            self.assertEqual(response.status_code, 201)
            self.assertEqual(response.json()["name"], self.test_regulator_data["name"])
            self.assertEqual(response.json()["country_id"], self.test_regulator_data["country_id"])
            
            # Verify methods were called
            mock_db.add.assert_called_once()
            mock_db.commit.assert_called_once()
            mock_db.refresh.assert_called_once()

    @patch('app.api.regulators.router.get_db')
    def test_create_regulator_country_not_found(self, mock_get_db):
        """Test creating a regulator with a non-existent country."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain to return no country
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        
        # Make request
        response = client.post("/api/v1/regulators/", json=self.test_regulator_data)
        
        # Check response
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.json()["detail"], "Country not found")

    @patch('app.api.regulators.router.get_db')
    def test_create_regulator_duplicate(self, mock_get_db):
        """Test creating a regulator with a duplicate name for the same country."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Mock the country query
        mock_country = MagicMock()
        mock_country.id = 1
        mock_country.name = "Test Country"
        
        # Create mock existing regulator
        mock_existing_regulator = MagicMock()
        mock_existing_regulator.id = 1
        mock_existing_regulator.name = self.test_regulator_data["name"]
        mock_existing_regulator.country_id = self.test_regulator_data["country_id"]
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.side_effect = [mock_query, mock_query]
        mock_query.filter.return_value = mock_query
        mock_query.first.side_effect = [mock_country, mock_existing_regulator]  # First return country, then existing regulator
        
        # Make request
        response = client.post("/api/v1/regulators/", json=self.test_regulator_data)
        
        # Check response
        self.assertEqual(response.status_code, 400)
        self.assertIn("already exists", response.json()["detail"])

    @patch('app.api.regulators.router.get_db')
    def test_update_regulator(self, mock_get_db):
        """Test updating a regulator."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock regulator to update
        mock_regulator = MagicMock()
        mock_regulator.id = 1
        mock_regulator.name = "Test Regulator"
        mock_regulator.description = "Test Description"
        mock_regulator.country_id = 1
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_regulator
        
        # Make request
        response = client.put("/api/v1/regulators/1", json=self.test_regulator_update_data)
        
        # Check response
        self.assertEqual(response.status_code, 200)
        
        # Verify setattr was called for each field
        self.assertEqual(mock_regulator.name, self.test_regulator_update_data["name"])
        self.assertEqual(mock_regulator.description, self.test_regulator_update_data["description"])
        
        # Verify commit and refresh were called
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()

    @patch('app.api.regulators.router.get_db')
    def test_delete_regulator(self, mock_get_db):
        """Test deleting a regulator."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock regulator to delete
        mock_regulator = MagicMock()
        mock_regulator.id = 1
        mock_regulator.name = "Test Regulator"
        mock_regulator.regulation_urls = []
        mock_regulator.changes = []
        mock_regulator.sources = []
        mock_regulator.alert_subscriptions = []
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_regulator
        
        # Make request
        response = client.delete("/api/v1/regulators/1")
        
        # Check response
        self.assertEqual(response.status_code, 204)
        
        # Verify delete, commit were called
        mock_db.delete.assert_called_once_with(mock_regulator)
        mock_db.commit.assert_called_once()

    @patch('app.api.regulators.router.get_db')
    def test_delete_regulator_with_relations(self, mock_get_db):
        """Test deleting a regulator that has related records."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock regulator with related records
        mock_regulator = MagicMock()
        mock_regulator.id = 1
        mock_regulator.name = "Test Regulator"
        mock_regulator.regulation_urls = [MagicMock()]  # Non-empty list
        mock_regulator.changes = []
        mock_regulator.sources = []
        mock_regulator.alert_subscriptions = []
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_regulator
        
        # Make request
        response = client.delete("/api/v1/regulators/1")
        
        # Check response
        self.assertEqual(response.status_code, 400)
        self.assertIn("Cannot delete regulator with related records", response.json()["detail"])
        
        # Verify delete was not called
        mock_db.delete.assert_not_called()


if __name__ == "__main__":
    unittest.main()
