"""
Tests for the regulation management service.
"""
import unittest
from unittest.mock import patch, MagicMock
from datetime import datetime

from app.api.regulation_management.service import RegulationService
from app.db.models import (
    Regulation, Industry, RegulationCategory, RegulationTag,
    ComplianceRequirement, RegulationDocument, RegulationChangeLog,
    RegulationStatus
)
from app.schemas.regulation import (
    RegulationCreate, RegulationUpdate,
    IndustryCreate, RegulationCategoryCreate, RegulationTagCreate,
    ComplianceRequirementCreate, ComplianceRequirementUpdate,
    RegulationDocumentCreate, RegulationDocumentUpdate,
    RegulationChangeLogCreate
)

class TestRegulationService(unittest.TestCase):
    """Tests for the regulation management service."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.service = RegulationService()
        self.mock_db = MagicMock()
        self.mock_query = MagicMock()
        self.mock_db.query.return_value = self.mock_query
    
    def test_create_regulation(self):
        """Test creating a regulation."""
        # Create a regulation
        regulation_data = RegulationCreate(
            title="Test Regulation",
            description="Test Description",
            status=RegulationStatus.ENACTED
        )
        
        # Mock the database operations
        mock_regulation = MagicMock()
        mock_regulation.id = 1
        self.mock_db.add.return_value = None
        self.mock_db.flush.return_value = None
        self.mock_db.commit.return_value = None
        self.mock_db.refresh.return_value = None
        
        # Call the service method
        result = self.service.create_regulation(self.mock_db, regulation_data)
        
        # Check that the database operations were called
        self.mock_db.add.assert_called_once()
        self.mock_db.flush.assert_called_once()
        self.mock_db.commit.assert_called_once()
        self.mock_db.refresh.assert_called_once()
    
    def test_get_regulations(self):
        """Test getting regulations."""
        # Mock the database operations
        mock_regulations = [MagicMock(), MagicMock()]
        self.mock_query.offset.return_value.limit.return_value.all.return_value = mock_regulations
        
        # Call the service method
        result = self.service.get_regulations(self.mock_db)
        
        # Check that the database operations were called
        self.mock_db.query.assert_called_once_with(Regulation)
        self.mock_query.offset.assert_called_once_with(0)
        self.mock_query.offset.return_value.limit.assert_called_once_with(100)
        self.mock_query.offset.return_value.limit.return_value.all.assert_called_once()
        
        # Check the result
        self.assertEqual(result, mock_regulations)
    
    def test_get_regulation(self):
        """Test getting a regulation by ID."""
        # Mock the database operations
        mock_regulation = MagicMock()
        self.mock_query.filter.return_value.first.return_value = mock_regulation
        
        # Call the service method
        result = self.service.get_regulation(self.mock_db, 1)
        
        # Check that the database operations were called
        self.mock_db.query.assert_called_once_with(Regulation)
        self.mock_query.filter.assert_called_once()
        self.mock_query.filter.return_value.first.assert_called_once()
        
        # Check the result
        self.assertEqual(result, mock_regulation)
    
    def test_update_regulation(self):
        """Test updating a regulation."""
        # Create an update
        regulation_update = RegulationUpdate(
            title="Updated Regulation",
            description="Updated Description",
            status=RegulationStatus.AMENDED
        )
        
        # Mock the database operations
        mock_regulation = MagicMock()
        self.mock_query.filter.return_value.first.return_value = mock_regulation
        self.mock_db.commit.return_value = None
        self.mock_db.refresh.return_value = None
        
        # Call the service method
        result = self.service.update_regulation(self.mock_db, 1, regulation_update)
        
        # Check that the database operations were called
        self.mock_db.query.assert_called_once_with(Regulation)
        self.mock_query.filter.assert_called_once()
        self.mock_query.filter.return_value.first.assert_called_once()
        self.mock_db.commit.assert_called_once()
        self.mock_db.refresh.assert_called_once()
        
        # Check that the regulation was updated
        self.assertEqual(mock_regulation.title, "Updated Regulation")
        self.assertEqual(mock_regulation.description, "Updated Description")
        self.assertEqual(mock_regulation.status, RegulationStatus.AMENDED)
    
    def test_delete_regulation(self):
        """Test deleting a regulation."""
        # Mock the database operations
        mock_regulation = MagicMock()
        self.mock_query.filter.return_value.first.return_value = mock_regulation
        self.mock_db.delete.return_value = None
        self.mock_db.commit.return_value = None
        
        # Call the service method
        self.service.delete_regulation(self.mock_db, 1)
        
        # Check that the database operations were called
        self.mock_db.query.assert_called_once_with(Regulation)
        self.mock_query.filter.assert_called_once()
        self.mock_query.filter.return_value.first.assert_called_once()
        self.mock_db.delete.assert_called_once_with(mock_regulation)
        self.mock_db.commit.assert_called_once()
    
    def test_create_industry(self):
        """Test creating an industry."""
        # Create an industry
        industry_data = IndustryCreate(
            name="Test Industry",
            description="Test Description"
        )
        
        # Mock the database operations
        mock_industry = MagicMock()
        self.mock_db.add.return_value = None
        self.mock_db.commit.return_value = None
        self.mock_db.refresh.return_value = None
        
        # Call the service method
        result = self.service.create_industry(self.mock_db, industry_data)
        
        # Check that the database operations were called
        self.mock_db.add.assert_called_once()
        self.mock_db.commit.assert_called_once()
        self.mock_db.refresh.assert_called_once()
    
    def test_create_category(self):
        """Test creating a category."""
        # Create a category
        category_data = RegulationCategoryCreate(
            name="Test Category",
            description="Test Description"
        )
        
        # Mock the database operations
        mock_category = MagicMock()
        self.mock_db.add.return_value = None
        self.mock_db.commit.return_value = None
        self.mock_db.refresh.return_value = None
        
        # Call the service method
        result = self.service.create_category(self.mock_db, category_data)
        
        # Check that the database operations were called
        self.mock_db.add.assert_called_once()
        self.mock_db.commit.assert_called_once()
        self.mock_db.refresh.assert_called_once()
    
    def test_create_tag(self):
        """Test creating a tag."""
        # Create a tag
        tag_data = RegulationTagCreate(
            name="Test Tag"
        )
        
        # Mock the database operations
        mock_tag = MagicMock()
        self.mock_db.add.return_value = None
        self.mock_db.commit.return_value = None
        self.mock_db.refresh.return_value = None
        
        # Call the service method
        result = self.service.create_tag(self.mock_db, tag_data)
        
        # Check that the database operations were called
        self.mock_db.add.assert_called_once()
        self.mock_db.commit.assert_called_once()
        self.mock_db.refresh.assert_called_once()
    
    def test_create_requirement(self):
        """Test creating a requirement."""
        # Create a requirement
        requirement_data = ComplianceRequirementCreate(
            regulation_id=1,
            title="Test Requirement",
            description="Test Description",
            priority="medium",
            is_mandatory=True
        )
        
        # Mock the database operations
        mock_requirement = MagicMock()
        self.mock_db.add.return_value = None
        self.mock_db.commit.return_value = None
        self.mock_db.refresh.return_value = None
        
        # Call the service method
        result = self.service.create_requirement(self.mock_db, requirement_data)
        
        # Check that the database operations were called
        self.mock_db.add.assert_called_once()
        self.mock_db.commit.assert_called_once()
        self.mock_db.refresh.assert_called_once()
    
    def test_create_document(self):
        """Test creating a document."""
        # Create a document
        document_data = RegulationDocumentCreate(
            regulation_id=1,
            title="Test Document",
            document_type="regulation_text"
        )
        
        # Mock the database operations
        mock_document = MagicMock()
        self.mock_db.add.return_value = None
        self.mock_db.commit.return_value = None
        self.mock_db.refresh.return_value = None
        
        # Call the service method
        result = self.service.create_document(self.mock_db, document_data)
        
        # Check that the database operations were called
        self.mock_db.add.assert_called_once()
        self.mock_db.commit.assert_called_once()
        self.mock_db.refresh.assert_called_once()
    
    @patch("os.makedirs")
    @patch("os.path.join")
    @patch("uuid.uuid4")
    @patch("builtins.open")
    def test_create_document_with_file(self, mock_open, mock_uuid, mock_join, mock_makedirs):
        """Test creating a document with a file."""
        # Create a document
        document_data = RegulationDocumentCreate(
            regulation_id=1,
            title="Test Document",
            document_type="regulation_text"
        )
        
        # Mock the file operations
        mock_uuid.return_value = "test-uuid"
        mock_join.return_value = "test/path"
        mock_file = MagicMock()
        mock_open.return_value.__enter__.return_value = mock_file
        
        # Mock the database operations
        mock_document = MagicMock()
        self.mock_db.add.return_value = None
        self.mock_db.commit.return_value = None
        self.mock_db.refresh.return_value = None
        
        # Call the service method
        result = self.service.create_document_with_file(
            self.mock_db,
            document_data,
            b"test content",
            "test.pdf"
        )
        
        # Check that the file operations were called
        mock_makedirs.assert_called_once()
        mock_join.assert_called()
        mock_open.assert_called_once_with("test/path", "wb")
        mock_file.write.assert_called_once_with(b"test content")
        
        # Check that the database operations were called
        self.mock_db.add.assert_called_once()
        self.mock_db.commit.assert_called_once()
        self.mock_db.refresh.assert_called_once()
    
    def test_create_change_log(self):
        """Test creating a change log."""
        # Create a change log
        change_log_data = RegulationChangeLogCreate(
            regulation_id=1,
            change_type="update",
            description="Test Description",
            changed_by="<EMAIL>"
        )
        
        # Mock the database operations
        mock_change_log = MagicMock()
        self.mock_db.add.return_value = None
        self.mock_db.commit.return_value = None
        self.mock_db.refresh.return_value = None
        
        # Call the service method
        result = self.service.create_change_log(self.mock_db, change_log_data)
        
        # Check that the database operations were called
        self.mock_db.add.assert_called_once()
        self.mock_db.commit.assert_called_once()
        self.mock_db.refresh.assert_called_once()


if __name__ == "__main__":
    unittest.main()
