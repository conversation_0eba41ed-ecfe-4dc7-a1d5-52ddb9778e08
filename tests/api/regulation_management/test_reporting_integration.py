import pytest
import json
import os
import sys
from fastapi.testclient import TestClient

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../'))

from app.main import app

# Create a test client
client = TestClient(app)

# Integration tests for the reporting API endpoints
class TestReportingIntegration:
    
    def test_summary_endpoint(self):
        """Test the summary reporting endpoint."""
        response = client.get("/api/v1/regulations/management/reporting/summary")
        assert response.status_code == 200
        data = response.json()
        
        # Verify the structure of the response
        assert "total_regulations" in data
        assert "status_counts" in data
        assert "upcoming_deadlines" in data
        assert "high_priority_requirements" in data
        assert "verified_regulations" in data
        assert "verification_percentage" in data
        assert "average_confidence" in data
    
    def test_jurisdictions_endpoint(self):
        """Test the jurisdictions reporting endpoint."""
        response = client.get("/api/v1/regulations/management/reporting/jurisdictions")
        assert response.status_code == 200
        data = response.json()
        
        # Verify the structure of the response
        assert "jurisdictions" in data
        
        # Check at least one jurisdiction
        assert len(data["jurisdictions"]) > 0
        
        # Check the structure of a jurisdiction
        first_jurisdiction = list(data["jurisdictions"].values())[0]
        assert "status_counts" in first_jurisdiction
        assert "upcoming_deadlines" in first_jurisdiction
        assert "total_requirements" in first_jurisdiction
    
    def test_industries_endpoint(self):
        """Test the industries reporting endpoint."""
        response = client.get("/api/v1/regulations/management/reporting/industries")
        assert response.status_code == 200
        data = response.json()
        
        # Verify the structure of the response
        assert "industries" in data
        
        # Check at least one industry
        assert len(data["industries"]) > 0
        
        # Check the structure of an industry
        first_industry = list(data["industries"].values())[0]
        assert "regulation_count" in first_industry
        assert "upcoming_deadlines" in first_industry
        assert "high_priority_requirements" in first_industry
    
    def test_timeline_endpoint(self):
        """Test the timeline reporting endpoint."""
        response = client.get("/api/v1/regulations/management/reporting/timeline", params={"days": 365})
        assert response.status_code == 200
        data = response.json()
        
        # Verify the structure of the response
        assert "deadlines_by_month" in data
        
        # Check the structure of the deadlines
        if len(data["deadlines_by_month"]) > 0:
            first_month = list(data["deadlines_by_month"].keys())[0]
            first_month_deadlines = data["deadlines_by_month"][first_month]
            
            # Check at least one deadline in the month
            if len(first_month_deadlines) > 0:
                first_deadline = first_month_deadlines[0]
                assert "id" in first_deadline
                assert "title" in first_deadline
                assert "compliance_deadline" in first_deadline
                assert "jurisdiction" in first_deadline
                assert "status" in first_deadline
    
    def test_requirements_endpoint(self):
        """Test the requirements reporting endpoint."""
        response = client.get("/api/v1/regulations/management/reporting/requirements")
        assert response.status_code == 200
        data = response.json()
        
        # Verify the structure of the response
        assert "requirements_by_priority" in data
        
        # Check the priorities
        priorities = ["critical", "high", "medium", "low"]
        for priority in priorities:
            if priority in data["requirements_by_priority"] and len(data["requirements_by_priority"][priority]) > 0:
                first_requirement = data["requirements_by_priority"][priority][0]
                assert "id" in first_requirement
                assert "title" in first_requirement
                assert "regulation" in first_requirement
    
    def test_export_endpoint(self):
        """Test the export reporting endpoint."""
        # Test exporting summary report as JSON
        response = client.get("/api/v1/regulations/management/reporting/export", 
                             params={"report_type": "summary", "format": "json"})
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/json"
        
        # Test exporting jurisdictions report as XLSX
        response = client.get("/api/v1/regulations/management/reporting/export", 
                             params={"report_type": "jurisdictions", "format": "xlsx"})
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        
        # Test exporting timeline report as PDF
        response = client.get("/api/v1/regulations/management/reporting/export", 
                             params={"report_type": "timeline", "format": "pdf"})
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/pdf"
