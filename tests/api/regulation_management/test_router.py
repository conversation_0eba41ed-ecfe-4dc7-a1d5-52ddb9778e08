"""
Tests for the regulation management API router.
"""
import unittest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.db.models import (
    Regulation, Industry, RegulationCategory, RegulationTag,
    ComplianceRequirement, RegulationDocument, RegulationChangeLog
)
from app.schemas.regulation import RegulationCreate, RegulationStatus

client = TestClient(app)

class TestRegulationManagementRouter(unittest.TestCase):
    """Tests for the regulation management API router."""
    
    @patch("app.api.regulation_management.router.regulation_service")
    def test_create_regulation(self, mock_service):
        """Test creating a regulation."""
        # Mock the service response
        mock_regulation = Regulation(
            id=1,
            title="Test Regulation",
            description="Test Description",
            status=RegulationStatus.ENACTED,
            confidence_level=0.0,
            is_verified=False,
            created_at="2023-01-01T00:00:00",
            changed_on="2023-01-01T00:00:00"
        )
        mock_service.create_regulation.return_value = mock_regulation
        
        # Make the request
        response = client.post(
            "/api/v1/regulations/management/regulations",
            json={
                "title": "Test Regulation",
                "description": "Test Description",
                "status": "enacted"
            }
        )
        
        # Check the response
        self.assertEqual(response.status_code, 201)
        data = response.json()
        self.assertEqual(data["title"], "Test Regulation")
        self.assertEqual(data["description"], "Test Description")
        self.assertEqual(data["status"], "enacted")
        
        # Check that the service was called
        mock_service.create_regulation.assert_called_once()
    
    @patch("app.api.regulation_management.router.regulation_service")
    def test_get_regulations(self, mock_service):
        """Test getting regulations."""
        # Mock the service response
        mock_regulations = [
            Regulation(
                id=1,
                title="Test Regulation 1",
                description="Test Description 1",
                status=RegulationStatus.ENACTED,
                confidence_level=0.0,
                is_verified=False,
                created_at="2023-01-01T00:00:00",
                changed_on="2023-01-01T00:00:00"
            ),
            Regulation(
                id=2,
                title="Test Regulation 2",
                description="Test Description 2",
                status=RegulationStatus.PROPOSED,
                confidence_level=0.0,
                is_verified=False,
                created_at="2023-01-01T00:00:00",
                changed_on="2023-01-01T00:00:00"
            )
        ]
        mock_service.get_regulations.return_value = mock_regulations
        
        # Make the request
        response = client.get("/api/v1/regulations/management/regulations")
        
        # Check the response
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(len(data), 2)
        self.assertEqual(data[0]["title"], "Test Regulation 1")
        self.assertEqual(data[1]["title"], "Test Regulation 2")
        
        # Check that the service was called
        mock_service.get_regulations.assert_called_once_with(
            unittest.mock.ANY,  # db
            skip=0,
            limit=100,
            status=None,
            jurisdiction=None,
            regulator_id=None,
            country_id=None,
            industry_id=None,
            category_id=None,
            tag_id=None,
            search=None
        )
    
    @patch("app.api.regulation_management.router.regulation_service")
    def test_get_regulation(self, mock_service):
        """Test getting a regulation by ID."""
        # Mock the service response
        mock_regulation = Regulation(
            id=1,
            title="Test Regulation",
            description="Test Description",
            status=RegulationStatus.ENACTED,
            confidence_level=0.0,
            is_verified=False,
            created_at="2023-01-01T00:00:00",
            changed_on="2023-01-01T00:00:00",
            industries=[],
            categories=[],
            tags=[],
            requirements=[],
            documents=[]
        )
        mock_service.get_regulation.return_value = mock_regulation
        
        # Make the request
        response = client.get("/api/v1/regulations/management/regulations/1")
        
        # Check the response
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data["title"], "Test Regulation")
        self.assertEqual(data["description"], "Test Description")
        
        # Check that the service was called
        mock_service.get_regulation.assert_called_once_with(unittest.mock.ANY, 1)
    
    @patch("app.api.regulation_management.router.regulation_service")
    def test_get_regulation_not_found(self, mock_service):
        """Test getting a regulation that doesn't exist."""
        # Mock the service response
        mock_service.get_regulation.return_value = None
        
        # Make the request
        response = client.get("/api/v1/regulations/management/regulations/999")
        
        # Check the response
        self.assertEqual(response.status_code, 404)
        data = response.json()
        self.assertEqual(data["detail"], "Regulation not found")
        
        # Check that the service was called
        mock_service.get_regulation.assert_called_once_with(unittest.mock.ANY, 999)
    
    @patch("app.api.regulation_management.router.regulation_service")
    def test_update_regulation(self, mock_service):
        """Test updating a regulation."""
        # Mock the service responses
        mock_regulation = Regulation(
            id=1,
            title="Test Regulation",
            description="Test Description",
            status=RegulationStatus.ENACTED,
            confidence_level=0.0,
            is_verified=False,
            created_at="2023-01-01T00:00:00",
            changed_on="2023-01-01T00:00:00"
        )
        mock_service.get_regulation.return_value = mock_regulation
        
        updated_regulation = Regulation(
            id=1,
            title="Updated Regulation",
            description="Updated Description",
            status=RegulationStatus.AMENDED,
            confidence_level=0.0,
            is_verified=True,
            created_at="2023-01-01T00:00:00",
            changed_on="2023-01-02T00:00:00"
        )
        mock_service.update_regulation.return_value = updated_regulation
        
        # Make the request
        response = client.put(
            "/api/v1/regulations/management/regulations/1",
            json={
                "title": "Updated Regulation",
                "description": "Updated Description",
                "status": "amended",
                "is_verified": True
            }
        )
        
        # Check the response
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data["title"], "Updated Regulation")
        self.assertEqual(data["description"], "Updated Description")
        self.assertEqual(data["status"], "amended")
        self.assertEqual(data["is_verified"], True)
        
        # Check that the service was called
        mock_service.get_regulation.assert_called_once_with(unittest.mock.ANY, 1)
        mock_service.update_regulation.assert_called_once()
        mock_service.create_change_log.assert_called_once()
    
    @patch("app.api.regulation_management.router.regulation_service")
    def test_delete_regulation(self, mock_service):
        """Test deleting a regulation."""
        # Mock the service response
        mock_regulation = Regulation(
            id=1,
            title="Test Regulation",
            description="Test Description",
            status=RegulationStatus.ENACTED,
            confidence_level=0.0,
            is_verified=False,
            created_at="2023-01-01T00:00:00",
            changed_on="2023-01-01T00:00:00"
        )
        mock_service.get_regulation.return_value = mock_regulation
        
        # Make the request
        response = client.delete("/api/v1/regulations/management/regulations/1")
        
        # Check the response
        self.assertEqual(response.status_code, 204)
        
        # Check that the service was called
        mock_service.get_regulation.assert_called_once_with(unittest.mock.ANY, 1)
        mock_service.delete_regulation.assert_called_once_with(unittest.mock.ANY, 1)
    
    @patch("app.api.regulation_management.router.gemini_enrichment_service")
    @patch("app.api.regulation_management.router.regulation_service")
    def test_enrich_regulation(self, mock_service, mock_enrichment_service):
        """Test enriching a regulation."""
        # Mock the service responses
        mock_regulation = Regulation(
            id=1,
            title="Test Regulation",
            description="Test Description",
            status=RegulationStatus.ENACTED,
            confidence_level=0.0,
            is_verified=False,
            created_at="2023-01-01T00:00:00",
            changed_on="2023-01-01T00:00:00"
        )
        mock_service.get_regulation.return_value = mock_regulation
        
        mock_enrichment_result = {
            "summary": "This is a test summary.",
            "confidence": 0.9
        }
        mock_enrichment_service.enrich_regulation.return_value = mock_enrichment_result
        
        # Make the request
        response = client.post(
            "/api/v1/regulations/management/enrich",
            json={
                "regulation_id": 1,
                "enrichment_type": "summary"
            }
        )
        
        # Check the response
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data["regulation_id"], 1)
        self.assertEqual(data["enrichment_type"], "summary")
        self.assertEqual(data["result"]["summary"], "This is a test summary.")
        self.assertEqual(data["result"]["confidence"], 0.9)
        
        # Check that the services were called
        mock_service.get_regulation.assert_called_once_with(unittest.mock.ANY, 1)
        mock_enrichment_service.enrich_regulation.assert_called_once_with(unittest.mock.ANY, 1, "summary")


if __name__ == "__main__":
    unittest.main()
