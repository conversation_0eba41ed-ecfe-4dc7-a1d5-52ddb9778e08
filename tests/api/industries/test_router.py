"""
Tests for the industry management API router.
"""
import unittest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.main import app
from app.db.models import Industry, Regulation
from app.schemas.schemas import IndustryCreate, IndustryUpdate

client = TestClient(app)

class TestIndustryRouter(unittest.TestCase):
    """Tests for the industry management API router."""

    def setUp(self):
        """Set up test data."""
        self.test_industry_data = {
            "name": "Banking",
            "description": "Financial institutions that provide banking services",
            "sector": "Financial Services"
        }
        
        self.test_industry_update_data = {
            "name": "Updated: Banking",
            "description": "Updated description for banking industry",
            "sector": "Financial"
        }

    @patch('app.api.industries.router.get_db')
    def test_get_industries(self, mock_get_db):
        """Test getting a list of industries."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock query result
        mock_industry = MagicMock()
        mock_industry.id = 1
        mock_industry.name = "Banking"
        mock_industry.description = "Financial institutions that provide banking services"
        mock_industry.sector = "Financial Services"
        mock_industry.created_at = "2023-01-01T00:00:00"
        mock_industry.changed_on = "2023-01-01T00:00:00"
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [mock_industry]
        
        # Make request
        response = client.get("/api/v1/industries/")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response.json(), list)
        self.assertEqual(len(response.json()), 1)
        self.assertEqual(response.json()[0]["name"], "Banking")
        
        # Verify query was called correctly
        mock_db.query.assert_called_once_with(Industry)
        mock_query.offset.assert_called_once_with(0)
        mock_query.limit.assert_called_once_with(100)

    @patch('app.api.industries.router.get_db')
    def test_get_industries_with_search(self, mock_get_db):
        """Test getting industries with search filter."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = []
        
        # Make request with search
        response = client.get("/api/v1/industries/?search=banking")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        
        # Verify filter was called
        mock_query.filter.assert_called_once()

    @patch('app.api.industries.router.get_db')
    def test_get_industry(self, mock_get_db):
        """Test getting a specific industry by ID."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock query result
        mock_industry = MagicMock()
        mock_industry.id = 1
        mock_industry.name = "Banking"
        mock_industry.description = "Financial institutions that provide banking services"
        mock_industry.sector = "Financial Services"
        mock_industry.created_at = "2023-01-01T00:00:00"
        mock_industry.changed_on = "2023-01-01T00:00:00"
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_industry
        
        # Make request
        response = client.get("/api/v1/industries/1")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["name"], "Banking")
        
        # Verify query was called correctly
        mock_db.query.assert_called_once_with(Industry)
        mock_query.filter.assert_called_once()
        mock_query.first.assert_called_once()

    @patch('app.api.industries.router.get_db')
    def test_get_industry_not_found(self, mock_get_db):
        """Test getting a non-existent industry."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain to return None
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        
        # Make request
        response = client.get("/api/v1/industries/999")
        
        # Check response
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.json()["detail"], "Industry not found")

    @patch('app.api.industries.router.get_db')
    def test_create_industry(self, mock_get_db):
        """Test creating a new industry."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain to check for existing industry
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None  # No existing industry with same name
        
        # Create mock industry object that will be returned
        mock_industry = MagicMock()
        mock_industry.id = 1
        mock_industry.name = self.test_industry_data["name"]
        mock_industry.description = self.test_industry_data["description"]
        mock_industry.sector = self.test_industry_data["sector"]
        mock_industry.created_at = "2023-01-01T00:00:00"
        mock_industry.changed_on = "2023-01-01T00:00:00"
        
        # Mock the add, commit, and refresh methods
        mock_db.add = MagicMock()
        mock_db.commit = MagicMock()
        mock_db.refresh = MagicMock()
        
        # Make the Industry constructor return our mock
        with patch('app.api.industries.router.Industry', return_value=mock_industry):
            # Make request
            response = client.post("/api/v1/industries/", json=self.test_industry_data)
            
            # Check response
            self.assertEqual(response.status_code, 201)
            self.assertEqual(response.json()["name"], self.test_industry_data["name"])
            
            # Verify methods were called
            mock_db.add.assert_called_once()
            mock_db.commit.assert_called_once()
            mock_db.refresh.assert_called_once()

    @patch('app.api.industries.router.get_db')
    def test_create_industry_duplicate_name(self, mock_get_db):
        """Test creating an industry with a duplicate name."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain to return an existing industry
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        
        # Create mock existing industry
        mock_existing_industry = MagicMock()
        mock_existing_industry.id = 1
        mock_existing_industry.name = self.test_industry_data["name"]
        
        mock_query.first.return_value = mock_existing_industry
        
        # Make request
        response = client.post("/api/v1/industries/", json=self.test_industry_data)
        
        # Check response
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.json()["detail"], "Industry with this name already exists")

    @patch('app.api.industries.router.get_db')
    def test_update_industry(self, mock_get_db):
        """Test updating an industry."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock industry to update
        mock_industry = MagicMock()
        mock_industry.id = 1
        mock_industry.name = "Banking"
        mock_industry.description = "Original description"
        mock_industry.sector = "Financial Services"
        
        # Set up the mock query chain for finding the industry
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.side_effect = [mock_industry, None]  # First call returns the industry, second call (checking for duplicate name) returns None
        
        # Make request
        response = client.put("/api/v1/industries/1", json=self.test_industry_update_data)
        
        # Check response
        self.assertEqual(response.status_code, 200)
        
        # Verify setattr was called for each field
        self.assertEqual(mock_industry.name, self.test_industry_update_data["name"])
        self.assertEqual(mock_industry.description, self.test_industry_update_data["description"])
        self.assertEqual(mock_industry.sector, self.test_industry_update_data["sector"])
        
        # Verify commit and refresh were called
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()

    @patch('app.api.industries.router.get_db')
    def test_update_industry_duplicate_name(self, mock_get_db):
        """Test updating an industry with a duplicate name."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock industry to update
        mock_industry = MagicMock()
        mock_industry.id = 1
        mock_industry.name = "Banking"
        
        # Create mock existing industry with the name we want to update to
        mock_existing_industry = MagicMock()
        mock_existing_industry.id = 2
        mock_existing_industry.name = self.test_industry_update_data["name"]
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.side_effect = [mock_industry, mock_existing_industry]  # First call returns the industry, second call returns the existing industry with duplicate name
        
        # Make request
        response = client.put("/api/v1/industries/1", json=self.test_industry_update_data)
        
        # Check response
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.json()["detail"], "Industry with this name already exists")

    @patch('app.api.industries.router.get_db')
    def test_delete_industry(self, mock_get_db):
        """Test deleting an industry."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock industry to delete
        mock_industry = MagicMock()
        mock_industry.id = 1
        mock_industry.name = "Banking"
        mock_industry.regulations = []  # No associated regulations
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_industry
        
        # Make request
        response = client.delete("/api/v1/industries/1")
        
        # Check response
        self.assertEqual(response.status_code, 204)
        
        # Verify delete, commit were called
        mock_db.delete.assert_called_once_with(mock_industry)
        mock_db.commit.assert_called_once()

    @patch('app.api.industries.router.get_db')
    def test_delete_industry_with_regulations(self, mock_get_db):
        """Test deleting an industry that has associated regulations."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock industry with associated regulations
        mock_industry = MagicMock()
        mock_industry.id = 1
        mock_industry.name = "Banking"
        mock_industry.regulations = [MagicMock(), MagicMock()]  # Two associated regulations
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_industry
        
        # Make request
        response = client.delete("/api/v1/industries/1")
        
        # Check response
        self.assertEqual(response.status_code, 400)
        self.assertIn("Cannot delete industry that is associated with", response.json()["detail"])
        
        # Verify delete was not called
        mock_db.delete.assert_not_called()

    @patch('app.api.industries.router.get_db')
    def test_get_regulations_by_industry(self, mock_get_db):
        """Test getting regulations associated with an industry."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock industry
        mock_industry = MagicMock()
        mock_industry.id = 1
        mock_industry.name = "Banking"
        
        # Create mock regulations
        mock_regulation1 = MagicMock()
        mock_regulation1.id = 1
        mock_regulation1.title = "Regulation 1"
        mock_regulation1.status = "Active"
        mock_regulation1.reference_number = "REG-001"
        
        mock_regulation2 = MagicMock()
        mock_regulation2.id = 2
        mock_regulation2.title = "Regulation 2"
        mock_regulation2.status = "Draft"
        mock_regulation2.reference_number = "REG-002"
        
        # Set up the industry's regulations
        mock_industry.regulations = [mock_regulation1, mock_regulation2]
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_industry
        
        # Make request
        response = client.get("/api/v1/industries/1/regulations")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response.json(), list)
        self.assertEqual(len(response.json()), 2)
        self.assertEqual(response.json()[0]["title"], "Regulation 1")
        self.assertEqual(response.json()[1]["title"], "Regulation 2")


if __name__ == "__main__":
    unittest.main()
