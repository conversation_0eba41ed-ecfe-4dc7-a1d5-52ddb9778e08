"""
Tests for the compliance requirement management API router.
"""
import unittest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.main import app
from app.db.models import ComplianceRequirement, Regulation
from app.schemas.schemas import ComplianceRequirementCreate, ComplianceRequirementUpdate

client = TestClient(app)

class TestComplianceRequirementRouter(unittest.TestCase):
    """Tests for the compliance requirement management API router."""

    def setUp(self):
        """Set up test data."""
        self.test_requirement_data = {
            "text": "Implement data protection measures for personal information",
            "section": "Article 32",
            "priority": "High",
            "status": "In Progress",
            "notes": "Need to implement encryption and access controls",
            "regulation_id": 1
        }
        
        self.test_requirement_update_data = {
            "text": "Updated: Implement data protection measures for personal information",
            "status": "Compliant",
            "notes": "Encryption and access controls implemented on 2023-10-15"
        }

    @patch('app.api.compliance_requirements.router.get_db')
    def test_get_compliance_requirements(self, mock_get_db):
        """Test getting a list of compliance requirements."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock query result
        mock_requirement = MagicMock()
        mock_requirement.id = 1
        mock_requirement.text = "Implement data protection measures"
        mock_requirement.section = "Article 32"
        mock_requirement.priority = "High"
        mock_requirement.status = "In Progress"
        mock_requirement.notes = "Test notes"
        mock_requirement.regulation_id = 1
        mock_requirement.created_at = "2023-01-01T00:00:00"
        mock_requirement.changed_on = "2023-01-01T00:00:00"
        
        # Create mock regulation
        mock_regulation = MagicMock()
        mock_regulation.id = 1
        mock_regulation.title = "Test Regulation"
        
        # Set up the relationship
        mock_requirement.regulation = mock_regulation
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [mock_requirement]
        
        # Make request
        response = client.get("/api/v1/compliance-requirements/")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response.json(), list)
        self.assertEqual(len(response.json()), 1)
        self.assertEqual(response.json()[0]["text"], "Implement data protection measures")
        self.assertEqual(response.json()[0]["regulation_id"], 1)
        
        # Verify query was called correctly
        mock_db.query.assert_called_once_with(ComplianceRequirement)
        mock_query.offset.assert_called_once_with(0)
        mock_query.limit.assert_called_once_with(100)

    @patch('app.api.compliance_requirements.router.get_db')
    def test_get_compliance_requirements_with_filters(self, mock_get_db):
        """Test getting compliance requirements with filters."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = []
        
        # Make request with filters
        response = client.get("/api/v1/compliance-requirements/?regulation_id=1&priority=High")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        
        # Verify filter was called twice (once for each filter)
        self.assertEqual(mock_query.filter.call_count, 2)

    @patch('app.api.compliance_requirements.router.get_db')
    def test_get_compliance_requirement(self, mock_get_db):
        """Test getting a specific compliance requirement by ID."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock query result
        mock_requirement = MagicMock()
        mock_requirement.id = 1
        mock_requirement.text = "Implement data protection measures"
        mock_requirement.section = "Article 32"
        mock_requirement.priority = "High"
        mock_requirement.status = "In Progress"
        mock_requirement.notes = "Test notes"
        mock_requirement.regulation_id = 1
        mock_requirement.created_at = "2023-01-01T00:00:00"
        mock_requirement.changed_on = "2023-01-01T00:00:00"
        
        # Create mock regulation
        mock_regulation = MagicMock()
        mock_regulation.id = 1
        mock_regulation.title = "Test Regulation"
        
        # Set up the relationship
        mock_requirement.regulation = mock_regulation
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_requirement
        
        # Make request
        response = client.get("/api/v1/compliance-requirements/1")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["text"], "Implement data protection measures")
        self.assertEqual(response.json()["regulation_id"], 1)
        
        # Verify query was called correctly
        mock_db.query.assert_called_once_with(ComplianceRequirement)
        mock_query.filter.assert_called_once()
        mock_query.first.assert_called_once()

    @patch('app.api.compliance_requirements.router.get_db')
    def test_get_compliance_requirement_not_found(self, mock_get_db):
        """Test getting a non-existent compliance requirement."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain to return None
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        
        # Make request
        response = client.get("/api/v1/compliance-requirements/999")
        
        # Check response
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.json()["detail"], "Compliance requirement not found")

    @patch('app.api.compliance_requirements.router.get_db')
    def test_create_compliance_requirement(self, mock_get_db):
        """Test creating a new compliance requirement."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Mock the regulation query
        mock_regulation = MagicMock()
        mock_regulation.id = 1
        
        # Set up the mock query chain for checking existing regulation
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_regulation
        
        # Create mock requirement object that will be returned
        mock_requirement = MagicMock()
        mock_requirement.id = 1
        mock_requirement.text = self.test_requirement_data["text"]
        mock_requirement.section = self.test_requirement_data["section"]
        mock_requirement.priority = self.test_requirement_data["priority"]
        mock_requirement.status = self.test_requirement_data["status"]
        mock_requirement.notes = self.test_requirement_data["notes"]
        mock_requirement.regulation_id = self.test_requirement_data["regulation_id"]
        mock_requirement.created_at = "2023-01-01T00:00:00"
        mock_requirement.changed_on = "2023-01-01T00:00:00"
        mock_requirement.regulation = mock_regulation
        
        # Mock the add, commit, and refresh methods
        mock_db.add = MagicMock()
        mock_db.commit = MagicMock()
        mock_db.refresh = MagicMock()
        
        # Make the ComplianceRequirement constructor return our mock
        with patch('app.api.compliance_requirements.router.ComplianceRequirement', return_value=mock_requirement):
            # Make request
            response = client.post("/api/v1/compliance-requirements/", json=self.test_requirement_data)
            
            # Check response
            self.assertEqual(response.status_code, 201)
            self.assertEqual(response.json()["text"], self.test_requirement_data["text"])
            self.assertEqual(response.json()["regulation_id"], self.test_requirement_data["regulation_id"])
            
            # Verify methods were called
            mock_db.add.assert_called_once()
            mock_db.commit.assert_called_once()
            mock_db.refresh.assert_called_once()

    @patch('app.api.compliance_requirements.router.get_db')
    def test_create_compliance_requirement_regulation_not_found(self, mock_get_db):
        """Test creating a compliance requirement with non-existent regulation."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain to return None (regulation not found)
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        
        # Make request
        response = client.post("/api/v1/compliance-requirements/", json=self.test_requirement_data)
        
        # Check response
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.json()["detail"], "Regulation not found")

    @patch('app.api.compliance_requirements.router.get_db')
    def test_update_compliance_requirement(self, mock_get_db):
        """Test updating a compliance requirement."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock requirement to update
        mock_requirement = MagicMock()
        mock_requirement.id = 1
        mock_requirement.text = "Implement data protection measures"
        mock_requirement.status = "In Progress"
        mock_requirement.notes = "Original notes"
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_requirement
        
        # Make request
        response = client.put("/api/v1/compliance-requirements/1", json=self.test_requirement_update_data)
        
        # Check response
        self.assertEqual(response.status_code, 200)
        
        # Verify setattr was called for each field
        self.assertEqual(mock_requirement.text, self.test_requirement_update_data["text"])
        self.assertEqual(mock_requirement.status, self.test_requirement_update_data["status"])
        self.assertEqual(mock_requirement.notes, self.test_requirement_update_data["notes"])
        
        # Verify commit and refresh were called
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()

    @patch('app.api.compliance_requirements.router.get_db')
    def test_update_compliance_requirement_not_found(self, mock_get_db):
        """Test updating a non-existent compliance requirement."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain to return None
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        
        # Make request
        response = client.put("/api/v1/compliance-requirements/999", json=self.test_requirement_update_data)
        
        # Check response
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.json()["detail"], "Compliance requirement not found")

    @patch('app.api.compliance_requirements.router.get_db')
    def test_delete_compliance_requirement(self, mock_get_db):
        """Test deleting a compliance requirement."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock requirement to delete
        mock_requirement = MagicMock()
        mock_requirement.id = 1
        mock_requirement.text = "Implement data protection measures"
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_requirement
        
        # Make request
        response = client.delete("/api/v1/compliance-requirements/1")
        
        # Check response
        self.assertEqual(response.status_code, 204)
        
        # Verify delete, commit were called
        mock_db.delete.assert_called_once_with(mock_requirement)
        mock_db.commit.assert_called_once()

    @patch('app.api.compliance_requirements.router.get_db')
    def test_delete_compliance_requirement_not_found(self, mock_get_db):
        """Test deleting a non-existent compliance requirement."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain to return None
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        
        # Make request
        response = client.delete("/api/v1/compliance-requirements/999")
        
        # Check response
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.json()["detail"], "Compliance requirement not found")


if __name__ == "__main__":
    unittest.main()
