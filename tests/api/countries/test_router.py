"""
Tests for the country management API router.
"""
import unittest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.main import app
from app.db.models import Country
from app.schemas.schemas import CountryCreate, CountryUpdate

client = TestClient(app)

class TestCountryRouter(unittest.TestCase):
    """Tests for the country management API router."""

    def setUp(self):
        """Set up test data."""
        self.test_country_data = {
            "name": "Test Country",
            "code": "TC",
            "region": "Test Region",
            "subregion": "Test Subregion",
            "flag_emoji": "🏳️"
        }
        
        self.test_country_update_data = {
            "name": "Updated Test Country",
            "region": "Updated Region"
        }

    @patch('app.api.countries.router.get_db')
    def test_get_countries(self, mock_get_db):
        """Test getting a list of countries."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock query result
        mock_country = MagicMock()
        mock_country.id = 1
        mock_country.name = "Test Country"
        mock_country.code = "TC"
        mock_country.region = "Test Region"
        mock_country.created_at = "2023-01-01T00:00:00"
        mock_country.changed_on = "2023-01-01T00:00:00"
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [mock_country]
        
        # Make request
        response = client.get("/api/v1/countries/")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response.json(), list)
        self.assertEqual(len(response.json()), 1)
        self.assertEqual(response.json()[0]["name"], "Test Country")
        self.assertEqual(response.json()[0]["code"], "TC")
        
        # Verify query was called correctly
        mock_db.query.assert_called_once_with(Country)
        mock_query.offset.assert_called_once_with(0)
        mock_query.limit.assert_called_once_with(100)

    @patch('app.api.countries.router.get_db')
    def test_get_countries_with_search(self, mock_get_db):
        """Test getting countries with search filter."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = []
        
        # Make request with search parameter
        response = client.get("/api/v1/countries/?search=Test")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        
        # Verify filter was called
        mock_query.filter.assert_called_once()

    @patch('app.api.countries.router.get_db')
    def test_get_country(self, mock_get_db):
        """Test getting a specific country by ID."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock query result
        mock_country = MagicMock()
        mock_country.id = 1
        mock_country.name = "Test Country"
        mock_country.code = "TC"
        mock_country.region = "Test Region"
        mock_country.created_at = "2023-01-01T00:00:00"
        mock_country.changed_on = "2023-01-01T00:00:00"
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_country
        
        # Make request
        response = client.get("/api/v1/countries/1")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["name"], "Test Country")
        self.assertEqual(response.json()["code"], "TC")
        
        # Verify query was called correctly
        mock_db.query.assert_called_once_with(Country)
        mock_query.filter.assert_called_once()
        mock_query.first.assert_called_once()

    @patch('app.api.countries.router.get_db')
    def test_get_country_not_found(self, mock_get_db):
        """Test getting a non-existent country."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain to return None
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        
        # Make request
        response = client.get("/api/v1/countries/999")
        
        # Check response
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.json()["detail"], "Country not found")

    @patch('app.api.countries.router.get_db')
    def test_create_country(self, mock_get_db):
        """Test creating a new country."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain for checking existing country
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        
        # Create mock country object that will be returned
        mock_country = MagicMock()
        mock_country.id = 1
        mock_country.name = self.test_country_data["name"]
        mock_country.code = self.test_country_data["code"]
        mock_country.region = self.test_country_data["region"]
        mock_country.subregion = self.test_country_data["subregion"]
        mock_country.flag_emoji = self.test_country_data["flag_emoji"]
        mock_country.created_at = "2023-01-01T00:00:00"
        mock_country.changed_on = "2023-01-01T00:00:00"
        
        # Mock the add, commit, and refresh methods
        mock_db.add = MagicMock()
        mock_db.commit = MagicMock()
        mock_db.refresh = MagicMock()
        
        # Make the Country constructor return our mock
        with patch('app.api.countries.router.Country', return_value=mock_country):
            # Make request
            response = client.post("/api/v1/countries/", json=self.test_country_data)
            
            # Check response
            self.assertEqual(response.status_code, 201)
            self.assertEqual(response.json()["name"], self.test_country_data["name"])
            self.assertEqual(response.json()["code"], self.test_country_data["code"])
            
            # Verify methods were called
            mock_db.add.assert_called_once()
            mock_db.commit.assert_called_once()
            mock_db.refresh.assert_called_once()

    @patch('app.api.countries.router.get_db')
    def test_create_country_duplicate(self, mock_get_db):
        """Test creating a country with a duplicate code."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain to return an existing country
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        
        # Create mock existing country
        mock_existing_country = MagicMock()
        mock_existing_country.id = 1
        mock_existing_country.name = "Existing Country"
        mock_existing_country.code = self.test_country_data["code"]
        
        mock_query.first.return_value = mock_existing_country
        
        # Make request
        response = client.post("/api/v1/countries/", json=self.test_country_data)
        
        # Check response
        self.assertEqual(response.status_code, 400)
        self.assertIn("already exists", response.json()["detail"])

    @patch('app.api.countries.router.get_db')
    def test_update_country(self, mock_get_db):
        """Test updating a country."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock country to update
        mock_country = MagicMock()
        mock_country.id = 1
        mock_country.name = "Test Country"
        mock_country.code = "TC"
        mock_country.region = "Test Region"
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_country
        
        # Make request
        response = client.put("/api/v1/countries/1", json=self.test_country_update_data)
        
        # Check response
        self.assertEqual(response.status_code, 200)
        
        # Verify setattr was called for each field
        self.assertEqual(mock_country.name, self.test_country_update_data["name"])
        self.assertEqual(mock_country.region, self.test_country_update_data["region"])
        
        # Verify commit and refresh were called
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()

    @patch('app.api.countries.router.get_db')
    def test_update_country_not_found(self, mock_get_db):
        """Test updating a non-existent country."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain to return None
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        
        # Make request
        response = client.put("/api/v1/countries/999", json=self.test_country_update_data)
        
        # Check response
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.json()["detail"], "Country not found")

    @patch('app.api.countries.router.get_db')
    def test_delete_country(self, mock_get_db):
        """Test deleting a country."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock country to delete
        mock_country = MagicMock()
        mock_country.id = 1
        mock_country.name = "Test Country"
        mock_country.code = "TC"
        mock_country.regulators = []
        mock_country.regulation_urls = []
        mock_country.regulatory_sources = []
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_country
        
        # Make request
        response = client.delete("/api/v1/countries/1")
        
        # Check response
        self.assertEqual(response.status_code, 204)
        
        # Verify delete, commit were called
        mock_db.delete.assert_called_once_with(mock_country)
        mock_db.commit.assert_called_once()

    @patch('app.api.countries.router.get_db')
    def test_delete_country_with_relations(self, mock_get_db):
        """Test deleting a country that has related records."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock country with related records
        mock_country = MagicMock()
        mock_country.id = 1
        mock_country.name = "Test Country"
        mock_country.code = "TC"
        mock_country.regulators = [MagicMock()]  # Non-empty list
        mock_country.regulation_urls = []
        mock_country.regulatory_sources = []
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_country
        
        # Make request
        response = client.delete("/api/v1/countries/1")
        
        # Check response
        self.assertEqual(response.status_code, 400)
        self.assertIn("Cannot delete country with related records", response.json()["detail"])
        
        # Verify delete was not called
        mock_db.delete.assert_not_called()


if __name__ == "__main__":
    unittest.main()
