"""
Tests for the regulation category management API router.
"""
import unittest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.main import app
from app.db.models import RegulationCategory, Regulation
from app.schemas.schemas import RegulationCategoryCreate, RegulationCategoryUpdate

client = TestClient(app)

class TestRegulationCategoryRouter(unittest.TestCase):
    """Tests for the regulation category management API router."""

    def setUp(self):
        """Set up test data."""
        self.test_category_data = {
            "name": "Financial Regulations",
            "description": "Regulations related to financial services and banking",
            "icon": "bank"
        }
        
        self.test_category_update_data = {
            "name": "Updated: Financial Regulations",
            "description": "Updated description for financial regulations category",
            "icon": "dollar"
        }

    @patch('app.api.regulation_categories.router.get_db')
    def test_get_regulation_categories(self, mock_get_db):
        """Test getting a list of regulation categories."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock query result
        mock_category = MagicMock()
        mock_category.id = 1
        mock_category.name = "Financial Regulations"
        mock_category.description = "Regulations related to financial services and banking"
        mock_category.icon = "bank"
        mock_category.created_at = "2023-01-01T00:00:00"
        mock_category.changed_on = "2023-01-01T00:00:00"
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [mock_category]
        
        # Make request
        response = client.get("/api/v1/regulation-categories/")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response.json(), list)
        self.assertEqual(len(response.json()), 1)
        self.assertEqual(response.json()[0]["name"], "Financial Regulations")
        
        # Verify query was called correctly
        mock_db.query.assert_called_once_with(RegulationCategory)
        mock_query.offset.assert_called_once_with(0)
        mock_query.limit.assert_called_once_with(100)

    @patch('app.api.regulation_categories.router.get_db')
    def test_get_regulation_categories_with_search(self, mock_get_db):
        """Test getting regulation categories with search filter."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = []
        
        # Make request with search
        response = client.get("/api/v1/regulation-categories/?search=financial")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        
        # Verify filter was called
        mock_query.filter.assert_called_once()

    @patch('app.api.regulation_categories.router.get_db')
    def test_get_regulation_category(self, mock_get_db):
        """Test getting a specific regulation category by ID."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock query result
        mock_category = MagicMock()
        mock_category.id = 1
        mock_category.name = "Financial Regulations"
        mock_category.description = "Regulations related to financial services and banking"
        mock_category.icon = "bank"
        mock_category.created_at = "2023-01-01T00:00:00"
        mock_category.changed_on = "2023-01-01T00:00:00"
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_category
        
        # Make request
        response = client.get("/api/v1/regulation-categories/1")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["name"], "Financial Regulations")
        
        # Verify query was called correctly
        mock_db.query.assert_called_once_with(RegulationCategory)
        mock_query.filter.assert_called_once()
        mock_query.first.assert_called_once()

    @patch('app.api.regulation_categories.router.get_db')
    def test_get_regulation_category_not_found(self, mock_get_db):
        """Test getting a non-existent regulation category."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain to return None
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        
        # Make request
        response = client.get("/api/v1/regulation-categories/999")
        
        # Check response
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.json()["detail"], "Regulation category not found")

    @patch('app.api.regulation_categories.router.get_db')
    def test_create_regulation_category(self, mock_get_db):
        """Test creating a new regulation category."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain to check for existing category
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None  # No existing category with same name
        
        # Create mock category object that will be returned
        mock_category = MagicMock()
        mock_category.id = 1
        mock_category.name = self.test_category_data["name"]
        mock_category.description = self.test_category_data["description"]
        mock_category.icon = self.test_category_data["icon"]
        mock_category.created_at = "2023-01-01T00:00:00"
        mock_category.changed_on = "2023-01-01T00:00:00"
        
        # Mock the add, commit, and refresh methods
        mock_db.add = MagicMock()
        mock_db.commit = MagicMock()
        mock_db.refresh = MagicMock()
        
        # Make the RegulationCategory constructor return our mock
        with patch('app.api.regulation_categories.router.RegulationCategory', return_value=mock_category):
            # Make request
            response = client.post("/api/v1/regulation-categories/", json=self.test_category_data)
            
            # Check response
            self.assertEqual(response.status_code, 201)
            self.assertEqual(response.json()["name"], self.test_category_data["name"])
            
            # Verify methods were called
            mock_db.add.assert_called_once()
            mock_db.commit.assert_called_once()
            mock_db.refresh.assert_called_once()

    @patch('app.api.regulation_categories.router.get_db')
    def test_create_regulation_category_duplicate_name(self, mock_get_db):
        """Test creating a regulation category with a duplicate name."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain to return an existing category
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        
        # Create mock existing category
        mock_existing_category = MagicMock()
        mock_existing_category.id = 1
        mock_existing_category.name = self.test_category_data["name"]
        
        mock_query.first.return_value = mock_existing_category
        
        # Make request
        response = client.post("/api/v1/regulation-categories/", json=self.test_category_data)
        
        # Check response
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.json()["detail"], "Category with this name already exists")

    @patch('app.api.regulation_categories.router.get_db')
    def test_update_regulation_category(self, mock_get_db):
        """Test updating a regulation category."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock category to update
        mock_category = MagicMock()
        mock_category.id = 1
        mock_category.name = "Financial Regulations"
        mock_category.description = "Original description"
        mock_category.icon = "bank"
        
        # Set up the mock query chain for finding the category
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.side_effect = [mock_category, None]  # First call returns the category, second call (checking for duplicate name) returns None
        
        # Make request
        response = client.put("/api/v1/regulation-categories/1", json=self.test_category_update_data)
        
        # Check response
        self.assertEqual(response.status_code, 200)
        
        # Verify setattr was called for each field
        self.assertEqual(mock_category.name, self.test_category_update_data["name"])
        self.assertEqual(mock_category.description, self.test_category_update_data["description"])
        self.assertEqual(mock_category.icon, self.test_category_update_data["icon"])
        
        # Verify commit and refresh were called
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()

    @patch('app.api.regulation_categories.router.get_db')
    def test_update_regulation_category_duplicate_name(self, mock_get_db):
        """Test updating a regulation category with a duplicate name."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock category to update
        mock_category = MagicMock()
        mock_category.id = 1
        mock_category.name = "Financial Regulations"
        
        # Create mock existing category with the name we want to update to
        mock_existing_category = MagicMock()
        mock_existing_category.id = 2
        mock_existing_category.name = self.test_category_update_data["name"]
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.side_effect = [mock_category, mock_existing_category]  # First call returns the category, second call returns the existing category with duplicate name
        
        # Make request
        response = client.put("/api/v1/regulation-categories/1", json=self.test_category_update_data)
        
        # Check response
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.json()["detail"], "Category with this name already exists")

    @patch('app.api.regulation_categories.router.get_db')
    def test_delete_regulation_category(self, mock_get_db):
        """Test deleting a regulation category."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain for the category
        mock_query_category = MagicMock()
        mock_db.query.side_effect = [mock_query_category, mock_query_category]  # First for finding category, second for checking regulations
        mock_query_category.filter.return_value = mock_query_category
        
        # Create mock category to delete
        mock_category = MagicMock()
        mock_category.id = 1
        mock_category.name = "Financial Regulations"
        
        # Set up the mock query chain for checking regulations
        mock_query_regulation = MagicMock()
        mock_query_regulation.filter.return_value = mock_query_regulation
        mock_query_regulation.count.return_value = 0  # No associated regulations
        
        mock_query_category.first.return_value = mock_category
        mock_query_category.count.return_value = 0
        
        # Make request
        response = client.delete("/api/v1/regulation-categories/1")
        
        # Check response
        self.assertEqual(response.status_code, 204)
        
        # Verify delete, commit were called
        mock_db.delete.assert_called_once_with(mock_category)
        mock_db.commit.assert_called_once()

    @patch('app.api.regulation_categories.router.get_db')
    def test_delete_regulation_category_with_regulations(self, mock_get_db):
        """Test deleting a regulation category that has associated regulations."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock category to delete
        mock_category = MagicMock()
        mock_category.id = 1
        mock_category.name = "Financial Regulations"
        
        # Set up the mock query chain for finding the category
        mock_query_category = MagicMock()
        mock_db.query.side_effect = [mock_query_category, mock_query_category]  # First for finding category, second for checking regulations
        mock_query_category.filter.return_value = mock_query_category
        mock_query_category.first.return_value = mock_category
        
        # Set up the mock query chain for checking regulations
        mock_query_regulation = MagicMock()
        mock_query_regulation.filter.return_value = mock_query_regulation
        mock_query_regulation.count.return_value = 2  # Two associated regulations
        
        # Make request
        response = client.delete("/api/v1/regulation-categories/1")
        
        # Check response
        self.assertEqual(response.status_code, 400)
        self.assertIn("Cannot delete category that is associated with", response.json()["detail"])
        
        # Verify delete was not called
        mock_db.delete.assert_not_called()

    @patch('app.api.regulation_categories.router.get_db')
    def test_get_regulations_by_category(self, mock_get_db):
        """Test getting regulations associated with a category."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock category
        mock_category = MagicMock()
        mock_category.id = 1
        mock_category.name = "Financial Regulations"
        
        # Create mock regulations
        mock_regulation1 = MagicMock()
        mock_regulation1.id = 1
        mock_regulation1.title = "Regulation 1"
        mock_regulation1.status = "Active"
        mock_regulation1.reference_number = "REG-001"
        
        mock_regulation2 = MagicMock()
        mock_regulation2.id = 2
        mock_regulation2.title = "Regulation 2"
        mock_regulation2.status = "Draft"
        mock_regulation2.reference_number = "REG-002"
        
        # Set up the mock query chain for finding the category
        mock_query_category = MagicMock()
        mock_db.query.side_effect = [mock_query_category, mock_query_category]  # First for finding category, second for finding regulations
        mock_query_category.filter.return_value = mock_query_category
        mock_query_category.first.return_value = mock_category
        
        # Set up the mock query chain for finding regulations
        mock_query_regulation = MagicMock()
        mock_query_regulation.filter.return_value = mock_query_regulation
        mock_query_regulation.offset.return_value = mock_query_regulation
        mock_query_regulation.limit.return_value = mock_query_regulation
        mock_query_regulation.all.return_value = [mock_regulation1, mock_regulation2]
        
        # Make request
        response = client.get("/api/v1/regulation-categories/1/regulations")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response.json(), list)
        self.assertEqual(len(response.json()), 2)
        self.assertEqual(response.json()[0]["title"], "Regulation 1")
        self.assertEqual(response.json()[1]["title"], "Regulation 2")


if __name__ == "__main__":
    unittest.main()
