"""
Tests for the regulation tag management API router.
"""
import unittest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.main import app
from app.db.models import RegulationTag, Regulation
from app.schemas.schemas import RegulationTagCreate, RegulationTagUpdate

client = TestClient(app)

class TestRegulationTagRouter(unittest.TestCase):
    """Tests for the regulation tag management API router."""

    def setUp(self):
        """Set up test data."""
        self.test_tag_data = {
            "name": "Data Protection",
            "description": "Regulations related to data protection and privacy",
            "color": "#1890ff"
        }
        
        self.test_tag_update_data = {
            "name": "Updated: Data Protection",
            "description": "Updated description for data protection tag",
            "color": "#52c41a"
        }

    @patch('app.api.regulation_tags.router.get_db')
    def test_get_regulation_tags(self, mock_get_db):
        """Test getting a list of regulation tags."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock query result
        mock_tag = MagicMock()
        mock_tag.id = 1
        mock_tag.name = "Data Protection"
        mock_tag.description = "Regulations related to data protection and privacy"
        mock_tag.color = "#1890ff"
        mock_tag.created_at = "2023-01-01T00:00:00"
        mock_tag.changed_on = "2023-01-01T00:00:00"
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [mock_tag]
        
        # Make request
        response = client.get("/api/v1/regulation-tags/")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response.json(), list)
        self.assertEqual(len(response.json()), 1)
        self.assertEqual(response.json()[0]["name"], "Data Protection")
        
        # Verify query was called correctly
        mock_db.query.assert_called_once_with(RegulationTag)
        mock_query.offset.assert_called_once_with(0)
        mock_query.limit.assert_called_once_with(100)

    @patch('app.api.regulation_tags.router.get_db')
    def test_get_regulation_tags_with_search(self, mock_get_db):
        """Test getting regulation tags with search filter."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = []
        
        # Make request with search
        response = client.get("/api/v1/regulation-tags/?search=data")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        
        # Verify filter was called
        mock_query.filter.assert_called_once()

    @patch('app.api.regulation_tags.router.get_db')
    def test_get_regulation_tag(self, mock_get_db):
        """Test getting a specific regulation tag by ID."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock query result
        mock_tag = MagicMock()
        mock_tag.id = 1
        mock_tag.name = "Data Protection"
        mock_tag.description = "Regulations related to data protection and privacy"
        mock_tag.color = "#1890ff"
        mock_tag.created_at = "2023-01-01T00:00:00"
        mock_tag.changed_on = "2023-01-01T00:00:00"
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_tag
        
        # Make request
        response = client.get("/api/v1/regulation-tags/1")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["name"], "Data Protection")
        
        # Verify query was called correctly
        mock_db.query.assert_called_once_with(RegulationTag)
        mock_query.filter.assert_called_once()
        mock_query.first.assert_called_once()

    @patch('app.api.regulation_tags.router.get_db')
    def test_get_regulation_tag_not_found(self, mock_get_db):
        """Test getting a non-existent regulation tag."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain to return None
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        
        # Make request
        response = client.get("/api/v1/regulation-tags/999")
        
        # Check response
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.json()["detail"], "Regulation tag not found")

    @patch('app.api.regulation_tags.router.get_db')
    def test_create_regulation_tag(self, mock_get_db):
        """Test creating a new regulation tag."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain to check for existing tag
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None  # No existing tag with same name
        
        # Create mock tag object that will be returned
        mock_tag = MagicMock()
        mock_tag.id = 1
        mock_tag.name = self.test_tag_data["name"]
        mock_tag.description = self.test_tag_data["description"]
        mock_tag.color = self.test_tag_data["color"]
        mock_tag.created_at = "2023-01-01T00:00:00"
        mock_tag.changed_on = "2023-01-01T00:00:00"
        
        # Mock the add, commit, and refresh methods
        mock_db.add = MagicMock()
        mock_db.commit = MagicMock()
        mock_db.refresh = MagicMock()
        
        # Make the RegulationTag constructor return our mock
        with patch('app.api.regulation_tags.router.RegulationTag', return_value=mock_tag):
            # Make request
            response = client.post("/api/v1/regulation-tags/", json=self.test_tag_data)
            
            # Check response
            self.assertEqual(response.status_code, 201)
            self.assertEqual(response.json()["name"], self.test_tag_data["name"])
            
            # Verify methods were called
            mock_db.add.assert_called_once()
            mock_db.commit.assert_called_once()
            mock_db.refresh.assert_called_once()

    @patch('app.api.regulation_tags.router.get_db')
    def test_create_regulation_tag_duplicate_name(self, mock_get_db):
        """Test creating a regulation tag with a duplicate name."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain to return an existing tag
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        
        # Create mock existing tag
        mock_existing_tag = MagicMock()
        mock_existing_tag.id = 1
        mock_existing_tag.name = self.test_tag_data["name"]
        
        mock_query.first.return_value = mock_existing_tag
        
        # Make request
        response = client.post("/api/v1/regulation-tags/", json=self.test_tag_data)
        
        # Check response
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.json()["detail"], "Tag with this name already exists")

    @patch('app.api.regulation_tags.router.get_db')
    def test_update_regulation_tag(self, mock_get_db):
        """Test updating a regulation tag."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock tag to update
        mock_tag = MagicMock()
        mock_tag.id = 1
        mock_tag.name = "Data Protection"
        mock_tag.description = "Original description"
        mock_tag.color = "#1890ff"
        
        # Set up the mock query chain for finding the tag
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.side_effect = [mock_tag, None]  # First call returns the tag, second call (checking for duplicate name) returns None
        
        # Make request
        response = client.put("/api/v1/regulation-tags/1", json=self.test_tag_update_data)
        
        # Check response
        self.assertEqual(response.status_code, 200)
        
        # Verify setattr was called for each field
        self.assertEqual(mock_tag.name, self.test_tag_update_data["name"])
        self.assertEqual(mock_tag.description, self.test_tag_update_data["description"])
        self.assertEqual(mock_tag.color, self.test_tag_update_data["color"])
        
        # Verify commit and refresh were called
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()

    @patch('app.api.regulation_tags.router.get_db')
    def test_update_regulation_tag_duplicate_name(self, mock_get_db):
        """Test updating a regulation tag with a duplicate name."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock tag to update
        mock_tag = MagicMock()
        mock_tag.id = 1
        mock_tag.name = "Data Protection"
        
        # Create mock existing tag with the name we want to update to
        mock_existing_tag = MagicMock()
        mock_existing_tag.id = 2
        mock_existing_tag.name = self.test_tag_update_data["name"]
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.side_effect = [mock_tag, mock_existing_tag]  # First call returns the tag, second call returns the existing tag with duplicate name
        
        # Make request
        response = client.put("/api/v1/regulation-tags/1", json=self.test_tag_update_data)
        
        # Check response
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.json()["detail"], "Tag with this name already exists")

    @patch('app.api.regulation_tags.router.get_db')
    def test_delete_regulation_tag(self, mock_get_db):
        """Test deleting a regulation tag."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock tag to delete
        mock_tag = MagicMock()
        mock_tag.id = 1
        mock_tag.name = "Data Protection"
        mock_tag.regulations = []  # No associated regulations
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_tag
        
        # Make request
        response = client.delete("/api/v1/regulation-tags/1")
        
        # Check response
        self.assertEqual(response.status_code, 204)
        
        # Verify delete, commit were called
        mock_db.delete.assert_called_once_with(mock_tag)
        mock_db.commit.assert_called_once()

    @patch('app.api.regulation_tags.router.get_db')
    def test_delete_regulation_tag_with_regulations(self, mock_get_db):
        """Test deleting a regulation tag that has associated regulations."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock tag with associated regulations
        mock_tag = MagicMock()
        mock_tag.id = 1
        mock_tag.name = "Data Protection"
        mock_tag.regulations = [MagicMock(), MagicMock()]  # Two associated regulations
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_tag
        
        # Make request
        response = client.delete("/api/v1/regulation-tags/1")
        
        # Check response
        self.assertEqual(response.status_code, 400)
        self.assertIn("Cannot delete tag that is associated with", response.json()["detail"])
        
        # Verify delete was not called
        mock_db.delete.assert_not_called()

    @patch('app.api.regulation_tags.router.get_db')
    def test_get_regulations_by_tag(self, mock_get_db):
        """Test getting regulations associated with a tag."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock tag
        mock_tag = MagicMock()
        mock_tag.id = 1
        mock_tag.name = "Data Protection"
        
        # Create mock regulations
        mock_regulation1 = MagicMock()
        mock_regulation1.id = 1
        mock_regulation1.title = "Regulation 1"
        mock_regulation1.status = "Active"
        mock_regulation1.reference_number = "REG-001"
        
        mock_regulation2 = MagicMock()
        mock_regulation2.id = 2
        mock_regulation2.title = "Regulation 2"
        mock_regulation2.status = "Draft"
        mock_regulation2.reference_number = "REG-002"
        
        # Set up the tag's regulations
        mock_tag.regulations = [mock_regulation1, mock_regulation2]
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_tag
        
        # Make request
        response = client.get("/api/v1/regulation-tags/1/regulations")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response.json(), list)
        self.assertEqual(len(response.json()), 2)
        self.assertEqual(response.json()[0]["title"], "Regulation 1")
        self.assertEqual(response.json()[1]["title"], "Regulation 2")


if __name__ == "__main__":
    unittest.main()
