"""
Tests for the regulation management API router.
"""
import unittest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.main import app
from app.db.models import Regulation, Regulator, Country, RegulationURL, RegulationTag, RegulationCategory, Industry
from app.schemas.schemas import RegulationCreate, RegulationUpdate

client = TestClient(app)

class TestRegulationRouter(unittest.TestCase):
    """Tests for the regulation management API router."""

    def setUp(self):
        """Set up test data."""
        self.test_regulation_data = {
            "title": "Test Regulation",
            "description": "Test Description",
            "status": "Active",
            "reference_number": "REG-123",
            "regulator_id": 1,
            "country_id": 1,
            "category_id": 1,
            "regulation_url_id": 1,
            "tags": [1, 2],
            "industries": [1, 2, 3]
        }
        
        self.test_regulation_update_data = {
            "title": "Updated Test Regulation",
            "status": "Amended",
            "tags": [1, 3]
        }

    @patch('app.api.regulations.router.get_db')
    def test_get_regulations(self, mock_get_db):
        """Test getting a list of regulations."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock query result
        mock_regulation = MagicMock()
        mock_regulation.id = 1
        mock_regulation.title = "Test Regulation"
        mock_regulation.description = "Test Description"
        mock_regulation.status = "Active"
        mock_regulation.reference_number = "REG-123"
        mock_regulation.regulator_id = 1
        mock_regulation.country_id = 1
        mock_regulation.category_id = 1
        mock_regulation.regulation_url_id = 1
        mock_regulation.created_at = "2023-01-01T00:00:00"
        mock_regulation.changed_on = "2023-01-01T00:00:00"
        
        # Create mock relationships
        mock_regulator = MagicMock()
        mock_regulator.id = 1
        mock_regulator.name = "Test Regulator"
        mock_regulator.country_id = 1
        
        mock_country = MagicMock()
        mock_country.id = 1
        mock_country.name = "Test Country"
        mock_country.code = "TC"
        
        mock_category = MagicMock()
        mock_category.id = 1
        mock_category.name = "Test Category"
        
        mock_url = MagicMock()
        mock_url.id = 1
        mock_url.url = "https://example.com/regulation"
        
        mock_tag1 = MagicMock()
        mock_tag1.id = 1
        mock_tag1.name = "Tag 1"
        
        mock_tag2 = MagicMock()
        mock_tag2.id = 2
        mock_tag2.name = "Tag 2"
        
        mock_industry1 = MagicMock()
        mock_industry1.id = 1
        mock_industry1.name = "Industry 1"
        
        mock_industry2 = MagicMock()
        mock_industry2.id = 2
        mock_industry2.name = "Industry 2"
        
        # Set up the relationships
        mock_regulator.country = mock_country
        mock_regulation.regulator = mock_regulator
        mock_regulation.category = mock_category
        mock_regulation.regulation_url = mock_url
        mock_regulation.tags = [mock_tag1, mock_tag2]
        mock_regulation.industries = [mock_industry1, mock_industry2]
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.join.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [mock_regulation]
        
        # Make request
        response = client.get("/api/v1/regulations/")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response.json(), list)
        self.assertEqual(len(response.json()), 1)
        self.assertEqual(response.json()[0]["title"], "Test Regulation")
        self.assertEqual(response.json()[0]["regulator_id"], 1)
        
        # Verify query was called correctly
        mock_db.query.assert_called_once_with(Regulation)
        mock_query.offset.assert_called_once_with(0)
        mock_query.limit.assert_called_once_with(100)

    @patch('app.api.regulations.router.get_db')
    def test_get_regulations_with_filters(self, mock_get_db):
        """Test getting regulations with filters."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.join.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = []
        
        # Make request with filters
        response = client.get("/api/v1/regulations/?regulator_id=1&status=Active")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        
        # Verify filter was called twice (once for each filter)
        self.assertEqual(mock_query.filter.call_count, 2)

    @patch('app.api.regulations.router.get_db')
    def test_get_regulation(self, mock_get_db):
        """Test getting a specific regulation by ID."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock query result
        mock_regulation = MagicMock()
        mock_regulation.id = 1
        mock_regulation.title = "Test Regulation"
        mock_regulation.description = "Test Description"
        mock_regulation.status = "Active"
        mock_regulation.reference_number = "REG-123"
        mock_regulation.regulator_id = 1
        mock_regulation.country_id = 1
        mock_regulation.category_id = 1
        mock_regulation.regulation_url_id = 1
        mock_regulation.created_at = "2023-01-01T00:00:00"
        mock_regulation.changed_on = "2023-01-01T00:00:00"
        
        # Create mock relationships
        mock_regulator = MagicMock()
        mock_regulator.id = 1
        mock_regulator.name = "Test Regulator"
        mock_regulator.country_id = 1
        
        mock_country = MagicMock()
        mock_country.id = 1
        mock_country.name = "Test Country"
        mock_country.code = "TC"
        
        # Set up the relationships
        mock_regulator.country = mock_country
        mock_regulation.regulator = mock_regulator
        mock_regulation.tags = []
        mock_regulation.industries = []
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_regulation
        
        # Make request
        response = client.get("/api/v1/regulations/1")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["title"], "Test Regulation")
        self.assertEqual(response.json()["regulator_id"], 1)
        
        # Verify query was called correctly
        mock_db.query.assert_called_once_with(Regulation)
        mock_query.filter.assert_called_once()
        mock_query.first.assert_called_once()

    @patch('app.api.regulations.router.get_db')
    def test_get_regulation_not_found(self, mock_get_db):
        """Test getting a non-existent regulation."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain to return None
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        
        # Make request
        response = client.get("/api/v1/regulations/999")
        
        # Check response
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.json()["detail"], "Regulation not found")

    @patch('app.api.regulations.router.get_db')
    def test_create_regulation(self, mock_get_db):
        """Test creating a new regulation."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Mock the queries for related entities
        mock_regulator = MagicMock()
        mock_regulator.id = 1
        
        mock_country = MagicMock()
        mock_country.id = 1
        
        mock_category = MagicMock()
        mock_category.id = 1
        
        mock_url = MagicMock()
        mock_url.id = 1
        
        mock_tag1 = MagicMock()
        mock_tag1.id = 1
        
        mock_tag2 = MagicMock()
        mock_tag2.id = 2
        
        mock_industry1 = MagicMock()
        mock_industry1.id = 1
        
        mock_industry2 = MagicMock()
        mock_industry2.id = 2
        
        mock_industry3 = MagicMock()
        mock_industry3.id = 3
        
        # Set up the mock query chain for checking existing entities
        mock_query = MagicMock()
        mock_db.query.side_effect = [
            mock_query,  # Regulator query
            mock_query,  # Country query
            mock_query,  # Category query
            mock_query,  # URL query
            mock_query,  # Tag 1 query
            mock_query,  # Tag 2 query
            mock_query,  # Industry 1 query
            mock_query,  # Industry 2 query
            mock_query   # Industry 3 query
        ]
        mock_query.filter.return_value = mock_query
        mock_query.first.side_effect = [
            mock_regulator,
            mock_country,
            mock_category,
            mock_url,
            mock_tag1,
            mock_tag2,
            mock_industry1,
            mock_industry2,
            mock_industry3
        ]
        
        # Create mock regulation object that will be returned
        mock_regulation = MagicMock()
        mock_regulation.id = 1
        mock_regulation.title = self.test_regulation_data["title"]
        mock_regulation.description = self.test_regulation_data["description"]
        mock_regulation.status = self.test_regulation_data["status"]
        mock_regulation.reference_number = self.test_regulation_data["reference_number"]
        mock_regulation.regulator_id = self.test_regulation_data["regulator_id"]
        mock_regulation.country_id = self.test_regulation_data["country_id"]
        mock_regulation.category_id = self.test_regulation_data["category_id"]
        mock_regulation.regulation_url_id = self.test_regulation_data["regulation_url_id"]
        mock_regulation.created_at = "2023-01-01T00:00:00"
        mock_regulation.changed_on = "2023-01-01T00:00:00"
        mock_regulation.tags = [mock_tag1, mock_tag2]
        mock_regulation.industries = [mock_industry1, mock_industry2, mock_industry3]
        mock_regulation.regulator = mock_regulator
        
        # Mock the add, commit, and refresh methods
        mock_db.add = MagicMock()
        mock_db.commit = MagicMock()
        mock_db.refresh = MagicMock()
        
        # Make the Regulation constructor return our mock
        with patch('app.api.regulations.router.Regulation', return_value=mock_regulation):
            # Make request
            response = client.post("/api/v1/regulations/", json=self.test_regulation_data)
            
            # Check response
            self.assertEqual(response.status_code, 201)
            self.assertEqual(response.json()["title"], self.test_regulation_data["title"])
            self.assertEqual(response.json()["regulator_id"], self.test_regulation_data["regulator_id"])
            
            # Verify methods were called
            mock_db.add.assert_called_once()
            mock_db.commit.assert_called_once()
            mock_db.refresh.assert_called_once()

    @patch('app.api.regulations.router.get_db')
    def test_update_regulation(self, mock_get_db):
        """Test updating a regulation."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock regulation to update
        mock_regulation = MagicMock()
        mock_regulation.id = 1
        mock_regulation.title = "Test Regulation"
        mock_regulation.status = "Active"
        mock_regulation.tags = []
        
        # Create mock tag
        mock_tag1 = MagicMock()
        mock_tag1.id = 1
        
        mock_tag3 = MagicMock()
        mock_tag3.id = 3
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.side_effect = [
            mock_query,  # Regulation query
            mock_query,  # Tag 1 query
            mock_query   # Tag 3 query
        ]
        mock_query.filter.return_value = mock_query
        mock_query.first.side_effect = [
            mock_regulation,
            mock_tag1,
            mock_tag3
        ]
        
        # Make request
        response = client.put("/api/v1/regulations/1", json=self.test_regulation_update_data)
        
        # Check response
        self.assertEqual(response.status_code, 200)
        
        # Verify setattr was called for each field
        self.assertEqual(mock_regulation.title, self.test_regulation_update_data["title"])
        self.assertEqual(mock_regulation.status, self.test_regulation_update_data["status"])
        
        # Verify tags were updated
        self.assertEqual(len(mock_regulation.tags), 2)
        
        # Verify commit and refresh were called
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()

    @patch('app.api.regulations.router.get_db')
    def test_delete_regulation(self, mock_get_db):
        """Test deleting a regulation."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock regulation to delete
        mock_regulation = MagicMock()
        mock_regulation.id = 1
        mock_regulation.title = "Test Regulation"
        mock_regulation.compliance_requirements = []
        mock_regulation.documents = []
        mock_regulation.tags = [MagicMock()]
        mock_regulation.industries = [MagicMock()]
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_regulation
        
        # Make request
        response = client.delete("/api/v1/regulations/1")
        
        # Check response
        self.assertEqual(response.status_code, 204)
        
        # Verify tags and industries were cleared
        self.assertEqual(mock_regulation.tags, [])
        self.assertEqual(mock_regulation.industries, [])
        
        # Verify delete, commit were called
        mock_db.delete.assert_called_once_with(mock_regulation)
        mock_db.commit.assert_called_once()

    @patch('app.api.regulations.router.get_db')
    def test_delete_regulation_with_relations(self, mock_get_db):
        """Test deleting a regulation that has related records."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock regulation with related records
        mock_regulation = MagicMock()
        mock_regulation.id = 1
        mock_regulation.title = "Test Regulation"
        mock_regulation.compliance_requirements = [MagicMock()]  # Non-empty list
        mock_regulation.documents = []
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_regulation
        
        # Make request
        response = client.delete("/api/v1/regulations/1")
        
        # Check response
        self.assertEqual(response.status_code, 400)
        self.assertIn("Cannot delete regulation with related records", response.json()["detail"])
        
        # Verify delete was not called
        mock_db.delete.assert_not_called()


if __name__ == "__main__":
    unittest.main()
