"""
Tests for the AI API router.
"""
import unittest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

class TestAIRouter(unittest.TestCase):
    """Tests for the AI API router."""
    
    @patch('app.api.ai.router.gemini_client')
    def test_search_regulatory_information(self, mock_gemini_client):
        """Test search_regulatory_information endpoint."""
        # Set up mock response
        mock_gemini_client.api_key = 'test_api_key'
        mock_gemini_client.search_regulatory_information.return_value = {
            'status': 'success',
            'structured_data': {
                'answer': 'Test answer',
                'citations': ['Citation 1'],
                'key_considerations': ['Consideration 1'],
                'deadlines': [],
                'penalties': []
            },
            'raw_text': 'Raw text response'
        }
        
        # Make request
        response = client.post(
            '/api/v1/ai/regulatory-search',
            json={
                'query': 'What is GDPR?',
                'regulation_name': 'GDPR',
                'jurisdiction': 'EU'
            }
        )
        
        # Check response
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['answer'], 'Test answer')
        self.assertEqual(data['citations'], ['Citation 1'])
        self.assertEqual(data['key_considerations'], ['Consideration 1'])
        self.assertEqual(data['deadlines'], [])
        self.assertEqual(data['penalties'], [])
        self.assertEqual(data['raw_text'], 'Raw text response')
        
        # Check that gemini_client was called with correct arguments
        mock_gemini_client.search_regulatory_information.assert_called_once_with(
            query='What is GDPR?',
            regulation_name='GDPR',
            jurisdiction='EU',
            max_tokens=1024,
            temperature=0.2
        )
    
    @patch('app.api.ai.router.gemini_client')
    def test_search_regulatory_information_error(self, mock_gemini_client):
        """Test search_regulatory_information endpoint with error."""
        # Set up mock response
        mock_gemini_client.api_key = 'test_api_key'
        mock_gemini_client.search_regulatory_information.return_value = {
            'status': 'error',
            'error': 'Test error'
        }
        
        # Make request
        response = client.post(
            '/api/v1/ai/regulatory-search',
            json={
                'query': 'What is GDPR?'
            }
        )
        
        # Check response
        self.assertEqual(response.status_code, 500)
        data = response.json()
        self.assertEqual(data['detail'], 'Test error')
    
    @patch('app.api.ai.router.gemini_client')
    def test_search_regulatory_information_no_api_key(self, mock_gemini_client):
        """Test search_regulatory_information endpoint with no API key."""
        # Set up mock response
        mock_gemini_client.api_key = None
        
        # Make request
        response = client.post(
            '/api/v1/ai/regulatory-search',
            json={
                'query': 'What is GDPR?'
            }
        )
        
        # Check response
        self.assertEqual(response.status_code, 501)
        data = response.json()
        self.assertEqual(data['detail'], 'Gemini integration not available. API key not configured.')

if __name__ == '__main__':
    unittest.main()
