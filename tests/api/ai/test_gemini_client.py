"""
Tests for the Gemini API client.
"""
import unittest
from unittest.mock import patch, MagicMock
import os
import json
from app.api.ai.gemini_client import GeminiClient, GeminiRateLimiter

class TestGeminiRateLimiter(unittest.TestCase):
    """Tests for the GeminiRateLimiter class."""
    
    def test_init(self):
        """Test initialization."""
        limiter = GeminiRateLimiter(max_requests_per_minute=10)
        self.assertEqual(limiter.max_requests_per_minute, 10)
        self.assertEqual(limiter.request_timestamps, [])
    
    @patch('time.time')
    @patch('time.sleep')
    def test_wait_if_needed(self, mock_sleep, mock_time):
        """Test wait_if_needed method."""
        # Set up mock time
        mock_time.side_effect = [100, 100]  # First call for current_time, second for adding timestamp
        
        limiter = GeminiRateLimiter(max_requests_per_minute=2)
        limiter.request_timestamps = [95, 96]  # Two requests in the last minute
        
        # Should wait because we've reached the limit
        limiter.wait_if_needed()
        
        # Should have called sleep with the correct wait time
        mock_sleep.assert_called_once_with(55)  # 60 - (100 - 95) = 55
        
        # Should have added the new timestamp
        self.assertEqual(limiter.request_timestamps, [95, 96, 100])

class TestGeminiClient(unittest.TestCase):
    """Tests for the GeminiClient class."""
    
    def setUp(self):
        """Set up test environment."""
        # Save original environment
        self.original_api_key = os.environ.get('GEMINI_API_KEY')
        
        # Set test environment
        os.environ['GEMINI_API_KEY'] = 'test_api_key'
        
        # Create client
        self.client = GeminiClient()
    
    def tearDown(self):
        """Tear down test environment."""
        # Restore original environment
        if self.original_api_key:
            os.environ['GEMINI_API_KEY'] = self.original_api_key
        else:
            del os.environ['GEMINI_API_KEY']
    
    def test_init(self):
        """Test initialization."""
        self.assertEqual(self.client.api_key, 'test_api_key')
        self.assertEqual(self.client.base_url, 'https://generativelanguage.googleapis.com/v1beta')
        self.assertEqual(self.client.model, 'gemini-pro')
        self.assertEqual(self.client.rate_limiter.max_requests_per_minute, 14)
    
    @patch('app.api.ai.gemini_client.GeminiRateLimiter.wait_if_needed')
    @patch('requests.Session.post')
    def test_search_regulatory_information(self, mock_post, mock_wait):
        """Test search_regulatory_information method."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {
            'candidates': [
                {
                    'content': {
                        'parts': [
                            {
                                'text': '{"answer": "Test answer", "citations": ["Citation 1"], "key_considerations": ["Consideration 1"], "deadlines": [], "penalties": []}'
                            }
                        ]
                    }
                }
            ]
        }
        mock_post.return_value = mock_response
        
        # Call the method
        result = self.client.search_regulatory_information(
            query='What is GDPR?',
            regulation_name='GDPR',
            jurisdiction='EU'
        )
        
        # Check that rate limiter was called
        mock_wait.assert_called_once()
        
        # Check that post was called with correct arguments
        mock_post.assert_called_once()
        args, kwargs = mock_post.call_args
        self.assertEqual(args[0], 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=test_api_key')
        
        # Check payload
        payload = kwargs['json']
        self.assertEqual(payload['contents'][0]['role'], 'user')
        self.assertIn('GDPR', payload['contents'][0]['parts'][0]['text'])
        self.assertIn('EU', payload['contents'][0]['parts'][0]['text'])
        
        # Check generation config
        self.assertEqual(payload['generationConfig']['temperature'], 0.2)
        self.assertEqual(payload['generationConfig']['maxOutputTokens'], 1024)
        
        # Check result
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['structured_data']['answer'], 'Test answer')
        self.assertEqual(result['structured_data']['citations'], ['Citation 1'])
        self.assertEqual(result['structured_data']['key_considerations'], ['Consideration 1'])
        self.assertEqual(result['structured_data']['deadlines'], [])
        self.assertEqual(result['structured_data']['penalties'], [])
    
    def test_create_regulatory_search_prompt(self):
        """Test _create_regulatory_search_prompt method."""
        prompt = self.client._create_regulatory_search_prompt(
            query='What are the requirements for data protection?',
            regulation_name='GDPR',
            jurisdiction='EU'
        )
        
        # Check that the prompt contains the query, regulation name, and jurisdiction
        self.assertIn('What are the requirements for data protection?', prompt)
        self.assertIn('GDPR', prompt)
        self.assertIn('EU', prompt)
        
        # Check that the prompt contains instructions for structured response
        self.assertIn('Format your response as JSON', prompt)
        self.assertIn('"answer":', prompt)
        self.assertIn('"citations":', prompt)
        self.assertIn('"key_considerations":', prompt)
        self.assertIn('"deadlines":', prompt)
        self.assertIn('"penalties":', prompt)

if __name__ == '__main__':
    unittest.main()
