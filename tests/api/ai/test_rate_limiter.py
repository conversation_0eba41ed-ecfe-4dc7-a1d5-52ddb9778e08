"""
Tests for the rate limiter.
"""
import unittest
from unittest.mock import patch, MagicMock
import time
import threading

from app.api.ai.rate_limiter import TokenBucket, RateLimiter, rate_limited, global_rate_limiter

class TestTokenBucket(unittest.TestCase):
    """Tests for the TokenBucket class."""
    
    def test_init(self):
        """Test initialization."""
        bucket = TokenBucket(tokens_per_second=1.0, max_tokens=10)
        self.assertEqual(bucket.tokens_per_second, 1.0)
        self.assertEqual(bucket.max_tokens, 10)
        self.assertEqual(bucket.tokens, 10)
    
    @patch('time.time')
    def test_refill(self, mock_time):
        """Test token refill."""
        # Set up mock time
        mock_time.side_effect = [100, 105]  # First call for init, second for refill
        
        bucket = TokenBucket(tokens_per_second=1.0, max_tokens=10)
        bucket.tokens = 5  # Manually set tokens to 5
        
        # Refill should add 5 tokens (5 seconds * 1 token/second)
        bucket._refill()
        
        self.assertEqual(bucket.tokens, 10)  # Max is 10
        self.assertEqual(bucket.last_refill, 105)
    
    @patch('time.time')
    @patch('time.sleep')
    def test_consume_success(self, mock_sleep, mock_time):
        """Test successful token consumption."""
        # Set up mock time
        mock_time.return_value = 100
        
        bucket = TokenBucket(tokens_per_second=1.0, max_tokens=10)
        
        # Should succeed because we have enough tokens
        result = bucket.consume(tokens=5)
        
        self.assertTrue(result)
        self.assertEqual(bucket.tokens, 5)
        mock_sleep.assert_not_called()
    
    @patch('time.time')
    @patch('time.sleep')
    def test_consume_wait(self, mock_sleep, mock_time):
        """Test token consumption with waiting."""
        # Set up mock time
        mock_time.side_effect = [100, 100, 105]  # Init, consume check, after sleep
        
        bucket = TokenBucket(tokens_per_second=1.0, max_tokens=10)
        bucket.tokens = 2  # Manually set tokens to 2
        
        # Should wait for 3 seconds (3 tokens needed, 1 token/second)
        result = bucket.consume(tokens=5, block=True)
        
        self.assertTrue(result)
        self.assertEqual(bucket.tokens, 0)  # 2 + 3 - 5 = 0
        mock_sleep.assert_called_once_with(3.0)
    
    @patch('time.time')
    def test_consume_no_wait_fail(self, mock_time):
        """Test token consumption without waiting (failure)."""
        # Set up mock time
        mock_time.return_value = 100
        
        bucket = TokenBucket(tokens_per_second=1.0, max_tokens=10)
        bucket.tokens = 2  # Manually set tokens to 2
        
        # Should fail because we don't have enough tokens and block=False
        result = bucket.consume(tokens=5, block=False)
        
        self.assertFalse(result)
        self.assertEqual(bucket.tokens, 2)  # Unchanged
    
    def test_consume_too_many_tokens(self):
        """Test consuming more tokens than max_tokens."""
        bucket = TokenBucket(tokens_per_second=1.0, max_tokens=10)
        
        # Should fail because we're trying to consume more than max_tokens
        result = bucket.consume(tokens=15)
        
        self.assertFalse(result)
        self.assertEqual(bucket.tokens, 10)  # Unchanged

class TestRateLimiter(unittest.TestCase):
    """Tests for the RateLimiter class."""
    
    def test_init(self):
        """Test initialization."""
        limiter = RateLimiter()
        self.assertEqual(limiter.limiters, {})
    
    def test_register_limiter(self):
        """Test registering a rate limiter."""
        limiter = RateLimiter()
        limiter.register_limiter("test", tokens_per_minute=60)
        
        self.assertIn("test", limiter.limiters)
        self.assertEqual(limiter.limiters["test"].tokens_per_second, 1.0)
        self.assertEqual(limiter.limiters["test"].max_tokens, 60)
    
    def test_register_limiter_with_max_burst(self):
        """Test registering a rate limiter with max_burst."""
        limiter = RateLimiter()
        limiter.register_limiter("test", tokens_per_minute=60, max_burst=100)
        
        self.assertIn("test", limiter.limiters)
        self.assertEqual(limiter.limiters["test"].tokens_per_second, 1.0)
        self.assertEqual(limiter.limiters["test"].max_tokens, 100)
    
    @patch('app.api.ai.rate_limiter.TokenBucket.consume')
    def test_limit_success(self, mock_consume):
        """Test successful rate limiting."""
        mock_consume.return_value = True
        
        limiter = RateLimiter()
        limiter.register_limiter("test", tokens_per_minute=60)
        
        result = limiter.limit("test", tokens=5, block=True)
        
        self.assertTrue(result)
        mock_consume.assert_called_once_with(5, True)
    
    @patch('app.api.ai.rate_limiter.TokenBucket.consume')
    def test_limit_failure(self, mock_consume):
        """Test rate limiting failure."""
        mock_consume.return_value = False
        
        limiter = RateLimiter()
        limiter.register_limiter("test", tokens_per_minute=60)
        
        result = limiter.limit("test", tokens=5, block=False)
        
        self.assertFalse(result)
        mock_consume.assert_called_once_with(5, False)
    
    def test_limit_unknown_limiter(self):
        """Test rate limiting with unknown limiter."""
        limiter = RateLimiter()
        
        # Should succeed because the limiter doesn't exist
        result = limiter.limit("unknown", tokens=5)
        
        self.assertTrue(result)

class TestRateLimitedDecorator(unittest.TestCase):
    """Tests for the rate_limited decorator."""
    
    @patch('app.api.ai.rate_limiter.global_rate_limiter.limit')
    def test_rate_limited_success(self, mock_limit):
        """Test successful rate limiting with decorator."""
        mock_limit.return_value = True
        
        @rate_limited("test", tokens=5, block=True)
        def test_function():
            return "success"
        
        result = test_function()
        
        self.assertEqual(result, "success")
        mock_limit.assert_called_once_with("test", 5, True)
    
    @patch('app.api.ai.rate_limiter.global_rate_limiter.limit')
    def test_rate_limited_failure(self, mock_limit):
        """Test rate limiting failure with decorator."""
        mock_limit.return_value = False
        
        @rate_limited("test", tokens=5, block=False)
        def test_function():
            return "success"
        
        with self.assertRaises(Exception) as context:
            test_function()
        
        self.assertIn("Rate limit exceeded", str(context.exception))
        mock_limit.assert_called_once_with("test", 5, False)

class TestGlobalRateLimiter(unittest.TestCase):
    """Tests for the global rate limiter."""
    
    def test_global_rate_limiter_exists(self):
        """Test that the global rate limiter exists."""
        self.assertIsInstance(global_rate_limiter, RateLimiter)
    
    def test_gemini_limiter_registered(self):
        """Test that the Gemini limiter is registered."""
        self.assertIn("gemini", global_rate_limiter.limiters)
        
        # Should be 14 requests per minute
        self.assertEqual(global_rate_limiter.limiters["gemini"].tokens_per_second, 14/60)
        
        # Max burst should be 20
        self.assertEqual(global_rate_limiter.limiters["gemini"].max_tokens, 20)

class TestRateLimiterIntegration(unittest.TestCase):
    """Integration tests for the rate limiter."""
    
    def test_concurrent_requests(self):
        """Test rate limiting with concurrent requests."""
        limiter = RateLimiter()
        limiter.register_limiter("test", tokens_per_minute=10, max_burst=5)
        
        results = []
        
        def worker(block):
            result = limiter.limit("test", tokens=1, block=block)
            results.append(result)
        
        # Start 10 threads
        threads = []
        for i in range(10):
            t = threading.Thread(target=worker, args=(False,))
            threads.append(t)
            t.start()
        
        # Wait for all threads to complete
        for t in threads:
            t.join()
        
        # Should have 5 successes and 5 failures
        self.assertEqual(results.count(True), 5)
        self.assertEqual(results.count(False), 5)

if __name__ == '__main__':
    unittest.main()
