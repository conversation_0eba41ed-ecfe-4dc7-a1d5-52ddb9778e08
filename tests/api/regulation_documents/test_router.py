"""
Tests for the regulation document management API router.
"""
import unittest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.main import app
from app.db.models import RegulationDocument, Regulation
from app.schemas.schemas import RegulationDocumentCreate, RegulationDocumentUpdate

client = TestClient(app)

class TestRegulationDocumentRouter(unittest.TestCase):
    """Tests for the regulation document management API router."""

    def setUp(self):
        """Set up test data."""
        self.test_document_data = {
            "name": "GDPR Full Text",
            "description": "Complete text of the General Data Protection Regulation",
            "document_type": "PDF",
            "file_name": "gdpr_full_text.pdf",
            "file_size": 2048576,
            "file_type": "application/pdf",
            "file_url": "https://example.com/documents/gdpr_full_text.pdf",
            "regulation_id": 1
        }
        
        self.test_document_update_data = {
            "name": "Updated: GDPR Full Text",
            "description": "Updated version of the GDPR text with annotations",
            "document_type": "PDF"
        }

    @patch('app.api.regulation_documents.router.get_db')
    def test_get_regulation_documents(self, mock_get_db):
        """Test getting a list of regulation documents."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock query result
        mock_document = MagicMock()
        mock_document.id = 1
        mock_document.name = "GDPR Full Text"
        mock_document.description = "Complete text of the General Data Protection Regulation"
        mock_document.document_type = "PDF"
        mock_document.file_name = "gdpr_full_text.pdf"
        mock_document.file_size = 2048576
        mock_document.file_type = "application/pdf"
        mock_document.file_url = "https://example.com/documents/gdpr_full_text.pdf"
        mock_document.regulation_id = 1
        mock_document.created_at = "2023-01-01T00:00:00"
        mock_document.changed_on = "2023-01-01T00:00:00"
        
        # Create mock regulation
        mock_regulation = MagicMock()
        mock_regulation.id = 1
        mock_regulation.title = "Test Regulation"
        
        # Set up the relationship
        mock_document.regulation = mock_regulation
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [mock_document]
        
        # Make request
        response = client.get("/api/v1/regulation-documents/")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response.json(), list)
        self.assertEqual(len(response.json()), 1)
        self.assertEqual(response.json()[0]["name"], "GDPR Full Text")
        self.assertEqual(response.json()[0]["regulation_id"], 1)
        
        # Verify query was called correctly
        mock_db.query.assert_called_once_with(RegulationDocument)
        mock_query.offset.assert_called_once_with(0)
        mock_query.limit.assert_called_once_with(100)

    @patch('app.api.regulation_documents.router.get_db')
    def test_get_regulation_documents_with_filters(self, mock_get_db):
        """Test getting regulation documents with filters."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = []
        
        # Make request with filters
        response = client.get("/api/v1/regulation-documents/?regulation_id=1&document_type=PDF")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        
        # Verify filter was called twice (once for each filter)
        self.assertEqual(mock_query.filter.call_count, 2)

    @patch('app.api.regulation_documents.router.get_db')
    def test_get_regulation_document(self, mock_get_db):
        """Test getting a specific regulation document by ID."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock query result
        mock_document = MagicMock()
        mock_document.id = 1
        mock_document.name = "GDPR Full Text"
        mock_document.description = "Complete text of the General Data Protection Regulation"
        mock_document.document_type = "PDF"
        mock_document.file_name = "gdpr_full_text.pdf"
        mock_document.file_size = 2048576
        mock_document.file_type = "application/pdf"
        mock_document.file_url = "https://example.com/documents/gdpr_full_text.pdf"
        mock_document.regulation_id = 1
        mock_document.created_at = "2023-01-01T00:00:00"
        mock_document.changed_on = "2023-01-01T00:00:00"
        
        # Create mock regulation
        mock_regulation = MagicMock()
        mock_regulation.id = 1
        mock_regulation.title = "Test Regulation"
        
        # Set up the relationship
        mock_document.regulation = mock_regulation
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_document
        
        # Make request
        response = client.get("/api/v1/regulation-documents/1")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["name"], "GDPR Full Text")
        self.assertEqual(response.json()["regulation_id"], 1)
        
        # Verify query was called correctly
        mock_db.query.assert_called_once_with(RegulationDocument)
        mock_query.filter.assert_called_once()
        mock_query.first.assert_called_once()

    @patch('app.api.regulation_documents.router.get_db')
    def test_get_regulation_document_not_found(self, mock_get_db):
        """Test getting a non-existent regulation document."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain to return None
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        
        # Make request
        response = client.get("/api/v1/regulation-documents/999")
        
        # Check response
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.json()["detail"], "Regulation document not found")

    @patch('app.api.regulation_documents.router.get_db')
    def test_create_regulation_document(self, mock_get_db):
        """Test creating a new regulation document."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Mock the regulation query
        mock_regulation = MagicMock()
        mock_regulation.id = 1
        
        # Set up the mock query chain for checking existing regulation
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_regulation
        
        # Create mock document object that will be returned
        mock_document = MagicMock()
        mock_document.id = 1
        mock_document.name = self.test_document_data["name"]
        mock_document.description = self.test_document_data["description"]
        mock_document.document_type = self.test_document_data["document_type"]
        mock_document.file_name = self.test_document_data["file_name"]
        mock_document.file_size = self.test_document_data["file_size"]
        mock_document.file_type = self.test_document_data["file_type"]
        mock_document.file_url = self.test_document_data["file_url"]
        mock_document.regulation_id = self.test_document_data["regulation_id"]
        mock_document.created_at = "2023-01-01T00:00:00"
        mock_document.changed_on = "2023-01-01T00:00:00"
        mock_document.regulation = mock_regulation
        
        # Mock the add, commit, and refresh methods
        mock_db.add = MagicMock()
        mock_db.commit = MagicMock()
        mock_db.refresh = MagicMock()
        
        # Make the RegulationDocument constructor return our mock
        with patch('app.api.regulation_documents.router.RegulationDocument', return_value=mock_document):
            # Make request
            response = client.post("/api/v1/regulation-documents/", json=self.test_document_data)
            
            # Check response
            self.assertEqual(response.status_code, 201)
            self.assertEqual(response.json()["name"], self.test_document_data["name"])
            self.assertEqual(response.json()["regulation_id"], self.test_document_data["regulation_id"])
            
            # Verify methods were called
            mock_db.add.assert_called_once()
            mock_db.commit.assert_called_once()
            mock_db.refresh.assert_called_once()

    @patch('app.api.regulation_documents.router.get_db')
    def test_create_regulation_document_regulation_not_found(self, mock_get_db):
        """Test creating a regulation document with non-existent regulation."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain to return None (regulation not found)
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        
        # Make request
        response = client.post("/api/v1/regulation-documents/", json=self.test_document_data)
        
        # Check response
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.json()["detail"], "Regulation not found")

    @patch('app.api.regulation_documents.router.get_db')
    def test_update_regulation_document(self, mock_get_db):
        """Test updating a regulation document."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock document to update
        mock_document = MagicMock()
        mock_document.id = 1
        mock_document.name = "GDPR Full Text"
        mock_document.description = "Original description"
        mock_document.document_type = "Original type"
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_document
        
        # Make request
        response = client.put("/api/v1/regulation-documents/1", json=self.test_document_update_data)
        
        # Check response
        self.assertEqual(response.status_code, 200)
        
        # Verify setattr was called for each field
        self.assertEqual(mock_document.name, self.test_document_update_data["name"])
        self.assertEqual(mock_document.description, self.test_document_update_data["description"])
        self.assertEqual(mock_document.document_type, self.test_document_update_data["document_type"])
        
        # Verify commit and refresh were called
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()

    @patch('app.api.regulation_documents.router.get_db')
    def test_update_regulation_document_not_found(self, mock_get_db):
        """Test updating a non-existent regulation document."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain to return None
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        
        # Make request
        response = client.put("/api/v1/regulation-documents/999", json=self.test_document_update_data)
        
        # Check response
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.json()["detail"], "Regulation document not found")

    @patch('app.api.regulation_documents.router.get_db')
    def test_delete_regulation_document(self, mock_get_db):
        """Test deleting a regulation document."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock document to delete
        mock_document = MagicMock()
        mock_document.id = 1
        mock_document.name = "GDPR Full Text"
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_document
        
        # Make request
        response = client.delete("/api/v1/regulation-documents/1")
        
        # Check response
        self.assertEqual(response.status_code, 204)
        
        # Verify delete, commit were called
        mock_db.delete.assert_called_once_with(mock_document)
        mock_db.commit.assert_called_once()

    @patch('app.api.regulation_documents.router.get_db')
    def test_delete_regulation_document_not_found(self, mock_get_db):
        """Test deleting a non-existent regulation document."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain to return None
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        
        # Make request
        response = client.delete("/api/v1/regulation-documents/999")
        
        # Check response
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.json()["detail"], "Regulation document not found")


if __name__ == "__main__":
    unittest.main()
