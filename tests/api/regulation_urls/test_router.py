"""
Tests for the regulation URL management API router.
"""
import unittest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.main import app
from app.db.models import RegulationURL, Regulator, Country
from app.schemas.schemas import RegulationURLCreate, RegulationURLUpdate

client = TestClient(app)

class TestRegulationURLRouter(unittest.TestCase):
    """Tests for the regulation URL management API router."""

    def setUp(self):
        """Set up test data."""
        self.test_url_data = {
            "url": "https://www.test-regulator.com/rules/test-rule.pdf",
            "category": "Test Category",
            "regulator_id": 1,
            "country_id": 1,
            "title": "Test Regulation",
            "confidence_level": 0.8
        }
        
        self.test_url_update_data = {
            "title": "Updated Test Regulation",
            "category": "Updated Category",
            "confidence_level": 0.9
        }

    @patch('app.api.regulation_urls.router.get_db')
    def test_get_regulation_urls(self, mock_get_db):
        """Test getting a list of regulation URLs."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock query result
        mock_url = MagicMock()
        mock_url.id = 1
        mock_url.url = "https://www.test-regulator.com/rules/test-rule.pdf"
        mock_url.domain = "test-regulator.com"
        mock_url.category = "Test Category"
        mock_url.regulator_id = 1
        mock_url.country_id = 1
        mock_url.title = "Test Regulation"
        mock_url.confidence_level = 0.8
        mock_url.created_at = "2023-01-01T00:00:00"
        mock_url.changed_on = "2023-01-01T00:00:00"
        
        # Create mock regulator for the relationship
        mock_regulator = MagicMock()
        mock_regulator.id = 1
        mock_regulator.name = "Test Regulator"
        mock_regulator.country_id = 1
        
        # Create mock country for the relationship
        mock_country = MagicMock()
        mock_country.id = 1
        mock_country.name = "Test Country"
        mock_country.code = "TC"
        
        # Set up the relationships
        mock_regulator.country = mock_country
        mock_url.regulator = mock_regulator
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [mock_url]
        
        # Make request
        response = client.get("/api/v1/regulation-urls/")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response.json(), list)
        self.assertEqual(len(response.json()), 1)
        self.assertEqual(response.json()[0]["url"], "https://www.test-regulator.com/rules/test-rule.pdf")
        self.assertEqual(response.json()[0]["regulator_id"], 1)
        
        # Verify query was called correctly
        mock_db.query.assert_called_once_with(RegulationURL)
        mock_query.offset.assert_called_once_with(0)
        mock_query.limit.assert_called_once_with(100)

    @patch('app.api.regulation_urls.router.get_db')
    def test_get_regulation_urls_with_filters(self, mock_get_db):
        """Test getting regulation URLs with filters."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = []
        
        # Make request with filters
        response = client.get("/api/v1/regulation-urls/?regulator_id=1&category=Financial")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        
        # Verify filter was called twice (once for each filter)
        self.assertEqual(mock_query.filter.call_count, 2)

    @patch('app.api.regulation_urls.router.get_db')
    def test_get_regulation_url(self, mock_get_db):
        """Test getting a specific regulation URL by ID."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock query result
        mock_url = MagicMock()
        mock_url.id = 1
        mock_url.url = "https://www.test-regulator.com/rules/test-rule.pdf"
        mock_url.domain = "test-regulator.com"
        mock_url.category = "Test Category"
        mock_url.regulator_id = 1
        mock_url.country_id = 1
        mock_url.title = "Test Regulation"
        mock_url.confidence_level = 0.8
        mock_url.created_at = "2023-01-01T00:00:00"
        mock_url.changed_on = "2023-01-01T00:00:00"
        
        # Create mock regulator for the relationship
        mock_regulator = MagicMock()
        mock_regulator.id = 1
        mock_regulator.name = "Test Regulator"
        mock_regulator.country_id = 1
        
        # Create mock country for the relationship
        mock_country = MagicMock()
        mock_country.id = 1
        mock_country.name = "Test Country"
        mock_country.code = "TC"
        
        # Set up the relationships
        mock_regulator.country = mock_country
        mock_url.regulator = mock_regulator
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_url
        
        # Make request
        response = client.get("/api/v1/regulation-urls/1")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["url"], "https://www.test-regulator.com/rules/test-rule.pdf")
        self.assertEqual(response.json()["regulator_id"], 1)
        
        # Verify query was called correctly
        mock_db.query.assert_called_once_with(RegulationURL)
        mock_query.filter.assert_called_once()
        mock_query.first.assert_called_once()

    @patch('app.api.regulation_urls.router.get_db')
    def test_get_regulation_url_not_found(self, mock_get_db):
        """Test getting a non-existent regulation URL."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Set up the mock query chain to return None
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        
        # Make request
        response = client.get("/api/v1/regulation-urls/999")
        
        # Check response
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.json()["detail"], "Regulation URL not found")

    @patch('app.api.regulation_urls.router.get_db')
    def test_create_regulation_url(self, mock_get_db):
        """Test creating a new regulation URL."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Mock the regulator and country queries
        mock_regulator = MagicMock()
        mock_regulator.id = 1
        mock_regulator.name = "Test Regulator"
        
        mock_country = MagicMock()
        mock_country.id = 1
        mock_country.name = "Test Country"
        
        # Set up the mock query chain for checking existing URL, regulator, and country
        mock_query = MagicMock()
        mock_db.query.side_effect = [mock_query, mock_query, mock_query]
        mock_query.filter.return_value = mock_query
        mock_query.first.side_effect = [None, mock_regulator, mock_country]  # No existing URL, then regulator, then country
        
        # Create mock URL object that will be returned
        mock_url = MagicMock()
        mock_url.id = 1
        mock_url.url = self.test_url_data["url"]
        mock_url.domain = "test-regulator.com"
        mock_url.category = self.test_url_data["category"]
        mock_url.regulator_id = self.test_url_data["regulator_id"]
        mock_url.country_id = self.test_url_data["country_id"]
        mock_url.title = self.test_url_data["title"]
        mock_url.confidence_level = self.test_url_data["confidence_level"]
        mock_url.created_at = "2023-01-01T00:00:00"
        mock_url.changed_on = "2023-01-01T00:00:00"
        mock_url.regulator = mock_regulator
        
        # Mock the add, commit, and refresh methods
        mock_db.add = MagicMock()
        mock_db.commit = MagicMock()
        mock_db.refresh = MagicMock()
        
        # Make the RegulationURL constructor return our mock
        with patch('app.api.regulation_urls.router.RegulationURL', return_value=mock_url):
            # Make request
            response = client.post("/api/v1/regulation-urls/", json=self.test_url_data)
            
            # Check response
            self.assertEqual(response.status_code, 201)
            self.assertEqual(response.json()["url"], self.test_url_data["url"])
            self.assertEqual(response.json()["regulator_id"], self.test_url_data["regulator_id"])
            
            # Verify methods were called
            mock_db.add.assert_called_once()
            mock_db.commit.assert_called_once()
            mock_db.refresh.assert_called_once()

    @patch('app.api.regulation_urls.router.get_db')
    def test_create_regulation_url_duplicate(self, mock_get_db):
        """Test creating a regulation URL with a duplicate URL."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock existing URL
        mock_existing_url = MagicMock()
        mock_existing_url.id = 1
        mock_existing_url.url = self.test_url_data["url"]
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_existing_url
        
        # Make request
        response = client.post("/api/v1/regulation-urls/", json=self.test_url_data)
        
        # Check response
        self.assertEqual(response.status_code, 400)
        self.assertIn("already exists", response.json()["detail"])

    @patch('app.api.regulation_urls.router.get_db')
    def test_update_regulation_url(self, mock_get_db):
        """Test updating a regulation URL."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock URL to update
        mock_url = MagicMock()
        mock_url.id = 1
        mock_url.url = "https://www.test-regulator.com/rules/test-rule.pdf"
        mock_url.title = "Test Regulation"
        mock_url.category = "Test Category"
        mock_url.confidence_level = 0.8
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_url
        
        # Make request
        response = client.put("/api/v1/regulation-urls/1", json=self.test_url_update_data)
        
        # Check response
        self.assertEqual(response.status_code, 200)
        
        # Verify setattr was called for each field
        self.assertEqual(mock_url.title, self.test_url_update_data["title"])
        self.assertEqual(mock_url.category, self.test_url_update_data["category"])
        self.assertEqual(mock_url.confidence_level, self.test_url_update_data["confidence_level"])
        
        # Verify commit and refresh were called
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()

    @patch('app.api.regulation_urls.router.get_db')
    def test_delete_regulation_url(self, mock_get_db):
        """Test deleting a regulation URL."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock URL to delete
        mock_url = MagicMock()
        mock_url.id = 1
        mock_url.url = "https://www.test-regulator.com/rules/test-rule.pdf"
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_url
        
        # Make request
        response = client.delete("/api/v1/regulation-urls/1")
        
        # Check response
        self.assertEqual(response.status_code, 204)
        
        # Verify delete, commit were called
        mock_db.delete.assert_called_once_with(mock_url)
        mock_db.commit.assert_called_once()


if __name__ == "__main__":
    unittest.main()
