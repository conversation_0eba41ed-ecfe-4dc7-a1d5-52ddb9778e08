import pytest
from fastapi.testclient import TestClient
import json
import os
import sys
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta

# Add root directory to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from app.main import app
from app.db.models import Country, Regulator, RegulationURL, RegulationText

client = TestClient(app)

class TestAPIComprehensive:
    """Comprehensive tests for API endpoints."""

    @patch('app.db.database.get_db')
    def test_countries_endpoint(self, mock_get_db):
        """Test the countries endpoint with filtering and pagination."""
        # Setup mock session
        mock_session = MagicMock()
        mock_get_db.return_value.__enter__.return_value = mock_session

        # Setup mock countries
        mock_countries = [
            MagicMock(id=1, name="United States", code="US", 
                     to_dict=lambda: {"id": 1, "name": "United States", "code": "US"}),
            MagicMock(id=2, name="United Kingdom", code="GB",
                     to_dict=lambda: {"id": 2, "name": "United Kingdom", "code": "GB"}),
            MagicMock(id=3, name="Canada", code="CA",
                     to_dict=lambda: {"id": 3, "name": "Canada", "code": "CA"})
        ]

        # Setup query mock
        mock_query = MagicMock()
        mock_session.query.return_value = mock_query
        mock_query.order_by.return_value.offset.return_value.limit.return_value.all.return_value = mock_countries
        mock_query.count.return_value = 3

        # Test basic endpoint
        response = client.get("/api/v1/countries")
        assert response.status_code == 200
        assert len(response.json()) == 3

        # Test pagination
        response = client.get("/api/v1/countries?skip=1&limit=1")
        mock_query.order_by.return_value.offset.assert_called_with(1)
        mock_query.order_by.return_value.offset.return_value.limit.assert_called_with(1)

    @patch('app.db.database.get_db')
    def test_search_endpoint(self, mock_get_db):
        """Test the search endpoint with various parameters."""
        # Setup mock session
        mock_session = MagicMock()
        mock_get_db.return_value.__enter__.return_value = mock_session

        # Setup mock search results
        mock_results = [
            {"id": 1, "title": "Data Protection Regulation", "score": 0.95, "type": "regulation"},
            {"id": 5, "name": "Data Protection Authority", "score": 0.85, "type": "regulator"}
        ]

        # Mock the search function
        mock_search_func = MagicMock(return_value=mock_results)
        # Inject the mock into the app state
        with patch('app.api.search.search_all_entities', mock_search_func):
            # Test search endpoint
            response = client.get("/api/v1/search?q=data+protection&limit=10")

            # Verify response
            assert response.status_code == 200
            assert len(response.json()["results"]) > 0
            assert "metadata" in response.json()

            # Verify search was called with correct parameters
            mock_search_func.assert_called_once()
            args, kwargs = mock_search_func.call_args
            assert "data protection" in args[0]
            assert kwargs.get("limit") == 10

    @patch('app.db.database.get_db')
    def test_regulation_text_endpoint(self, mock_get_db):
        """Test the regulation text endpoint."""
        # Setup mock session
        mock_session = MagicMock()
        mock_get_db.return_value.__enter__.return_value = mock_session

        # Setup mock regulation text
        mock_text = MagicMock(
            id=1, 
            url_id=1,
            content="This is regulation content.",
            extracted_at="2023-01-01T12:00:00",
            to_dict=lambda: {
                "id": 1,
                "url_id": 1,
                "content": "This is regulation content.",
                "extracted_at": "2023-01-01T12:00:00"
            }
        )
        mock_session.query.return_value.filter.return_value.first.return_value = mock_text

        # Test endpoint
        response = client.get("/api/v1/regulation-texts/1")
        assert response.status_code == 200
        assert response.json()["content"] == "This is regulation content."

    @patch('app.db.database.get_db')
    def test_bulk_operations(self, mock_get_db):
        """Test bulk operations endpoints."""
        # Setup mock session
        mock_session = MagicMock()
        mock_get_db.return_value.__enter__.return_value = mock_session

        # Test bulk create
        bulk_data = [
            {"name": "Regulator 1", "country_id": 1},
            {"name": "Regulator 2", "country_id": 1}
        ]

        # Mock bulk insertion
        mock_session.bulk_insert_mappings = MagicMock()
        mock_session.commit = MagicMock()

        # Make request
        response = client.post("/api/v1/regulators/bulk", json=bulk_data)

        # Verify response and that bulk_insert_mappings was called
        assert response.status_code == 201
        assert mock_session.bulk_insert_mappings.called
        assert mock_session.commit.called

    @patch('app.db.database.get_db')
    def test_complex_filtering(self, mock_get_db):
        """Test complex filtering on API endpoints."""
        # Setup mock session
        mock_session = MagicMock()
        mock_get_db.return_value.__enter__.return_value = mock_session

        # Setup mock query building
        mock_query = MagicMock()
        mock_filter = MagicMock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_filter

        # Mock results
        mock_results = [
            MagicMock(id=1, name="Result 1", to_dict=lambda: {"id": 1, "name": "Result 1"}),
            MagicMock(id=2, name="Result 2", to_dict=lambda: {"id": 2, "name": "Result 2"})
        ]
        mock_filter.all.return_value = mock_results

        # Test filtering endpoint
        response = client.get("/api/v1/regulations?country_id=1&year=2023&status=active")

        # Verify filter was called and response is correct
        assert response.status_code == 200
        assert len(response.json()) > 0


@pytest.mark.api
class TestComprehensiveAPIFlow:
    """Test comprehensive API workflows."""

    def test_api_flow(self):
        """Test an end-to-end API workflow."""
        # This would typically test a series of API calls that depend on each other
        client = TestClient(app)

        # Step 1: Get health
        health_response = client.get("/health")
        assert health_response.status_code == 200

        # Step 2: Get countries
        countries_response = client.get("/api/v1/countries")
        assert countries_response.status_code == 200
        # More assertions would follow

    def test_regulatory_analysis_flow(self):
        """Test a comprehensive regulatory analysis workflow."""
        client = TestClient(app)

        # Step 1: Get compliance summary
        compliance_response = client.get("/api/v1/compliance/summary")
        assert compliance_response.status_code == 200
        compliance_data = compliance_response.json()
        assert "overall_score" in compliance_data

        # Step 2: Get recent alerts (mock response for demonstration)
        alerts_response = client.get("/api/v1/alerts/recent")
        assert alerts_response.status_code == 200
        alerts_data = alerts_response.json() # This would be a mock response in a real test
        assert isinstance(alerts_data, list)

        # Step 3: Analyze trends based on regions from compliance data (mock response for demonstration)
        regions = [r["region"] for r in compliance_data["regional_scores"]]
        trend_request = {
            "start_date": (datetime.now() - timedelta(days=90)).isoformat(),
            "end_date": datetime.now().isoformat(),
            "regions": regions[:2]  # Use first two regions
        }

        trend_response = client.post("/api/v1/trends/analyze", json=trend_request)
        assert trend_response.status_code == 200
        trend_data = trend_response.json() # This would be a mock response in a real test
        assert "trends" in trend_data

        # Step 4: Perform impact assessment for highest impact alert if any (mock response for demonstration)
        if alerts_data:
            sorted_alerts = sorted(alerts_data, key=lambda x: x["importance"], reverse=True)
            highest_impact = sorted_alerts[0]

            assessment_input = {
                "regulation_url_id": highest_impact["id"],
                "business_processes": [
                    {"name": "Customer Onboarding", "importance": 0.8},
                    {"name": "Data Processing", "importance": 0.9}
                ],
                "company_size": "medium",
                "industry": "Financial Services",
                "geographical_regions": ["US", "EU"],
                "data_types_processed": ["PII", "Financial"],
                "existing_compliance_level": 7.5
            }

            assessment_response = client.post("/api/v1/impact/assess", json=assessment_input)
            assert assessment_response.status_code == 200
            assessment_data = assessment_response.json()
            assert "impact_score" in assessment_data