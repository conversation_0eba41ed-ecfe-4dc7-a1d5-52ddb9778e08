"""
Tests for authentication.
"""
import pytest
from fastapi.testclient import <PERSON><PERSON><PERSON>
from unittest.mock import MagicMock, patch
from datetime import datetime, timedelta

from app.main import app
from app.auth.jwt import create_access_token, decode_token, get_current_user
from app.auth.password import verify_password, get_password_hash
from app.db.models import User

client = TestClient(app)

def test_create_access_token():
    """Test creating an access token."""
    # Create a token with a test payload
    payload = {"sub": "<EMAIL>", "roles": ["user"]}
    token = create_access_token(payload)
    
    # Check that the token is a string
    assert isinstance(token, str)
    
    # Decode the token and check the payload
    decoded = decode_token(token)
    assert decoded["sub"] == "<EMAIL>"
    assert decoded["roles"] == ["user"]
    
    # Check that the token has an expiration time
    assert "exp" in decoded
    assert decoded["exp"] > datetime.utcnow().timestamp()

def test_decode_token():
    """Test decoding an access token."""
    # Create a token with a test payload
    payload = {"sub": "<EMAIL>", "roles": ["user"]}
    token = create_access_token(payload)
    
    # Decode the token
    decoded = decode_token(token)
    
    # Check the payload
    assert decoded["sub"] == "<EMAIL>"
    assert decoded["roles"] == ["user"]
    
    # Test with an invalid token
    with pytest.raises(Exception):
        decode_token("invalid.token")
    
    # Test with an expired token
    with patch("app.auth.jwt.datetime") as mock_datetime:
        # Set the current time to after the token expiration
        mock_datetime.utcnow.return_value = datetime.utcnow() + timedelta(days=2)
        
        # Try to decode the token
        with pytest.raises(Exception):
            decode_token(token)

def test_get_current_user():
    """Test getting the current user from a token."""
    # Create a mock user
    mock_user = MagicMock()
    mock_user.email = "<EMAIL>"
    mock_user.roles = ["user"]
    
    # Mock the get_db dependency
    mock_db = MagicMock()
    mock_db.query.return_value.filter.return_value.first.return_value = mock_user
    
    # Create a token for the user
    token = create_access_token({"sub": "<EMAIL>", "roles": ["user"]})
    
    # Get the current user
    with patch("app.auth.jwt.get_db", return_value=mock_db):
        user = get_current_user(token)
        
        # Check that the user is returned
        assert user == mock_user
        
        # Check that the database was queried correctly
        mock_db.query.assert_called_once_with(User)
        mock_db.query.return_value.filter.assert_called_once()
    
    # Test with an invalid token
    with pytest.raises(Exception):
        get_current_user("invalid.token")
    
    # Test with a valid token but non-existent user
    mock_db.query.return_value.filter.return_value.first.return_value = None
    with patch("app.auth.jwt.get_db", return_value=mock_db):
        with pytest.raises(Exception):
            get_current_user(token)

def test_verify_password():
    """Test password verification."""
    # Create a password hash
    password = "password123"
    hashed_password = get_password_hash(password)
    
    # Verify the password
    assert verify_password(password, hashed_password) is True
    
    # Verify with incorrect password
    assert verify_password("wrong_password", hashed_password) is False
    
    # Verify with empty password
    assert verify_password("", hashed_password) is False
    
    # Verify with None password
    assert verify_password(None, hashed_password) is False

def test_get_password_hash():
    """Test password hashing."""
    # Create a password hash
    password = "password123"
    hashed_password = get_password_hash(password)
    
    # Check that the hash is a string
    assert isinstance(hashed_password, str)
    
    # Check that the hash is not the same as the password
    assert hashed_password != password
    
    # Check that the hash can be verified
    assert verify_password(password, hashed_password) is True
    
    # Check that different passwords produce different hashes
    another_password = "another_password"
    another_hash = get_password_hash(another_password)
    assert hashed_password != another_hash

def test_login_endpoint():
    """Test the login endpoint."""
    # Create a mock user
    mock_user = MagicMock()
    mock_user.email = "<EMAIL>"
    mock_user.hashed_password = get_password_hash("password123")
    mock_user.roles = ["user"]
    mock_user.is_active = True
    
    # Mock the get_db dependency
    mock_db = MagicMock()
    mock_db.query.return_value.filter.return_value.first.return_value = mock_user
    
    # Mock the dependencies
    with patch("app.auth.router.get_db", return_value=mock_db):
        # Make a login request
        response = client.post(
            "/api/v1/auth/token",
            data={"username": "<EMAIL>", "password": "password123"}
        )
        
        # Check the response
        assert response.status_code == 200
        assert "access_token" in response.json()
        assert response.json()["token_type"] == "bearer"
        
        # Test with incorrect password
        response = client.post(
            "/api/v1/auth/token",
            data={"username": "<EMAIL>", "password": "wrong_password"}
        )
        assert response.status_code == 401
        assert "detail" in response.json()
        assert "Incorrect username or password" in response.json()["detail"]
        
        # Test with non-existent user
        mock_db.query.return_value.filter.return_value.first.return_value = None
        response = client.post(
            "/api/v1/auth/token",
            data={"username": "<EMAIL>", "password": "password123"}
        )
        assert response.status_code == 401
        assert "detail" in response.json()
        assert "Incorrect username or password" in response.json()["detail"]
        
        # Test with inactive user
        mock_user.is_active = False
        mock_db.query.return_value.filter.return_value.first.return_value = mock_user
        response = client.post(
            "/api/v1/auth/token",
            data={"username": "<EMAIL>", "password": "password123"}
        )
        assert response.status_code == 401
        assert "detail" in response.json()
        assert "Inactive user" in response.json()["detail"]
