
import pytest
from unittest.mock import patch, MagicMock
import sys
import os

# Add app directory to path if needed
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.visualization.worldmap import (
    create_world_map,
    add_country_data,
    generate_color_scale,
    save_map
)

class TestWorldMapVisualization:
    @patch('app.visualization.worldmap.folium.Map')
    def test_create_world_map(self, mock_map):
        # Setup mock
        mock_map_instance = MagicMock()
        mock_map.return_value = mock_map_instance
        
        # Test map creation
        result = create_world_map()
        
        # Verify map was created with correct parameters
        mock_map.assert_called_once()
        assert result == mock_map_instance
    
    @patch('app.visualization.worldmap.folium.Choropleth')
    def test_add_country_data(self, mock_choropleth):
        # Setup mocks
        mock_map = MagicMock()
        mock_choropleth_instance = MagicMock()
        mock_choropleth.return_value = mock_choropleth_instance
        
        # Test data
        country_data = {
            'USA': 95,
            'CAN': 87,
            'GBR': 92,
            'DEU': 88,
            'FRA': 85
        }
        
        # Call function
        result = add_country_data(mock_map, country_data)
        
        # Verify choropleth was created
        mock_choropleth.assert_called_once()
        
        # Verify the choropleth was added to the map
        mock_map.add_child.assert_called_once_with(mock_choropleth_instance)
        
        # Verify result
        assert result == mock_map
    
    def test_generate_color_scale(self):
        # Test with default range
        colors = generate_color_scale()
        assert len(colors) > 0
        
        # Test with custom range
        colors = generate_color_scale(5)
        assert len(colors) == 5
    
    @patch('app.visualization.worldmap.os.path.exists')
    @patch('app.visualization.worldmap.os.makedirs')
    def test_save_map(self, mock_makedirs, mock_exists):
        # Setup mocks
        mock_map = MagicMock()
        mock_exists.return_value = False
        
        # Call function
        save_map(mock_map, "test_map.html")
        
        # Verify directory was created if it didn't exist
        mock_makedirs.assert_called_once()
        
        # Verify map was saved
        mock_map.save.assert_called_once_with("test_map.html")
        
    @patch('app.visualization.worldmap.os.path.exists')
    def test_save_map_existing_dir(self, mock_exists):
        # Setup mocks
        mock_map = MagicMock()
        mock_exists.return_value = True
        
        # Call function
        save_map(mock_map, "test_map.html")
        
        # Verify map was saved
        mock_map.save.assert_called_once_with("test_map.html")
