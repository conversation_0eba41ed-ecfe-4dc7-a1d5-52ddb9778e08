
import pytest
from unittest.mock import patch, MagicMock, mock_open
import sys
import os
import pandas as pd
import numpy as np

# Add app directory to path if needed
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from scripts.analyze_regulatory_results import (
    load_regulatory_data,
    calculate_compliance_scores,
    identify_critical_gaps,
    generate_recommendations,
    calculate_regional_averages,
    export_analysis_results,
    run_analysis
)

@pytest.fixture
def sample_regulatory_data():
    """Provide sample regulatory data for testing."""
    return pd.DataFrame({
        'country': ['US', 'UK', 'EU', 'JP', 'CA'],
        'data_protection_score': [85, 90, 95, 80, 88],
        'breach_notification_score': [80, 85, 90, 75, 82],
        'penalties_score': [70, 80, 95, 65, 75],
        'overall_compliance': [78, 85, 93, 73, 82]
    })

def test_load_regulatory_data():
    """Test loading regulatory data from CSV."""
    mock_df = pd.DataFrame({
        'country': ['US', 'UK'],
        'data_protection_score': [85, 90],
        'breach_notification_score': [80, 85],
        'penalties_score': [70, 80],
        'overall_compliance': [78, 85]
    })
    
    # Mock pd.read_csv to return our sample dataframe
    with patch('pandas.read_csv', return_value=mock_df):
        result = load_regulatory_data('fake_path.csv')
        
        # Check the result
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 2
        assert 'country' in result.columns
        assert 'overall_compliance' in result.columns

def test_calculate_compliance_scores(sample_regulatory_data):
    """Test calculation of compliance scores."""
    result = calculate_compliance_scores(sample_regulatory_data)
    
    # Check that the result has the expected columns
    assert 'weighted_compliance_score' in result.columns
    
    # Check that the scores are calculated correctly
    assert all(result['weighted_compliance_score'] <= 100)
    assert all(result['weighted_compliance_score'] >= 0)

def test_identify_critical_gaps(sample_regulatory_data):
    """Test identification of critical compliance gaps."""
    result = identify_critical_gaps(sample_regulatory_data, threshold=80)
    
    # Check the structure of the result
    assert isinstance(result, dict)
    assert 'countries_with_gaps' in result
    assert 'gap_categories' in result
    
    # There should be at least one country with a gap
    countries_with_gaps = result['countries_with_gaps']
    assert len(countries_with_gaps) > 0
    
    # JP should be in the countries with gaps based on our sample data
    assert 'JP' in countries_with_gaps

def test_generate_recommendations(sample_regulatory_data):
    """Test generation of regulatory compliance recommendations."""
    # First identify gaps
    gaps = identify_critical_gaps(sample_regulatory_data, threshold=80)
    
    # Then generate recommendations
    result = generate_recommendations(sample_regulatory_data, gaps)
    
    # Check the structure of the result
    assert isinstance(result, dict)
    assert 'country_recommendations' in result
    
    # There should be recommendations for countries with gaps
    country_recommendations = result['country_recommendations']
    assert len(country_recommendations) > 0
    
    # Check that recommendations exist for Japan (JP)
    assert 'JP' in country_recommendations

def test_calculate_regional_averages(sample_regulatory_data):
    """Test calculation of regional compliance averages."""
    # Add a region column to our sample data
    data = sample_regulatory_data.copy()
    data['region'] = ['Americas', 'Europe', 'Europe', 'Asia', 'Americas']
    
    result = calculate_regional_averages(data)
    
    # Check the structure of the result
    assert isinstance(result, pd.DataFrame)
    assert 'region' in result.columns
    assert 'avg_overall_compliance' in result.columns
    
    # There should be 3 regions
    assert len(result) == 3
    
    # Check that Europe has the highest average compliance
    europe_row = result[result['region'] == 'Europe']
    assert europe_row['avg_overall_compliance'].values[0] > result['avg_overall_compliance'].max() * 0.8

def test_export_analysis_results():
    """Test exporting analysis results to CSV."""
    # Create sample results
    results = {
        'compliance_scores': pd.DataFrame({
            'country': ['US', 'UK'],
            'weighted_compliance_score': [80, 90]
        }),
        'critical_gaps': {
            'countries_with_gaps': ['US'],
            'gap_categories': {'US': ['penalties']}
        },
        'recommendations': {
            'country_recommendations': {
                'US': ['Improve penalty enforcement mechanisms']
            }
        },
        'regional_averages': pd.DataFrame({
            'region': ['Americas', 'Europe'],
            'avg_overall_compliance': [80, 90]
        })
    }
    
    # Mock open and pd.DataFrame.to_csv
    with patch('builtins.open', mock_open()) as mock_file:
        with patch.object(pd.DataFrame, 'to_csv') as mock_to_csv:
            export_analysis_results(results, 'output_dir')
            
            # Check that files were written
            assert mock_to_csv.call_count == 2
            assert mock_file.call_count > 0

@patch('scripts.analyze_regulatory_results.load_regulatory_data')
@patch('scripts.analyze_regulatory_results.calculate_compliance_scores')
@patch('scripts.analyze_regulatory_results.identify_critical_gaps')
@patch('scripts.analyze_regulatory_results.generate_recommendations')
@patch('scripts.analyze_regulatory_results.calculate_regional_averages')
@patch('scripts.analyze_regulatory_results.export_analysis_results')
def test_run_analysis(mock_export, mock_regional, mock_recommendations, 
                     mock_gaps, mock_scores, mock_load):
    """Test the main analysis workflow."""
    # Configure mocks
    mock_data = MagicMock()
    mock_load.return_value = mock_data
    mock_scores.return_value = mock_data
    mock_gaps.return_value = {'countries_with_gaps': ['US'], 'gap_categories': {}}
    mock_recommendations.return_value = {'country_recommendations': {}}
    mock_regional.return_value = mock_data
    
    # Run the analysis
    run_analysis('input.csv', 'output_dir')
    
    # Verify all functions were called with correct arguments
    mock_load.assert_called_once_with('input.csv')
    mock_scores.assert_called_once_with(mock_data)
    mock_gaps.assert_called_once()
    mock_recommendations.assert_called_once()
    mock_regional.assert_called_once_with(mock_data)
    mock_export.assert_called_once()
