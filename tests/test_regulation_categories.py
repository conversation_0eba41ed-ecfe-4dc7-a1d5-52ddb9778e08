"""
Tests for the regulation categories API.
"""
import pytest
from fastapi.testclient import TestClient

from app.main import app

client = TestClient(app)

def test_get_regulation_categories(client, test_data):
    """Test getting a list of regulation categories."""
    response = client.get("/api/v1/regulation-categories/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) > 0
    assert response.json()[0]["name"] == test_data["category"].name

def test_get_regulation_categories_with_search(client, test_data):
    """Test getting regulation categories with search filter."""
    response = client.get("/api/v1/regulation-categories/?search=Test")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) > 0
    assert response.json()[0]["name"] == test_data["category"].name

def test_get_regulation_category(client, test_data):
    """Test getting a specific regulation category by ID."""
    response = client.get(f"/api/v1/regulation-categories/{test_data['category'].id}")
    assert response.status_code == 200
    assert response.json()["name"] == test_data["category"].name
    assert response.json()["description"] == test_data["category"].description
    assert response.json()["icon"] == test_data["category"].icon

def test_get_regulation_category_not_found(client):
    """Test getting a non-existent regulation category."""
    response = client.get("/api/v1/regulation-categories/999")
    assert response.status_code == 404
    assert response.json()["detail"] == "Regulation category not found"

def test_create_regulation_category(client):
    """Test creating a new regulation category."""
    category_data = {
        "name": "New Test Category",
        "description": "New test category description",
        "icon": "bank"
    }
    response = client.post("/api/v1/regulation-categories/", json=category_data)
    assert response.status_code == 201
    assert response.json()["name"] == category_data["name"]
    assert response.json()["description"] == category_data["description"]
    assert response.json()["icon"] == category_data["icon"]

def test_create_regulation_category_duplicate_name(client, test_data):
    """Test creating a regulation category with a duplicate name."""
    category_data = {
        "name": test_data["category"].name,
        "description": "Duplicate category description",
        "icon": "bank"
    }
    response = client.post("/api/v1/regulation-categories/", json=category_data)
    assert response.status_code == 400
    assert "already exists" in response.json()["detail"]

def test_update_regulation_category(client, test_data):
    """Test updating a regulation category."""
    category_data = {
        "name": "Updated Test Category",
        "description": "Updated test category description",
        "icon": "global"
    }
    response = client.put(f"/api/v1/regulation-categories/{test_data['category'].id}", json=category_data)
    assert response.status_code == 200
    assert response.json()["name"] == category_data["name"]
    assert response.json()["description"] == category_data["description"]
    assert response.json()["icon"] == category_data["icon"]

def test_update_regulation_category_not_found(client):
    """Test updating a non-existent regulation category."""
    category_data = {
        "name": "Updated Test Category",
        "description": "Updated test category description",
        "icon": "global"
    }
    response = client.put("/api/v1/regulation-categories/999", json=category_data)
    assert response.status_code == 404
    assert response.json()["detail"] == "Regulation category not found"

def test_update_regulation_category_duplicate_name(client, test_data):
    """Test updating a regulation category with a duplicate name."""
    # Create a new category first
    new_category_data = {
        "name": "Another Test Category",
        "description": "Another test category description",
        "icon": "bank"
    }
    new_category_response = client.post("/api/v1/regulation-categories/", json=new_category_data)
    assert new_category_response.status_code == 201
    new_category_id = new_category_response.json()["id"]
    
    # Try to update the new category with the name of the existing category
    update_data = {
        "name": test_data["category"].name,
        "description": "Updated description",
        "icon": "global"
    }
    response = client.put(f"/api/v1/regulation-categories/{new_category_id}", json=update_data)
    assert response.status_code == 400
    assert "already exists" in response.json()["detail"]

def test_delete_regulation_category(client):
    """Test deleting a regulation category."""
    # Create a new category first
    category_data = {
        "name": "Category to Delete",
        "description": "Category to be deleted",
        "icon": "bank"
    }
    create_response = client.post("/api/v1/regulation-categories/", json=category_data)
    assert create_response.status_code == 201
    category_id = create_response.json()["id"]
    
    # Delete the category
    response = client.delete(f"/api/v1/regulation-categories/{category_id}")
    assert response.status_code == 204
    
    # Verify the category is deleted
    get_response = client.get(f"/api/v1/regulation-categories/{category_id}")
    assert get_response.status_code == 404

def test_delete_regulation_category_not_found(client):
    """Test deleting a non-existent regulation category."""
    response = client.delete("/api/v1/regulation-categories/999")
    assert response.status_code == 404
    assert response.json()["detail"] == "Regulation category not found"

def test_delete_regulation_category_with_regulations(client, test_data):
    """Test deleting a regulation category that has associated regulations."""
    response = client.delete(f"/api/v1/regulation-categories/{test_data['category'].id}")
    assert response.status_code == 400
    assert "Cannot delete category that is associated with" in response.json()["detail"]

def test_get_regulations_by_category(client, test_data):
    """Test getting regulations associated with a category."""
    response = client.get(f"/api/v1/regulation-categories/{test_data['category'].id}/regulations")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) > 0
    assert response.json()[0]["title"] == test_data["regulation"].title
