
"""Test cases for the URL processor module."""
import os
import pytest
from app.utils import (
    extract_domain_and_tld,
    identify_country_from_url,
    identify_regulator_from_url,
    process_url,
    load_urls_from_file
)


def test_extract_domain_and_tld():
    """Test extracting domain and TLD from URLs."""
    # Simple domain
    domain, tld = extract_domain_and_tld("https://www.example.com/path")
    assert domain == "example.com"
    assert tld == "com"
    
    # Government domain
    domain, tld = extract_domain_and_tld("https://www.mass.gov/regulations")
    assert domain == "mass.gov"
    assert tld == "gov"
    
    # Domain with subdomain
    domain, tld = extract_domain_and_tld("https://docs.example.co.uk/file")
    assert domain == "example.co.uk"
    assert tld == "uk"
    
    # URL without protocol
    domain, tld = extract_domain_and_tld("example.org/page")
    assert domain == "example.org"
    assert tld == "org"


def test_identify_country_from_url():
    """Test identifying country from URLs."""
    # Simple country code TLD
    domain, tld = extract_domain_and_tld("https://example.ca/page")
    country = identify_country_from_url("https://example.ca/page", domain, tld)
    assert country == "Canada"
    
    # Government domain (US implied)
    domain, tld = extract_domain_and_tld("https://www.sec.gov/rules")
    country = identify_country_from_url("https://www.sec.gov/rules", domain, tld)
    assert country == "United States"
    
    # EU domain
    domain, tld = extract_domain_and_tld("https://eur-lex.europa.eu/legal-content")
    country = identify_country_from_url("https://eur-lex.europa.eu/legal-content", domain, tld)
    assert country == "European Union"
    
    # Country in URL path
    domain, tld = extract_domain_and_tld("https://www.regulator.org/japan-rules")
    country = identify_country_from_url("https://www.regulator.org/japan-rules", domain, tld)
    assert country == "Japan"


def test_identify_regulator_from_url():
    """Test identifying regulator from URLs."""
    # Known regulator domain
    regulator = identify_regulator_from_url(
        "https://www.mas.gov.sg/regulations", 
        "mas.gov.sg"
    )
    assert regulator == "Monetary Authority of Singapore"
    
    # Regulator by category
    regulator = identify_regulator_from_url(
        "https://www.example.com/privacy", 
        "example.com", 
        "privacy_data_protection"
    )
    assert regulator == "Data Protection Authority"
    
    # Regulator by terms in domain
    regulator = identify_regulator_from_url(
        "https://www.privacycommission.example.org", 
        "privacycommission.example.org"
    )
    assert "Privacy" in regulator


def test_process_url():
    """Test processing a complete URL."""
    result = process_url("https://www.mas.gov.sg/-/media/MAS/Regulations-and-Financial-Stability/Regulatory-and-Supervisory-Framework/Risk-Management/TRM-Guidelines-18-January-2021.pdf")
    assert result["country"] == "Singapore"
    assert result["regulator"] == "Monetary Authority of Singapore"
    assert "domain" in result
    assert "url" in result
    
    result = process_url("https://eur-lex.europa.eu/eli/reg/2016/679/oj")
    assert result["country"] == "European Union"
    assert "EUR-Lex" in result["regulator"]
    assert result["category"] == "privacy_data_protection"


def test_load_urls_from_file(tmp_path):
    """Test loading URLs from a file."""
    # Create a temporary file with URLs - one per line
    test_file = tmp_path / "test_urls.txt"
    test_file.write_text("https://example.com/page1\nhttps://example.org/page2\n\nhttps://example.net/page3")
    
    urls = load_urls_from_file(str(test_file))
    assert len(urls) == 3
    assert urls[0] == "https://example.com/page1"
    assert urls[1] == "https://example.org/page2"
    assert urls[2] == "https://example.net/page3"
    
    # Create a file with space-separated URLs
    space_separated_file = tmp_path / "space_separated.txt"
    space_separated_file.write_text("https://example.com/page1 https://example.org/page2 https://example.net/page3")
    
    urls = load_urls_from_file(str(space_separated_file))
    assert len(urls) == 3
    assert urls[0] == "https://example.com/page1"
    assert urls[1] == "https://example.org/page2"
    assert urls[2] == "https://example.net/page3"
    
    # Test with empty file
    empty_file = tmp_path / "empty.txt"
    empty_file.write_text("")
    urls = load_urls_from_file(str(empty_file))
    assert len(urls) == 0
