"""
Unit tests for the reporting and analytics functionality.
"""
import unittest
from unittest.mock import patch, MagicMock
import datetime
import json
import pandas as pd
import numpy as np

# Import the modules to be tested
# These imports will need to be updated based on the actual implementation
from app.regulatory_change.reporting import (
    ReportingService,
    WorkflowAnalytics,
    RegulatoryIntelligence,
    ImplementationAnalytics,
    ResourceAnalytics,
    ComplianceMetrics,
    TimeAnalytics,
    Report,
    ReportTemplate,
    Dashboard,
    DashboardWidget
)


class TestReportingAnalytics(unittest.TestCase):
    """Test cases for reporting and analytics functionality."""

    def setUp(self):
        """Set up test fixtures."""
        # Create reporting service
        self.reporting_service = ReportingService()
        
        # Sample date range for reports
        self.start_date = datetime.datetime(2022, 1, 1)
        self.end_date = datetime.datetime(2022, 12, 31)
        
        # Mock data service
        self.mock_data_service = MagicMock()
        self.reporting_service.data_service = self.mock_data_service
        
        # Create sample user
        self.user = MagicMock()
        self.user.id = "user123"
        self.user.name = "Test User"
        self.user.role = "Compliance Officer"

    def test_workflow_performance_metrics(self):
        """Test workflow performance metrics calculation."""
        # Mock workflow data
        workflow_data = [
            {
                "id": "wf1",
                "name": "Regulatory Change 1",
                "start_date": datetime.datetime(2022, 1, 15),
                "end_date": datetime.datetime(2022, 3, 10),
                "status": "COMPLETED",
                "stages": [
                    {
                        "name": "Initial Assessment",
                        "start_date": datetime.datetime(2022, 1, 15),
                        "end_date": datetime.datetime(2022, 1, 25),
                        "duration_days": 10
                    },
                    {
                        "name": "Impact Analysis",
                        "start_date": datetime.datetime(2022, 1, 26),
                        "end_date": datetime.datetime(2022, 2, 15),
                        "duration_days": 20
                    },
                    {
                        "name": "Implementation",
                        "start_date": datetime.datetime(2022, 2, 16),
                        "end_date": datetime.datetime(2022, 3, 10),
                        "duration_days": 23
                    }
                ],
                "total_duration_days": 55
            },
            {
                "id": "wf2",
                "name": "Regulatory Change 2",
                "start_date": datetime.datetime(2022, 2, 10),
                "end_date": datetime.datetime(2022, 4, 5),
                "status": "COMPLETED",
                "stages": [
                    {
                        "name": "Initial Assessment",
                        "start_date": datetime.datetime(2022, 2, 10),
                        "end_date": datetime.datetime(2022, 2, 18),
                        "duration_days": 8
                    },
                    {
                        "name": "Impact Analysis",
                        "start_date": datetime.datetime(2022, 2, 19),
                        "end_date": datetime.datetime(2022, 3, 15),
                        "duration_days": 25
                    },
                    {
                        "name": "Implementation",
                        "start_date": datetime.datetime(2022, 3, 16),
                        "end_date": datetime.datetime(2022, 4, 5),
                        "duration_days": 20
                    }
                ],
                "total_duration_days": 55
            }
        ]
        
        # Configure mock to return workflow data
        self.mock_data_service.get_completed_workflows.return_value = workflow_data
        
        # Create workflow analytics
        workflow_analytics = WorkflowAnalytics(self.reporting_service)
        
        # Calculate workflow performance metrics
        metrics = workflow_analytics.calculate_performance_metrics(
            start_date=self.start_date,
            end_date=self.end_date
        )
        
        # Assertions
        self.assertEqual(metrics["total_workflows"], 2)
        self.assertEqual(metrics["avg_workflow_duration"], 55)  # (55 + 55) / 2
        
        # Check stage duration metrics
        stage_durations = metrics["avg_stage_durations"]
        self.assertEqual(stage_durations["Initial Assessment"], 9)  # (10 + 8) / 2
        self.assertEqual(stage_durations["Impact Analysis"], 22.5)  # (20 + 25) / 2
        self.assertEqual(stage_durations["Implementation"], 21.5)  # (23 + 20) / 2
        
        # Check bottleneck identification
        bottlenecks = metrics["bottleneck_stages"]
        self.assertEqual(bottlenecks[0]["name"], "Impact Analysis")  # Highest average duration

    def test_regulatory_change_trends(self):
        """Test regulatory change trend analysis."""
        # Mock regulatory change data
        regulatory_changes = [
            {
                "id": "rc1",
                "title": "Change 1",
                "detected_date": datetime.datetime(2022, 1, 5),
                "jurisdiction": "US",
                "change_type": "NEW_REGULATION",
                "industry_sector": "Banking"
            },
            {
                "id": "rc2",
                "title": "Change 2",
                "detected_date": datetime.datetime(2022, 1, 15),
                "jurisdiction": "EU",
                "change_type": "AMENDMENT",
                "industry_sector": "Banking"
            },
            {
                "id": "rc3",
                "title": "Change 3",
                "detected_date": datetime.datetime(2022, 2, 10),
                "jurisdiction": "US",
                "change_type": "GUIDANCE",
                "industry_sector": "Insurance"
            },
            {
                "id": "rc4",
                "title": "Change 4",
                "detected_date": datetime.datetime(2022, 3, 5),
                "jurisdiction": "UK",
                "change_type": "AMENDMENT",
                "industry_sector": "Banking"
            },
            {
                "id": "rc5",
                "title": "Change 5",
                "detected_date": datetime.datetime(2022, 3, 20),
                "jurisdiction": "US",
                "change_type": "NEW_REGULATION",
                "industry_sector": "Securities"
            }
        ]
        
        # Configure mock to return regulatory change data
        self.mock_data_service.get_regulatory_changes.return_value = regulatory_changes
        
        # Create regulatory intelligence
        regulatory_intelligence = RegulatoryIntelligence(self.reporting_service)
        
        # Analyze regulatory change trends
        trends = regulatory_intelligence.analyze_trends(
            start_date=self.start_date,
            end_date=self.end_date
        )
        
        # Assertions
        self.assertEqual(trends["total_changes"], 5)
        
        # Check jurisdiction distribution
        jurisdiction_dist = trends["jurisdiction_distribution"]
        self.assertEqual(jurisdiction_dist["US"], 3)
        self.assertEqual(jurisdiction_dist["EU"], 1)
        self.assertEqual(jurisdiction_dist["UK"], 1)
        
        # Check change type distribution
        change_type_dist = trends["change_type_distribution"]
        self.assertEqual(change_type_dist["NEW_REGULATION"], 2)
        self.assertEqual(change_type_dist["AMENDMENT"], 2)
        self.assertEqual(change_type_dist["GUIDANCE"], 1)
        
        # Check industry sector distribution
        sector_dist = trends["industry_sector_distribution"]
        self.assertEqual(sector_dist["Banking"], 3)
        self.assertEqual(sector_dist["Insurance"], 1)
        self.assertEqual(sector_dist["Securities"], 1)
        
        # Check monthly distribution
        monthly_dist = trends["monthly_distribution"]
        self.assertEqual(monthly_dist["2022-01"], 2)
        self.assertEqual(monthly_dist["2022-02"], 1)
        self.assertEqual(monthly_dist["2022-03"], 2)

    def test_implementation_efficiency_metrics(self):
        """Test implementation efficiency metrics calculation."""
        # Mock implementation data
        implementation_data = [
            {
                "id": "imp1",
                "workflow_id": "wf1",
                "planned_start_date": datetime.datetime(2022, 2, 1),
                "planned_end_date": datetime.datetime(2022, 3, 15),
                "actual_start_date": datetime.datetime(2022, 2, 5),
                "actual_end_date": datetime.datetime(2022, 3, 20),
                "planned_duration_days": 43,
                "actual_duration_days": 44,
                "planned_effort_hours": 120,
                "actual_effort_hours": 135,
                "planned_cost": 15000.00,
                "actual_cost": 16200.00,
                "quality_score": 4.2  # Out of 5
            },
            {
                "id": "imp2",
                "workflow_id": "wf2",
                "planned_start_date": datetime.datetime(2022, 3, 1),
                "planned_end_date": datetime.datetime(2022, 4, 15),
                "actual_start_date": datetime.datetime(2022, 3, 3),
                "actual_end_date": datetime.datetime(2022, 4, 10),
                "planned_duration_days": 45,
                "actual_duration_days": 38,
                "planned_effort_hours": 160,
                "actual_effort_hours": 145,
                "planned_cost": 20000.00,
                "actual_cost": 18500.00,
                "quality_score": 4.5  # Out of 5
            }
        ]
        
        # Configure mock to return implementation data
        self.mock_data_service.get_implementation_data.return_value = implementation_data
        
        # Create implementation analytics
        implementation_analytics = ImplementationAnalytics(self.reporting_service)
        
        # Calculate implementation efficiency metrics
        metrics = implementation_analytics.calculate_efficiency_metrics(
            start_date=self.start_date,
            end_date=self.end_date
        )
        
        # Assertions
        self.assertEqual(metrics["total_implementations"], 2)
        
        # Check schedule efficiency
        self.assertAlmostEqual(metrics["schedule_efficiency"], 1.01, places=2)  # (44/43 + 38/45) / 2
        
        # Check effort efficiency
        self.assertAlmostEqual(metrics["effort_efficiency"], 1.04, places=2)  # (135/120 + 145/160) / 2
        
        # Check cost efficiency
        self.assertAlmostEqual(metrics["cost_efficiency"], 0.99, places=2)  # (16200/15000 + 18500/20000) / 2
        
        # Check quality score
        self.assertEqual(metrics["avg_quality_score"], 4.35)  # (4.2 + 4.5) / 2

    def test_resource_utilization_analysis(self):
        """Test resource utilization analysis."""
        # Mock resource allocation data
        resource_data = [
            {
                "resource_id": "res1",
                "resource_name": "John Doe",
                "resource_type": "STAFF",
                "department": "Compliance",
                "allocations": [
                    {
                        "workflow_id": "wf1",
                        "task_id": "task1",
                        "allocation_percentage": 50,
                        "start_date": datetime.datetime(2022, 1, 15),
                        "end_date": datetime.datetime(2022, 2, 15),
                        "planned_hours": 80,
                        "actual_hours": 85
                    },
                    {
                        "workflow_id": "wf2",
                        "task_id": "task2",
                        "allocation_percentage": 30,
                        "start_date": datetime.datetime(2022, 2, 20),
                        "end_date": datetime.datetime(2022, 3, 15),
                        "planned_hours": 60,
                        "actual_hours": 55
                    }
                ]
            },
            {
                "resource_id": "res2",
                "resource_name": "Jane Smith",
                "resource_type": "STAFF",
                "department": "IT",
                "allocations": [
                    {
                        "workflow_id": "wf1",
                        "task_id": "task3",
                        "allocation_percentage": 75,
                        "start_date": datetime.datetime(2022, 2, 1),
                        "end_date": datetime.datetime(2022, 3, 1),
                        "planned_hours": 120,
                        "actual_hours": 130
                    }
                ]
            }
        ]
        
        # Configure mock to return resource data
        self.mock_data_service.get_resource_allocation_data.return_value = resource_data
        
        # Create resource analytics
        resource_analytics = ResourceAnalytics(self.reporting_service)
        
        # Analyze resource utilization
        utilization = resource_analytics.analyze_utilization(
            start_date=self.start_date,
            end_date=self.end_date
        )
        
        # Assertions
        self.assertEqual(len(utilization["resources"]), 2)
        
        # Check resource utilization
        res1_util = next(r for r in utilization["resources"] if r["resource_id"] == "res1")
        self.assertEqual(res1_util["total_planned_hours"], 140)  # 80 + 60
        self.assertEqual(res1_util["total_actual_hours"], 140)  # 85 + 55
        self.assertAlmostEqual(res1_util["utilization_rate"], 1.0, places=2)  # 140/140
        
        res2_util = next(r for r in utilization["resources"] if r["resource_id"] == "res2")
        self.assertEqual(res2_util["total_planned_hours"], 120)
        self.assertEqual(res2_util["total_actual_hours"], 130)
        self.assertAlmostEqual(res2_util["utilization_rate"], 1.08, places=2)  # 130/120
        
        # Check department utilization
        dept_util = utilization["department_utilization"]
        self.assertAlmostEqual(dept_util["Compliance"]["utilization_rate"], 1.0, places=2)
        self.assertAlmostEqual(dept_util["IT"]["utilization_rate"], 1.08, places=2)

    def test_compliance_gap_metrics(self):
        """Test compliance gap metrics calculation."""
        # Mock gap analysis data
        gap_data = [
            {
                "workflow_id": "wf1",
                "regulatory_change_id": "rc1",
                "gaps": [
                    {
                        "id": "gap1",
                        "category": "Policy",
                        "severity": "High",
                        "identified_date": datetime.datetime(2022, 1, 20),
                        "closed_date": datetime.datetime(2022, 2, 15),
                        "status": "CLOSED"
                    },
                    {
                        "id": "gap2",
                        "category": "System",
                        "severity": "Medium",
                        "identified_date": datetime.datetime(2022, 1, 20),
                        "closed_date": datetime.datetime(2022, 3, 1),
                        "status": "CLOSED"
                    },
                    {
                        "id": "gap3",
                        "category": "Training",
                        "severity": "Low",
                        "identified_date": datetime.datetime(2022, 1, 20),
                        "closed_date": None,
                        "status": "OPEN"
                    }
                ]
            },
            {
                "workflow_id": "wf2",
                "regulatory_change_id": "rc2",
                "gaps": [
                    {
                        "id": "gap4",
                        "category": "Policy",
                        "severity": "Medium",
                        "identified_date": datetime.datetime(2022, 2, 25),
                        "closed_date": datetime.datetime(2022, 3, 20),
                        "status": "CLOSED"
                    },
                    {
                        "id": "gap5",
                        "category": "Process",
                        "severity": "High",
                        "identified_date": datetime.datetime(2022, 2, 25),
                        "closed_date": datetime.datetime(2022, 4, 10),
                        "status": "CLOSED"
                    }
                ]
            }
        ]
        
        # Configure mock to return gap data
        self.mock_data_service.get_gap_analysis_data.return_value = gap_data
        
        # Create compliance metrics
        compliance_metrics = ComplianceMetrics(self.reporting_service)
        
        # Calculate compliance gap metrics
        metrics = compliance_metrics.calculate_gap_metrics(
            start_date=self.start_date,
            end_date=self.end_date
        )
        
        # Assertions
        self.assertEqual(metrics["total_gaps_identified"], 5)
        self.assertEqual(metrics["total_gaps_closed"], 4)
        self.assertEqual(metrics["gap_closure_rate"], 0.8)  # 4/5
        
        # Check gap severity distribution
        severity_dist = metrics["severity_distribution"]
        self.assertEqual(severity_dist["High"], 2)
        self.assertEqual(severity_dist["Medium"], 2)
        self.assertEqual(severity_dist["Low"], 1)
        
        # Check gap category distribution
        category_dist = metrics["category_distribution"]
        self.assertEqual(category_dist["Policy"], 2)
        self.assertEqual(category_dist["System"], 1)
        self.assertEqual(category_dist["Training"], 1)
        self.assertEqual(category_dist["Process"], 1)
        
        # Check average closure time (in days)
        # gap1: 26 days, gap2: 40 days, gap4: 24 days, gap5: 45 days
        self.assertAlmostEqual(metrics["avg_closure_time_days"], 33.75, places=2)  # (26 + 40 + 24 + 45) / 4

    def test_time_to_compliance_metrics(self):
        """Test time-to-compliance metrics calculation."""
        # Mock workflow timeline data
        timeline_data = [
            {
                "workflow_id": "wf1",
                "regulatory_change_id": "rc1",
                "detection_date": datetime.datetime(2022, 1, 5),
                "assessment_start_date": datetime.datetime(2022, 1, 15),
                "assessment_end_date": datetime.datetime(2022, 2, 10),
                "implementation_start_date": datetime.datetime(2022, 2, 15),
                "implementation_end_date": datetime.datetime(2022, 3, 20),
                "verification_date": datetime.datetime(2022, 3, 25),
                "compliance_deadline": datetime.datetime(2022, 6, 1),
                "status": "COMPLIANT"
            },
            {
                "workflow_id": "wf2",
                "regulatory_change_id": "rc2",
                "detection_date": datetime.datetime(2022, 2, 10),
                "assessment_start_date": datetime.datetime(2022, 2, 20),
                "assessment_end_date": datetime.datetime(2022, 3, 15),
                "implementation_start_date": datetime.datetime(2022, 3, 20),
                "implementation_end_date": datetime.datetime(2022, 4, 25),
                "verification_date": datetime.datetime(2022, 5, 5),
                "compliance_deadline": datetime.datetime(2022, 7, 1),
                "status": "COMPLIANT"
            }
        ]
        
        # Configure mock to return timeline data
        self.mock_data_service.get_workflow_timeline_data.return_value = timeline_data
        
        # Create time analytics
        time_analytics = TimeAnalytics(self.reporting_service)
        
        # Calculate time-to-compliance metrics
        metrics = time_analytics.calculate_time_metrics(
            start_date=self.start_date,
            end_date=self.end_date
        )
        
        # Assertions
        self.assertEqual(metrics["total_workflows"], 2)
        
        # Check average time metrics (in days)
        # wf1: detection to compliance = 79 days (Jan 5 to Mar 25)
        # wf2: detection to compliance = 84 days (Feb 10 to May 5)
        self.assertAlmostEqual(metrics["avg_detection_to_compliance_days"], 81.5, places=1)  # (79 + 84) / 2
        
        # Check phase duration metrics (in days)
        # wf1: assessment = 26 days, implementation = 34 days
        # wf2: assessment = 24 days, implementation = 36 days
        self.assertAlmostEqual(metrics["avg_assessment_duration_days"], 25.0, places=1)  # (26 + 24) / 2
        self.assertAlmostEqual(metrics["avg_implementation_duration_days"], 35.0, places=1)  # (34 + 36) / 2
        
        # Check deadline metrics
        # wf1: 68 days before deadline (Mar 25 vs Jun 1)
        # wf2: 57 days before deadline (May 5 vs Jul 1)
        self.assertAlmostEqual(metrics["avg_days_before_deadline"], 62.5, places=1)  # (68 + 57) / 2
        self.assertEqual(metrics["deadline_compliance_rate"], 1.0)  # Both compliant before deadline

    def test_report_generation(self):
        """Test report generation functionality."""
        # Create report template
        template = ReportTemplate(
            name="Regulatory Change Summary",
            description="Summary of regulatory change activities",
            sections=[
                {
                    "name": "Overview",
                    "metrics": ["total_changes", "total_workflows", "compliance_rate"]
                },
                {
                    "name": "Workflow Performance",
                    "metrics": ["avg_workflow_duration", "bottleneck_stages"]
                },
                {
                    "name": "Implementation Efficiency",
                    "metrics": ["schedule_efficiency", "cost_efficiency", "quality_score"]
                },
                {
                    "name": "Compliance Metrics",
                    "metrics": ["gap_closure_rate", "avg_closure_time_days"]
                }
            ],
            created_by=self.user,
            created_at=datetime.datetime.now()
        )
        
        # Mock metric data
        mock_metrics = {
            "total_changes": 10,
            "total_workflows": 8,
            "compliance_rate": 0.95,
            "avg_workflow_duration": 45.5,
            "bottleneck_stages": [{"name": "Impact Analysis", "avg_duration": 22.5}],
            "schedule_efficiency": 1.01,
            "cost_efficiency": 0.99,
            "quality_score": 4.35,
            "gap_closure_rate": 0.8,
            "avg_closure_time_days": 33.75
        }
        
        # Configure mocks to return metric data
        self.mock_data_service.get_combined_metrics.return_value = mock_metrics
        
        # Generate report
        report = self.reporting_service.generate_report(
            template=template,
            start_date=self.start_date,
            end_date=self.end_date,
            created_by=self.user
        )
        
        # Assertions
        self.assertEqual(report.template, template)
        self.assertEqual(report.start_date, self.start_date)
        self.assertEqual(report.end_date, self.end_date)
        self.assertEqual(report.created_by, self.user)
        
        # Check report data
        self.assertEqual(len(report.sections), 4)
        self.assertEqual(report.sections[0]["name"], "Overview")
        self.assertEqual(report.sections[0]["data"]["total_changes"], 10)
        self.assertEqual(report.sections[1]["name"], "Workflow Performance")
        self.assertEqual(report.sections[1]["data"]["avg_workflow_duration"], 45.5)
        self.assertEqual(report.sections[2]["name"], "Implementation Efficiency")
        self.assertAlmostEqual(report.sections[2]["data"]["schedule_efficiency"], 1.01)
        self.assertEqual(report.sections[3]["name"], "Compliance Metrics")
        self.assertEqual(report.sections[3]["data"]["gap_closure_rate"], 0.8)

    def test_dashboard_creation(self):
        """Test dashboard creation and widget configuration."""
        # Create dashboard
        dashboard = Dashboard(
            name="Regulatory Change Dashboard",
            description="Executive dashboard for regulatory change management",
            created_by=self.user,
            created_at=datetime.datetime.now()
        )
        
        # Add widgets to dashboard
        widget1 = dashboard.add_widget(
            name="Regulatory Change Volume",
            widget_type="LINE_CHART",
            data_source="regulatory_changes",
            metrics=["monthly_change_count"],
            dimensions=["month"],
            position={"row": 0, "col": 0, "width": 6, "height": 4}
        )
        
        widget2 = dashboard.add_widget(
            name="Workflow Status",
            widget_type="PIE_CHART",
            data_source="workflows",
            metrics=["workflow_count"],
            dimensions=["status"],
            position={"row": 0, "col": 6, "width": 6, "height": 4}
        )
        
        widget3 = dashboard.add_widget(
            name="Implementation Efficiency",
            widget_type="GAUGE",
            data_source="implementation",
            metrics=["schedule_efficiency"],
            position={"row": 4, "col": 0, "width": 4, "height": 4}
        )
        
        widget4 = dashboard.add_widget(
            name="Compliance Gap Closure",
            widget_type="PROGRESS_BAR",
            data_source="compliance",
            metrics=["gap_closure_rate"],
            position={"row": 4, "col": 4, "width": 8, "height": 4}
        )
        
        # Mock widget data
        mock_widget_data = {
            "widget1": {
                "data": [
                    {"month": "Jan", "monthly_change_count": 2},
                    {"month": "Feb", "monthly_change_count": 3},
                    {"month": "Mar", "monthly_change_count": 5}
                ]
            },
            "widget2": {
                "data": [
                    {"status": "In Progress", "workflow_count": 3},
                    {"status": "Completed", "workflow_count": 5}
                ]
            },
            "widget3": {
                "data": {"schedule_efficiency": 1.01}
            },
            "widget4": {
                "data": {"gap_closure_rate": 0.8}
            }
        }
        
        # Configure mock to return widget data
        self.mock_data_service.get_widget_data.side_effect = lambda widget_id, **kwargs: mock_widget_data[widget_id]
        
        # Generate dashboard data
        dashboard_data = self.reporting_service.generate_dashboard_data(
            dashboard=dashboard,
            start_date=self.start_date,
            end_date=self.end_date
        )
        
        # Assertions
        self.assertEqual(len(dashboard_data["widgets"]), 4)
        
        # Check widget data
        widget1_data = next(w for w in dashboard_data["widgets"] if w["name"] == "Regulatory Change Volume")
        self.assertEqual(len(widget1_data["data"]), 3)
        self.assertEqual(widget1_data["data"][2]["monthly_change_count"], 5)
        
        widget2_data = next(w for w in dashboard_data["widgets"] if w["name"] == "Workflow Status")
        self.assertEqual(len(widget2_data["data"]), 2)
        self.assertEqual(widget2_data["data"][1]["workflow_count"], 5)
        
        widget3_data = next(w for w in dashboard_data["widgets"] if w["name"] == "Implementation Efficiency")
        self.assertAlmostEqual(widget3_data["data"]["schedule_efficiency"], 1.01)
        
        widget4_data = next(w for w in dashboard_data["widgets"] if w["name"] == "Compliance Gap Closure")
        self.assertEqual(widget4_data["data"]["gap_closure_rate"], 0.8)


if __name__ == '__main__':
    unittest.main()
