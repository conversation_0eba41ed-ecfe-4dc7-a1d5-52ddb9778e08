"""Additional tests for URL processor functionality."""
import pytest
from unittest.mock import patch, <PERSON><PERSON>, MagicMock
import json
import os

from app.api.data_collection import process_url, extract_domain, normalize_url

def test_process_url_with_json_response():
    """Test processing a URL with a valid JSON response"""
    mock_response = Mock()
    mock_response.status_code = 200
    mock_response.text = json.dumps({"data": "test"})
    mock_response.json.return_value = {"data": "test"}

    with patch('requests.get', return_value=mock_response):
        result = process_url("https://example.com/api/data")
        assert result["status"] == "success"
        assert result["data"] == {"data": "test"}
        assert result["content_type"] == "application/json"

def test_extract_domain():
    """Test domain extraction from various URLs"""
    assert extract_domain("https://www.example.com/page") == "example.com"
    assert extract_domain("http://subdomain.example.co.uk/page?q=test") == "example.co.uk"
    assert extract_domain("https://gov.uk/regulations") == "gov.uk"

def test_normalize_url():
    """Test URL normalization"""
    assert normalize_url("example.com") == "https://example.com"
    assert normalize_url("http://example.com") == "http://example.com"
    assert normalize_url("https://example.com") == "https://example.com"
    assert normalize_url("example.com/page") == "https://example.com/page"

def test_process_url_error_handling():
    """Test error handling when processing URLs"""
    with patch('requests.get', side_effect=Exception("Connection error")):
        result = process_url("https://example.com")
        assert result["status"] == "error"
        assert "Connection error" in result["error"]

def test_process_url_dumps_json():
    """Test that process_url dumps JSON content correctly"""
    mock_response = Mock()
    mock_response.status_code = 200
    mock_response.text = "<html><body>Test</body></html>"

    with patch('requests.get', return_value=mock_response), \
         patch('json.dump') as mock_json_dump:

        process_url("https://example.com", save_to_file=True)
        mock_json_dump.assert_called_once()

import pytest
from unittest.mock import patch, MagicMock, mock_open
import requests
import sys
import os
import json
from bs4 import BeautifulSoup

# Add app directory to path if needed
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.utils.url_processor import (
    fetch_url_content,
    extract_text_from_html,
    clean_text,
    detect_language,
    extract_regulatory_info,
    process_url,
    batch_process_urls,
    extract_metadata,
    save_processed_content,
    load_processed_content,
    extract_compliance_requirements
)

def test_fetch_url_content_success():
    """Test successful URL content fetching."""
    test_url = "https://example.com"
    test_content = "<html><body>Test content</body></html>"
    
    # Mock successful response
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.text = test_content
    mock_response.headers = {'Content-Type': 'text/html'}
    
    with patch('requests.get', return_value=mock_response) as mock_get:
        content, content_type = fetch_url_content(test_url)
        
        # Verify request was made correctly
        mock_get.assert_called_once_with(test_url, timeout=30)
        assert content == test_content
        assert content_type == 'text/html'

def test_fetch_url_content_error():
    """Test URL content fetching with an error."""
    test_url = "https://example.com"
    
    # Mock request exception
    with patch('requests.get', side_effect=requests.RequestException("Connection error")) as mock_get:
        content, content_type = fetch_url_content(test_url)
        
        # Verify request was attempted
        mock_get.assert_called_once_with(test_url, timeout=30)
        assert content is None
        assert content_type is None

def test_fetch_url_content_404():
    """Test URL content fetching with a 404 error."""
    test_url = "https://example.com/nonexistent"
    
    # Mock 404 response
    mock_response = MagicMock()
    mock_response.status_code = 404
    
    with patch('requests.get', return_value=mock_response) as mock_get:
        content, content_type = fetch_url_content(test_url)
        
        # Verify request was made
        mock_get.assert_called_once_with(test_url, timeout=30)
        assert content is None
        assert content_type is None

def test_extract_text_from_html_real():
    """Test extracting text from HTML content using actual BeautifulSoup."""
    html_content = "<html><body><h1>Title</h1><p>Paragraph text</p></body></html>"
    expected_text = "Title Paragraph text"
    
    # Use actual BeautifulSoup for this test
    text = extract_text_from_html(html_content)
    
    # Normalize whitespace for comparison
    text = ' '.join(text.split())
    expected_text = ' '.join(expected_text.split())
    
    assert text == expected_text

def test_extract_text_from_html_mocked():
    """Test extracting text from HTML content with mocked BeautifulSoup."""
    html_content = "<html><body><h1>Title</h1><p>Paragraph text</p></body></html>"
    expected_text = "Title Paragraph text"
    
    with patch('bs4.BeautifulSoup') as mock_bs:
        mock_soup = MagicMock()
        mock_bs.return_value = mock_soup
        mock_soup.get_text.return_value = "Title Paragraph text"
        
        text = extract_text_from_html(html_content)
        
        assert text == expected_text

def test_clean_text():
    """Test cleaning text."""
    dirty_text = "  This has\n\nextra  spaces\t and tabs\n."
    expected_text = "This has extra spaces and tabs."
    
    cleaned_text = clean_text(dirty_text)
    
    assert cleaned_text == expected_text

def test_clean_text_with_unicode():
    """Test cleaning text with Unicode characters."""
    dirty_text = "Unicode chars: \u2022 \u2013 \u2014 \u201c \u201d"
    expected_text = "Unicode chars: • – — " ""
    
    cleaned_text = clean_text(dirty_text)
    
    assert cleaned_text == expected_text

def test_detect_language():
    """Test language detection."""
    text = "This is English text for testing language detection."
    
    with patch('langdetect.detect', return_value='en') as mock_detect:
        language = detect_language(text)
        
        mock_detect.assert_called_once_with(text)
        assert language == 'en'

def test_detect_language_error():
    """Test language detection with an error."""
    text = ""  # Empty text will cause an error in langdetect
    
    with patch('langdetect.detect', side_effect=Exception("Detection error")) as mock_detect:
        language = detect_language(text)
        
        mock_detect.assert_called_once_with(text)
        assert language == 'unknown'

def test_extract_regulatory_info():
    """Test extracting regulatory information from text."""
    text = """
    This regulation requires companies to protect personal data.
    All organizations must report breaches within 72 hours.
    Penalties for non-compliance can reach up to 4% of annual revenue.
    """
    
    with patch('app.utils.url_processor.extract_compliance_requirements') as mock_extract:
        mock_extract.return_value = {
            'data_protection': True,
            'breach_notification': True,
            'penalties': '4% of annual revenue'
        }
        
        info = extract_regulatory_info(text)
        
        mock_extract.assert_called_once_with(text)
        assert info == {
            'data_protection': True,
            'breach_notification': True,
            'penalties': '4% of annual revenue'
        }

def test_process_url():
    """Test processing a single URL."""
    test_url = "https://example.com"
    test_html = "<html><body><h1>Privacy Regulation</h1><p>Data protection rules</p></body></html>"
    
    # Mock the component functions
    with patch('app.utils.url_processor.fetch_url_content') as mock_fetch:
        with patch('app.utils.url_processor.extract_text_from_html') as mock_extract_text:
            with patch('app.utils.url_processor.clean_text') as mock_clean:
                with patch('app.utils.url_processor.detect_language') as mock_detect:
                    with patch('app.utils.url_processor.extract_regulatory_info') as mock_extract_info:
                        with patch('app.utils.url_processor.extract_metadata') as mock_metadata:
                            
                            # Configure mocks
                            mock_fetch.return_value = (test_html, 'text/html')
                            mock_extract_text.return_value = "Privacy Regulation Data protection rules"
                            mock_clean.return_value = "Privacy Regulation Data protection rules"
                            mock_detect.return_value = 'en'
                            mock_extract_info.return_value = {'data_protection': True}
                            mock_metadata.return_value = {'title': 'Privacy Regulation'}
                            
                            # Process the URL
                            result = process_url(test_url)
                            
                            # Verify all functions were called
                            mock_fetch.assert_called_once_with(test_url)
                            mock_extract_text.assert_called_once_with(test_html)
                            mock_clean.assert_called_once()
                            mock_detect.assert_called_once()
                            mock_extract_info.assert_called_once()
                            mock_metadata.assert_called_once()
                            
                            # Check result structure
                            assert 'url' in result
                            assert 'content' in result
                            assert 'content_type' in result
                            assert 'text' in result
                            assert 'language' in result
                            assert 'regulatory_info' in result
                            assert 'metadata' in result
                            
                            # Check specific values
                            assert result['url'] == test_url
                            assert result['language'] == 'en'

def test_process_url_with_error():
    """Test processing a URL that fails."""
    test_url = "https://example.com"
    
    # Mock fetch_url_content to return None (error)
    with patch('app.utils.url_processor.fetch_url_content', return_value=(None, None)):
        result = process_url(test_url)
        
        # Check that result indicates an error
        assert 'url' in result
        assert result['url'] == test_url
        assert result['content'] is None
        assert result['error'] == "Failed to fetch URL content"

def test_batch_process_urls():
    """Test batch processing of URLs."""
    test_urls = ["https://example.com/1", "https://example.com/2"]
    
    # Mock process_url
    with patch('app.utils.url_processor.process_url') as mock_process:
        # Configure mock to return different values for each URL
        mock_process.side_effect = [
            {'url': test_urls[0], 'text': 'Content 1'},
            {'url': test_urls[1], 'text': 'Content 2'}
        ]
        
        # Process the URLs
        results = batch_process_urls(test_urls)
        
        # Verify process_url was called for each URL
        assert mock_process.call_count == 2
        mock_process.assert_any_call(test_urls[0])
        mock_process.assert_any_call(test_urls[1])
        
        # Check results
        assert len(results) == 2
        assert results[0]['url'] == test_urls[0]
        assert results[1]['url'] == test_urls[1]

def test_extract_metadata():
    """Test extracting metadata from HTML."""
    test_html = """
    <html>
    <head>
        <title>Test Document</title>
        <meta name="description" content="A test description">
        <meta name="keywords" content="test, metadata, extraction">
    </head>
    <body>
        <h1>Test Document</h1>
        <p>Some content</p>
    </body>
    </html>
    """
    
    metadata = extract_metadata(test_html)
    
    # Check metadata fields
    assert 'title' in metadata
    assert metadata['title'] == 'Test Document'
    assert 'description' in metadata
    assert metadata['description'] == 'A test description'

def test_save_processed_content():
    """Test saving processed content to file."""
    test_content = [
        {'url': 'https://example.com/1', 'text': 'Content 1'},
        {'url': 'https://example.com/2', 'text': 'Content 2'}
    ]
    filename = 'test_output.json'
    
    # Mock open
    with patch('builtins.open', mock_open()) as mock_file:
        with patch('json.dump') as mock_json_dump:
            save_processed_content(test_content, filename)
            
            # Verify file was opened and JSON dumped
            mock_file.assert_called_once_with(filename, 'w', encoding='utf-8')
            mock_json_dump.assert_called_once()

def test_load_processed_content():
    """Test loading processed content from file."""
    test_content = [
        {'url': 'https://example.com/1', 'text': 'Content 1'},
        {'url': 'https://example.com/2', 'text': 'Content 2'}
    ]
    filename = 'test_input.json'
    
    # Mock open and json.load
    with patch('builtins.open', mock_open()) as mock_file:
        with patch('json.load', return_value=test_content) as mock_json_load:
            content = load_processed_content(filename)
            
            # Verify file was opened and JSON loaded
            mock_file.assert_called_once_with(filename, 'r', encoding='utf-8')
            mock_json_load.assert_called_once()
            
            # Check content
            assert len(content) == 2
            assert content[0]['url'] == 'https://example.com/1'
            assert content[1]['url'] == 'https://example.com/2'

def test_extract_compliance_requirements():
    """Test extracting compliance requirements from text."""
    text = """
    Companies must protect personal data and ensure confidentiality.
    Security breaches must be reported within 72 hours.
    Non-compliance penalties can reach 20 million euros or 4% of annual revenue.
    Companies must appoint a data protection officer.
    """
    
    requirements = extract_compliance_requirements(text)
    
    # Check extracted requirements
    assert 'data_protection' in requirements
    assert requirements['data_protection'] is True
    assert 'breach_notification' in requirements
    assert requirements['breach_notification'] is True
    assert 'penalties' in requirements
    assert '4%' in requirements['penalties']