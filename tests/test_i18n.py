
"""Test cases for internationalization (i18n) functionality."""
from unittest.mock import patch

import pytest
from fastapi.testclient import Test<PERSON>lient

from main import app
from app.i18n import get_translator, get_locale


@pytest.fixture
def client():
    """
    Create a test client for the FastAPI application.
    
    Returns:
        TestClient: The test client instance
    """
    return TestClient(app)


def test_default_language(client):
    """
    Test default language (English).
    
    Args:
        client (TestClient): The test client
    """
    response = client.get("/")
    assert response.status_code == 200
    assert "message" in response.json()


def test_language_from_query_parameter(client):
    """
    Test language selection from query parameter.
    
    Args:
        client (TestClient): The test client
    """
    # Test with Spanish language parameter
    response = client.get("/?lang=es")
    assert response.status_code == 200
    
    # Verify cookie was set
    assert "lang" in response.cookies
    assert response.cookies["lang"] == "es"


def test_language_from_cookie(client):
    """
    Test language selection from cookie.
    
    Args:
        client (TestClient): The test client
    """
    # Set language cookie
    client.cookies.set("lang", "fr")
    
    # Make request with cookie
    with patch('i18n.get_locale', return_value="fr"):
        response = client.get("/")
        assert response.status_code == 200


def test_get_languages_endpoint(client):
    """
    Test the languages endpoint.
    
    Args:
        client (TestClient): The test client
    """
    response = client.get("/languages")
    assert response.status_code == 200
    assert "current_language" in response.json()
    assert "available_languages" in response.json()
    assert "en" in response.json()["available_languages"]
    assert "es" in response.json()["available_languages"]
    assert "fr" in response.json()["available_languages"]


def test_translator_function():
    """Test the translator function."""
    # Test with English locale
    with patch('i18n.get_locale', return_value="en"):
        translator = get_translator()
        assert translator("Hello World") == "Hello World"
    
    # Test with an unknown message
    with patch('i18n.get_locale', return_value="en"):
        translator = get_translator()
        assert translator("Unknown message") == "Unknown message"
"""Test cases for the i18n module."""
import pytest
from unittest.mock import patch, MagicMock

from fastapi import Request
from app.i18n import i18n


@pytest.fixture
def mock_request():
    """Create a mock request with configurable cookies and query params."""
    mock = MagicMock(spec=Request)
    mock.cookies = {}
    mock.query_params = {}
    return mock


def test_get_locale_from_cookie(mock_request):
    """Test getting locale from request cookies."""
    # Set cookie
    mock_request.cookies = {"lang": "es"}
    
    # Get locale
    locale = i18n.get_locale(mock_request)
    
    # Check result
    assert locale == "es"


def test_get_locale_from_query_param(mock_request):
    """Test getting locale from query parameters."""
    # Set query param
    mock_request.query_params = {"lang": "fr"}
    
    # Get locale
    locale = i18n.get_locale(mock_request)
    
    # Check result
    assert locale == "fr"


def test_get_locale_default(mock_request):
    """Test getting default locale when no preference specified."""
    # No cookies or query params set
    
    # Get locale
    locale = i18n.get_locale(mock_request)
    
    # Check result
    assert locale == "en"  # Default should be English


def test_get_locale_invalid(mock_request):
    """Test getting locale with invalid language code."""
    # Set invalid language
    mock_request.cookies = {"lang": "invalid"}
    
    # Get locale
    locale = i18n.get_locale(mock_request)
    
    # Check result
    assert locale == "en"  # Should fall back to default


def test_get_translator(mock_request):
    """Test getting a translator function."""
    # Set Spanish locale
    mock_request.cookies = {"lang": "es"}
    
    with patch('i18n.gettext.translation') as mock_translation:
        # Setup mock translator
        mock_translator = MagicMock()
        mock_translator.gettext.side_effect = lambda text: f"ES:{text}"
        mock_translation.return_value = mock_translator
        
        # Get translator function
        translator = i18n.get_translator(mock_request)
        
        # Test translation
        result = translator("Hello")
        
        # Check result
        assert result == "ES:Hello"
        mock_translator.gettext.assert_called_once_with("Hello")


def test_get_translator_fallback(mock_request):
    """Test translator falling back when translation fails."""
    with patch('i18n.gettext.translation') as mock_translation:
        # Setup mock to raise an error
        mock_translation.side_effect = FileNotFoundError("No translation file")
        
        # Get translator function
        translator = i18n.get_translator(mock_request)
        
        # Test translation
        result = translator("Hello")
        
        # Check result - should return original text
        assert result == "Hello"
