
"""
Integration tests for impact assessment functionality.
"""
import os
import pytest
import json
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient

try:
    from app.main import app
    from app.api.impact_assessment import generate_impact_recommendations, analyze_affected_processes
    from app.utils.regulatory_parser import RegulatoryParser
    HAS_FASTAPI = True
except ImportError:
    HAS_FASTAPI = False


@pytest.mark.skipif(not HAS_FASTAPI, reason="FastAPI not installed or API routes not available")
class TestImpactAssessmentIntegration:
    """Integration tests for impact assessment functionality."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        if HAS_FASTAPI:
            return TestClient(app)
        return None
    
    @pytest.fixture
    def sample_regulation(self):
        """Sample regulation data."""
        return {
            "id": "reg123",
            "title": "General Data Protection Regulation",
            "scope": "Personal data processing in the EU",
            "requirements": [
                {
                    "text": "Organizations shall implement appropriate security measures.",
                    "priority": "high"
                },
                {
                    "text": "Data breaches must be reported within 72 hours.",
                    "priority": "high"
                }
            ],
            "penalties": [
                {
                    "text": "Violations may result in fines up to €20,000,000 or 4% of annual revenue.",
                    "severity": "severe"
                }
            ]
        }
    
    @pytest.fixture
    def sample_business_processes(self):
        """Sample business processes."""
        return [
            {
                "name": "Customer Data Collection",
                "importance": 9,
                "description": "Process for collecting customer data via website forms"
            },
            {
                "name": "Marketing Campaigns",
                "importance": 7,
                "description": "Process for running targeted marketing campaigns"
            },
            {
                "name": "Supply Chain Management",
                "importance": 8,
                "description": "Process for managing suppliers and inventory"
            }
        ]
    
    @pytest.fixture
    def sample_risk_factors(self):
        """Sample risk factors."""
        return [
            {"name": "Data Sensitivity", "score": 8},
            {"name": "Industry Risk", "score": 6},
            {"name": "Organizational Complexity", "score": 7},
            {"name": "Geographical Complexity", "score": 5},
            {"name": "Compliance Gap", "score": 8}
        ]
    
    def test_analyze_affected_processes(self, sample_business_processes, sample_regulation):
        """Test analysis of affected business processes."""
        # Convert dict to mock object with attributes
        class BusinessProcessModel:
            def __init__(self, data):
                self.name = data["name"]
                self.importance = data["importance"]
                self.description = data["description"]
        
        class RegulationURL:
            def __init__(self, data):
                self.id = data["id"]
                self.title = data["title"]
        
        business_processes = [BusinessProcessModel(bp) for bp in sample_business_processes]
        regulation = RegulationURL(sample_regulation)
        
        result = analyze_affected_processes(business_processes, regulation)
        
        assert len(result) == len(sample_business_processes)
        assert "Customer Data Collection" in [p["process_name"] for p in result]
        assert any(p["impact_level"] == "high" for p in result)
        assert all("required_changes" in p for p in result)
        
        # Verify customer data process has high impact
        customer_process = next(p for p in result if p["process_name"] == "Customer Data Collection")
        assert customer_process["impact_level"] == "high"
        
    def test_generate_impact_recommendations(self, sample_risk_factors, sample_regulation):
        """Test generation of impact recommendations."""
        # Convert dict to mock object with attributes
        class RiskFactorModel:
            def __init__(self, data):
                self.name = data["name"]
                self.score = data["score"]
        
        class RegulationURL:
            def __init__(self, data):
                self.id = data["id"]
                self.title = data["title"]
        
        risk_factors = [RiskFactorModel(rf) for rf in sample_risk_factors]
        regulation = RegulationURL(sample_regulation)
        
        # Test critical impact
        affected_processes = [{"process_name": "Customer Data", "impact_level": "high"}]
        recommendations = generate_impact_recommendations(
            "Critical", risk_factors, affected_processes, 0.4, regulation
        )
        
        assert len(recommendations) > 5
        assert any("task force" in rec.lower() for rec in recommendations)
        assert any("data sensitivity" in rec.lower() for rec in recommendations)
        
        # Test high impact
        recommendations = generate_impact_recommendations(
            "High", risk_factors, affected_processes, 0.6, regulation
        )
        
        assert len(recommendations) > 3
        assert any("cross-functional" in rec.lower() for rec in recommendations)
    
    @pytest.mark.skipif(not HAS_FASTAPI, reason="FastAPI not installed or API routes not available")
    def test_impact_assessment_api(self, client, sample_regulation, sample_business_processes, sample_risk_factors):
        """Test the impact assessment API endpoint."""
        if not client:
            pytest.skip("Test client not available")
        
        # Mock data
        request_data = {
            "regulation_id": sample_regulation["id"],
            "business_processes": sample_business_processes,
            "risk_factors": sample_risk_factors,
            "existing_compliance": 0.45
        }
        
        # Mock service function
        with patch('app.api.impact_assessment.get_regulation_by_id', return_value=sample_regulation), \
             patch('app.api.impact_assessment.analyze_affected_processes', side_effect=analyze_affected_processes), \
             patch('app.api.impact_assessment.generate_impact_recommendations', side_effect=generate_impact_recommendations):
            
            response = client.post("/api/impact-assessment", json=request_data)
            
            # Verify response
            assert response.status_code == 200
            result = response.json()
            
            assert "impact_category" in result
            assert "affected_processes" in result
            assert "recommendations" in result
            assert len(result["recommendations"]) > 0
            assert len(result["affected_processes"]) > 0
