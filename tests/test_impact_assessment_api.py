
"""
Tests for the regulatory impact assessment API endpoints.
"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

from app.main import app
from app.api import impact_assessment
from app.db.models.regulatory_changes import ChangeImpact

client = TestClient(app)

class TestImpactAssessmentAPI:
    
    def test_assess_regulatory_impact(self):
        """Test performing a regulatory impact assessment."""
        # Test data
        assessment_input = {
            "regulation_url_id": 1,
            "business_processes": [
                {"name": "Customer Onboarding", "importance": 0.8},
                {"name": "Data Processing", "importance": 0.9},
                {"name": "Financial Reporting", "importance": 0.7}
            ],
            "company_size": "medium",
            "industry": "Financial Services",
            "geographical_regions": ["US", "EU"],
            "data_types_processed": ["PII", "Financial", "Health"],
            "existing_compliance_level": 7.5
        }
        
        # Mock database query to avoid actual DB dependency
        with patch('app.api.impact_assessment.db.query') as mock_query:
            # Setup mock regulation
            mock_regulation = MagicMock()
            mock_regulation.id = 1
            mock_regulation.title = "GDPR"
            mock_regulation.url = "https://example.com/gdpr"
            
            # Make query return our mock regulation
            mock_filter = MagicMock()
            mock_filter.first.return_value = mock_regulation
            mock_query.return_value.filter.return_value = mock_filter
            
            # Make request
            response = client.post("/api/v1/impact/assess", json=assessment_input)
            
            # Check response
            assert response.status_code == 200
            result = response.json()
            
            # Check structure
            assert "overall_impact_score" in result
            assert "impact_category" in result
            assert "risk_factors" in result
            assert "affected_processes" in result
            assert "estimated_implementation_cost" in result
            assert "estimated_implementation_time" in result
            assert "recommendations" in result
            
            # Check content types
            assert isinstance(result["overall_impact_score"], (int, float))
            assert isinstance(result["impact_category"], str)
            assert isinstance(result["risk_factors"], list)
            assert isinstance(result["affected_processes"], list)
            assert isinstance(result["estimated_implementation_cost"], (int, float))
            assert isinstance(result["estimated_implementation_time"], (int, float))
            assert isinstance(result["recommendations"], list)
    
    def test_assess_regulatory_impact_regulation_not_found(self):
        """Test impact assessment with non-existent regulation."""
        # Test data with non-existent regulation ID
        assessment_input = {
            "regulation_url_id": 999,
            "business_processes": [
                {"name": "Customer Onboarding", "importance": 0.8}
            ],
            "company_size": "medium",
            "industry": "Financial Services",
            "geographical_regions": ["US"],
            "data_types_processed": ["PII"],
            "existing_compliance_level": 7.5
        }
        
        # Mock database query to return None (regulation not found)
        with patch('app.api.impact_assessment.db.query') as mock_query:
            mock_filter = MagicMock()
            mock_filter.first.return_value = None
            mock_query.return_value.filter.return_value = mock_filter
            
            # Make request
            response = client.post("/api/v1/impact/assess", json=assessment_input)
            
            # Check response for not found
            assert response.status_code == 404
            assert "detail" in response.json()
            assert "not found" in response.json()["detail"].lower()
    
    def test_assess_regulatory_impact_invalid_input(self):
        """Test impact assessment with invalid input."""
        # Test data with invalid company size
        invalid_input = {
            "regulation_url_id": 1,
            "business_processes": [
                {"name": "Customer Onboarding", "importance": 0.8}
            ],
            "company_size": "invalid_size",  # Invalid value
            "industry": "Financial Services",
            "geographical_regions": ["US"],
            "data_types_processed": ["PII"],
            "existing_compliance_level": 7.5
        }
        
        # Make request
        response = client.post("/api/v1/impact/assess", json=invalid_input)
        
        # In FastAPI, validation errors typically return 422 Unprocessable Entity
        # but this would depend on your validation setup
        assert response.status_code in [400, 422]
