
"""
Integration tests for regulatory document parsing and relationship mapping components.
These tests focus on the interaction between different components.
"""
import os
import pytest
from unittest.mock import patch, MagicMock
import tempfile
import json

from app.utils.regulatory_parser import RegulatoryParser
from app.utils.relationship_mapper import RelationshipMapper
from app.utils.pdf_analyzer import analyze_pdf_document

# Mock API client for testing
class MockAPIClient:
    def __init__(self):
        self.analyzed_documents = []
        
    async def analyze_document(self, file_path):
        parser = RegulatoryParser()
        result = parser.parse_document(file_path)
        result["file_name"] = os.path.basename(file_path)
        result["id"] = str(hash(file_path))
        self.analyzed_documents.append(result)
        return result
        
    async def map_relationships(self, documents):
        mapper = RelationshipMapper()
        return mapper.map_document_relationships(documents)

class TestRegulatoryIntegration:
    """Integration tests for regulatory functionality."""
    
    @pytest.fixture
    def sample_regulatory_text(self):
        """Provide sample regulatory text for testing."""
        return """
        Article 1. Scope
        This regulation applies to all organizations processing personal data.
        
        Article 2. Requirements
        2.1. Organizations shall implement appropriate security measures.
        2.2. Data breaches must be reported within 72 hours.
        
        Section 3. Penalties
        3.1. Violations may result in fines up to $10,000,000 or 2% of annual revenue.
        3.2. Serious violations may result in imprisonment of up to 5 years.
        """
    
    @pytest.fixture
    def api_client(self):
        """Create a mock API client."""
        return MockAPIClient()
    
    @patch('app.utils.pdf_analyzer.PyPDF2.PdfReader')
    @patch('app.utils.regulatory_parser.PyPDF2.PdfReader')
    async def test_document_analysis_workflow(self, mock_parser_pdf, mock_analyzer_pdf, 
                                        api_client, sample_regulatory_text, tmp_path):
        """Test the full workflow from document upload to relationship mapping."""
        # Create mock PDF readers
        mock_reader = MagicMock()
        mock_page = MagicMock()
        mock_page.extract_text.return_value = sample_regulatory_text
        mock_reader.pages = [mock_page]
        mock_parser_pdf.return_value = mock_reader
        mock_analyzer_pdf.return_value = mock_reader
        
        # Create test documents
        doc_files = []
        for i in range(2):
            pdf_path = os.path.join(tmp_path, f"regulation{i}.pdf")
            with open(pdf_path, 'wb') as f:
                f.write(b'Dummy PDF content')
            doc_files.append(pdf_path)
        
        # Test analysis of each document
        analyzed_docs = []
        for doc_path in doc_files:
            result = await api_client.analyze_document(doc_path)
            assert result["confidence_score"] > 0.5
            assert len(result["requirements"]) >= 2
            analyzed_docs.append(result)
        
        # Test relationship mapping
        mapping_result = await api_client.map_relationships(analyzed_docs)
        
        # Verify mapping structure
        assert "documents" in mapping_result
        assert "hierarchical_relationships" in mapping_result
        assert "cross_references" in mapping_result
        assert "framework_mappings" in mapping_result
        
        # Verify document count
        assert len(mapping_result["documents"]) == 2
    
    @patch('app.utils.pdf_analyzer.extract_text_from_pdf')
    def test_document_analysis_to_json(self, mock_extract_text, sample_regulatory_text, tmp_path):
        """Test exporting document analysis to JSON and importing it back."""
        # Mock text extraction
        mock_extract_text.return_value = sample_regulatory_text
        
        # Create temp file
        pdf_path = os.path.join(tmp_path, "regulation.pdf")
        with open(pdf_path, 'wb') as f:
            f.write(b'Dummy PDF content')
        
        # Analyze document
        result = analyze_pdf_document(pdf_path)
        
        # Export to JSON
        json_path = os.path.join(tmp_path, "analysis.json")
        with open(json_path, 'w') as f:
            json.dump(result, f)
        
        # Import back from JSON
        with open(json_path, 'r') as f:
            loaded_result = json.load(f)
        
        # Verify data integrity
        assert loaded_result["confidence_score"] == result["confidence_score"]
        assert loaded_result["regulatory_sections"] == result["regulatory_sections"]
        assert loaded_result["entities"] == result["entities"]
