"""Unit tests for Enhanced Regulatory Map feature."""
import pytest
from datetime import datetime
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

# from app.main import app  # Temporarily disabled for testing
from fastapi import FastAPI
from app.api.regulatory_map import router as regulatory_map_router

# Create a minimal test app
app = FastAPI()
app.include_router(regulatory_map_router)
from app.db import get_db
from app.db.models import Base, RegulatoryEntity, RegulatoryRelationship, RegulatoryComplianceStatus
from app.db.models import EntityType, RelationshipType, ComplianceStatusEnum, ImplementationStatusEnum

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_regulatory_map.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """Override database dependency for testing."""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db
client = TestClient(app)


@pytest.fixture(scope="module")
def setup_database():
    """Set up test database."""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def db_session():
    """Create a database session for testing."""
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()


@pytest.fixture
def sample_entity(db_session):
    """Create a sample regulatory entity for testing."""
    entity = RegulatoryEntity(
        name="Test Regulation",
        description="A test regulation for unit testing",
        type=EntityType.REGULATION,
        jurisdiction="US",
        status="active",
        version="1.0"
    )
    db_session.add(entity)
    db_session.commit()
    db_session.refresh(entity)
    return entity


@pytest.fixture
def sample_entities(db_session):
    """Create multiple sample entities for testing."""
    entities = []
    
    # Parent regulation
    parent = RegulatoryEntity(
        name="Parent Regulation",
        description="Parent regulation",
        type=EntityType.REGULATION,
        jurisdiction="US",
        status="active"
    )
    db_session.add(parent)
    db_session.commit()
    db_session.refresh(parent)
    entities.append(parent)
    
    # Child requirement
    child = RegulatoryEntity(
        name="Child Requirement",
        description="Child requirement",
        type=EntityType.REQUIREMENT,
        parent_id=parent.id,
        jurisdiction="US",
        status="active"
    )
    db_session.add(child)
    db_session.commit()
    db_session.refresh(child)
    entities.append(child)
    
    # Control
    control = RegulatoryEntity(
        name="Test Control",
        description="Test control",
        type=EntityType.CONTROL,
        jurisdiction="US",
        status="active"
    )
    db_session.add(control)
    db_session.commit()
    db_session.refresh(control)
    entities.append(control)
    
    return entities


class TestRegulatoryEntityAPI:
    """Test cases for regulatory entity API endpoints."""
    
    def test_create_regulatory_entity(self, setup_database):
        """Test creating a new regulatory entity."""
        entity_data = {
            "name": "Test Entity",
            "description": "Test description",
            "type": "regulation",
            "jurisdiction": "US",
            "status": "active",
            "version": "1.0"
        }
        
        response = client.post("/api/v1/regulatory-map/entities", json=entity_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data["name"] == entity_data["name"]
        assert data["type"] == entity_data["type"]
        assert data["jurisdiction"] == entity_data["jurisdiction"]
        assert "id" in data
        assert "created_at" in data
    
    def test_get_regulatory_entities(self, setup_database, sample_entity):
        """Test retrieving regulatory entities."""
        response = client.get("/api/v1/regulatory-map/entities")
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1
        
        # Check if our sample entity is in the response
        entity_names = [entity["name"] for entity in data]
        assert sample_entity.name in entity_names
    
    def test_get_regulatory_entity_by_id(self, setup_database, sample_entity):
        """Test retrieving a specific regulatory entity by ID."""
        response = client.get(f"/api/v1/regulatory-map/entities/{sample_entity.id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data["id"] == sample_entity.id
        assert data["name"] == sample_entity.name
        assert data["type"] == sample_entity.type.value
    
    def test_get_nonexistent_entity(self, setup_database):
        """Test retrieving a non-existent entity."""
        response = client.get("/api/v1/regulatory-map/entities/nonexistent-id")
        assert response.status_code == 404
    
    def test_update_regulatory_entity(self, setup_database, sample_entity):
        """Test updating a regulatory entity."""
        update_data = {
            "name": "Updated Entity Name",
            "description": "Updated description"
        }
        
        response = client.put(
            f"/api/v1/regulatory-map/entities/{sample_entity.id}",
            json=update_data
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["name"] == update_data["name"]
        assert data["description"] == update_data["description"]
    
    def test_delete_regulatory_entity(self, setup_database, sample_entity):
        """Test soft deleting a regulatory entity."""
        response = client.delete(f"/api/v1/regulatory-map/entities/{sample_entity.id}")
        assert response.status_code == 200
        
        # Verify entity is soft deleted
        response = client.get(f"/api/v1/regulatory-map/entities/{sample_entity.id}")
        assert response.status_code == 404
    
    def test_filter_entities_by_type(self, setup_database, sample_entities):
        """Test filtering entities by type."""
        response = client.get("/api/v1/regulatory-map/entities?entity_type=regulation")
        assert response.status_code == 200
        
        data = response.json()
        for entity in data:
            assert entity["type"] == "regulation"
    
    def test_filter_entities_by_jurisdiction(self, setup_database, sample_entities):
        """Test filtering entities by jurisdiction."""
        response = client.get("/api/v1/regulatory-map/entities?jurisdiction=US")
        assert response.status_code == 200
        
        data = response.json()
        for entity in data:
            assert entity["jurisdiction"] == "US"
    
    def test_search_entities(self, setup_database, sample_entities):
        """Test searching entities by name/description."""
        response = client.get("/api/v1/regulatory-map/entities?search=Parent")
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) >= 1
        
        # Check that search results contain the search term
        found = False
        for entity in data:
            if "Parent" in entity["name"] or "Parent" in (entity["description"] or ""):
                found = True
                break
        assert found


class TestRegulatoryRelationshipAPI:
    """Test cases for regulatory relationship API endpoints."""
    
    def test_create_regulatory_relationship(self, setup_database, sample_entities):
        """Test creating a regulatory relationship."""
        parent, child, control = sample_entities
        
        relationship_data = {
            "source_id": parent.id,
            "target_id": child.id,
            "relationship_type": "contains",
            "strength": 0.8,
            "description": "Parent contains child"
        }
        
        response = client.post("/api/v1/regulatory-map/relationships", json=relationship_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data["source_id"] == relationship_data["source_id"]
        assert data["target_id"] == relationship_data["target_id"]
        assert data["relationship_type"] == relationship_data["relationship_type"]
        assert data["strength"] == relationship_data["strength"]
    
    def test_get_regulatory_relationships(self, setup_database, sample_entities):
        """Test retrieving regulatory relationships."""
        # First create a relationship
        parent, child, control = sample_entities
        
        relationship_data = {
            "source_id": parent.id,
            "target_id": child.id,
            "relationship_type": "contains"
        }
        
        client.post("/api/v1/regulatory-map/relationships", json=relationship_data)
        
        # Now retrieve relationships
        response = client.get("/api/v1/regulatory-map/relationships")
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1
    
    def test_get_entity_relationships(self, setup_database, sample_entities):
        """Test retrieving relationships for a specific entity."""
        parent, child, control = sample_entities
        
        # Create a relationship
        relationship_data = {
            "source_id": parent.id,
            "target_id": child.id,
            "relationship_type": "contains"
        }
        
        client.post("/api/v1/regulatory-map/relationships", json=relationship_data)
        
        # Get relationships for parent entity
        response = client.get(f"/api/v1/regulatory-map/entities/{parent.id}/relationships")
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1
        
        # Check that the relationship involves our parent entity
        found = False
        for rel in data:
            if rel["source_id"] == parent.id or rel["target_id"] == parent.id:
                found = True
                break
        assert found


class TestRegulatoryComplianceStatusAPI:
    """Test cases for regulatory compliance status API endpoints."""

    def test_create_compliance_status(self, setup_database, sample_entity):
        """Test creating a compliance status record."""
        status_data = {
            "entity_id": sample_entity.id,
            "compliance_status": "compliant",
            "implementation_status": "implemented",
            "compliance_score": 85.5,
            "risk_score": 15.0,
            "assessment_date": datetime.utcnow().isoformat(),
            "assessed_by": "Test Assessor",
            "notes": "Test compliance assessment"
        }

        response = client.post("/api/v1/regulatory-map/compliance-status", json=status_data)
        assert response.status_code == 200

        data = response.json()
        assert data["entity_id"] == status_data["entity_id"]
        assert data["compliance_status"] == status_data["compliance_status"]
        assert data["compliance_score"] == status_data["compliance_score"]

    def test_get_compliance_statuses(self, setup_database, sample_entity):
        """Test retrieving compliance status records."""
        # First create a status record
        status_data = {
            "entity_id": sample_entity.id,
            "compliance_status": "at_risk",
            "assessment_date": datetime.utcnow().isoformat(),
            "assessed_by": "Test Assessor"
        }

        client.post("/api/v1/regulatory-map/compliance-status", json=status_data)

        # Now retrieve status records
        response = client.get("/api/v1/regulatory-map/compliance-status")
        assert response.status_code == 200

        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1


class TestRegulatoryMapAPI:
    """Test cases for regulatory map API endpoints."""

    def test_get_regulatory_map(self, setup_database, sample_entities):
        """Test retrieving the complete regulatory map."""
        parent, child, control = sample_entities

        # Create a relationship
        relationship_data = {
            "source_id": parent.id,
            "target_id": child.id,
            "relationship_type": "contains"
        }
        client.post("/api/v1/regulatory-map/relationships", json=relationship_data)

        # Create a compliance status
        status_data = {
            "entity_id": parent.id,
            "compliance_status": "compliant",
            "assessment_date": datetime.utcnow().isoformat()
        }
        client.post("/api/v1/regulatory-map/compliance-status", json=status_data)

        # Get the regulatory map
        response = client.get(
            "/api/v1/regulatory-map/map?include_relationships=true&include_compliance_status=true"
        )
        assert response.status_code == 200

        data = response.json()
        assert "entities" in data
        assert "relationships" in data
        assert "compliance_statuses" in data
        assert "total_entities" in data
        assert "total_relationships" in data

        assert isinstance(data["entities"], list)
        assert isinstance(data["relationships"], list)
        assert isinstance(data["compliance_statuses"], list)
        assert len(data["entities"]) >= 3  # Our sample entities

    def test_get_regulatory_map_stats(self, setup_database, sample_entities):
        """Test retrieving regulatory map statistics."""
        response = client.get("/api/v1/regulatory-map/stats")
        assert response.status_code == 200

        data = response.json()
        assert "total_entities" in data
        assert "entities_by_type" in data
        assert "entities_by_jurisdiction" in data
        assert "total_relationships" in data
        assert "relationships_by_type" in data
        assert "compliance_summary" in data
        assert "last_updated" in data

        assert data["total_entities"] >= 3  # Our sample entities
        assert isinstance(data["entities_by_type"], dict)
        assert isinstance(data["entities_by_jurisdiction"], dict)

    def test_get_entity_hierarchy(self, setup_database, sample_entities):
        """Test retrieving entity hierarchy."""
        parent, child, control = sample_entities

        response = client.get(f"/api/v1/regulatory-map/entities/{parent.id}/hierarchy")
        assert response.status_code == 200

        data = response.json()
        assert "entity" in data
        assert "parents" in data
        assert "children" in data

        assert data["entity"]["id"] == parent.id
        assert isinstance(data["parents"], list)
        assert isinstance(data["children"], list)


class TestRegulatoryMapModels:
    """Test cases for regulatory map database models."""

    def test_regulatory_entity_creation(self, db_session):
        """Test creating a regulatory entity model."""
        entity = RegulatoryEntity(
            name="Test Entity",
            description="Test description",
            type=EntityType.REGULATION,
            jurisdiction="US",
            status="active"
        )

        db_session.add(entity)
        db_session.commit()
        db_session.refresh(entity)

        assert entity.id is not None
        assert entity.name == "Test Entity"
        assert entity.type == EntityType.REGULATION
        assert entity.created_at is not None
        assert entity.is_deleted == False

    def test_regulatory_relationship_creation(self, db_session, sample_entities):
        """Test creating a regulatory relationship model."""
        parent, child, control = sample_entities

        relationship = RegulatoryRelationship(
            source_id=parent.id,
            target_id=child.id,
            relationship_type=RelationshipType.CONTAINS,
            strength=0.9,
            description="Test relationship"
        )

        db_session.add(relationship)
        db_session.commit()
        db_session.refresh(relationship)

        assert relationship.id is not None
        assert relationship.source_id == parent.id
        assert relationship.target_id == child.id
        assert relationship.relationship_type == RelationshipType.CONTAINS
        assert relationship.strength == 0.9

    def test_compliance_status_creation(self, db_session, sample_entity):
        """Test creating a compliance status model."""
        status = RegulatoryComplianceStatus(
            entity_id=sample_entity.id,
            compliance_status=ComplianceStatusEnum.COMPLIANT,
            implementation_status=ImplementationStatusEnum.IMPLEMENTED,
            compliance_score=90.0,
            risk_score=10.0,
            assessment_date=datetime.utcnow(),
            assessed_by="Test Assessor"
        )

        db_session.add(status)
        db_session.commit()
        db_session.refresh(status)

        assert status.id is not None
        assert status.entity_id == sample_entity.id
        assert status.compliance_status == ComplianceStatusEnum.COMPLIANT
        assert status.compliance_score == 90.0

    def test_soft_delete_functionality(self, db_session):
        """Test soft delete functionality."""
        entity = RegulatoryEntity(
            name="Test Delete Entity",
            type=EntityType.CONTROL,
            jurisdiction="US"
        )

        db_session.add(entity)
        db_session.commit()
        db_session.refresh(entity)

        # Verify entity exists
        assert entity.is_deleted == False
        assert entity.deleted_at is None

        # Soft delete the entity
        entity.soft_delete()
        db_session.commit()

        # Verify soft delete
        assert entity.is_deleted == True
        assert entity.deleted_at is not None
