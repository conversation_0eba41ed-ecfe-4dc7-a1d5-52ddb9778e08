
"""
Workflow tests for regulatory document parsing and relationship mapping.
These tests simulate real user workflows and API interactions.
"""
import os
import pytest
from unittest.mock import patch, MagicMock
import tempfile
import json
import asyncio
from fastapi.testclient import TestClient

# Import the API router or FastAPI app as needed
# This is a placeholder for the actual import
try:
    from app.api.relationship_mapping import router as relationship_router
    from app.main import app
    HAS_FASTAPI = True
except ImportError:
    HAS_FASTAPI = False

class TestRegulatoryWorkflow:
    """Tests simulating user workflows for regulatory functionality."""
    
    @pytest.fixture
    def sample_regulatory_text(self):
        """Provide sample regulatory text for testing."""
        return """
        Article 1. Scope
        This regulation applies to all organizations processing personal data.
        
        Article 2. Requirements
        2.1. Organizations shall implement appropriate security measures.
        2.2. Data breaches must be reported within 72 hours.
        
        Section 3. Penalties
        3.1. Violations may result in fines up to $10,000,000 or 2% of annual revenue.
        3.2. Serious violations may result in imprisonment of up to 5 years.
        """
    
    @pytest.mark.skipif(not HAS_FASTAPI, reason="FastAPI not installed or API routes not available")
    def test_api_document_analysis(self, sample_regulatory_text, tmp_path):
        """Test the API endpoints for document analysis and relationship mapping."""
        # Set up test client if FastAPI is available
        if HAS_FASTAPI:
            client = TestClient(app)
            
            # Create mock PDF file
            pdf_path = os.path.join(tmp_path, "regulation.pdf")
            with open(pdf_path, 'wb') as f:
                f.write(b'Dummy PDF content for testing')
            
            # Mock the PDF extraction
            with patch('app.utils.regulatory_parser.RegulatoryParser.extract_text_from_pdf', 
                      return_value=sample_regulatory_text):
                # Upload document for analysis
                with open(pdf_path, 'rb') as f:
                    response = client.post(
                        "/api/documents/analyze",
                        files={"file": ("regulation.pdf", f, "application/pdf")}
                    )
                
                # Check response
                assert response.status_code == 200
                result = response.json()
                assert result["confidence_score"] > 0.5
                assert "requirements" in result
                assert "penalties" in result
                
                # Store document analysis
                document = result
                
                # Test relationship mapping endpoint
                response = client.post(
                    "/api/documents/map-relationships",
                    json=[document]
                )
                
                # Check mapping response
                assert response.status_code == 200
                mapping = response.json()
                assert "documents" in mapping
                assert "hierarchical_relationships" in mapping
                assert "cross_references" in mapping
                assert "framework_mappings" in mapping
    
    def test_document_processing_workflow(self, sample_regulatory_text, tmp_path):
        """Test a simulated user workflow for document processing."""
        from app.utils.regulatory_parser import RegulatoryParser
        from app.utils.relationship_mapper import RelationshipMapper
        
        # Initialize components
        parser = RegulatoryParser()
        mapper = RelationshipMapper()
        
        # Create mock documents
        doc_paths = []
        for i in range(3):
            doc_path = os.path.join(tmp_path, f"regulation{i}.pdf")
            with open(doc_path, 'wb') as f:
                f.write(f"Dummy content for regulation {i}".encode())
            doc_paths.append(doc_path)
        
        # Process documents
        documents = []
        
        # Mock extraction
        with patch.object(parser, 'extract_text_from_pdf', return_value=sample_regulatory_text):
            for i, path in enumerate(doc_paths):
                # Parse document
                doc = parser.parse_document(path)
                doc["id"] = f"doc{i}"
                doc["title"] = f"Regulation {i}"
                doc["file_name"] = os.path.basename(path)
                documents.append(doc)
                
                # Verify document analysis
                assert doc["confidence_score"] > 0.5
                assert len(doc["requirements"]) >= 2
                assert len(doc["penalties"]) >= 1
        
        # Map relationships
        result = mapper.map_document_relationships(documents)
        
        # Save mapping results
        mapping_path = os.path.join(tmp_path, "mapping_results.json")
        with open(mapping_path, 'w') as f:
            json.dump(result, f)
        
        # Verify saved mapping can be loaded
        with open(mapping_path, 'r') as f:
            loaded_mapping = json.load(f)
        
        assert len(loaded_mapping["documents"]) == len(documents)
        
        # Simulate adding a new document and updating the mapping
        with patch.object(parser, 'extract_text_from_pdf', return_value=sample_regulatory_text):
            new_doc_path = os.path.join(tmp_path, "new_regulation.pdf")
            with open(new_doc_path, 'wb') as f:
                f.write(b"Dummy content for new regulation")
            
            new_doc = parser.parse_document(new_doc_path)
            new_doc["id"] = "new_doc"
            new_doc["title"] = "New Regulation with References to Regulation 0"
            new_doc["file_name"] = os.path.basename(new_doc_path)
            
            # Add a cross-reference to an existing document
            new_doc["sections"]["articles"].append(
                "Article 4: This regulation extends the requirements in Regulation 0."
            )
            
            # Update mapping with the new document
            documents.append(new_doc)
            updated_result = mapper.map_document_relationships(documents)
            
            # Verify updated mapping
            assert len(updated_result["documents"]) == len(documents)
