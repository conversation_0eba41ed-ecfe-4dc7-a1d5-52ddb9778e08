"""Tests for UI integration between FastAPI and Starlette Admin."""
import pytest
from fastapi.testclient import TestClient
from app.main import app
from unittest.mock import patch

client = TestClient(app)

def test_dashboard_links_to_admin():
    """Test that the main dashboard links to the admin interface."""
    response = client.get("/")
    assert response.status_code == 200

    # Check for admin UI link
    assert '/ui/manage' in response.text
    assert 'Admin' in response.text

def test_admin_links_to_dashboard():
    """
    Test that the admin UI links back to the main dashboard.

    This is a limited test since we can't directly test the WSGI app content.
    """
    # We can't directly test the admin content via TestClient since it's mounted as WSGI
    # But we can verify the mount exists
    admin_mount = next((route for route in app.routes if getattr(route, "path", "") == "/ui/manage"), None)
    assert admin_mount is not None

def test_theme_persistence():
    """
    Test that theme preference persists between dashboard and admin UI.

    Note: This is a conceptual test that would require browser automation
    to fully test with localStorage.
    """
    # In a real test environment, this would use Selenium or similar to:
    # 1. Visit the dashboard
    # 2. Toggle dark mode
    # 3. Navigate to admin
    # 4. Verify dark mode is still active
    # 5. Toggle back to light mode
    # 6. Return to dashboard
    # 7. Verify light mode is active

    # Conceptual placeholder assertion
    assert "localStorage.getItem('theme')" in client.get("/").text

def test_consistent_styling():
    """
    Test that styling elements are consistent across interfaces.
    """
    response = client.get("/")
    assert response.status_code == 200

    # Check for consistent UI elements
    assert 'theme-switch' in response.text
    assert 'material-icons' in response.text
    assert 'bootstrap' in response.text.lower()
    assert 'roboto' in response.text.lower()