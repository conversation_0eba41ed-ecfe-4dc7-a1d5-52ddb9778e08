
import pytest
from sqlalchemy.exc import SQLAlchemyError
from unittest.mock import patch, MagicMock
import sys
import os
import datetime

# Add app directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.db.database import get_db, init_db, Base
from app.db.models import Country, Regulator, RegulationURL, RegulationText

class TestDatabaseComprehensive:
    """Comprehensive tests for database operations."""
    
    def test_get_db_session_lifecycle(self, monkeypatch):
        """Test the complete lifecycle of a database session."""
        # Mock SQLAlchemy session
        mock_session = MagicMock()
        mock_session_maker = MagicMock(return_value=mock_session)
        
        # Apply the mock
        monkeypatch.setattr("app.db.database.SessionLocal", mock_session_maker)
        
        # Use the get_db function
        with get_db() as db:
            # Verify session was created
            assert db is mock_session
            
        # Verify session was closed
        mock_session.close.assert_called_once()
    
    def test_get_db_with_exception(self, monkeypatch):
        """Test get_db with an exception."""
        # Mock SQLAlchemy session
        mock_session = MagicMock()
        mock_session_maker = MagicMock(return_value=mock_session)
        
        # Apply the mock
        monkeypatch.setattr("app.db.database.SessionLocal", mock_session_maker)
        
        # Use the get_db function with an exception
        try:
            with get_db() as db:
                assert db is mock_session
                raise ValueError("Test exception")
        except ValueError:
            pass
            
        # Verify session was closed even with exception
        mock_session.close.assert_called_once()
    
    @patch("app.db.database.create_engine")
    def test_init_db(self, mock_create_engine):
        """Test database initialization."""
        # Setup mocks
        mock_engine = MagicMock()
        mock_create_engine.return_value = mock_engine
        
        # Call init_db
        init_db("sqlite:///test_init.db")
        
        # Verify engine creation
        mock_create_engine.assert_called_once_with(
            "sqlite:///test_init.db", 
            connect_args={"check_same_thread": False}
        )
        
        # Verify tables creation
        Base.metadata.create_all.assert_called_once_with(bind=mock_engine)
    
    def test_model_relationships(self, db_session):
        """Test relationships between models."""
        # Create test data with relationships
        country = Country(
            name="Test Country", 
            code="TC",
            region="Test Region"
        )
        db_session.add(country)
        db_session.flush()
        
        regulator = Regulator(
            name="Test Regulator",
            country_id=country.id,
            website="https://regulator.test"
        )
        db_session.add(regulator)
        db_session.flush()
        
        url = RegulationURL(
            url="https://regulation.test/law",
            country_id=country.id,
            regulator_id=regulator.id,
            title="Test Regulation",
            date_added=datetime.datetime.now()
        )
        db_session.add(url)
        db_session.flush()
        
        regulation_text = RegulationText(
            url_id=url.id,
            text="This is a test regulation text",
            language="en",
            date_processed=datetime.datetime.now()
        )
        db_session.add(regulation_text)
        db_session.commit()
        
        # Test country-regulator relationship
        db_country = db_session.query(Country).filter_by(id=country.id).first()
        assert db_country is not None
        assert len(db_country.regulators) > 0
        assert db_country.regulators[0].name == "Test Regulator"
        
        # Test regulator-country relationship
        db_regulator = db_session.query(Regulator).filter_by(id=regulator.id).first()
        assert db_regulator is not None
        assert db_regulator.country.name == "Test Country"
        
        # Test URL relationships
        db_url = db_session.query(RegulationURL).filter_by(id=url.id).first()
        assert db_url is not None
        assert db_url.country.name == "Test Country"
        assert db_url.regulator.name == "Test Regulator"
        assert len(db_url.texts) > 0
        assert db_url.texts[0].text == "This is a test regulation text"
        
        # Test regulation text relationship
        db_text = db_session.query(RegulationText).filter_by(id=regulation_text.id).first()
        assert db_text is not None
        assert db_text.url.title == "Test Regulation"
