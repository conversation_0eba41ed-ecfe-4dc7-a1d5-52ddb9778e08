
"""
Pytest configuration file with common test fixtures.
"""
import os
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

from app.main import app
try:
    from app.db.models import Base
    MODELS_AVAILABLE = True
except ImportError:
    MODELS_AVAILABLE = False
    Base = None
from app.db import get_db

# Check if we should use PostgreSQL for testing
USE_POSTGRESQL = os.getenv("USE_POSTGRESQL_TESTS", "false").lower() == "true"

if USE_POSTGRESQL:
    try:
        from tests.conftest_postgresql import *
        print("✅ Using PostgreSQL test configuration")
    except ImportError:
        print("⚠️ PostgreSQL test configuration not available, falling back to SQLite")
        USE_POSTGRESQL = False

# Use in-memory SQLite for testing
SQLALCHEMY_DATABASE_URL = "sqlite:///:memory:"

def pytest_configure(config):
    """
    Configure pytest.

    Args:
        config: Pytest configuration object
    """
    config.addinivalue_line("markers", "ui: mark test as requiring UI testing with selenium")

@pytest.fixture(scope="session")
def test_engine():
    """Create a SQLAlchemy engine for testing."""
    engine = create_engine(
        SQLALCHEMY_DATABASE_URL,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    Base.metadata.create_all(bind=engine)
    yield engine
    Base.metadata.drop_all(bind=engine)

@pytest.fixture(scope="function")
def db_session(test_engine):
    """Create a SQLAlchemy session for testing."""
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.rollback()
        session.close()

@pytest.fixture(scope="function")
def client(db_session):
    """Create a FastAPI TestClient with a test database session."""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass

    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as test_client:
        yield test_client
    app.dependency_overrides.clear()

@pytest.fixture(scope="function")
def test_data(db_session):
    """Create test data for the database."""
    from app.db.models import (
        Country, Regulator, RegulationTag, RegulationCategory,
        Industry, Regulation, ComplianceRequirement, RegulationDocument
    )

    # Create test country
    country = Country(
        name="Test Country",
        code="TC",
        region="Test Region",
        subregion="Test Subregion",
        flag_emoji="🏳️"
    )
    db_session.add(country)

    # Create test regulator
    regulator = Regulator(
        name="Test Regulator",
        website="https://example.com",
        country_id=1
    )
    db_session.add(regulator)

    # Create test regulation tag
    tag = RegulationTag(
        name="Test Tag",
        description="Test tag description",
        color="#1890ff"
    )
    db_session.add(tag)

    # Create test regulation category
    category = RegulationCategory(
        name="Test Category",
        description="Test category description",
        icon="bank"
    )
    db_session.add(category)

    # Create test industry
    industry = Industry(
        name="Test Industry",
        description="Test industry description",
        sector="Financial Services"
    )
    db_session.add(industry)

    # Commit to get IDs
    db_session.commit()

    # Create test regulation
    regulation = Regulation(
        title="Test Regulation",
        description="Test regulation description",
        status="Active",
        reference_number="REG-001",
        category_id=category.id
    )
    regulation.tags.append(tag)
    regulation.industries.append(industry)
    db_session.add(regulation)
    db_session.commit()

    # Create test compliance requirement
    requirement = ComplianceRequirement(
        text="Test requirement",
        section="Section 1",
        priority="High",
        status="In Progress",
        regulation_id=regulation.id
    )
    db_session.add(requirement)

    # Create test regulation document
    document = RegulationDocument(
        name="Test Document",
        description="Test document description",
        document_type="PDF",
        file_url="https://example.com/test.pdf",
        regulation_id=regulation.id
    )
    db_session.add(document)

    db_session.commit()

    return {
        "country": country,
        "regulator": regulator,
        "tag": tag,
        "category": category,
        "industry": industry,
        "regulation": regulation,
        "requirement": requirement,
        "document": document
    }
