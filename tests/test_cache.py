"""
Tests for the cache utilities.
"""
import pytest
from unittest.mock import MagicMock, patch
import json
import hashlib
import redis

from app.utils.cache import (
    get_redis_client, generate_cache_key, cache, api_cache,
    invalidate_cache, clear_cache
)

@pytest.fixture
def mock_redis():
    """Mock Redis client."""
    mock_client = MagicMock(spec=redis.Redis)
    mock_client.ping.return_value = True
    mock_client.get.return_value = None
    mock_client.setex.return_value = True
    mock_client.keys.return_value = ["key1", "key2"]
    mock_client.delete.return_value = 2
    return mock_client

def test_get_redis_client():
    """Test getting the Redis client."""
    with patch("redis.from_url") as mock_from_url:
        mock_client = MagicMock()
        mock_from_url.return_value = mock_client
        
        # Call the function
        client = get_redis_client()
        
        # Check that the client was created
        assert client == mock_client
        mock_from_url.assert_called_once()
        
        # Call the function again
        client2 = get_redis_client()
        
        # Check that the same client is returned
        assert client2 == mock_client
        assert mock_from_url.call_count == 1

def test_generate_cache_key():
    """Test generating a cache key."""
    # Test with simple arguments
    key = generate_cache_key("test_func", (1, 2, 3), {"a": "b"})
    assert isinstance(key, str)
    assert len(key) == 32  # MD5 hash length
    
    # Test with complex arguments
    key2 = generate_cache_key("test_func", ({"x": 1}, [1, 2, 3]), {"obj": {"y": 2}})
    assert isinstance(key2, str)
    assert len(key2) == 32
    
    # Test that different arguments produce different keys
    key3 = generate_cache_key("test_func", (1, 2, 4), {"a": "b"})
    assert key != key3
    
    # Test that different function names produce different keys
    key4 = generate_cache_key("other_func", (1, 2, 3), {"a": "b"})
    assert key != key4

@pytest.mark.asyncio
async def test_cache_decorator(mock_redis):
    """Test the cache decorator."""
    with patch("app.utils.cache.get_redis_client", return_value=mock_redis):
        # Create a mock async function
        mock_func = MagicMock()
        mock_func.__name__ = "test_func"
        mock_func.__module__ = "test_module"
        mock_func.return_value = {"result": "test"}
        
        # Create an async function that returns the mock's return value
        async def async_func(*args, **kwargs):
            return mock_func(*args, **kwargs)
        
        # Apply the cache decorator
        cached_func = cache(ttl=60, key_prefix="test")(async_func)
        
        # Call the function
        result = await cached_func(1, 2, a="b")
        
        # Check that the function was called
        mock_func.assert_called_once_with(1, 2, a="b")
        assert result == {"result": "test"}
        
        # Check that the result was cached
        mock_redis.setex.assert_called_once()
        cache_key = mock_redis.setex.call_args[0][0]
        assert "test:test_module.test_func:" in cache_key
        assert mock_redis.setex.call_args[0][1] == 60
        assert json.loads(mock_redis.setex.call_args[0][2]) == {"result": "test"}
        
        # Reset the mock
        mock_func.reset_mock()
        mock_redis.setex.reset_mock()
        
        # Set up the mock to return a cached result
        mock_redis.get.return_value = json.dumps({"result": "cached"})
        
        # Call the function again
        result = await cached_func(1, 2, a="b")
        
        # Check that the function was not called
        mock_func.assert_not_called()
        assert result == {"result": "cached"}
        
        # Check that the result was not cached again
        mock_redis.setex.assert_not_called()

@pytest.mark.asyncio
async def test_cache_decorator_skip_args(mock_redis):
    """Test the cache decorator with skipped arguments."""
    with patch("app.utils.cache.get_redis_client", return_value=mock_redis):
        # Create a mock async function
        mock_func = MagicMock()
        mock_func.__name__ = "test_func"
        mock_func.__module__ = "test_module"
        mock_func.return_value = {"result": "test"}
        
        # Create an async function that returns the mock's return value
        async def async_func(*args, **kwargs):
            return mock_func(*args, **kwargs)
        
        # Apply the cache decorator with skipped arguments
        cached_func = cache(ttl=60, key_prefix="test", skip_args=[0], skip_kwargs=["b"])(async_func)
        
        # Call the function
        result = await cached_func(1, 2, a="a", b="b")
        
        # Check that the function was called
        mock_func.assert_called_once_with(1, 2, a="a", b="b")
        assert result == {"result": "test"}
        
        # Check that the result was cached
        mock_redis.setex.assert_called_once()
        cache_key = mock_redis.setex.call_args[0][0]
        
        # Reset the mock
        mock_func.reset_mock()
        mock_redis.setex.reset_mock()
        mock_redis.get.return_value = None
        
        # Call the function with different skipped arguments
        result = await cached_func(3, 2, a="a", b="c")
        
        # Check that the function was called again (because the cache key is the same)
        mock_func.assert_called_once_with(3, 2, a="a", b="c")
        assert result == {"result": "test"}
        
        # Check that the result was cached again
        mock_redis.setex.assert_called_once()
        assert mock_redis.setex.call_args[0][0] == cache_key

@pytest.mark.asyncio
async def test_api_cache_decorator(mock_redis):
    """Test the API cache decorator."""
    with patch("app.utils.cache.get_redis_client", return_value=mock_redis):
        # Create a mock request
        mock_request = MagicMock()
        mock_request.method = "GET"
        mock_request.url.path = "/api/v1/test"
        mock_request.query_params = {"param1": "value1", "param2": "value2"}
        mock_request.headers = {"header1": "value1", "header2": "value2"}
        
        # Create a mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.headers = {"content-type": "application/json"}
        mock_response.media_type = "application/json"
        mock_response.body = b'{"result": "test"}'
        
        # Create a mock async function
        mock_func = MagicMock()
        mock_func.__name__ = "test_func"
        mock_func.__module__ = "test_module"
        mock_func.return_value = mock_response
        
        # Create an async function that returns the mock's return value
        async def async_func(request, *args, **kwargs):
            return mock_func(request, *args, **kwargs)
        
        # Apply the API cache decorator
        cached_func = api_cache(ttl=60, key_prefix="api_test")(async_func)
        
        # Call the function
        result = await cached_func(mock_request, 1, 2, a="b")
        
        # Check that the function was called
        mock_func.assert_called_once_with(mock_request, 1, 2, a="b")
        assert result == mock_response
        
        # Check that the response was cached
        mock_redis.setex.assert_called_once()
        cache_key = mock_redis.setex.call_args[0][0]
        assert mock_redis.setex.call_args[0][1] == 60
        cached_data = json.loads(mock_redis.setex.call_args[0][2])
        assert cached_data["content"] == '{"result": "test"}'
        assert cached_data["status_code"] == 200
        assert cached_data["headers"] == {"content-type": "application/json"}
        assert cached_data["media_type"] == "application/json"
        
        # Reset the mock
        mock_func.reset_mock()
        mock_redis.setex.reset_mock()
        
        # Set up the mock to return a cached response
        mock_redis.get.return_value = json.dumps({
            "content": '{"result": "cached"}',
            "status_code": 200,
            "headers": {"content-type": "application/json"},
            "media_type": "application/json"
        })
        
        # Call the function again
        result = await cached_func(mock_request, 1, 2, a="b")
        
        # Check that the function was not called
        mock_func.assert_not_called()
        
        # Check that the cached response was returned
        assert result.body == b'{"result": "cached"}'
        assert result.status_code == 200
        assert result.headers["content-type"] == "application/json"
        assert result.media_type == "application/json"

def test_invalidate_cache(mock_redis):
    """Test invalidating cache entries."""
    with patch("app.utils.cache.get_redis_client", return_value=mock_redis):
        # Invalidate cache entries
        result = invalidate_cache("test:*")
        
        # Check that the keys were deleted
        mock_redis.keys.assert_called_once_with("test:*")
        mock_redis.delete.assert_called_once_with("key1", "key2")
        assert result == 2
        
        # Test with no matching keys
        mock_redis.keys.return_value = []
        result = invalidate_cache("test:*")
        assert result == 0
        
        # Test with Redis error
        mock_redis.keys.side_effect = Exception("Redis error")
        result = invalidate_cache("test:*")
        assert result == 0

def test_clear_cache(mock_redis):
    """Test clearing all cache entries."""
    with patch("app.utils.cache.get_redis_client", return_value=mock_redis):
        # Clear all cache entries
        result = clear_cache()
        
        # Check that all keys were deleted
        mock_redis.keys.assert_called_once_with("*")
        mock_redis.delete.assert_called_once_with("key1", "key2")
        assert result == 2
        
        # Test with no keys
        mock_redis.keys.return_value = []
        result = clear_cache()
        assert result == 0
        
        # Test with Redis error
        mock_redis.keys.side_effect = Exception("Redis error")
        result = clear_cache()
        assert result == 0
