
"""Test cases for the regulation URL routes."""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from main import app
from app.db import models


@pytest.fixture
def test_regulation_url(db: Session):
    """Create a test regulation URL."""
    regulation_url = models.RegulationURL(
        url="https://example.com/test-regulation",
        domain="example.com",
        title="Test Regulation",
        confidence_level=0.9
    )
    db.add(regulation_url)
    db.commit()
    db.refresh(regulation_url)
    yield regulation_url
    db.delete(regulation_url)
    db.commit()


def test_regulation_urls_html_page(client: TestClient, test_regulation_url):
    """Test the HTML page that lists regulation URLs."""
    response = client.get("/api/v1/regulation-urls")
    assert response.status_code == 200
    assert "text/html" in response.headers["content-type"]
    # Check that the page contains the URL title
    assert test_regulation_url.title in response.text
    assert test_regulation_url.url in response.text


def test_regulations_api_endpoint(client: TestClient, test_regulation_url):
    """Test the API endpoint for regulations."""
    response = client.get("/api/v1/regulations/")
    assert response.status_code == 200
    assert response.headers["content-type"] == "application/json"
    
    data = response.json()
    assert isinstance(data, list)
    
    # Check if our test regulation URL is in the results
    found = False
    for item in data:
        if item.get("id") == test_regulation_url.id:
            found = True
            assert item["url"] == test_regulation_url.url
            assert item["title"] == test_regulation_url.title
            break
    
    assert found, "Test regulation URL not found in API response"
