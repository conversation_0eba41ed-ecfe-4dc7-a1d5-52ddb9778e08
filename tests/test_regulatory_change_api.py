"""
Unit tests for the Regulatory Change Workflow API endpoints.
"""
import unittest
from unittest.mock import patch, MagicMock
import json
import datetime
from flask import Flask
from flask.testing import FlaskClient

# Import the modules to be tested
# These imports will need to be updated based on the actual implementation
from app.api import create_app
from app.regulatory_change.models import (
    RegulatoryChange,
    WorkflowDefinition,
    WorkflowInstance,
    ImpactAssessment
)
from app.regulatory_change.services import (
    RegulatoryChangeService,
    WorkflowService,
    ImpactAssessmentService,
    ImplementationService
)


class TestRegulatoryChangeAPI(unittest.TestCase):
    """Test cases for Regulatory Change Workflow API endpoints."""

    def setUp(self):
        """Set up test fixtures."""
        # Create Flask test app
        self.app = create_app(testing=True)
        self.client = self.app.test_client()
        
        # Create mock services
        self.mock_regulatory_service = MagicMock(spec=RegulatoryChangeService)
        self.mock_workflow_service = MagicMock(spec=WorkflowService)
        self.mock_impact_service = MagicMock(spec=ImpactAssessmentService)
        self.mock_implementation_service = MagicMock(spec=ImplementationService)
        
        # Patch services in the app
        self.patches = [
            patch('app.api.regulatory_change.service', self.mock_regulatory_service),
            patch('app.api.workflow.service', self.mock_workflow_service),
            patch('app.api.impact.service', self.mock_impact_service),
            patch('app.api.implementation.service', self.mock_implementation_service)
        ]
        
        for p in self.patches:
            p.start()
        
        # Create sample data
        self.sample_regulatory_change = RegulatoryChange(
            id="rc123",
            title="New Security Requirements",
            description="Financial institutions shall implement new security measures.",
            source="Test Source",
            change_type="NEW_REGULATION",
            detected_date=datetime.datetime.now()
        )
        
        self.sample_workflow_definition = WorkflowDefinition(
            id="wd123",
            name="Standard Regulatory Change Workflow",
            description="Standard workflow for regulatory changes"
        )
        
        self.sample_workflow_instance = WorkflowInstance(
            id="wi123",
            workflow_definition_id="wd123",
            regulatory_change_id="rc123",
            status="IN_PROGRESS",
            current_stage="Initial Assessment",
            created_at=datetime.datetime.now()
        )
        
        self.sample_impact_assessment = ImpactAssessment(
            id="ia123",
            workflow_instance_id="wi123",
            regulatory_change_id="rc123",
            status="DRAFT",
            created_at=datetime.datetime.now()
        )
        
        # Set up authentication
        self.auth_headers = {
            'Authorization': 'Bearer test_token',
            'Content-Type': 'application/json'
        }

    def tearDown(self):
        """Tear down test fixtures."""
        for p in self.patches:
            p.stop()

    def test_get_regulatory_changes(self):
        """Test GET /api/v1/regulatory-changes endpoint."""
        # Configure mock to return sample data
        self.mock_regulatory_service.get_regulatory_changes.return_value = [
            self.sample_regulatory_change
        ]
        
        # Make request
        response = self.client.get(
            '/api/v1/regulatory-changes',
            headers=self.auth_headers
        )
        
        # Assertions
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['id'], 'rc123')
        self.assertEqual(data[0]['title'], 'New Security Requirements')
        
        # Verify service was called
        self.mock_regulatory_service.get_regulatory_changes.assert_called_once()

    def test_get_regulatory_change(self):
        """Test GET /api/v1/regulatory-changes/{id} endpoint."""
        # Configure mock to return sample data
        self.mock_regulatory_service.get_regulatory_change.return_value = self.sample_regulatory_change
        
        # Make request
        response = self.client.get(
            '/api/v1/regulatory-changes/rc123',
            headers=self.auth_headers
        )
        
        # Assertions
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['id'], 'rc123')
        self.assertEqual(data['title'], 'New Security Requirements')
        
        # Verify service was called with correct ID
        self.mock_regulatory_service.get_regulatory_change.assert_called_once_with('rc123')

    def test_create_regulatory_change(self):
        """Test POST /api/v1/regulatory-changes endpoint."""
        # Configure mock to return created object
        self.mock_regulatory_service.create_regulatory_change.return_value = self.sample_regulatory_change
        
        # Request data
        request_data = {
            'title': 'New Security Requirements',
            'description': 'Financial institutions shall implement new security measures.',
            'source': 'Test Source',
            'change_type': 'NEW_REGULATION'
        }
        
        # Make request
        response = self.client.post(
            '/api/v1/regulatory-changes',
            headers=self.auth_headers,
            data=json.dumps(request_data)
        )
        
        # Assertions
        self.assertEqual(response.status_code, 201)
        data = json.loads(response.data)
        self.assertEqual(data['id'], 'rc123')
        self.assertEqual(data['title'], 'New Security Requirements')
        
        # Verify service was called with correct data
        self.mock_regulatory_service.create_regulatory_change.assert_called_once()
        call_args = self.mock_regulatory_service.create_regulatory_change.call_args[0][0]
        self.assertEqual(call_args['title'], 'New Security Requirements')
        self.assertEqual(call_args['change_type'], 'NEW_REGULATION')

    def test_update_regulatory_change(self):
        """Test PUT /api/v1/regulatory-changes/{id} endpoint."""
        # Configure mock to return updated object
        updated_change = self.sample_regulatory_change
        updated_change.title = "Updated Security Requirements"
        self.mock_regulatory_service.update_regulatory_change.return_value = updated_change
        
        # Request data
        request_data = {
            'title': 'Updated Security Requirements',
            'description': 'Financial institutions shall implement new security measures.',
            'source': 'Test Source',
            'change_type': 'NEW_REGULATION'
        }
        
        # Make request
        response = self.client.put(
            '/api/v1/regulatory-changes/rc123',
            headers=self.auth_headers,
            data=json.dumps(request_data)
        )
        
        # Assertions
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['id'], 'rc123')
        self.assertEqual(data['title'], 'Updated Security Requirements')
        
        # Verify service was called with correct data
        self.mock_regulatory_service.update_regulatory_change.assert_called_once()
        call_args = self.mock_regulatory_service.update_regulatory_change.call_args
        self.assertEqual(call_args[0][0], 'rc123')
        self.assertEqual(call_args[0][1]['title'], 'Updated Security Requirements')

    def test_get_workflow_definitions(self):
        """Test GET /api/v1/workflow-definitions endpoint."""
        # Configure mock to return sample data
        self.mock_workflow_service.get_workflow_definitions.return_value = [
            self.sample_workflow_definition
        ]
        
        # Make request
        response = self.client.get(
            '/api/v1/workflow-definitions',
            headers=self.auth_headers
        )
        
        # Assertions
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['id'], 'wd123')
        self.assertEqual(data[0]['name'], 'Standard Regulatory Change Workflow')
        
        # Verify service was called
        self.mock_workflow_service.get_workflow_definitions.assert_called_once()

    def test_create_workflow_instance(self):
        """Test POST /api/v1/workflow-instances endpoint."""
        # Configure mock to return created object
        self.mock_workflow_service.create_workflow_instance.return_value = self.sample_workflow_instance
        
        # Request data
        request_data = {
            'workflow_definition_id': 'wd123',
            'regulatory_change_id': 'rc123'
        }
        
        # Make request
        response = self.client.post(
            '/api/v1/workflow-instances',
            headers=self.auth_headers,
            data=json.dumps(request_data)
        )
        
        # Assertions
        self.assertEqual(response.status_code, 201)
        data = json.loads(response.data)
        self.assertEqual(data['id'], 'wi123')
        self.assertEqual(data['workflow_definition_id'], 'wd123')
        self.assertEqual(data['regulatory_change_id'], 'rc123')
        self.assertEqual(data['status'], 'IN_PROGRESS')
        
        # Verify service was called with correct data
        self.mock_workflow_service.create_workflow_instance.assert_called_once()
        call_args = self.mock_workflow_service.create_workflow_instance.call_args[0][0]
        self.assertEqual(call_args['workflow_definition_id'], 'wd123')
        self.assertEqual(call_args['regulatory_change_id'], 'rc123')

    def test_progress_workflow(self):
        """Test POST /api/v1/workflow-instances/{id}/progress endpoint."""
        # Configure mock to return updated object
        progressed_instance = self.sample_workflow_instance
        progressed_instance.current_stage = "Impact Analysis"
        self.mock_workflow_service.progress_workflow.return_value = progressed_instance
        
        # Make request
        response = self.client.post(
            '/api/v1/workflow-instances/wi123/progress',
            headers=self.auth_headers
        )
        
        # Assertions
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['id'], 'wi123')
        self.assertEqual(data['current_stage'], 'Impact Analysis')
        
        # Verify service was called with correct ID
        self.mock_workflow_service.progress_workflow.assert_called_once_with('wi123')

    def test_create_impact_assessment(self):
        """Test POST /api/v1/impact-assessments endpoint."""
        # Configure mock to return created object
        self.mock_impact_service.create_impact_assessment.return_value = self.sample_impact_assessment
        
        # Request data
        request_data = {
            'workflow_instance_id': 'wi123',
            'regulatory_change_id': 'rc123',
            'template_id': 'template123'
        }
        
        # Make request
        response = self.client.post(
            '/api/v1/impact-assessments',
            headers=self.auth_headers,
            data=json.dumps(request_data)
        )
        
        # Assertions
        self.assertEqual(response.status_code, 201)
        data = json.loads(response.data)
        self.assertEqual(data['id'], 'ia123')
        self.assertEqual(data['workflow_instance_id'], 'wi123')
        self.assertEqual(data['regulatory_change_id'], 'rc123')
        self.assertEqual(data['status'], 'DRAFT')
        
        # Verify service was called with correct data
        self.mock_impact_service.create_impact_assessment.assert_called_once()
        call_args = self.mock_impact_service.create_impact_assessment.call_args[0][0]
        self.assertEqual(call_args['workflow_instance_id'], 'wi123')
        self.assertEqual(call_args['regulatory_change_id'], 'rc123')
        self.assertEqual(call_args['template_id'], 'template123')

    def test_update_impact_assessment(self):
        """Test PUT /api/v1/impact-assessments/{id} endpoint."""
        # Configure mock to return updated object
        updated_assessment = self.sample_impact_assessment
        updated_assessment.status = "COMPLETED"
        self.mock_impact_service.update_impact_assessment.return_value = updated_assessment
        
        # Request data
        request_data = {
            'data': {
                'section1': {
                    'field1': 'value1',
                    'field2': 'value2'
                },
                'section2': {
                    'field3': 'value3'
                }
            },
            'status': 'COMPLETED'
        }
        
        # Make request
        response = self.client.put(
            '/api/v1/impact-assessments/ia123',
            headers=self.auth_headers,
            data=json.dumps(request_data)
        )
        
        # Assertions
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['id'], 'ia123')
        self.assertEqual(data['status'], 'COMPLETED')
        
        # Verify service was called with correct data
        self.mock_impact_service.update_impact_assessment.assert_called_once()
        call_args = self.mock_impact_service.update_impact_assessment.call_args
        self.assertEqual(call_args[0][0], 'ia123')
        self.assertEqual(call_args[0][1]['status'], 'COMPLETED')
        self.assertEqual(call_args[0][1]['data']['section1']['field1'], 'value1')

    def test_create_implementation_plan(self):
        """Test POST /api/v1/implementation-plans endpoint."""
        # Configure mock to return created object
        sample_plan = MagicMock()
        sample_plan.id = "ip123"
        sample_plan.workflow_instance_id = "wi123"
        sample_plan.name = "Implementation Plan"
        sample_plan.status = "DRAFT"
        self.mock_implementation_service.create_implementation_plan.return_value = sample_plan
        
        # Request data
        request_data = {
            'workflow_instance_id': 'wi123',
            'name': 'Implementation Plan',
            'description': 'Plan to implement regulatory changes',
            'target_completion_date': '2023-01-01'
        }
        
        # Make request
        response = self.client.post(
            '/api/v1/implementation-plans',
            headers=self.auth_headers,
            data=json.dumps(request_data)
        )
        
        # Assertions
        self.assertEqual(response.status_code, 201)
        data = json.loads(response.data)
        self.assertEqual(data['id'], 'ip123')
        self.assertEqual(data['workflow_instance_id'], 'wi123')
        self.assertEqual(data['name'], 'Implementation Plan')
        self.assertEqual(data['status'], 'DRAFT')
        
        # Verify service was called with correct data
        self.mock_implementation_service.create_implementation_plan.assert_called_once()
        call_args = self.mock_implementation_service.create_implementation_plan.call_args[0][0]
        self.assertEqual(call_args['workflow_instance_id'], 'wi123')
        self.assertEqual(call_args['name'], 'Implementation Plan')
        self.assertEqual(call_args['target_completion_date'], '2023-01-01')

    def test_add_implementation_milestone(self):
        """Test POST /api/v1/implementation-plans/{id}/milestones endpoint."""
        # Configure mock to return created object
        sample_milestone = MagicMock()
        sample_milestone.id = "im123"
        sample_milestone.implementation_plan_id = "ip123"
        sample_milestone.name = "Requirements Gathering"
        sample_milestone.order = 1
        self.mock_implementation_service.add_milestone.return_value = sample_milestone
        
        # Request data
        request_data = {
            'name': 'Requirements Gathering',
            'description': 'Gather detailed requirements',
            'due_date': '2022-07-15',
            'order': 1
        }
        
        # Make request
        response = self.client.post(
            '/api/v1/implementation-plans/ip123/milestones',
            headers=self.auth_headers,
            data=json.dumps(request_data)
        )
        
        # Assertions
        self.assertEqual(response.status_code, 201)
        data = json.loads(response.data)
        self.assertEqual(data['id'], 'im123')
        self.assertEqual(data['implementation_plan_id'], 'ip123')
        self.assertEqual(data['name'], 'Requirements Gathering')
        self.assertEqual(data['order'], 1)
        
        # Verify service was called with correct data
        self.mock_implementation_service.add_milestone.assert_called_once()
        call_args = self.mock_implementation_service.add_milestone.call_args
        self.assertEqual(call_args[0][0], 'ip123')
        self.assertEqual(call_args[0][1]['name'], 'Requirements Gathering')
        self.assertEqual(call_args[0][1]['due_date'], '2022-07-15')

    def test_add_implementation_task(self):
        """Test POST /api/v1/implementation-plans/milestones/{id}/tasks endpoint."""
        # Configure mock to return created object
        sample_task = MagicMock()
        sample_task.id = "it123"
        sample_task.milestone_id = "im123"
        sample_task.name = "Review Current Architecture"
        sample_task.status = "NOT_STARTED"
        self.mock_implementation_service.add_task.return_value = sample_task
        
        # Request data
        request_data = {
            'name': 'Review Current Architecture',
            'description': 'Review current system architecture',
            'estimated_effort': 16.0,
            'assignee_id': 'user123'
        }
        
        # Make request
        response = self.client.post(
            '/api/v1/implementation-plans/milestones/im123/tasks',
            headers=self.auth_headers,
            data=json.dumps(request_data)
        )
        
        # Assertions
        self.assertEqual(response.status_code, 201)
        data = json.loads(response.data)
        self.assertEqual(data['id'], 'it123')
        self.assertEqual(data['milestone_id'], 'im123')
        self.assertEqual(data['name'], 'Review Current Architecture')
        self.assertEqual(data['status'], 'NOT_STARTED')
        
        # Verify service was called with correct data
        self.mock_implementation_service.add_task.assert_called_once()
        call_args = self.mock_implementation_service.add_task.call_args
        self.assertEqual(call_args[0][0], 'im123')
        self.assertEqual(call_args[0][1]['name'], 'Review Current Architecture')
        self.assertEqual(call_args[0][1]['estimated_effort'], 16.0)
        self.assertEqual(call_args[0][1]['assignee_id'], 'user123')

    def test_update_task_status(self):
        """Test PUT /api/v1/implementation-plans/tasks/{id}/status endpoint."""
        # Configure mock to return updated object
        sample_task = MagicMock()
        sample_task.id = "it123"
        sample_task.name = "Review Current Architecture"
        sample_task.status = "COMPLETED"
        self.mock_implementation_service.update_task_status.return_value = sample_task
        
        # Request data
        request_data = {
            'status': 'COMPLETED',
            'completion_notes': 'Task completed successfully',
            'actual_effort': 18.5
        }
        
        # Make request
        response = self.client.put(
            '/api/v1/implementation-plans/tasks/it123/status',
            headers=self.auth_headers,
            data=json.dumps(request_data)
        )
        
        # Assertions
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['id'], 'it123')
        self.assertEqual(data['name'], 'Review Current Architecture')
        self.assertEqual(data['status'], 'COMPLETED')
        
        # Verify service was called with correct data
        self.mock_implementation_service.update_task_status.assert_called_once()
        call_args = self.mock_implementation_service.update_task_status.call_args
        self.assertEqual(call_args[0][0], 'it123')
        self.assertEqual(call_args[0][1]['status'], 'COMPLETED')
        self.assertEqual(call_args[0][1]['completion_notes'], 'Task completed successfully')
        self.assertEqual(call_args[0][1]['actual_effort'], 18.5)


if __name__ == '__main__':
    unittest.main()
