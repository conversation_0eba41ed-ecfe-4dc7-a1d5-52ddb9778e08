
"""Test cases for database operations with SQLAlchemy."""
import pytest
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session, sessionmaker

from app.db.models import Base
from app.db import get_db
from app.db.database import SQLALCHEMY_DATABASE_URL, engine


def test_get_db():
    """Test the get_db function yields a database session and closes it."""
    db_gen = get_db()
    db = next(db_gen)
    
    # Verify it's a session
    assert isinstance(db, Session)
    
    # Try a simple query
    result = db.execute(text("SELECT 1")).scalar()
    assert result == 1
    
    # Close the session by finishing the generator
    try:
        next(db_gen)
    except StopIteration:
        pass
    
    # Verify the session is closed
    assert db.is_active is False


def test_database_connection():
    """Test that we can connect to the database and create tables."""
    # Create a test engine with echo for logging
    test_engine = create_engine(SQLALCHEMY_DATABASE_URL, echo=True, connect_args={"check_same_thread": False})
    
    # Create all tables (this should not raise exceptions)
    Base.metadata.create_all(bind=test_engine)
    
    # Create a test session
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)
    test_db = TestingSessionLocal()
    
    # Try a simple query
    result = test_db.execute(text("SELECT 1")).scalar()
    assert result == 1
    
    # Close the session
    test_db.close()


def test_database_transaction():
    """Test that database transactions work as expected."""
    # Create a session for testing
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = TestingSessionLocal()
    
    try:
        # Start a transaction
        db.begin()
        
        # Execute a query
        db.execute(text("CREATE TABLE IF NOT EXISTS test_transaction (id INTEGER PRIMARY KEY)"))
        db.execute(text("INSERT INTO test_transaction (id) VALUES (1)"))
        
        # Check the data exists
        result = db.execute(text("SELECT COUNT(*) FROM test_transaction")).scalar()
        assert result == 1
        
        # Rollback the transaction
        db.rollback()
        
        # The table should exist but be empty after rollback
        result = db.execute(text("SELECT COUNT(*) FROM test_transaction")).scalar()
        assert result == 0
        
        # Clean up - drop the test table
        db.execute(text("DROP TABLE test_transaction"))
        db.commit()
        
    except SQLAlchemyError as e:
        db.rollback()
        pytest.fail(f"Database transaction test failed: {str(e)}")
    finally:
        db.close()


def test_connection_error_handling():
    """Test handling of database connection errors."""
    # Create an engine with an invalid connection string to simulate errors
    invalid_engine = create_engine("sqlite:///nonexistent/path/db.sqlite", connect_args={"check_same_thread": False})
    
    # Create a session factory with the invalid engine
    InvalidSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=invalid_engine)
    
    # Attempt to create a session and execute a query
    db = InvalidSessionLocal()
    
    # This should raise an exception
    with pytest.raises(SQLAlchemyError):
        db.execute(text("SELECT 1"))
        db.commit()
