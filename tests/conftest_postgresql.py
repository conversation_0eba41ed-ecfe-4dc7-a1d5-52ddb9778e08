"""
PostgreSQL Integration Test Configuration
Real database testing without mocks for RegulationGuru
"""
import os
import pytest
import asyncio
from typing import Generator, AsyncGenerator
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from fastapi.testclient import TestClient
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Import application components
from app.main import app
from app.db.database import get_db
from app.db.models import Base
# Import all available models
try:
    from app.db.models import *
except ImportError:
    pass

# Test database configuration
TEST_DATABASE_URL = os.getenv(
    "TEST_DATABASE_URL", 
    "postgresql://postgres:postgres@localhost:5433/regulationguru_test"
)

INTEGRATION_DATABASE_URL = os.getenv(
    "INTEGRATION_DATABASE_URL",
    "postgresql://postgres:postgres@localhost:5433/regulationguru_integration"
)

class DatabaseManager:
    """Manages test database lifecycle"""
    
    def __init__(self, database_url: str):
        self.database_url = database_url
        self.engine = None
        self.session_factory = None
        
    def create_database_if_not_exists(self):
        """Create test database if it doesn't exist"""
        # Parse database URL to get connection info
        from urllib.parse import urlparse
        parsed = urlparse(self.database_url)
        
        # Connect to postgres database to create test database
        admin_url = f"postgresql://{parsed.username}:{parsed.password}@{parsed.hostname}:{parsed.port}/postgres"
        
        try:
            # Connect to admin database
            admin_conn = psycopg2.connect(admin_url)
            admin_conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            admin_cursor = admin_conn.cursor()
            
            # Check if database exists
            db_name = parsed.path[1:]  # Remove leading slash
            admin_cursor.execute(
                "SELECT 1 FROM pg_catalog.pg_database WHERE datname = %s", 
                (db_name,)
            )
            
            if not admin_cursor.fetchone():
                # Create database
                admin_cursor.execute(f'CREATE DATABASE "{db_name}"')
                print(f"✅ Created test database: {db_name}")
            else:
                print(f"✅ Test database already exists: {db_name}")
                
            admin_cursor.close()
            admin_conn.close()
            
        except Exception as e:
            print(f"⚠️ Could not create database: {e}")
            # Continue anyway - database might already exist
            
    def setup_engine(self):
        """Setup SQLAlchemy engine and session factory"""
        self.engine = create_engine(
            self.database_url,
            pool_pre_ping=True,
            pool_size=5,
            max_overflow=10,
            echo=False  # Set to True for SQL debugging
        )
        
        self.session_factory = sessionmaker(
            autocommit=False, 
            autoflush=False, 
            bind=self.engine
        )
        
    def create_tables(self):
        """Create all database tables"""
        Base.metadata.create_all(bind=self.engine)
        print("✅ Database tables created")
        
    def drop_tables(self):
        """Drop all database tables"""
        Base.metadata.drop_all(bind=self.engine)
        print("✅ Database tables dropped")
        
    def get_session(self) -> Session:
        """Get a database session"""
        return self.session_factory()
        
    def cleanup(self):
        """Cleanup database resources"""
        if self.engine:
            self.engine.dispose()

# Global database managers
test_db_manager = DatabaseManager(TEST_DATABASE_URL)
integration_db_manager = DatabaseManager(INTEGRATION_DATABASE_URL)

@pytest.fixture(scope="session", autouse=True)
def setup_test_database():
    """Setup test database for the entire test session"""
    print("🐘 Setting up PostgreSQL test database...")
    
    # Create database if needed
    test_db_manager.create_database_if_not_exists()
    
    # Setup engine
    test_db_manager.setup_engine()
    
    # Create tables
    test_db_manager.create_tables()
    
    yield test_db_manager
    
    # Cleanup
    print("🧹 Cleaning up test database...")
    test_db_manager.cleanup()

@pytest.fixture(scope="function")
def db_session(setup_test_database) -> Generator[Session, None, None]:
    """
    Create a fresh database session for each test.
    Uses transaction rollback for isolation.
    """
    connection = setup_test_database.engine.connect()
    transaction = connection.begin()
    session = Session(bind=connection)
    
    try:
        yield session
    finally:
        session.close()
        transaction.rollback()
        connection.close()

@pytest.fixture(scope="function")
def client(db_session) -> TestClient:
    """
    Create a test client with database session override.
    """
    def override_get_db():
        try:
            yield db_session
        finally:
            pass  # Session cleanup handled by db_session fixture
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    # Clean up dependency override
    app.dependency_overrides.clear()

@pytest.fixture(scope="session")
def integration_db():
    """Setup integration database for full integration tests"""
    print("🔗 Setting up integration database...")
    
    # Create database if needed
    integration_db_manager.create_database_if_not_exists()
    
    # Setup engine
    integration_db_manager.setup_engine()
    
    # Create tables
    integration_db_manager.create_tables()
    
    yield integration_db_manager
    
    # Cleanup
    print("🧹 Cleaning up integration database...")
    integration_db_manager.cleanup()

@pytest.fixture
def integration_session(integration_db) -> Generator[Session, None, None]:
    """Create integration test session"""
    session = integration_db.get_session()
    try:
        yield session
        session.commit()
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()

@pytest.fixture
def integration_client(integration_session) -> TestClient:
    """Create integration test client"""
    def override_get_db():
        try:
            yield integration_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    app.dependency_overrides.clear()

# Test data fixtures
@pytest.fixture
def sample_organization(db_session) -> dict:
    """Create a sample organization for testing"""
    # Use the main models from app.db.models
    from app.db.models import Country

    # Create a simple test organization using Country as a proxy
    org = Country(
        name="Test Organization",
        code="TO",
        region="Test Region"
    )
    db_session.add(org)
    db_session.commit()
    db_session.refresh(org)

    return {
        "id": str(org.id),
        "name": org.name,
        "code": org.code
    }

@pytest.fixture
def sample_user(db_session, sample_organization) -> dict:
    """Create a sample user for testing"""
    from app.db.models.governance.user import User
    
    user = User(
        username="testuser",
        email="<EMAIL>",
        role="admin",
        organization_id=sample_organization["id"]
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    
    return {
        "id": str(user.id),
        "username": user.username,
        "email": user.email,
        "role": user.role,
        "organization_id": str(user.organization_id)
    }

@pytest.fixture
def sample_regulation(db_session, sample_organization) -> dict:
    """Create a sample regulation for testing"""
    from app.db.models.governance.regulation import Regulation
    from datetime import date
    
    regulation = Regulation(
        name="Test Financial Regulation",
        description="A comprehensive test regulation for financial services",
        category="Financial Services",
        effective_date=date.today(),
        organization_id=sample_organization["id"]
    )
    db_session.add(regulation)
    db_session.commit()
    db_session.refresh(regulation)
    
    return {
        "id": str(regulation.id),
        "name": regulation.name,
        "description": regulation.description,
        "category": regulation.category,
        "effective_date": regulation.effective_date.isoformat() if regulation.effective_date else None,
        "organization_id": str(regulation.organization_id)
    }

@pytest.fixture
def sample_risk(db_session, sample_organization, sample_user) -> dict:
    """Create a sample risk for testing"""
    from app.db.models.governance.risk import Risk
    
    risk = Risk(
        name="Test Operational Risk",
        description="A test operational risk for compliance testing",
        category="Operational",
        likelihood="Medium",
        impact="High",
        status="active",
        owner_id=sample_user["id"],
        organization_id=sample_organization["id"]
    )
    db_session.add(risk)
    db_session.commit()
    db_session.refresh(risk)
    
    return {
        "id": str(risk.id),
        "name": risk.name,
        "description": risk.description,
        "category": risk.category,
        "likelihood": risk.likelihood,
        "impact": risk.impact,
        "status": risk.status,
        "owner_id": str(risk.owner_id),
        "organization_id": str(risk.organization_id)
    }

@pytest.fixture
def sample_control(db_session, sample_organization, sample_user) -> dict:
    """Create a sample control for testing"""
    from app.db.models.governance.control import Control
    
    control = Control(
        name="Test Access Control",
        description="A test access control for security compliance",
        category="Security",
        type="Preventive",
        status="active",
        owner_id=sample_user["id"],
        organization_id=sample_organization["id"]
    )
    db_session.add(control)
    db_session.commit()
    db_session.refresh(control)
    
    return {
        "id": str(control.id),
        "name": control.name,
        "description": control.description,
        "category": control.category,
        "type": control.type,
        "status": control.status,
        "owner_id": str(control.owner_id),
        "organization_id": str(control.organization_id)
    }

# Utility functions for tests
def assert_soft_delete(session: Session, model_class, record_id: str):
    """Assert that a record is soft deleted"""
    record = session.query(model_class).filter(model_class.id == record_id).first()
    assert record is not None, f"Record {record_id} should exist"
    assert record.is_deleted is True, f"Record {record_id} should be soft deleted"
    assert record.deleted_at is not None, f"Record {record_id} should have deleted_at timestamp"

def assert_not_soft_deleted(session: Session, model_class, record_id: str):
    """Assert that a record is not soft deleted"""
    record = session.query(model_class).filter(model_class.id == record_id).first()
    assert record is not None, f"Record {record_id} should exist"
    assert record.is_deleted is False, f"Record {record_id} should not be soft deleted"
    assert record.deleted_at is None, f"Record {record_id} should not have deleted_at timestamp"
