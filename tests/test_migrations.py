
import pytest
import sys
import os
from unittest.mock import patch, MagicMock

# Add the scripts directory to the path so we can import migrate directly
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../scripts')))

# Import the migration module
import migrate

def test_migration_runner():
    """Test migration runner function"""
    mock_alembic = MagicMock()
    
    with patch("migrate.subprocess.run", return_value=mock_alembic) as mock_run:
        # Test upgrade migration
        migrate.run_migration("upgrade", "head")
        mock_run.assert_called_with(
            ["alembic", "upgrade", "head"], 
            check=True, 
            capture_output=True, 
            text=True
        )
        
        # Reset mock
        mock_run.reset_mock()
        
        # Test downgrade migration
        migrate.run_migration("downgrade", "base")
        mock_run.assert_called_with(
            ["alembic", "downgrade", "base"], 
            check=True, 
            capture_output=True, 
            text=True
        )

def test_migration_main():
    """Test main function of migration script"""
    with patch("migrate.run_migration") as mock_run:
        with patch("sys.argv", ["migrate.py", "upgrade", "head"]):
            migrate.main()
            mock_run.assert_called_with("upgrade", "head")
        
        mock_run.reset_mock()
        
        with patch("sys.argv", ["migrate.py", "downgrade", "base"]):
            migrate.main()
            mock_run.assert_called_with("downgrade", "base")
