"""
Tests for the OpenAPI schema.
"""
import pytest
from fastapi.testclient import TestClient

from app.main import app

client = TestClient(app)

def test_openapi_schema():
    """Test the OpenAPI schema endpoint."""
    response = client.get("/openapi.json")
    assert response.status_code == 200
    schema = response.json()
    
    # Check basic structure
    assert "openapi" in schema
    assert "info" in schema
    assert "paths" in schema
    assert "components" in schema
    
    # Check info
    assert "title" in schema["info"]
    assert "description" in schema["info"]
    assert "version" in schema["info"]
    
    # Check security schemes
    assert "securitySchemes" in schema["components"]
    assert "bearerAuth" in schema["components"]["securitySchemes"]
    
    # Check custom responses
    assert "responses" in schema["components"]
    assert "NotFound" in schema["components"]["responses"]
    assert "Unauthorized" in schema["components"]["responses"]
    assert "Forbidden" in schema["components"]["responses"]
    assert "ValidationError" in schema["components"]["responses"]
    
    # Check tags
    assert "tags" in schema
    assert any(tag["name"] == "regulations" for tag in schema["tags"])
    assert any(tag["name"] == "compliance_requirements" for tag in schema["tags"])
    assert any(tag["name"] == "regulation_documents" for tag in schema["tags"])
    assert any(tag["name"] == "regulation_tags" for tag in schema["tags"])
    assert any(tag["name"] == "regulation_categories" for tag in schema["tags"])
    assert any(tag["name"] == "industries" for tag in schema["tags"])
    
    # Check paths
    assert "/api/v1/regulations/" in schema["paths"]
    assert "/api/v1/compliance-requirements/" in schema["paths"]
    assert "/api/v1/regulation-documents/" in schema["paths"]
    assert "/api/v1/regulation-tags/" in schema["paths"]
    assert "/api/v1/regulation-categories/" in schema["paths"]
    assert "/api/v1/industries/" in schema["paths"]
    
    # Check regulation paths
    regulation_path = schema["paths"]["/api/v1/regulations/"]
    assert "get" in regulation_path
    assert "post" in regulation_path
    
    # Check regulation path parameters
    get_params = regulation_path["get"]["parameters"]
    assert any(param["name"] == "skip" for param in get_params)
    assert any(param["name"] == "limit" for param in get_params)
    assert any(param["name"] == "search" for param in get_params)
    assert any(param["name"] == "status" for param in get_params)
    assert any(param["name"] == "category_id" for param in get_params)
    assert any(param["name"] == "industry_id" for param in get_params)
    assert any(param["name"] == "tag_id" for param in get_params)
    
    # Check regulation schemas
    assert "Regulation" in schema["components"]["schemas"]
    assert "RegulationCreate" in schema["components"]["schemas"]
    assert "RegulationUpdate" in schema["components"]["schemas"]
    
    # Check compliance requirement schemas
    assert "ComplianceRequirement" in schema["components"]["schemas"]
    assert "ComplianceRequirementCreate" in schema["components"]["schemas"]
    assert "ComplianceRequirementUpdate" in schema["components"]["schemas"]
    
    # Check regulation document schemas
    assert "RegulationDocument" in schema["components"]["schemas"]
    assert "RegulationDocumentCreate" in schema["components"]["schemas"]
    assert "RegulationDocumentUpdate" in schema["components"]["schemas"]
    
    # Check regulation tag schemas
    assert "RegulationTag" in schema["components"]["schemas"]
    assert "RegulationTagCreate" in schema["components"]["schemas"]
    assert "RegulationTagUpdate" in schema["components"]["schemas"]
    
    # Check regulation category schemas
    assert "RegulationCategory" in schema["components"]["schemas"]
    assert "RegulationCategoryCreate" in schema["components"]["schemas"]
    assert "RegulationCategoryUpdate" in schema["components"]["schemas"]
    
    # Check industry schemas
    assert "Industry" in schema["components"]["schemas"]
    assert "IndustryCreate" in schema["components"]["schemas"]
    assert "IndustryUpdate" in schema["components"]["schemas"]
