"""
Tests for the regulation documents API.
"""
import pytest
from fastapi.testclient import TestClient

from app.main import app

client = TestClient(app)

def test_get_regulation_documents(client, test_data):
    """Test getting a list of regulation documents."""
    response = client.get("/api/v1/regulation-documents/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) > 0
    assert response.json()[0]["name"] == test_data["document"].name

def test_get_regulation_documents_with_search(client, test_data):
    """Test getting regulation documents with search filter."""
    response = client.get("/api/v1/regulation-documents/?search=Test")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) > 0
    assert response.json()[0]["name"] == test_data["document"].name

def test_get_regulation_documents_with_document_type_filter(client, test_data):
    """Test getting regulation documents with document type filter."""
    response = client.get(f"/api/v1/regulation-documents/?document_type={test_data['document'].document_type}")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) > 0
    assert response.json()[0]["document_type"] == test_data["document"].document_type

def test_get_regulation_documents_with_regulation_filter(client, test_data):
    """Test getting regulation documents with regulation filter."""
    response = client.get(f"/api/v1/regulation-documents/?regulation_id={test_data['regulation'].id}")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) > 0
    assert response.json()[0]["regulation_id"] == test_data["regulation"].id

def test_get_regulation_document(client, test_data):
    """Test getting a specific regulation document by ID."""
    response = client.get(f"/api/v1/regulation-documents/{test_data['document'].id}")
    assert response.status_code == 200
    assert response.json()["name"] == test_data["document"].name
    assert response.json()["description"] == test_data["document"].description
    assert response.json()["document_type"] == test_data["document"].document_type
    assert response.json()["file_url"] == test_data["document"].file_url
    assert response.json()["regulation_id"] == test_data["regulation"].id

def test_get_regulation_document_not_found(client):
    """Test getting a non-existent regulation document."""
    response = client.get("/api/v1/regulation-documents/999")
    assert response.status_code == 404
    assert response.json()["detail"] == "Regulation document not found"

def test_create_regulation_document(client, test_data):
    """Test creating a new regulation document."""
    document_data = {
        "name": "New Test Document",
        "description": "New test document description",
        "document_type": "DOCX",
        "file_url": "https://example.com/new-test.docx",
        "regulation_id": test_data["regulation"].id
    }
    response = client.post("/api/v1/regulation-documents/", json=document_data)
    assert response.status_code == 201
    assert response.json()["name"] == document_data["name"]
    assert response.json()["description"] == document_data["description"]
    assert response.json()["document_type"] == document_data["document_type"]
    assert response.json()["file_url"] == document_data["file_url"]
    assert response.json()["regulation_id"] == document_data["regulation_id"]

def test_create_regulation_document_invalid_regulation(client):
    """Test creating a regulation document with an invalid regulation."""
    document_data = {
        "name": "New Test Document",
        "description": "New test document description",
        "document_type": "DOCX",
        "file_url": "https://example.com/new-test.docx",
        "regulation_id": 999
    }
    response = client.post("/api/v1/regulation-documents/", json=document_data)
    assert response.status_code == 400
    assert "Regulation not found" in response.json()["detail"]

def test_update_regulation_document(client, test_data):
    """Test updating a regulation document."""
    document_data = {
        "name": "Updated Test Document",
        "description": "Updated test document description",
        "document_type": "XLSX",
        "file_url": "https://example.com/updated-test.xlsx"
    }
    response = client.put(f"/api/v1/regulation-documents/{test_data['document'].id}", json=document_data)
    assert response.status_code == 200
    assert response.json()["name"] == document_data["name"]
    assert response.json()["description"] == document_data["description"]
    assert response.json()["document_type"] == document_data["document_type"]
    assert response.json()["file_url"] == document_data["file_url"]

def test_update_regulation_document_not_found(client):
    """Test updating a non-existent regulation document."""
    document_data = {
        "name": "Updated Test Document",
        "description": "Updated test document description",
        "document_type": "XLSX",
        "file_url": "https://example.com/updated-test.xlsx"
    }
    response = client.put("/api/v1/regulation-documents/999", json=document_data)
    assert response.status_code == 404
    assert response.json()["detail"] == "Regulation document not found"

def test_update_regulation_document_invalid_regulation(client, test_data):
    """Test updating a regulation document with an invalid regulation."""
    document_data = {
        "regulation_id": 999
    }
    response = client.put(f"/api/v1/regulation-documents/{test_data['document'].id}", json=document_data)
    assert response.status_code == 400
    assert "Regulation not found" in response.json()["detail"]

def test_delete_regulation_document(client, test_data):
    """Test deleting a regulation document."""
    # Create a new document first
    document_data = {
        "name": "Document to Delete",
        "description": "Document to be deleted",
        "document_type": "DOCX",
        "file_url": "https://example.com/delete-test.docx",
        "regulation_id": test_data["regulation"].id
    }
    create_response = client.post("/api/v1/regulation-documents/", json=document_data)
    assert create_response.status_code == 201
    document_id = create_response.json()["id"]
    
    # Delete the document
    response = client.delete(f"/api/v1/regulation-documents/{document_id}")
    assert response.status_code == 204
    
    # Verify the document is deleted
    get_response = client.get(f"/api/v1/regulation-documents/{document_id}")
    assert get_response.status_code == 404

def test_delete_regulation_document_not_found(client):
    """Test deleting a non-existent regulation document."""
    response = client.delete("/api/v1/regulation-documents/999")
    assert response.status_code == 404
    assert response.json()["detail"] == "Regulation document not found"
