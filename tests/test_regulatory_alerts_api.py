
"""
Tests for the regulatory alerts API endpoints.
"""
import pytest
from fastapi.testclient import TestClient
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

from app.main import app
from app.api import regulatory_alerts

client = TestClient(app)

class TestRegulatoryAlertsAPI:
    
    def test_configure_alerts(self):
        """Test configuring alert thresholds."""
        # Test data
        config_data = {
            "importance": 5,
            "categories": ["Data Protection", "Privacy"],
            "countries": ["US", "EU", "UK"],
            "lookback_days": 60
        }
        
        # Make request
        response = client.post("/api/v1/alerts/configure", json=config_data)
        
        # Check response
        assert response.status_code == 200
        result = response.json()
        assert result["status"] == "success"
        assert result["configuration"]["importance"] == 5
        assert "Data Protection" in result["configuration"]["categories"]
        assert "US" in result["configuration"]["countries"]
        assert result["configuration"]["lookback_days"] == 60
    
    def test_get_recent_alerts_default(self):
        """Test getting recent alerts with default parameters."""
        response = client.get("/api/v1/alerts/recent")
        
        # Check response
        assert response.status_code == 200
        alerts = response.json()
        assert isinstance(alerts, list)
        assert len(alerts) > 0
        
        # Check alert structure
        for alert in alerts:
            assert "id" in alert
            assert "title" in alert
            assert "description" in alert
            assert "country_code" in alert
            assert "importance" in alert
            assert "publication_date" in alert
            assert "categories" in alert
    
    def test_get_recent_alerts_filtered(self):
        """Test getting recent alerts with filters."""
        # Test with importance filter
        response = client.get("/api/v1/alerts/recent?importance=8")
        assert response.status_code == 200
        alerts = response.json()
        for alert in alerts:
            assert alert["importance"] >= 8
        
        # Test with countries filter
        response = client.get("/api/v1/alerts/recent?countries=US")
        assert response.status_code == 200
        alerts = response.json()
        for alert in alerts:
            assert alert["country_code"] == "US"
        
        # Test with categories filter
        response = client.get("/api/v1/alerts/recent?categories=Cybersecurity")
        assert response.status_code == 200
        alerts = response.json()
        for alert in alerts:
            assert "Cybersecurity" in alert["categories"]
        
        # Test with days filter
        response = client.get("/api/v1/alerts/recent?days=7")
        assert response.status_code == 200
        # Time-based filtering would need to be mocked or tested differently
    
    def test_get_alert_details_found(self):
        """Test getting details for an existing alert."""
        response = client.get("/api/v1/alerts/1")
        
        # Check response
        assert response.status_code == 200
        alert = response.json()
        assert alert["id"] == 1
        assert "title" in alert
        assert "description" in alert
        assert "country_code" in alert
        assert "importance" in alert
        assert "url" in alert
    
    def test_get_alert_details_not_found(self):
        """Test getting details for a non-existent alert."""
        response = client.get("/api/v1/alerts/999")
        
        # Check response
        assert response.status_code == 404
        assert "detail" in response.json()
        assert "not found" in response.json()["detail"].lower()
