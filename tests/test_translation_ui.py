
"""Test UI translations for various languages."""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch

from main import app
from app.i18n import get_translator, get_locale


@pytest.fixture
def client():
    """Create a test client for the FastAPI application."""
    return TestClient(app)


def test_language_selector(client):
    """Test that language endpoint returns correct language options."""
    response = client.get("/api/v1/languages")
    assert response.status_code == 200
    data = response.json()
    
    # Check that all languages are included
    assert "en_US" in data["available_languages"]
    assert "en_GB" in data["available_languages"]
    assert "es" in data["available_languages"]
    assert "de" in data["available_languages"]
    assert "af" in data["available_languages"]
    assert "zu" in data["available_languages"]


def test_language_cookie_setting(client):
    """Test that language cookie is properly set."""
    for lang in ["en_US", "en_GB", "es", "de", "af", "zu"]:
        response = client.get(f"/?lang={lang}")
        assert response.status_code == 200
        assert "lang" in response.cookies
        assert response.cookies["lang"] == lang


@patch('i18n.get_locale')
def test_translation_for_languages(mock_get_locale, client):
    """Test that translations work for each language."""
    languages = ["en_US", "en_GB", "es", "de", "af", "zu"]
    
    for lang in languages:
        mock_get_locale.return_value = lang
        response = client.get("/api/v1/")
        assert response.status_code == 200
