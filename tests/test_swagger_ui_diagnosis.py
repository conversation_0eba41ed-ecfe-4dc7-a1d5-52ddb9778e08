
"""Diagnostic tests for Swagger UI and OpenAPI documentation."""
import pytest
from fastapi.testclient import TestClient
import re

from main import app


@pytest.fixture
def client():
    """Create a test client for the FastAPI application."""
    return TestClient(app)


def test_openapi_json_structure(client):
    """
    Test that the OpenAPI JSON schema is correctly structured.
    This is critical for Swagger UI to work properly.
    """
    response = client.get("/openapi.json")
    assert response.status_code == 200
    
    json_data = response.json()
    
    # Essential OpenAPI structure
    assert "openapi" in json_data
    assert json_data["openapi"].startswith("3.")  # Should be OpenAPI 3.x
    assert "info" in json_data
    assert "title" in json_data["info"]
    assert "paths" in json_data
    
    # Check that there's at least one path defined
    assert len(json_data["paths"]) > 0
    
    print("OpenAPI schema structure seems valid")
    return json_data


def test_swagger_ui_html_structure(client):
    """
    Test that the Swagger UI HTML has the expected structure.
    """
    response = client.get("/docs")
    assert response.status_code == 200
    
    html_content = response.text
    
    # Check for key Swagger UI elements
    assert "<html" in html_content
    assert "<head" in html_content
    assert "<body" in html_content
    
    # Swagger UI specific elements
    assert "swagger-ui" in html_content.lower()
    
    # Look for the script that initializes Swagger UI
    assert "SwaggerUIBundle" in html_content
    
    # Check for the URL to the OpenAPI schema
    assert "/openapi.json" in html_content
    
    print("Swagger UI HTML structure seems valid")


def test_swagger_ui_resources_loading(client):
    """
    Test that the Swagger UI can load its resources.
    """
    response = client.get("/docs")
    assert response.status_code == 200
    
    html_content = response.text
    
    # Extract all script and stylesheet URLs
    script_pattern = r'<script[^>]*src="([^"]+)"[^>]*>'
    style_pattern = r'<link[^>]*href="([^"]+)"[^>]*>'
    
    script_urls = re.findall(script_pattern, html_content)
    style_urls = re.findall(style_pattern, html_content)
    
    # Test loading a few of these resources
    resources_to_test = script_urls + style_urls
    for url in resources_to_test[:3]:  # Test just the first few
        if url.startswith("/"):
            resource_response = client.get(url)
            print(f"Resource {url} status: {resource_response.status_code}")
            # We don't assert here because some resources might be external
            # Just log for diagnostic purposes


def test_docs_redirect(client):
    """Test that /api/v1/docs redirects to /docs correctly."""
    response = client.get("/api/v1/docs", allow_redirects=False)
    assert response.status_code in [301, 302, 307, 308]  # Any redirect status
    assert response.headers["location"] == "/docs"


def print_swagger_ui_diagnostics(client):
    """Print diagnostic information about the Swagger UI setup."""
    # Get the OpenAPI schema
    openapi_response = client.get("/openapi.json")
    if openapi_response.status_code == 200:
        openapi_data = openapi_response.json()
        print("OpenAPI Schema Info:")
        print(f"  Version: {openapi_data.get('openapi')}")
        print(f"  Title: {openapi_data.get('info', {}).get('title')}")
        print(f"  Paths: {len(openapi_data.get('paths', {}))}")
    else:
        print(f"Error loading OpenAPI schema: {openapi_response.status_code}")
    
    # Get the Swagger UI HTML
    docs_response = client.get("/docs")
    if docs_response.status_code == 200:
        html_content = docs_response.text
        print("Swagger UI HTML Info:")
        print(f"  Content Length: {len(html_content)} characters")
        if "SwaggerUIBundle" in html_content:
            print("  SwaggerUIBundle found in HTML")
        else:
            print("  WARNING: SwaggerUIBundle not found in HTML")
        if "/openapi.json" in html_content:
            print("  OpenAPI JSON URL found in HTML")
        else:
            print("  WARNING: OpenAPI JSON URL not found in HTML")
    else:
        print(f"Error loading Swagger UI: {docs_response.status_code}")


if __name__ == "__main__":
    # This allows running the diagnostics directly
    client = TestClient(app)
    print_swagger_ui_diagnostics(client)
