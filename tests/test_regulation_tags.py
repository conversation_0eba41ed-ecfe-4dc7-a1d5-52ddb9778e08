"""
Tests for the regulation tags API.
"""
import pytest
from fastapi.testclient import TestClient

from app.main import app

client = TestClient(app)

def test_get_regulation_tags(client, test_data):
    """Test getting a list of regulation tags."""
    response = client.get("/api/v1/regulation-tags/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) > 0
    assert response.json()[0]["name"] == test_data["tag"].name

def test_get_regulation_tags_with_search(client, test_data):
    """Test getting regulation tags with search filter."""
    response = client.get("/api/v1/regulation-tags/?search=Test")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) > 0
    assert response.json()[0]["name"] == test_data["tag"].name

def test_get_regulation_tag(client, test_data):
    """Test getting a specific regulation tag by ID."""
    response = client.get(f"/api/v1/regulation-tags/{test_data['tag'].id}")
    assert response.status_code == 200
    assert response.json()["name"] == test_data["tag"].name
    assert response.json()["description"] == test_data["tag"].description
    assert response.json()["color"] == test_data["tag"].color

def test_get_regulation_tag_not_found(client):
    """Test getting a non-existent regulation tag."""
    response = client.get("/api/v1/regulation-tags/999")
    assert response.status_code == 404
    assert response.json()["detail"] == "Regulation tag not found"

def test_create_regulation_tag(client):
    """Test creating a new regulation tag."""
    tag_data = {
        "name": "New Test Tag",
        "description": "New test tag description",
        "color": "#52c41a"
    }
    response = client.post("/api/v1/regulation-tags/", json=tag_data)
    assert response.status_code == 201
    assert response.json()["name"] == tag_data["name"]
    assert response.json()["description"] == tag_data["description"]
    assert response.json()["color"] == tag_data["color"]

def test_create_regulation_tag_duplicate_name(client, test_data):
    """Test creating a regulation tag with a duplicate name."""
    tag_data = {
        "name": test_data["tag"].name,
        "description": "Duplicate tag description",
        "color": "#52c41a"
    }
    response = client.post("/api/v1/regulation-tags/", json=tag_data)
    assert response.status_code == 400
    assert "already exists" in response.json()["detail"]

def test_update_regulation_tag(client, test_data):
    """Test updating a regulation tag."""
    tag_data = {
        "name": "Updated Test Tag",
        "description": "Updated test tag description",
        "color": "#52c41a"
    }
    response = client.put(f"/api/v1/regulation-tags/{test_data['tag'].id}", json=tag_data)
    assert response.status_code == 200
    assert response.json()["name"] == tag_data["name"]
    assert response.json()["description"] == tag_data["description"]
    assert response.json()["color"] == tag_data["color"]

def test_update_regulation_tag_not_found(client):
    """Test updating a non-existent regulation tag."""
    tag_data = {
        "name": "Updated Test Tag",
        "description": "Updated test tag description",
        "color": "#52c41a"
    }
    response = client.put("/api/v1/regulation-tags/999", json=tag_data)
    assert response.status_code == 404
    assert response.json()["detail"] == "Regulation tag not found"

def test_update_regulation_tag_duplicate_name(client, test_data):
    """Test updating a regulation tag with a duplicate name."""
    # Create a new tag first
    new_tag_data = {
        "name": "Another Test Tag",
        "description": "Another test tag description",
        "color": "#52c41a"
    }
    new_tag_response = client.post("/api/v1/regulation-tags/", json=new_tag_data)
    assert new_tag_response.status_code == 201
    new_tag_id = new_tag_response.json()["id"]
    
    # Try to update the new tag with the name of the existing tag
    update_data = {
        "name": test_data["tag"].name,
        "description": "Updated description",
        "color": "#52c41a"
    }
    response = client.put(f"/api/v1/regulation-tags/{new_tag_id}", json=update_data)
    assert response.status_code == 400
    assert "already exists" in response.json()["detail"]

def test_delete_regulation_tag(client):
    """Test deleting a regulation tag."""
    # Create a new tag first
    tag_data = {
        "name": "Tag to Delete",
        "description": "Tag to be deleted",
        "color": "#52c41a"
    }
    create_response = client.post("/api/v1/regulation-tags/", json=tag_data)
    assert create_response.status_code == 201
    tag_id = create_response.json()["id"]
    
    # Delete the tag
    response = client.delete(f"/api/v1/regulation-tags/{tag_id}")
    assert response.status_code == 204
    
    # Verify the tag is deleted
    get_response = client.get(f"/api/v1/regulation-tags/{tag_id}")
    assert get_response.status_code == 404

def test_delete_regulation_tag_not_found(client):
    """Test deleting a non-existent regulation tag."""
    response = client.delete("/api/v1/regulation-tags/999")
    assert response.status_code == 404
    assert response.json()["detail"] == "Regulation tag not found"

def test_delete_regulation_tag_with_regulations(client, test_data):
    """Test deleting a regulation tag that has associated regulations."""
    response = client.delete(f"/api/v1/regulation-tags/{test_data['tag'].id}")
    assert response.status_code == 400
    assert "Cannot delete tag that is associated with" in response.json()["detail"]

def test_get_regulations_by_tag(client, test_data):
    """Test getting regulations associated with a tag."""
    response = client.get(f"/api/v1/regulation-tags/{test_data['tag'].id}/regulations")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) > 0
    assert response.json()[0]["title"] == test_data["regulation"].title
