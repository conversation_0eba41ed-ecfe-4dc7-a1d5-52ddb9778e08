"""
PostgreSQL Integration Tests for Soft Delete Functionality
Tests real database operations without mocks
"""
import pytest
from datetime import datetime, date
from sqlalchemy.orm import Session
from fastapi.testclient import TestClient

# Import models
from app.db.models.governance.organization import Organization
from app.db.models.governance.user import User
from app.db.models.governance.regulation import Regulation
from app.db.models.governance.risk import Risk
from app.db.models.governance.control import Control
from app.db.models.governance.assessment import Assessment

# Import test utilities
from tests.conftest_postgresql import assert_soft_delete, assert_not_soft_deleted

class TestSoftDeleteFunctionality:
    """Test soft delete functionality across all models"""
    
    def test_organization_soft_delete(self, db_session: Session, sample_organization):
        """Test organization soft delete"""
        org_id = sample_organization["id"]
        
        # Get the organization
        org = db_session.query(Organization).filter(Organization.id == org_id).first()
        assert org is not None
        assert_not_soft_deleted(db_session, Organization, org_id)
        
        # Soft delete the organization
        org.soft_delete()
        db_session.commit()
        
        # Verify soft delete
        assert_soft_delete(db_session, Organization, org_id)
        
        # Verify organization is still in database
        org_check = db_session.query(Organization).filter(Organization.id == org_id).first()
        assert org_check is not None
        assert org_check.is_deleted is True
        
    def test_user_soft_delete(self, db_session: Session, sample_user):
        """Test user soft delete"""
        user_id = sample_user["id"]
        
        # Get the user
        user = db_session.query(User).filter(User.id == user_id).first()
        assert user is not None
        assert_not_soft_deleted(db_session, User, user_id)
        
        # Soft delete the user
        user.soft_delete()
        db_session.commit()
        
        # Verify soft delete
        assert_soft_delete(db_session, User, user_id)
        
    def test_regulation_soft_delete(self, db_session: Session, sample_regulation):
        """Test regulation soft delete"""
        regulation_id = sample_regulation["id"]
        
        # Get the regulation
        regulation = db_session.query(Regulation).filter(Regulation.id == regulation_id).first()
        assert regulation is not None
        assert_not_soft_deleted(db_session, Regulation, regulation_id)
        
        # Soft delete the regulation
        regulation.soft_delete()
        db_session.commit()
        
        # Verify soft delete
        assert_soft_delete(db_session, Regulation, regulation_id)
        
    def test_risk_soft_delete(self, db_session: Session, sample_risk):
        """Test risk soft delete"""
        risk_id = sample_risk["id"]
        
        # Get the risk
        risk = db_session.query(Risk).filter(Risk.id == risk_id).first()
        assert risk is not None
        assert_not_soft_deleted(db_session, Risk, risk_id)
        
        # Soft delete the risk
        risk.soft_delete()
        db_session.commit()
        
        # Verify soft delete
        assert_soft_delete(db_session, Risk, risk_id)
        
    def test_control_soft_delete(self, db_session: Session, sample_control):
        """Test control soft delete"""
        control_id = sample_control["id"]
        
        # Get the control
        control = db_session.query(Control).filter(Control.id == control_id).first()
        assert control is not None
        assert_not_soft_deleted(db_session, Control, control_id)
        
        # Soft delete the control
        control.soft_delete()
        db_session.commit()
        
        # Verify soft delete
        assert_soft_delete(db_session, Control, control_id)

class TestSoftDeleteQueries:
    """Test querying with soft delete filters"""
    
    def test_active_records_query(self, db_session: Session, sample_organization, sample_user):
        """Test querying only active (non-deleted) records"""
        org_id = sample_organization["id"]
        user_id = sample_user["id"]
        
        # Initially both should be active
        active_orgs = db_session.query(Organization).filter(Organization.is_deleted == False).all()
        active_users = db_session.query(User).filter(User.is_deleted == False).all()
        
        assert len(active_orgs) >= 1
        assert len(active_users) >= 1
        
        # Soft delete organization
        org = db_session.query(Organization).filter(Organization.id == org_id).first()
        org.soft_delete()
        db_session.commit()
        
        # Query active records again
        active_orgs_after = db_session.query(Organization).filter(Organization.is_deleted == False).all()
        active_users_after = db_session.query(User).filter(User.is_deleted == False).all()
        
        # Organization count should decrease, user count should remain
        assert len(active_orgs_after) == len(active_orgs) - 1
        assert len(active_users_after) == len(active_users)
        
    def test_deleted_records_query(self, db_session: Session, sample_regulation):
        """Test querying only deleted records"""
        regulation_id = sample_regulation["id"]
        
        # Initially no deleted regulations
        deleted_regulations = db_session.query(Regulation).filter(Regulation.is_deleted == True).all()
        initial_deleted_count = len(deleted_regulations)
        
        # Soft delete regulation
        regulation = db_session.query(Regulation).filter(Regulation.id == regulation_id).first()
        regulation.soft_delete()
        db_session.commit()
        
        # Query deleted records
        deleted_regulations_after = db_session.query(Regulation).filter(Regulation.is_deleted == True).all()
        
        # Deleted count should increase
        assert len(deleted_regulations_after) == initial_deleted_count + 1
        
    def test_all_records_query(self, db_session: Session, sample_risk):
        """Test querying all records (including deleted)"""
        risk_id = sample_risk["id"]
        
        # Count all risks initially
        all_risks = db_session.query(Risk).all()
        initial_count = len(all_risks)
        
        # Soft delete risk
        risk = db_session.query(Risk).filter(Risk.id == risk_id).first()
        risk.soft_delete()
        db_session.commit()
        
        # Count all risks after deletion
        all_risks_after = db_session.query(Risk).all()
        
        # Total count should remain the same
        assert len(all_risks_after) == initial_count

class TestSoftDeleteRelationships:
    """Test soft delete behavior with relationships"""
    
    def test_organization_user_relationship(self, db_session: Session, sample_organization, sample_user):
        """Test soft delete with organization-user relationship"""
        org_id = sample_organization["id"]
        user_id = sample_user["id"]
        
        # Verify relationship exists
        user = db_session.query(User).filter(User.id == user_id).first()
        assert str(user.organization_id) == org_id
        
        # Soft delete organization
        org = db_session.query(Organization).filter(Organization.id == org_id).first()
        org.soft_delete()
        db_session.commit()
        
        # User should still exist and reference the organization
        user_after = db_session.query(User).filter(User.id == user_id).first()
        assert user_after is not None
        assert user_after.is_deleted is False
        assert str(user_after.organization_id) == org_id
        
    def test_regulation_control_relationship(self, db_session: Session, sample_regulation, sample_control):
        """Test soft delete with regulation-control relationship"""
        from app.db.models.governance.control import RegulationControl
        
        regulation_id = sample_regulation["id"]
        control_id = sample_control["id"]
        
        # Create relationship
        reg_control = RegulationControl(
            regulation_id=regulation_id,
            control_id=control_id
        )
        db_session.add(reg_control)
        db_session.commit()
        
        # Soft delete regulation
        regulation = db_session.query(Regulation).filter(Regulation.id == regulation_id).first()
        regulation.soft_delete()
        db_session.commit()
        
        # Relationship should still exist
        relationship = db_session.query(RegulationControl).filter(
            RegulationControl.regulation_id == regulation_id,
            RegulationControl.control_id == control_id
        ).first()
        assert relationship is not None
        assert relationship.is_deleted is False

class TestSoftDeleteTimestamps:
    """Test soft delete timestamp functionality"""
    
    def test_deleted_at_timestamp(self, db_session: Session, sample_organization):
        """Test that deleted_at timestamp is set correctly"""
        org_id = sample_organization["id"]
        
        # Get organization
        org = db_session.query(Organization).filter(Organization.id == org_id).first()
        assert org.deleted_at is None
        
        # Record time before deletion
        before_deletion = datetime.utcnow()
        
        # Soft delete
        org.soft_delete()
        db_session.commit()
        
        # Record time after deletion
        after_deletion = datetime.utcnow()
        
        # Verify timestamp is set and within expected range
        db_session.refresh(org)
        assert org.deleted_at is not None
        assert before_deletion <= org.deleted_at <= after_deletion
        
    def test_created_at_preserved(self, db_session: Session, sample_user):
        """Test that created_at timestamp is preserved after soft delete"""
        user_id = sample_user["id"]
        
        # Get user and record created_at
        user = db_session.query(User).filter(User.id == user_id).first()
        original_created_at = user.created_at
        
        # Soft delete
        user.soft_delete()
        db_session.commit()
        
        # Verify created_at is unchanged
        db_session.refresh(user)
        assert user.created_at == original_created_at
        
    def test_changed_on_updated(self, db_session: Session, sample_regulation):
        """Test that changed_on timestamp is updated on soft delete"""
        regulation_id = sample_regulation["id"]
        
        # Get regulation and record changed_on
        regulation = db_session.query(Regulation).filter(Regulation.id == regulation_id).first()
        original_changed_on = regulation.changed_on
        
        # Wait a moment to ensure timestamp difference
        import time
        time.sleep(0.1)
        
        # Soft delete
        regulation.soft_delete()
        db_session.commit()
        
        # Verify changed_on is updated
        db_session.refresh(regulation)
        assert regulation.changed_on > original_changed_on

class TestSoftDeleteAPI:
    """Test soft delete functionality through API endpoints"""
    
    def test_api_soft_delete_organization(self, client: TestClient, sample_organization):
        """Test soft delete through API"""
        org_id = sample_organization["id"]
        
        # Delete through API
        response = client.delete(f"/api/v1/governance/organizations/{org_id}")
        assert response.status_code == 200
        
        # Verify organization is soft deleted
        get_response = client.get(f"/api/v1/governance/organizations/{org_id}")
        # Should return 404 for soft deleted records in normal queries
        assert get_response.status_code == 404
        
    def test_api_list_excludes_deleted(self, client: TestClient, sample_user):
        """Test that API list endpoints exclude soft deleted records"""
        user_id = sample_user["id"]
        
        # Get initial list
        response = client.get("/api/v1/governance/users")
        assert response.status_code == 200
        initial_users = response.json()
        initial_count = len(initial_users)
        
        # Soft delete user through API
        delete_response = client.delete(f"/api/v1/governance/users/{user_id}")
        assert delete_response.status_code == 200
        
        # Get list again
        response_after = client.get("/api/v1/governance/users")
        assert response_after.status_code == 200
        users_after = response_after.json()
        
        # Count should decrease
        assert len(users_after) == initial_count - 1
        
        # Deleted user should not be in list
        user_ids = [user["id"] for user in users_after]
        assert user_id not in user_ids
