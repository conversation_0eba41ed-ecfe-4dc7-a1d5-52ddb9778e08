"""
PostgreSQL API Integration Tests
Real API testing with PostgreSQL database - no mocks
"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session
import json

class TestGovernanceAPIIntegration:
    """Test governance module API with real PostgreSQL"""
    
    def test_organization_crud_operations(self, client: TestClient, db_session: Session):
        """Test complete CRUD operations for organizations"""
        
        # CREATE - Create new organization
        org_data = {
            "name": "Integration Test Corp",
            "description": "A test organization for API integration testing"
        }
        
        create_response = client.post("/api/v1/governance/organizations", json=org_data)
        assert create_response.status_code == 201
        created_org = create_response.json()
        
        assert created_org["name"] == org_data["name"]
        assert created_org["description"] == org_data["description"]
        assert "id" in created_org
        assert created_org["is_deleted"] is False
        
        org_id = created_org["id"]
        
        # READ - Get organization by ID
        get_response = client.get(f"/api/v1/governance/organizations/{org_id}")
        assert get_response.status_code == 200
        retrieved_org = get_response.json()
        
        assert retrieved_org["id"] == org_id
        assert retrieved_org["name"] == org_data["name"]
        
        # UPDATE - Update organization
        update_data = {
            "name": "Updated Integration Test Corp",
            "description": "Updated description for testing"
        }
        
        update_response = client.put(f"/api/v1/governance/organizations/{org_id}", json=update_data)
        assert update_response.status_code == 200
        updated_org = update_response.json()
        
        assert updated_org["name"] == update_data["name"]
        assert updated_org["description"] == update_data["description"]
        
        # LIST - Get all organizations
        list_response = client.get("/api/v1/governance/organizations")
        assert list_response.status_code == 200
        organizations = list_response.json()
        
        # Our organization should be in the list
        org_ids = [org["id"] for org in organizations]
        assert org_id in org_ids
        
        # DELETE - Soft delete organization
        delete_response = client.delete(f"/api/v1/governance/organizations/{org_id}")
        assert delete_response.status_code == 200
        
        # Verify soft delete - should return 404 for normal queries
        get_deleted_response = client.get(f"/api/v1/governance/organizations/{org_id}")
        assert get_deleted_response.status_code == 404
        
    def test_user_management_integration(self, client: TestClient, sample_organization):
        """Test user management with real database"""
        org_id = sample_organization["id"]
        
        # Create user
        user_data = {
            "username": "integrationuser",
            "email": "<EMAIL>",
            "role": "user",
            "organization_id": org_id
        }
        
        create_response = client.post("/api/v1/governance/users", json=user_data)
        assert create_response.status_code == 201
        created_user = create_response.json()
        
        assert created_user["username"] == user_data["username"]
        assert created_user["email"] == user_data["email"]
        assert created_user["organization_id"] == org_id
        
        user_id = created_user["id"]
        
        # Test user authentication/authorization endpoints
        auth_response = client.get(f"/api/v1/governance/users/{user_id}/permissions")
        assert auth_response.status_code == 200
        
        # Update user role
        role_update = {"role": "admin"}
        role_response = client.patch(f"/api/v1/governance/users/{user_id}/role", json=role_update)
        assert role_response.status_code == 200
        
        updated_user = role_response.json()
        assert updated_user["role"] == "admin"
        
    def test_regulation_management_integration(self, client: TestClient, sample_organization):
        """Test regulation management with PostgreSQL"""
        org_id = sample_organization["id"]
        
        # Create regulation
        regulation_data = {
            "name": "Integration Test Regulation",
            "description": "A comprehensive regulation for integration testing",
            "category": "Financial Services",
            "effective_date": "2024-01-01",
            "organization_id": org_id
        }
        
        create_response = client.post("/api/v1/governance/regulations", json=regulation_data)
        assert create_response.status_code == 201
        created_regulation = create_response.json()
        
        regulation_id = created_regulation["id"]
        
        # Test regulation search
        search_response = client.get("/api/v1/governance/regulations/search", params={"q": "Integration"})
        assert search_response.status_code == 200
        search_results = search_response.json()
        
        # Our regulation should be in search results
        regulation_names = [reg["name"] for reg in search_results]
        assert regulation_data["name"] in regulation_names
        
        # Test regulation categorization
        category_response = client.get("/api/v1/governance/regulations/categories")
        assert category_response.status_code == 200
        categories = category_response.json()
        assert "Financial Services" in categories
        
        # Test regulation compliance status
        compliance_response = client.get(f"/api/v1/governance/regulations/{regulation_id}/compliance")
        assert compliance_response.status_code == 200
        
    def test_risk_control_integration(self, client: TestClient, sample_organization, sample_user):
        """Test risk and control management integration"""
        org_id = sample_organization["id"]
        user_id = sample_user["id"]
        
        # Create risk
        risk_data = {
            "name": "Integration Test Risk",
            "description": "A test risk for integration testing",
            "category": "Operational",
            "likelihood": "Medium",
            "impact": "High",
            "owner_id": user_id,
            "organization_id": org_id
        }
        
        risk_response = client.post("/api/v1/governance/risks", json=risk_data)
        assert risk_response.status_code == 201
        created_risk = risk_response.json()
        risk_id = created_risk["id"]
        
        # Create control
        control_data = {
            "name": "Integration Test Control",
            "description": "A test control for integration testing",
            "category": "Security",
            "type": "Preventive",
            "owner_id": user_id,
            "organization_id": org_id
        }
        
        control_response = client.post("/api/v1/governance/controls", json=control_data)
        assert control_response.status_code == 201
        created_control = control_response.json()
        control_id = created_control["id"]
        
        # Link risk and control
        link_data = {
            "risk_id": risk_id,
            "control_id": control_id
        }
        
        link_response = client.post("/api/v1/governance/risk-controls", json=link_data)
        assert link_response.status_code == 201
        
        # Test risk assessment
        assessment_data = {
            "risk_id": risk_id,
            "assessment_type": "annual",
            "status": "in_progress"
        }
        
        assessment_response = client.post("/api/v1/governance/assessments", json=assessment_data)
        assert assessment_response.status_code == 201

class TestDataSourcesAPIIntegration:
    """Test data sources API with real PostgreSQL"""
    
    def test_public_data_sources_integration(self, client: TestClient):
        """Test public data sources API integration"""
        
        # Test SEC EDGAR integration
        sec_response = client.get("/api/v1/data-sources/public/sec-edgar/companies", params={"limit": 5})
        assert sec_response.status_code == 200
        sec_data = sec_response.json()
        assert isinstance(sec_data, list)
        
        # Test CFPB complaints integration
        cfpb_response = client.get("/api/v1/data-sources/public/cfpb/complaints", params={"limit": 5})
        assert cfpb_response.status_code == 200
        cfpb_data = cfpb_response.json()
        assert isinstance(cfpb_data, list)
        
        # Test FINRA rules integration
        finra_response = client.get("/api/v1/data-sources/public/finra/rules", params={"limit": 5})
        assert finra_response.status_code == 200
        finra_data = finra_response.json()
        assert isinstance(finra_data, list)
        
    def test_data_source_health_checks(self, client: TestClient):
        """Test data source health check endpoints"""
        
        # Test all data sources health
        health_response = client.get("/api/v1/data-sources/health")
        assert health_response.status_code == 200
        health_data = health_response.json()
        
        expected_sources = ["sec_edgar", "cfpb_complaints", "finra_rules", "federal_reserve", "usa_spending"]
        for source in expected_sources:
            assert source in health_data
            assert "status" in health_data[source]
            
    def test_data_synchronization(self, client: TestClient):
        """Test data synchronization endpoints"""
        
        # Test sync status
        sync_status_response = client.get("/api/v1/data-sources/sync/status")
        assert sync_status_response.status_code == 200
        
        # Test manual sync trigger (should be async)
        sync_trigger_response = client.post("/api/v1/data-sources/sync/trigger", json={"sources": ["sec_edgar"]})
        assert sync_trigger_response.status_code == 202  # Accepted for async processing

class TestComplianceAPIIntegration:
    """Test compliance management API with PostgreSQL"""
    
    def test_compliance_calendar_integration(self, client: TestClient, sample_organization):
        """Test compliance calendar API"""
        org_id = sample_organization["id"]
        
        # Create compliance event
        event_data = {
            "title": "Integration Test Compliance Event",
            "description": "A test compliance event",
            "event_type": "deadline",
            "start_date": "2024-06-15T09:00:00",
            "end_date": "2024-06-15T17:00:00",
            "organization_id": org_id
        }
        
        event_response = client.post("/api/v1/compliance/calendar/events", json=event_data)
        assert event_response.status_code == 201
        created_event = event_response.json()
        event_id = created_event["id"]
        
        # Get calendar events
        calendar_response = client.get("/api/v1/compliance/calendar/events", params={"month": "2024-06"})
        assert calendar_response.status_code == 200
        events = calendar_response.json()
        
        # Our event should be in the calendar
        event_titles = [event["title"] for event in events]
        assert event_data["title"] in event_titles
        
        # Test event reminders
        reminder_data = {
            "event_id": event_id,
            "reminder_type": "email",
            "time_before_event": 24,
            "time_unit": "hours"
        }
        
        reminder_response = client.post("/api/v1/compliance/calendar/reminders", json=reminder_data)
        assert reminder_response.status_code == 201
        
    def test_compliance_evidence_repository(self, client: TestClient, sample_organization, sample_regulation):
        """Test compliance evidence repository API"""
        org_id = sample_organization["id"]
        regulation_id = sample_regulation["id"]
        
        # Upload compliance evidence
        evidence_data = {
            "title": "Integration Test Evidence",
            "description": "Test evidence document",
            "evidence_type": "document",
            "regulation_id": regulation_id,
            "organization_id": org_id
        }
        
        evidence_response = client.post("/api/v1/compliance/evidence", json=evidence_data)
        assert evidence_response.status_code == 201
        created_evidence = evidence_response.json()
        evidence_id = created_evidence["id"]
        
        # Get evidence by regulation
        regulation_evidence_response = client.get(f"/api/v1/compliance/evidence/regulation/{regulation_id}")
        assert regulation_evidence_response.status_code == 200
        regulation_evidence = regulation_evidence_response.json()
        
        # Our evidence should be linked to the regulation
        evidence_titles = [evidence["title"] for evidence in regulation_evidence]
        assert evidence_data["title"] in evidence_titles
        
        # Test evidence search
        search_response = client.get("/api/v1/compliance/evidence/search", params={"q": "Integration"})
        assert search_response.status_code == 200
        search_results = search_response.json()
        
        search_titles = [evidence["title"] for evidence in search_results]
        assert evidence_data["title"] in search_titles

class TestAnalyticsAPIIntegration:
    """Test analytics and reporting API with PostgreSQL"""
    
    def test_governance_analytics(self, client: TestClient, sample_organization):
        """Test governance analytics endpoints"""
        org_id = sample_organization["id"]
        
        # Test organization dashboard
        dashboard_response = client.get(f"/api/v1/analytics/governance/dashboard/{org_id}")
        assert dashboard_response.status_code == 200
        dashboard_data = dashboard_response.json()
        
        expected_metrics = ["total_risks", "total_controls", "total_regulations", "compliance_score"]
        for metric in expected_metrics:
            assert metric in dashboard_data
            
        # Test risk analytics
        risk_analytics_response = client.get(f"/api/v1/analytics/governance/risks/{org_id}")
        assert risk_analytics_response.status_code == 200
        risk_analytics = risk_analytics_response.json()
        
        assert "risk_distribution" in risk_analytics
        assert "risk_trends" in risk_analytics
        
    def test_compliance_reporting(self, client: TestClient, sample_organization):
        """Test compliance reporting endpoints"""
        org_id = sample_organization["id"]
        
        # Test compliance report generation
        report_request = {
            "organization_id": org_id,
            "report_type": "compliance_summary",
            "date_range": {
                "start_date": "2024-01-01",
                "end_date": "2024-12-31"
            }
        }
        
        report_response = client.post("/api/v1/analytics/compliance/reports", json=report_request)
        assert report_response.status_code == 202  # Accepted for async processing
        
        # Test report status
        report_data = report_response.json()
        report_id = report_data["report_id"]
        
        status_response = client.get(f"/api/v1/analytics/compliance/reports/{report_id}/status")
        assert status_response.status_code == 200

class TestPerformanceIntegration:
    """Test API performance with real PostgreSQL"""
    
    def test_bulk_operations_performance(self, client: TestClient, sample_organization):
        """Test bulk operations performance"""
        org_id = sample_organization["id"]
        
        # Create multiple regulations in bulk
        bulk_regulations = []
        for i in range(10):
            regulation_data = {
                "name": f"Bulk Test Regulation {i}",
                "description": f"Bulk test regulation number {i}",
                "category": "Testing",
                "organization_id": org_id
            }
            bulk_regulations.append(regulation_data)
            
        bulk_request = {"regulations": bulk_regulations}
        
        import time
        start_time = time.time()
        
        bulk_response = client.post("/api/v1/governance/regulations/bulk", json=bulk_request)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        assert bulk_response.status_code == 201
        assert execution_time < 5.0  # Should complete within 5 seconds
        
        created_regulations = bulk_response.json()
        assert len(created_regulations) == 10
        
    def test_pagination_performance(self, client: TestClient):
        """Test pagination performance with large datasets"""
        
        # Test large page sizes
        large_page_response = client.get("/api/v1/governance/regulations", params={"page": 1, "size": 100})
        assert large_page_response.status_code == 200
        
        # Test deep pagination
        deep_page_response = client.get("/api/v1/governance/regulations", params={"page": 10, "size": 20})
        assert deep_page_response.status_code == 200
        
    def test_concurrent_requests(self, client: TestClient, sample_organization):
        """Test handling concurrent requests"""
        import concurrent.futures
        import threading
        
        org_id = sample_organization["id"]
        
        def make_request(index):
            response = client.get(f"/api/v1/governance/organizations/{org_id}")
            return response.status_code == 200
            
        # Make 10 concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request, i) for i in range(10)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
            
        # All requests should succeed
        assert all(results)
