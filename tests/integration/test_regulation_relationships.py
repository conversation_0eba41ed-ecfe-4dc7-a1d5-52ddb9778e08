"""
Integration tests for regulation relationships with other entities.
Tests the interactions between regulations and related entities like
tags, categories, industries, compliance requirements, and documents.
"""
import unittest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.db.models import (
    Regulation, RegulationTag, RegulationCategory, Industry,
    ComplianceRequirement, RegulationDocument
)

client = TestClient(app)

class TestRegulationRelationships(unittest.TestCase):
    """Integration tests for regulation relationships."""

    def setUp(self):
        """Set up test data."""
        # Test regulation data
        self.test_regulation_data = {
            "title": "Test Regulation",
            "description": "Test regulation description",
            "status": "Active",
            "reference_number": "REG-001",
            "tags": [1, 2],
            "industries": [1],
            "category_id": 1
        }
        
        # Test tag data
        self.test_tag_data = {
            "name": "Test Tag",
            "description": "Test tag description",
            "color": "#1890ff"
        }
        
        # Test category data
        self.test_category_data = {
            "name": "Test Category",
            "description": "Test category description",
            "icon": "bank"
        }
        
        # Test industry data
        self.test_industry_data = {
            "name": "Test Industry",
            "description": "Test industry description",
            "sector": "Financial Services"
        }
        
        # Test compliance requirement data
        self.test_requirement_data = {
            "text": "Test requirement",
            "section": "Section 1",
            "priority": "High",
            "status": "In Progress",
            "regulation_id": 1
        }
        
        # Test document data
        self.test_document_data = {
            "name": "Test Document",
            "description": "Test document description",
            "document_type": "PDF",
            "file_url": "https://example.com/test.pdf",
            "regulation_id": 1
        }

    @patch('app.api.regulations.router.get_db')
    @patch('app.api.regulation_tags.router.get_db')
    @patch('app.api.regulation_categories.router.get_db')
    @patch('app.api.industries.router.get_db')
    def test_create_regulation_with_relationships(
        self, mock_industries_get_db, mock_categories_get_db, 
        mock_tags_get_db, mock_regulations_get_db
    ):
        """Test creating a regulation with relationships to tags, categories, and industries."""
        # Create mock database session
        mock_db = MagicMock()
        mock_regulations_get_db.return_value = mock_db
        mock_tags_get_db.return_value = mock_db
        mock_categories_get_db.return_value = mock_db
        mock_industries_get_db.return_value = mock_db
        
        # Create mock tag
        mock_tag1 = MagicMock()
        mock_tag1.id = 1
        mock_tag1.name = "Tag 1"
        
        mock_tag2 = MagicMock()
        mock_tag2.id = 2
        mock_tag2.name = "Tag 2"
        
        # Create mock category
        mock_category = MagicMock()
        mock_category.id = 1
        mock_category.name = "Category 1"
        
        # Create mock industry
        mock_industry = MagicMock()
        mock_industry.id = 1
        mock_industry.name = "Industry 1"
        
        # Set up the mock query chain for tags
        mock_query_tags = MagicMock()
        mock_db.query.return_value = mock_query_tags
        mock_query_tags.filter.return_value = mock_query_tags
        mock_query_tags.all.return_value = [mock_tag1, mock_tag2]
        
        # Set up the mock query chain for category
        mock_query_category = MagicMock()
        mock_db.query.return_value = mock_query_category
        mock_query_category.filter.return_value = mock_query_category
        mock_query_category.first.return_value = mock_category
        
        # Set up the mock query chain for industry
        mock_query_industry = MagicMock()
        mock_db.query.return_value = mock_query_industry
        mock_query_industry.filter.return_value = mock_query_industry
        mock_query_industry.all.return_value = [mock_industry]
        
        # Create mock regulation object that will be returned
        mock_regulation = MagicMock()
        mock_regulation.id = 1
        mock_regulation.title = self.test_regulation_data["title"]
        mock_regulation.description = self.test_regulation_data["description"]
        mock_regulation.status = self.test_regulation_data["status"]
        mock_regulation.reference_number = self.test_regulation_data["reference_number"]
        mock_regulation.category_id = self.test_regulation_data["category_id"]
        mock_regulation.tags = [mock_tag1, mock_tag2]
        mock_regulation.industries = [mock_industry]
        mock_regulation.category = mock_category
        mock_regulation.created_at = "2023-01-01T00:00:00"
        mock_regulation.changed_on = "2023-01-01T00:00:00"
        
        # Mock the add, commit, and refresh methods
        mock_db.add = MagicMock()
        mock_db.commit = MagicMock()
        mock_db.refresh = MagicMock()
        
        # Make the Regulation constructor return our mock
        with patch('app.api.regulations.router.Regulation', return_value=mock_regulation):
            # Make request to create regulation
            response = client.post("/api/v1/regulations/", json=self.test_regulation_data)
            
            # Check response
            self.assertEqual(response.status_code, 201)
            self.assertEqual(response.json()["title"], self.test_regulation_data["title"])
            
            # Verify methods were called
            mock_db.add.assert_called_once()
            mock_db.commit.assert_called_once()
            mock_db.refresh.assert_called_once()

    @patch('app.api.regulations.router.get_db')
    @patch('app.api.compliance_requirements.router.get_db')
    def test_regulation_with_compliance_requirements(self, mock_requirements_get_db, mock_regulations_get_db):
        """Test the relationship between regulations and compliance requirements."""
        # Create mock database session
        mock_db = MagicMock()
        mock_regulations_get_db.return_value = mock_db
        mock_requirements_get_db.return_value = mock_db
        
        # Create mock regulation
        mock_regulation = MagicMock()
        mock_regulation.id = 1
        mock_regulation.title = "Test Regulation"
        
        # Create mock compliance requirement
        mock_requirement = MagicMock()
        mock_requirement.id = 1
        mock_requirement.text = self.test_requirement_data["text"]
        mock_requirement.section = self.test_requirement_data["section"]
        mock_requirement.priority = self.test_requirement_data["priority"]
        mock_requirement.status = self.test_requirement_data["status"]
        mock_requirement.regulation_id = self.test_requirement_data["regulation_id"]
        mock_requirement.regulation = mock_regulation
        mock_requirement.created_at = "2023-01-01T00:00:00"
        mock_requirement.changed_on = "2023-01-01T00:00:00"
        
        # Set up the mock query chain for regulation
        mock_query_regulation = MagicMock()
        mock_db.query.return_value = mock_query_regulation
        mock_query_regulation.filter.return_value = mock_query_regulation
        mock_query_regulation.first.return_value = mock_regulation
        
        # Mock the add, commit, and refresh methods
        mock_db.add = MagicMock()
        mock_db.commit = MagicMock()
        mock_db.refresh = MagicMock()
        
        # Make the ComplianceRequirement constructor return our mock
        with patch('app.api.compliance_requirements.router.ComplianceRequirement', return_value=mock_requirement):
            # Make request to create compliance requirement
            response = client.post("/api/v1/compliance-requirements/", json=self.test_requirement_data)
            
            # Check response
            self.assertEqual(response.status_code, 201)
            self.assertEqual(response.json()["text"], self.test_requirement_data["text"])
            self.assertEqual(response.json()["regulation_id"], self.test_requirement_data["regulation_id"])
            
            # Verify methods were called
            mock_db.add.assert_called_once()
            mock_db.commit.assert_called_once()
            mock_db.refresh.assert_called_once()

    @patch('app.api.regulations.router.get_db')
    @patch('app.api.regulation_documents.router.get_db')
    def test_regulation_with_documents(self, mock_documents_get_db, mock_regulations_get_db):
        """Test the relationship between regulations and documents."""
        # Create mock database session
        mock_db = MagicMock()
        mock_regulations_get_db.return_value = mock_db
        mock_documents_get_db.return_value = mock_db
        
        # Create mock regulation
        mock_regulation = MagicMock()
        mock_regulation.id = 1
        mock_regulation.title = "Test Regulation"
        
        # Create mock document
        mock_document = MagicMock()
        mock_document.id = 1
        mock_document.name = self.test_document_data["name"]
        mock_document.description = self.test_document_data["description"]
        mock_document.document_type = self.test_document_data["document_type"]
        mock_document.file_url = self.test_document_data["file_url"]
        mock_document.regulation_id = self.test_document_data["regulation_id"]
        mock_document.regulation = mock_regulation
        mock_document.created_at = "2023-01-01T00:00:00"
        mock_document.changed_on = "2023-01-01T00:00:00"
        
        # Set up the mock query chain for regulation
        mock_query_regulation = MagicMock()
        mock_db.query.return_value = mock_query_regulation
        mock_query_regulation.filter.return_value = mock_query_regulation
        mock_query_regulation.first.return_value = mock_regulation
        
        # Mock the add, commit, and refresh methods
        mock_db.add = MagicMock()
        mock_db.commit = MagicMock()
        mock_db.refresh = MagicMock()
        
        # Make the RegulationDocument constructor return our mock
        with patch('app.api.regulation_documents.router.RegulationDocument', return_value=mock_document):
            # Make request to create document
            response = client.post("/api/v1/regulation-documents/", json=self.test_document_data)
            
            # Check response
            self.assertEqual(response.status_code, 201)
            self.assertEqual(response.json()["name"], self.test_document_data["name"])
            self.assertEqual(response.json()["regulation_id"], self.test_document_data["regulation_id"])
            
            # Verify methods were called
            mock_db.add.assert_called_once()
            mock_db.commit.assert_called_once()
            mock_db.refresh.assert_called_once()

    @patch('app.api.regulations.router.get_db')
    def test_get_regulation_with_related_entities(self, mock_get_db):
        """Test getting a regulation with all related entities."""
        # Create mock database session
        mock_db = MagicMock()
        mock_get_db.return_value = mock_db
        
        # Create mock tag
        mock_tag1 = MagicMock()
        mock_tag1.id = 1
        mock_tag1.name = "Tag 1"
        mock_tag1.color = "#1890ff"
        
        mock_tag2 = MagicMock()
        mock_tag2.id = 2
        mock_tag2.name = "Tag 2"
        mock_tag2.color = "#52c41a"
        
        # Create mock category
        mock_category = MagicMock()
        mock_category.id = 1
        mock_category.name = "Category 1"
        mock_category.icon = "bank"
        
        # Create mock industry
        mock_industry = MagicMock()
        mock_industry.id = 1
        mock_industry.name = "Industry 1"
        mock_industry.sector = "Financial Services"
        
        # Create mock regulation with related entities
        mock_regulation = MagicMock()
        mock_regulation.id = 1
        mock_regulation.title = "Test Regulation"
        mock_regulation.description = "Test regulation description"
        mock_regulation.status = "Active"
        mock_regulation.reference_number = "REG-001"
        mock_regulation.category_id = 1
        mock_regulation.tags = [mock_tag1, mock_tag2]
        mock_regulation.industries = [mock_industry]
        mock_regulation.category = mock_category
        mock_regulation.created_at = "2023-01-01T00:00:00"
        mock_regulation.changed_on = "2023-01-01T00:00:00"
        
        # Set up the mock query chain
        mock_query = MagicMock()
        mock_db.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_regulation
        
        # Make request
        response = client.get("/api/v1/regulations/1")
        
        # Check response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["title"], "Test Regulation")
        self.assertEqual(len(response.json()["tags"]), 2)
        self.assertEqual(response.json()["tags"][0]["name"], "Tag 1")
        self.assertEqual(response.json()["tags"][1]["name"], "Tag 2")
        self.assertEqual(len(response.json()["industries"]), 1)
        self.assertEqual(response.json()["industries"][0]["name"], "Industry 1")
        self.assertEqual(response.json()["category"]["name"], "Category 1")


if __name__ == "__main__":
    unittest.main()
