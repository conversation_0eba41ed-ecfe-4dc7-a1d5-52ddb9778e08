"""
Basic PostgreSQL Integration Tests
Tests real database operations with existing models
"""
import pytest
import os
from datetime import datetime
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from fastapi.testclient import TestClient

# Import application components
from app.main import app
from app.db.database import get_db
from app.db.models import Base, Country, Regulator, RegulationURL, Item

# Test database configuration
TEST_DATABASE_URL = os.getenv(
    "DATABASE_URL", 
    "postgresql://postgres:postgres@localhost:5433/regulationguru_test"
)

class TestPostgreSQLBasicOperations:
    """Test basic PostgreSQL operations with existing models"""
    
    @pytest.fixture(scope="class")
    def pg_engine(self):
        """Create PostgreSQL engine for testing"""
        engine = create_engine(TEST_DATABASE_URL, echo=False)
        
        # Create tables
        Base.metadata.create_all(bind=engine)
        
        yield engine
        
        # Cleanup
        Base.metadata.drop_all(bind=engine)
        engine.dispose()
        
    @pytest.fixture
    def pg_session(self, pg_engine):
        """Create PostgreSQL session for testing"""
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=pg_engine)
        session = SessionLocal()
        
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
            
    @pytest.fixture
    def pg_client(self, pg_session):
        """Create test client with PostgreSQL session"""
        def override_get_db():
            try:
                yield pg_session
            finally:
                pass
        
        app.dependency_overrides[get_db] = override_get_db
        
        with TestClient(app) as test_client:
            yield test_client
        
        app.dependency_overrides.clear()

    def test_postgresql_connection(self, pg_engine):
        """Test PostgreSQL connection"""
        with pg_engine.connect() as conn:
            result = conn.execute(text("SELECT version()"))
            version = result.fetchone()[0]
            assert "PostgreSQL" in version
            print(f"✅ Connected to: {version}")
            
    def test_country_crud_operations(self, pg_session: Session):
        """Test CRUD operations with Country model"""
        
        # CREATE
        country = Country(
            name="Test Country PostgreSQL",
            code="TCP",
            region="Test Region",
            subregion="Test Subregion",
            flag_emoji="🏳️"
        )
        pg_session.add(country)
        pg_session.commit()
        pg_session.refresh(country)
        
        assert country.id is not None
        assert country.name == "Test Country PostgreSQL"
        country_id = country.id
        
        # READ
        retrieved_country = pg_session.query(Country).filter(Country.id == country_id).first()
        assert retrieved_country is not None
        assert retrieved_country.name == "Test Country PostgreSQL"
        assert retrieved_country.code == "TCP"
        
        # UPDATE
        retrieved_country.name = "Updated Test Country"
        pg_session.commit()
        
        updated_country = pg_session.query(Country).filter(Country.id == country_id).first()
        assert updated_country.name == "Updated Test Country"
        
        # DELETE
        pg_session.delete(updated_country)
        pg_session.commit()
        
        deleted_country = pg_session.query(Country).filter(Country.id == country_id).first()
        assert deleted_country is None
        
    def test_regulator_crud_operations(self, pg_session: Session):
        """Test CRUD operations with Regulator model"""
        
        # First create a country
        country = Country(
            name="Regulator Test Country",
            code="RTC",
            region="Test Region"
        )
        pg_session.add(country)
        pg_session.commit()
        pg_session.refresh(country)
        
        # CREATE regulator
        regulator = Regulator(
            name="Test Regulator PostgreSQL",
            website="https://test-regulator.example.com",
            country_id=country.id
        )
        pg_session.add(regulator)
        pg_session.commit()
        pg_session.refresh(regulator)
        
        assert regulator.id is not None
        assert regulator.name == "Test Regulator PostgreSQL"
        regulator_id = regulator.id
        
        # READ with relationship
        retrieved_regulator = pg_session.query(Regulator).filter(Regulator.id == regulator_id).first()
        assert retrieved_regulator is not None
        assert retrieved_regulator.country_id == country.id
        
        # UPDATE
        retrieved_regulator.website = "https://updated-regulator.example.com"
        pg_session.commit()
        
        updated_regulator = pg_session.query(Regulator).filter(Regulator.id == regulator_id).first()
        assert updated_regulator.website == "https://updated-regulator.example.com"
        
        # DELETE
        pg_session.delete(updated_regulator)
        pg_session.delete(country)
        pg_session.commit()
        
    def test_soft_delete_functionality(self, pg_session: Session):
        """Test soft delete functionality with Item model"""
        
        # CREATE item
        item = Item(
            name="Test Item for Soft Delete",
            description="This item will be soft deleted"
        )
        pg_session.add(item)
        pg_session.commit()
        pg_session.refresh(item)
        
        item_id = item.id
        assert item.is_deleted is False
        assert item.deleted_at is None
        
        # SOFT DELETE
        item.soft_delete()
        pg_session.commit()
        
        # Verify soft delete
        pg_session.refresh(item)
        assert item.is_deleted is True
        assert item.deleted_at is not None
        assert isinstance(item.deleted_at, datetime)
        
        # Verify item still exists in database
        all_items = pg_session.query(Item).filter(Item.id == item_id).all()
        assert len(all_items) == 1
        
        # Verify active items query excludes soft deleted
        active_items = pg_session.query(Item).filter(Item.is_deleted == False).all()
        item_ids = [i.id for i in active_items]
        assert item_id not in item_ids
        
        # Verify deleted items query includes soft deleted
        deleted_items = pg_session.query(Item).filter(Item.is_deleted == True).all()
        deleted_item_ids = [i.id for i in deleted_items]
        assert item_id in deleted_item_ids
        
    def test_regulation_url_operations(self, pg_session: Session):
        """Test operations with RegulationURL model"""
        
        # CREATE
        regulation_url = RegulationURL(
            url="https://test-regulation.example.com/reg1",
            title="Test Regulation PostgreSQL",
            description="A test regulation for PostgreSQL integration",
            status="Active"
        )
        pg_session.add(regulation_url)
        pg_session.commit()
        pg_session.refresh(regulation_url)
        
        assert regulation_url.id is not None
        regulation_id = regulation_url.id
        
        # Test search functionality
        search_results = pg_session.query(RegulationURL).filter(
            RegulationURL.title.ilike("%PostgreSQL%")
        ).all()
        
        assert len(search_results) >= 1
        found_regulation = next((r for r in search_results if r.id == regulation_id), None)
        assert found_regulation is not None
        
        # Test status filtering
        active_regulations = pg_session.query(RegulationURL).filter(
            RegulationURL.status == "Active"
        ).all()
        
        active_ids = [r.id for r in active_regulations]
        assert regulation_id in active_ids
        
    def test_timestamp_functionality(self, pg_session: Session):
        """Test timestamp mixins work correctly"""
        
        # CREATE item
        item = Item(
            name="Timestamp Test Item",
            description="Testing timestamp functionality"
        )
        
        before_create = datetime.utcnow()
        pg_session.add(item)
        pg_session.commit()
        pg_session.refresh(item)
        after_create = datetime.utcnow()
        
        # Verify created_at timestamp
        assert item.created_at is not None
        assert before_create <= item.created_at <= after_create
        
        # Store original timestamps
        original_created_at = item.created_at
        original_changed_on = item.changed_on
        
        # Wait a moment and update
        import time
        time.sleep(0.1)
        
        before_update = datetime.utcnow()
        item.description = "Updated description"
        pg_session.commit()
        pg_session.refresh(item)
        after_update = datetime.utcnow()
        
        # Verify created_at unchanged, changed_on updated
        assert item.created_at == original_created_at
        assert item.changed_on > original_changed_on
        assert before_update <= item.changed_on <= after_update
        
    def test_database_constraints(self, pg_session: Session):
        """Test database constraints and unique indexes"""
        
        # Test unique constraint on country code
        country1 = Country(name="Country 1", code="UC", region="Region 1")
        pg_session.add(country1)
        pg_session.commit()
        
        # Try to create another country with same code
        country2 = Country(name="Country 2", code="UC", region="Region 2")
        pg_session.add(country2)
        
        with pytest.raises(Exception):  # Should raise integrity error
            pg_session.commit()
            
        pg_session.rollback()
        
    def test_api_integration_with_postgresql(self, pg_client: TestClient):
        """Test API endpoints with PostgreSQL backend"""
        
        # Test health endpoint
        health_response = pg_client.get("/api/v1/health")
        assert health_response.status_code == 200
        
        # Test countries API
        countries_response = pg_client.get("/api/v1/countries")
        assert countries_response.status_code == 200
        countries_data = countries_response.json()
        assert isinstance(countries_data, list)
        
        # Create country via API
        country_data = {
            "name": "API Test Country",
            "code": "ATC",
            "region": "API Test Region",
            "subregion": "API Test Subregion",
            "flag_emoji": "🏳️"
        }
        
        create_response = pg_client.post("/api/v1/countries", json=country_data)
        assert create_response.status_code == 201
        created_country = create_response.json()
        
        assert created_country["name"] == country_data["name"]
        assert created_country["code"] == country_data["code"]
        country_id = created_country["id"]
        
        # Get country by ID
        get_response = pg_client.get(f"/api/v1/countries/{country_id}")
        assert get_response.status_code == 200
        retrieved_country = get_response.json()
        assert retrieved_country["id"] == country_id
        
        # Update country
        update_data = {"name": "Updated API Test Country"}
        update_response = pg_client.put(f"/api/v1/countries/{country_id}", json=update_data)
        assert update_response.status_code == 200
        updated_country = update_response.json()
        assert updated_country["name"] == update_data["name"]
        
        # Delete country
        delete_response = pg_client.delete(f"/api/v1/countries/{country_id}")
        assert delete_response.status_code == 200
        
        # Verify deletion
        get_deleted_response = pg_client.get(f"/api/v1/countries/{country_id}")
        assert get_deleted_response.status_code == 404
        
    def test_concurrent_operations(self, pg_engine):
        """Test concurrent database operations"""
        import threading
        import concurrent.futures
        
        def create_country(thread_id):
            SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=pg_engine)
            session = SessionLocal()
            try:
                country = Country(
                    name=f"Concurrent Country {thread_id}",
                    code=f"CC{thread_id:02d}",
                    region="Concurrent Region"
                )
                session.add(country)
                session.commit()
                session.refresh(country)
                return country.id
            finally:
                session.close()
                
        # Create 5 countries concurrently
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(create_country, i) for i in range(5)]
            country_ids = [future.result() for future in concurrent.futures.as_completed(futures)]
            
        assert len(country_ids) == 5
        assert len(set(country_ids)) == 5  # All IDs should be unique
        
        # Verify all countries were created
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=pg_engine)
        session = SessionLocal()
        try:
            concurrent_countries = session.query(Country).filter(
                Country.name.like("Concurrent Country%")
            ).all()
            assert len(concurrent_countries) == 5
        finally:
            session.close()

class TestPostgreSQLPerformance:
    """Test PostgreSQL performance characteristics"""
    
    @pytest.fixture(scope="class")
    def pg_engine(self):
        """Create PostgreSQL engine for performance testing"""
        engine = create_engine(TEST_DATABASE_URL, echo=False, pool_size=10, max_overflow=20)
        Base.metadata.create_all(bind=engine)
        yield engine
        Base.metadata.drop_all(bind=engine)
        engine.dispose()
        
    def test_bulk_insert_performance(self, pg_engine):
        """Test bulk insert performance"""
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=pg_engine)
        session = SessionLocal()
        
        try:
            # Create 100 countries in bulk
            countries = []
            for i in range(100):
                country = Country(
                    name=f"Bulk Country {i}",
                    code=f"BC{i:03d}",
                    region="Bulk Region"
                )
                countries.append(country)
                
            import time
            start_time = time.time()
            
            session.add_all(countries)
            session.commit()
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            print(f"✅ Bulk inserted 100 countries in {execution_time:.3f} seconds")
            assert execution_time < 2.0  # Should complete within 2 seconds
            
            # Verify all countries were created
            count = session.query(Country).filter(Country.name.like("Bulk Country%")).count()
            assert count == 100
            
        finally:
            session.close()
            
    def test_query_performance(self, pg_engine):
        """Test query performance with indexes"""
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=pg_engine)
        session = SessionLocal()
        
        try:
            import time
            
            # Test indexed query (country code)
            start_time = time.time()
            result = session.query(Country).filter(Country.code == "BC001").first()
            end_time = time.time()
            
            indexed_query_time = end_time - start_time
            print(f"✅ Indexed query completed in {indexed_query_time:.6f} seconds")
            assert indexed_query_time < 0.1  # Should be very fast with index
            
            # Test range query
            start_time = time.time()
            results = session.query(Country).filter(Country.name.like("Bulk Country%")).limit(10).all()
            end_time = time.time()
            
            range_query_time = end_time - start_time
            print(f"✅ Range query completed in {range_query_time:.6f} seconds")
            assert len(results) == 10
            
        finally:
            session.close()
