"""
Comprehensive unit tests for LocalRegulationImporter.

Tests cover all functionality including file discovery, validation,
import processing, error handling, and storage management.
"""

import os
import tempfile
import shutil
import pytest
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

import pandas as pd
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.db.models import Base
from app.db.models.regulations_csv import RegulationCSVRecord, RegulationCSVImportLog
from app.services.local_regulation_importer import LocalRegulationImporter, LocalStorageConfig
from app.schemas.regulations_csv import RegulationCSVImportResult


class TestLocalStorageConfig:
    """Test LocalStorageConfig class."""
    
    def test_init_with_default_path(self):
        """Test initialization with default path."""
        config = LocalStorageConfig()
        assert config.base_path == Path("local_storage")
        assert config.imports_path == Path("local_storage/imports")
        assert config.exports_path == Path("local_storage/exports")
    
    def test_init_with_custom_path(self):
        """Test initialization with custom path."""
        config = LocalStorageConfig("custom_storage")
        assert config.base_path == Path("custom_storage")
        assert config.imports_path == Path("custom_storage/imports")
    
    def test_ensure_directories_created(self):
        """Test that directories are created during initialization."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = LocalStorageConfig(temp_dir)
            
            # Check all directories exist
            assert config.imports_path.exists()
            assert config.exports_path.exists()
            assert config.temp_path.exists()
            assert config.processed_path.exists()
            assert config.failed_path.exists()
            assert config.backups_path.exists()


class TestLocalRegulationImporter:
    """Test LocalRegulationImporter class."""
    
    @pytest.fixture
    def temp_storage(self):
        """Create temporary storage for testing."""
        temp_dir = tempfile.mkdtemp()
        config = LocalStorageConfig(temp_dir)
        yield config
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def mock_db(self):
        """Create mock database session."""
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        SessionLocal = sessionmaker(bind=engine)
        return SessionLocal()
    
    @pytest.fixture
    def importer(self, mock_db, temp_storage):
        """Create LocalRegulationImporter instance for testing."""
        return LocalRegulationImporter(mock_db, temp_storage)
    
    @pytest.fixture
    def sample_csv_content(self):
        """Sample CSV content for testing."""
        return """Country_Name,Country_Code,Document_Title,Document_Type,Issuing_Authority,Publication_Date,Effective_Date,Legal_Status,Document_URL,Language,Scope_Application,Key_Compliance_Requirements,Enforcement_Mechanisms,Penalties,Cross_Border_Elements,Data_Protection_Provisions,Incident_Reporting_Requirements,Risk_Management_Mandates,Third_Party_Requirements,Audit_Obligations,Certification_Requirements,Implementation_Timeline,International_Standards_Alignment,Extraterritorial_Reach,Safe_Harbor_Provisions,Industry_Specific_Provisions,Technology_Specific_Provisions
United States,US,Test Regulation,Federal Law,Test Authority,2024-01-01,2024-01-01,Binding,https://example.com,English,Test scope,Test requirements,Test enforcement,Test penalties,Test cross-border,Test data protection,Test incident reporting,Test risk management,Test third party,Test audit,Test certification,Test timeline,Test standards,Test extraterritorial,Test safe harbor,Test industry,Test technology"""
    
    def create_test_csv(self, temp_storage, filename="test.csv", content=None):
        """Helper to create test CSV file."""
        if content is None:
            content = self.sample_csv_content
        
        file_path = temp_storage.imports_path / filename
        with open(file_path, 'w') as f:
            f.write(content)
        return file_path
    
    def test_discover_import_files_empty_directory(self, importer):
        """Test file discovery in empty directory."""
        files = importer.discover_import_files()
        assert files == []
    
    def test_discover_import_files_with_csv_files(self, importer, temp_storage, sample_csv_content):
        """Test file discovery with CSV files present."""
        # Create test files
        self.create_test_csv(temp_storage, "test1.csv", sample_csv_content)
        self.create_test_csv(temp_storage, "test2.csv", sample_csv_content)
        
        # Create non-CSV file (should be ignored)
        (temp_storage.imports_path / "test.txt").write_text("not a csv")
        
        files = importer.discover_import_files()
        assert len(files) == 2
        assert all(f.suffix == '.csv' for f in files)
        assert all(f.name.startswith('test') for f in files)
    
    def test_discover_import_files_with_pattern(self, importer, temp_storage, sample_csv_content):
        """Test file discovery with custom pattern."""
        self.create_test_csv(temp_storage, "regulations_2024.csv", sample_csv_content)
        self.create_test_csv(temp_storage, "other_data.csv", sample_csv_content)
        
        files = importer.discover_import_files("regulations_*.csv")
        assert len(files) == 1
        assert files[0].name == "regulations_2024.csv"
    
    def test_validate_file_success(self, importer, temp_storage, sample_csv_content):
        """Test successful file validation."""
        file_path = self.create_test_csv(temp_storage, "valid.csv", sample_csv_content)
        
        is_valid, errors = importer.validate_file(file_path)
        assert is_valid
        assert errors == []
    
    def test_validate_file_missing_file(self, importer, temp_storage):
        """Test validation of missing file."""
        file_path = temp_storage.imports_path / "missing.csv"
        
        is_valid, errors = importer.validate_file(file_path)
        assert not is_valid
        assert any("does not exist" in error for error in errors)
    
    def test_validate_file_empty_file(self, importer, temp_storage):
        """Test validation of empty file."""
        file_path = temp_storage.imports_path / "empty.csv"
        file_path.touch()  # Create empty file
        
        is_valid, errors = importer.validate_file(file_path)
        assert not is_valid
        assert any("empty" in error.lower() for error in errors)
    
    def test_validate_file_missing_columns(self, importer, temp_storage):
        """Test validation of file with missing required columns."""
        content = "Column1,Column2\nvalue1,value2"
        file_path = self.create_test_csv(temp_storage, "invalid.csv", content)
        
        is_valid, errors = importer.validate_file(file_path)
        assert not is_valid
        assert any("Missing required columns" in error for error in errors)
    
    def test_validate_file_malformed_csv(self, importer, temp_storage):
        """Test validation of malformed CSV."""
        content = "This is not a valid CSV file\nwith proper structure"
        file_path = self.create_test_csv(temp_storage, "malformed.csv", content)
        
        is_valid, errors = importer.validate_file(file_path)
        assert not is_valid
        assert len(errors) > 0
    
    def test_calculate_file_hash(self, importer, temp_storage, sample_csv_content):
        """Test file hash calculation."""
        file_path = self.create_test_csv(temp_storage, "hash_test.csv", sample_csv_content)
        
        hash1 = importer.calculate_file_hash(file_path)
        hash2 = importer.calculate_file_hash(file_path)
        
        assert hash1 == hash2  # Same file should have same hash
        assert len(hash1) == 64  # SHA-256 hash length
        assert all(c in '0123456789abcdef' for c in hash1)  # Valid hex
    
    def test_calculate_file_hash_different_files(self, importer, temp_storage, sample_csv_content):
        """Test that different files have different hashes."""
        file1 = self.create_test_csv(temp_storage, "file1.csv", sample_csv_content)
        file2 = self.create_test_csv(temp_storage, "file2.csv", sample_csv_content + "\nExtra line")
        
        hash1 = importer.calculate_file_hash(file1)
        hash2 = importer.calculate_file_hash(file2)
        
        assert hash1 != hash2
    
    def test_backup_file(self, importer, temp_storage, sample_csv_content):
        """Test file backup functionality."""
        file_path = self.create_test_csv(temp_storage, "backup_test.csv", sample_csv_content)
        
        backup_path = importer.backup_file(file_path, "test_reason")
        
        assert backup_path.exists()
        assert backup_path.parent == temp_storage.backups_path
        assert "backup_test" in backup_path.name
        assert "test_reason" in backup_path.name
        assert backup_path.read_text() == file_path.read_text()
    
    def test_move_file_to_processed_success(self, importer, temp_storage, sample_csv_content):
        """Test moving file to processed directory on success."""
        file_path = self.create_test_csv(temp_storage, "move_test.csv", sample_csv_content)
        original_content = file_path.read_text()
        
        new_path = importer.move_file_to_processed(file_path, success=True)
        
        assert not file_path.exists()  # Original file moved
        assert new_path.exists()
        assert new_path.parent == temp_storage.processed_path
        assert "move_test" in new_path.name
        assert new_path.read_text() == original_content
    
    def test_move_file_to_processed_failure(self, importer, temp_storage, sample_csv_content):
        """Test moving file to failed directory on failure."""
        file_path = self.create_test_csv(temp_storage, "fail_test.csv", sample_csv_content)
        
        new_path = importer.move_file_to_processed(file_path, success=False)
        
        assert not file_path.exists()
        assert new_path.exists()
        assert new_path.parent == temp_storage.failed_path
    
    @patch('app.services.local_regulation_importer.RegulationCSVProcessor')
    def test_import_single_file_success(self, mock_processor_class, importer, temp_storage, sample_csv_content):
        """Test successful single file import."""
        # Setup mock
        mock_processor = Mock()
        mock_processor_class.return_value = mock_processor
        mock_processor.import_csv_file.return_value = RegulationCSVImportResult(
            batch_id="test_batch",
            total_records=1,
            successful_imports=1,
            failed_imports=0,
            updated_records=0,
            errors=[],
            warnings=[],
            processing_time=1.0
        )
        
        # Create test file
        file_path = self.create_test_csv(temp_storage, "import_test.csv", sample_csv_content)
        
        # Import file
        result = importer.import_single_file(file_path, user_id="test_user")
        
        # Verify results
        assert result.successful_imports == 1
        assert result.failed_imports == 0
        assert not file_path.exists()  # File should be moved
        assert len(list(temp_storage.processed_path.glob("*"))) == 1  # File in processed
        assert len(list(temp_storage.backups_path.glob("*"))) == 1  # Backup created
    
    @patch('app.services.local_regulation_importer.RegulationCSVProcessor')
    def test_import_single_file_validation_failure(self, mock_processor_class, importer, temp_storage):
        """Test import with validation failure."""
        # Create invalid file
        file_path = self.create_test_csv(temp_storage, "invalid.csv", "invalid,content")
        
        result = importer.import_single_file(file_path, validate_first=True)
        
        assert result.failed_imports == 0  # No processing attempted
        assert len(result.errors) > 0
        assert not file_path.exists()  # File moved to failed
        assert len(list(temp_storage.failed_path.glob("*"))) == 1
    
    def test_import_all_files_empty_directory(self, importer):
        """Test importing from empty directory."""
        results = importer.import_all_files()
        assert results == []
    
    @patch('app.services.local_regulation_importer.RegulationCSVProcessor')
    def test_import_all_files_multiple_files(self, mock_processor_class, importer, temp_storage, sample_csv_content):
        """Test importing multiple files."""
        # Setup mock
        mock_processor = Mock()
        mock_processor_class.return_value = mock_processor
        mock_processor.import_csv_file.return_value = RegulationCSVImportResult(
            batch_id="test_batch",
            total_records=1,
            successful_imports=1,
            failed_imports=0,
            updated_records=0,
            errors=[],
            warnings=[],
            processing_time=1.0
        )
        
        # Create test files
        self.create_test_csv(temp_storage, "file1.csv", sample_csv_content)
        self.create_test_csv(temp_storage, "file2.csv", sample_csv_content)
        
        results = importer.import_all_files()
        
        assert len(results) == 2
        assert all(r.successful_imports == 1 for r in results)
        assert len(list(temp_storage.imports_path.glob("*"))) == 0  # All files moved
    
    def test_cleanup_old_backups(self, importer, temp_storage):
        """Test cleanup of old backup files."""
        # Create old and new backup files
        old_file = temp_storage.backups_path / "old_backup.csv"
        new_file = temp_storage.backups_path / "new_backup.csv"
        
        old_file.write_text("old content")
        new_file.write_text("new content")
        
        # Make old file appear old
        old_time = (datetime.now() - timedelta(days=35)).timestamp()
        os.utime(old_file, (old_time, old_time))
        
        cleaned_count = importer.cleanup_old_backups(days_to_keep=30)
        
        assert cleaned_count == 1
        assert not old_file.exists()
        assert new_file.exists()
    
    def test_get_storage_stats(self, importer, temp_storage, sample_csv_content):
        """Test storage statistics calculation."""
        # Create some test files
        self.create_test_csv(temp_storage, "test1.csv", sample_csv_content)
        (temp_storage.processed_path / "processed1.csv").write_text(sample_csv_content)
        
        stats = importer.get_storage_stats()
        
        assert "imports" in stats
        assert "processed" in stats
        assert "backups" in stats
        
        assert stats["imports"]["file_count"] == 1
        assert stats["processed"]["file_count"] == 1
        assert stats["imports"]["total_size_mb"] > 0
        assert stats["processed"]["total_size_mb"] > 0
    
    def test_get_storage_stats_empty_directories(self, importer):
        """Test storage statistics with empty directories."""
        stats = importer.get_storage_stats()
        
        for key in ["imports", "exports", "processed", "failed", "backups", "temp"]:
            assert key in stats
            assert stats[key]["file_count"] == 0
            assert stats[key]["total_size_mb"] == 0.0


class TestIntegrationScenarios:
    """Integration tests for complex scenarios."""
    
    @pytest.fixture
    def temp_storage(self):
        """Create temporary storage for testing."""
        temp_dir = tempfile.mkdtemp()
        config = LocalStorageConfig(temp_dir)
        yield config
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def mock_db(self):
        """Create mock database session."""
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        SessionLocal = sessionmaker(bind=engine)
        return SessionLocal()
    
    def test_complete_import_workflow(self, mock_db, temp_storage):
        """Test complete import workflow from file discovery to cleanup."""
        importer = LocalRegulationImporter(mock_db, temp_storage)
        
        # Create test CSV content
        csv_content = """Country_Name,Country_Code,Document_Title,Document_Type,Issuing_Authority,Publication_Date,Effective_Date,Legal_Status,Document_URL,Language,Scope_Application,Key_Compliance_Requirements,Enforcement_Mechanisms,Penalties,Cross_Border_Elements,Data_Protection_Provisions,Incident_Reporting_Requirements,Risk_Management_Mandates,Third_Party_Requirements,Audit_Obligations,Certification_Requirements,Implementation_Timeline,International_Standards_Alignment,Extraterritorial_Reach,Safe_Harbor_Provisions,Industry_Specific_Provisions,Technology_Specific_Provisions
United States,US,Test Regulation 1,Federal Law,Test Authority,2024-01-01,2024-01-01,Binding,https://example.com,English,Test scope,Test requirements,Test enforcement,Test penalties,Test cross-border,Test data protection,Test incident reporting,Test risk management,Test third party,Test audit,Test certification,Test timeline,Test standards,Test extraterritorial,Test safe harbor,Test industry,Test technology
Canada,CA,Test Regulation 2,National Law,Canadian Authority,2024-01-02,2024-01-02,Binding,https://canada.example.com,English,Canadian scope,Canadian requirements,Canadian enforcement,Canadian penalties,Canadian cross-border,Canadian data protection,Canadian incident reporting,Canadian risk management,Canadian third party,Canadian audit,Canadian certification,Canadian timeline,Canadian standards,Canadian extraterritorial,Canadian safe harbor,Canadian industry,Canadian technology"""
        
        # Create test files
        file1 = temp_storage.imports_path / "regulations_batch1.csv"
        file2 = temp_storage.imports_path / "regulations_batch2.csv"
        
        file1.write_text(csv_content)
        file2.write_text(csv_content)
        
        # Step 1: Discover files
        discovered_files = importer.discover_import_files()
        assert len(discovered_files) == 2
        
        # Step 2: Validate files
        for file_path in discovered_files:
            is_valid, errors = importer.validate_file(file_path)
            assert is_valid, f"Validation failed for {file_path}: {errors}"
        
        # Step 3: Import files (with mocked processor)
        with patch('app.services.local_regulation_importer.RegulationCSVProcessor') as mock_processor_class:
            mock_processor = Mock()
            mock_processor_class.return_value = mock_processor
            mock_processor.import_csv_file.return_value = RegulationCSVImportResult(
                batch_id="test_batch",
                total_records=2,
                successful_imports=2,
                failed_imports=0,
                updated_records=0,
                errors=[],
                warnings=[],
                processing_time=1.0
            )
            
            results = importer.import_all_files(user_id="integration_test_user")
        
        # Step 4: Verify results
        assert len(results) == 2
        assert all(r.successful_imports == 2 for r in results)
        
        # Step 5: Check file movements
        assert len(list(temp_storage.imports_path.glob("*"))) == 0  # No files left in imports
        assert len(list(temp_storage.processed_path.glob("*"))) == 2  # Files moved to processed
        assert len(list(temp_storage.backups_path.glob("*"))) == 2  # Backups created
        
        # Step 6: Get storage stats
        stats = importer.get_storage_stats()
        assert stats["imports"]["file_count"] == 0
        assert stats["processed"]["file_count"] == 2
        assert stats["backups"]["file_count"] == 2
        
        # Step 7: Cleanup old backups (should not affect recent backups)
        cleaned_count = importer.cleanup_old_backups(days_to_keep=1)
        assert cleaned_count == 0  # No old backups to clean
