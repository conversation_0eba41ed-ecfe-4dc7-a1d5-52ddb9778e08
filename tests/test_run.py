
import pytest
import sys
import os
from unittest.mock import patch, MagicMock

# Add root directory to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

import run

class TestRun:
    @patch('run.uvicorn.run')
    def test_main_function(self, mock_run):
        """Test that the main function calls uvicorn.run with the right parameters."""
        # Call the main function with test arguments
        with patch.object(sys, 'argv', ['run.py', '--host', '0.0.0.0', '--port', '8000']):
            run.main()
        
        # Check that uvicorn.run was called with correct parameters
        mock_run.assert_called_once()
        args, kwargs = mock_run.call_args
        assert 'app.main:app' in args
        assert kwargs.get('host') == '0.0.0.0'
        assert kwargs.get('port') == 8000
    
    @patch('run.uvicorn.run')
    def test_main_with_default_parameters(self, mock_run):
        """Test that the main function uses default parameters when none are provided."""
        # Call the main function with no arguments
        with patch.object(sys, 'argv', ['run.py']):
            run.main()
        
        # Check that uvicorn.run was called with default parameters
        mock_run.assert_called_once()
        args, kwargs = mock_run.call_args
        assert 'app.main:app' in args
        assert kwargs.get('host') == '127.0.0.1'  # Default host
        assert kwargs.get('port') == 8000  # Default port
    
    @patch('run.uvicorn.run')
    def test_main_with_custom_port(self, mock_run):
        """Test that the main function uses custom port when provided."""
        # Call the main function with custom port
        with patch.object(sys, 'argv', ['run.py', '--port', '9000']):
            run.main()
        
        # Check that uvicorn.run was called with custom port
        mock_run.assert_called_once()
        args, kwargs = mock_run.call_args
        assert kwargs.get('port') == 9000
    
    @patch('run.uvicorn.run')
    def test_main_with_reload_flag(self, mock_run):
        """Test that the main function enables reload when flag is provided."""
        # Call the main function with reload flag
        with patch.object(sys, 'argv', ['run.py', '--reload']):
            run.main()
        
        # Check that uvicorn.run was called with reload=True
        mock_run.assert_called_once()
        args, kwargs = mock_run.call_args
        assert kwargs.get('reload') == True
