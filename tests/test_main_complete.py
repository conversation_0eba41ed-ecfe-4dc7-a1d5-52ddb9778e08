
"""Comprehensive test cases for the main app module."""
import os
import pytest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.db import models


@pytest.fixture
def client():
    """Create a test client for the FastAPI application."""
    return TestClient(app)


def test_health_check_detailed(client):
    """Test the health check endpoint with detailed verification."""
    response = client.get("/health")
    
    # Verify basic response
    assert response.status_code == 200
    data = response.json()
    
    # Check required fields
    assert "status" in data
    assert "timestamp" in data
    assert "database" in data
    
    # Check values
    assert data["status"] == "healthy"
    assert isinstance(data["timestamp"], str)
    
    # Check test_coverage if it exists
    if "test_coverage" in data:
        assert isinstance(data["test_coverage"], dict)


def test_health_check_alternate_route(client):
    """Test the alternate health check route."""
    response = client.get("/api/v1/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"


def test_root_endpoint(client):
    """Test the root endpoint."""
    response = client.get("/")
    assert response.status_code == 200
    
    # Check that it returns HTML
    assert "text/html" in response.headers["content-type"]


def test_api_root_endpoint(client):
    """Test the API root endpoint."""
    response = client.get("/api/v1/")
    assert response.status_code == 200
    
    # Check that it returns HTML
    assert "text/html" in response.headers["content-type"]


@patch("app.main.get_translator")
def test_root_with_translation(mock_get_translator, client):
    """Test the root endpoint with translations."""
    # Setup mock translator
    mock_translator = MagicMock()
    mock_translator.return_value = "Welcome Message"
    mock_get_translator.return_value = mock_translator
    
    response = client.get("/")
    assert response.status_code == 200
    
    # Check that the translator was called
    mock_translator.assert_called_once()


def test_get_languages(client):
    """Test the languages endpoint."""
    response = client.get("/api/v1/languages")
    assert response.status_code == 200
    
    data = response.json()
    assert "current_language" in data
    assert "available_languages" in data
    assert isinstance(data["available_languages"], dict)
    assert len(data["available_languages"]) > 0


def test_get_countries(client):
    """Test the countries endpoint."""
    with patch("app.db.get_db") as mock_get_db:
        # Mock db session
        mock_session = MagicMock(spec=Session)
        mock_get_db.return_value.__enter__.return_value = mock_session
        
        # Mock query result
        mock_query = MagicMock()
        mock_session.query.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = []
        
        response = client.get("/api/v1/countries/")
        assert response.status_code == 200
        assert isinstance(response.json(), list)


def test_worldmap_endpoint(client):
    """Test the worldmap endpoint."""
    response = client.get("/api/v1/worldmap")
    assert response.status_code == 200
    
    # Check that it returns HTML
    assert "text/html" in response.headers["content-type"]
    
    # Test with custom parameters
    response = client.get("/api/v1/worldmap?lat=40.7128&lon=-74.0060&zoom=10")
    assert response.status_code == 200


def test_locale_middleware(client):
    """Test the locale middleware."""
    # Test changing language via query param
    response = client.get("/?lang=en_US")
    assert response.status_code == 200
    
    # Check that cookie was set
    assert "lang" in response.cookies
    assert response.cookies["lang"] == "en_US"
    
    # Test with an invalid language
    response = client.get("/?lang=invalid")
    assert response.status_code == 200
    assert "lang" not in response.cookies


@patch("app.db.get_db")
def test_create_item(mock_get_db, client):
    """Test creating an item."""
    # Mock db session
    mock_session = MagicMock(spec=Session)
    mock_get_db.return_value.__enter__.return_value = mock_session
    
    # Mock Item model
    mock_item = MagicMock(spec=models.Item)
    
    # Mock session methods
    mock_session.add = MagicMock()
    mock_session.commit = MagicMock()
    mock_session.refresh = MagicMock()
    
    # Test creating an item
    response = client.post(
        "/api/v1/items/",
        json={"name": "Test Item", "description": "Test Description"}
    )
    
    assert response.status_code == 200
    
    # Verify session methods were called
    mock_session.add.assert_called_once()
    mock_session.commit.assert_called_once()
    mock_session.refresh.assert_called_once()


def test_read_items(client):
    """Test reading items."""
    with patch("app.db.get_db") as mock_get_db:
        # Mock db session
        mock_session = MagicMock(spec=Session)
        mock_get_db.return_value.__enter__.return_value = mock_session
        
        # Mock query result
        mock_query = MagicMock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = []
        
        response = client.get("/api/v1/items/")
        assert response.status_code == 200
        assert isinstance(response.json(), list)


def test_read_item_not_found(client):
    """Test reading a non-existent item."""
    with patch("app.db.get_db") as mock_get_db:
        # Mock db session
        mock_session = MagicMock(spec=Session)
        mock_get_db.return_value.__enter__.return_value = mock_session
        
        # Mock query result (no item found)
        mock_query = MagicMock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        
        response = client.get("/api/v1/items/999")
        assert response.status_code == 404
        assert response.json()["detail"] == "Item not found"
