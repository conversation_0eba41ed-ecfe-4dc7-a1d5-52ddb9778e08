
import pytest
import json
import os
import tempfile
from unittest.mock import patch, mock_open, MagicMock
import sys

# Add scripts directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'scripts'))

from scripts.analyze_test_coverage import (
    get_coverage_from_html,
    analyze_coverage,
    print_report
)

class TestAnalyzeTestCoverage:
    def test_get_coverage_from_html(self):
        # Mock HTML content
        html_content = """
        <html>
        <head>
            <title>Coverage for app/main.py: 67%</title>
        </head>
        <body>
            Some coverage data
        </body>
        </html>
        """
        
        # Set up the mock open to return our HTML content
        with patch("builtins.open", mock_open(read_data=html_content)):
            module_name, coverage_pct = get_coverage_from_html("fake_path.html")
            
            # Check results
            assert module_name == "app/main.py"
            assert coverage_pct == 67
    
    def test_get_coverage_from_html_no_match(self):
        # Mock HTML content with no title match
        html_content = "<html><head><title>No match here</title></head></html>"
        
        # Set up the mock open
        with patch("builtins.open", mock_open(read_data=html_content)):
            module_name, coverage_pct = get_coverage_from_html("fake_path.html")
            
            # Check results for no match
            assert module_name is None
            assert coverage_pct == 0
    
    @patch("glob.glob")
    @patch("os.path.exists")
    def test_analyze_coverage(self, mock_exists, mock_glob):
        # Mock that coverage directory exists
        mock_exists.return_value = True
        
        # Mock the glob to return a list of HTML files
        mock_glob.return_value = ["coverage/html/app_main_py.html", "coverage/html/models_py.html"]
        
        # Mock HTML content for each file
        html_contents = {
            "coverage/html/app_main_py.html": "<title>Coverage for app/main.py: 67%</title>",
            "coverage/html/models_py.html": "<title>Coverage for models.py: 96%</title>"
        }
        
        # Create a side effect function for open to return different content based on the file path
        def mock_open_side_effect(file_path, *args, **kwargs):
            content = html_contents.get(file_path, "")
            return mock_open(read_data=content)()
        
        # Patch open and run analyze_coverage
        with patch("builtins.open", side_effect=mock_open_side_effect):
            results = analyze_coverage()
            
            # Check the results
            assert "modules" in results
            assert "summary" in results
            assert results["modules"].get("app/main.py") == 67
            assert results["modules"].get("models.py") == 96
            assert results["summary"]["total_modules"] == 2
            assert results["summary"]["high_coverage"] == 1  # models.py > 90%
            assert results["summary"]["medium_coverage"] == 1  # app/main.py > 60%
            assert results["summary"]["overall_percentage"] == 81.5  # (67+96)/2
    
    @patch("builtins.print")
    def test_print_report(self, mock_print):
        # Create a sample results dictionary
        results = {
            "modules": {
                "app/main.py": 67,
                "models.py": 96,
                "url_processor.py": 18
            },
            "summary": {
                "total_modules": 3,
                "high_coverage": 1,
                "medium_coverage": 1,
                "low_coverage": 1,
                "overall_percentage": 60.3
            }
        }
        
        # Call the print_report function
        print_report(results)
        
        # Check that print was called multiple times
        assert mock_print.call_count > 5

    @patch("json.dump")
    @patch("builtins.open", new_callable=mock_open)
    @patch("scripts.analyze_test_coverage.analyze_coverage")
    @patch("scripts.analyze_test_coverage.print_report")
    def test_main_function(self, mock_print_report, mock_analyze, mock_file_open, mock_json_dump):
        # Import main function from the module
        from scripts.analyze_test_coverage import main
        
        # Mock the analyze_coverage return value
        mock_results = {"modules": {}, "summary": {}}
        mock_analyze.return_value = mock_results
        
        # Call the main function
        main()
        
        # Verify analyze_coverage was called
        mock_analyze.assert_called_once()
        
        # Verify print_report was called with the results
        mock_print_report.assert_called_once_with(mock_results)
        
        # Verify the file was opened for writing the JSON
        mock_file_open.assert_called_once_with("coverage_report.json", "w")
        
        # Verify json.dump was called with the results
        mock_json_dump.assert_called_once()
