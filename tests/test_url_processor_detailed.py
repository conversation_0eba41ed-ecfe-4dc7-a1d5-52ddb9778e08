
import pytest
from app.utils.url_processor import (
    extract_domain,
    normalize_url,
    is_valid_url,
    get_url_content,
    parse_url
)
from unittest.mock import patch, MagicMock

class TestUrlProcessor:
    def test_extract_domain(self):
        # Test various URL formats
        assert extract_domain("https://www.example.com/path") == "example.com"
        assert extract_domain("http://subdomain.example.co.uk/path?query=1") == "example.co.uk"
        assert extract_domain("https://example.org") == "example.org"
        assert extract_domain("invalid-url") is None
    
    def test_normalize_url(self):
        # Test URL normalization
        assert normalize_url("www.EXAMPLE.com") == "http://www.example.com"
        assert normalize_url("example.com/") == "http://example.com"
        assert normalize_url("https://example.com/path/../page") == "https://example.com/page"
        assert normalize_url("https://example.com?a=1&b=2") == "https://example.com?a=1&b=2"
    
    def test_is_valid_url(self):
        # Test URL validation
        assert is_valid_url("https://www.example.com") is True
        assert is_valid_url("http://example.com/path") is True
        assert is_valid_url("ftp://example.com") is False
        assert is_valid_url("not-a-url") is False
        assert is_valid_url("") is False
        assert is_valid_url(None) is False
    
    @patch('app.utils.url_processor.requests.get')
    def test_get_url_content_success(self, mock_get):
        # Setup mock response
        mock_response = MagicMock()
        mock_response.text = "Sample content"
        mock_response.status_code = 200
        mock_get.return_value = mock_response
        
        # Test successful content retrieval
        content = get_url_content("https://example.com")
        assert content == "Sample content"
        mock_get.assert_called_once_with("https://example.com", timeout=10)
    
    @patch('app.utils.url_processor.requests.get')
    def test_get_url_content_error(self, mock_get):
        # Setup mock to raise exception
        mock_get.side_effect = Exception("Connection error")
        
        # Test error handling
        content = get_url_content("https://example.com")
        assert content is None
        mock_get.assert_called_once()
    
    @patch('app.utils.url_processor.BeautifulSoup')
    @patch('app.utils.url_processor.get_url_content')
    def test_parse_url(self, mock_get_content, mock_bs):
        # Setup mocks
        mock_get_content.return_value = "<html><body><h1>Title</h1><p>Content</p></body></html>"
        mock_soup = MagicMock()
        mock_soup.get_text.return_value = "Title Content"
        mock_bs.return_value = mock_soup
        
        # Test URL parsing
        text = parse_url("https://example.com")
        assert text == "Title Content"
        mock_get_content.assert_called_once_with("https://example.com")
