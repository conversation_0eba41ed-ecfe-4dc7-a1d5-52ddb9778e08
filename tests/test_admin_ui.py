"""Tests for the Admin UI functionality."""
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_admin_ui_mount_works(client):
    """Test that the admin UI mount works."""
    response = client.get("/ui/manage")
    assert response.status_code == 200  # Should redirect successfully
    
    # Get the redirected URL
    admin_response = client.get("/ui/manage/admin")
    assert admin_response.status_code == 200
    assert "Regulations Admin" in admin_response.text


def test_admin_ui_contains_bootstrap_elements(client):
    """Test that the admin UI contains Bootstrap elements."""
    response = client.get("/ui/manage/admin")
    assert response.status_code == 200
    assert "bootstrap" in response.text
    assert "navbar" in response.text
    assert "container" in response.text


def test_admin_ui_contains_theme_toggle(client):
    """Test that the admin UI contains theme toggle elements."""
    response = client.get("/ui/manage/admin")
    assert response.status_code == 200
    assert "themeToggle" in response.text
    assert "localStorage.getItem('theme')" in response.text


def test_admin_ui_has_roboto_font(client):
    """Test that the admin UI has Roboto font."""
    response = client.get("/ui/manage/admin")
    assert response.status_code == 200
    assert "Roboto" in response.text


def test_admin_ui_has_bootstrap_icons(client):
    """Test that the admin UI has Bootstrap Icons."""
    response = client.get("/ui/manage/admin")
    assert response.status_code == 200
    assert "bootstrap-icons" in response.text
    assert "bi bi-" in response.text


def test_admin_ui_has_home_link(client):
    """Test that the admin UI has a link back to the home page."""
    response = client.get("/ui/manage/admin")
    assert response.status_code == 200
    assert "Dashboard" in response.text
    assert "href=\"/\"" in response.text

def test_admin_link_in_dashboard():
    """Test that the dashboard contains a link to the admin interface."""
    response = client.get("/")
    assert response.status_code == 200

    # Check for admin link
    assert 'href="/ui/manage"' in response.text
    assert 'admin_panel_settings' in response.text

def test_admin_route_registered():
    """Test that the admin route is registered in the app."""
    # This test verifies the mount exists - the actual WSGIMiddleware response
    # would require more complex testing that's out of scope for this test

    # Check app.routes for the admin mount
    admin_mount = next((route for route in app.routes if getattr(route, "path", "") == "/ui/manage"), None)
    assert admin_mount is not None

def test_admin_ui_theme_compatibility():
    """
    Test the admin UI theme compatibility.

    This is a placeholder test as we can't directly test the WSGI app in this context.
    In a real scenario, we would use Selenium or similar to verify the actual UI.
    """
    # Placeholder test - would be implemented with browser automation in a real scenario
    pass