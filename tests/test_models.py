"""
Tests for database models.
"""
import pytest
from datetime import datetime, date

from app.db.models import (
    Regulation, RegulationTag, RegulationCategory, Industry,
    ComplianceRequirement, RegulationDocument
)

def test_regulation_model(db_session):
    """Test the Regulation model."""
    # Create a category
    category = RegulationCategory(
        name="Test Category",
        description="Test category description",
        icon="bank"
    )
    db_session.add(category)
    db_session.flush()
    
    # Create a regulation
    regulation = Regulation(
        title="Test Regulation",
        description="Test regulation description",
        status="Active",
        reference_number="REG-001",
        category_id=category.id
    )
    db_session.add(regulation)
    db_session.commit()
    
    # Retrieve the regulation from the database
    db_regulation = db_session.query(Regulation).filter(Regulation.id == regulation.id).first()
    
    # Check the regulation attributes
    assert db_regulation.title == "Test Regulation"
    assert db_regulation.description == "Test regulation description"
    assert db_regulation.status == "Active"
    assert db_regulation.reference_number == "REG-001"
    assert db_regulation.category_id == category.id
    
    # Check the relationship with category
    assert db_regulation.category.name == "Test Category"
    
    # Check the created_at and changed_on attributes
    assert isinstance(db_regulation.created_at, datetime)
    assert isinstance(db_regulation.changed_on, datetime)

def test_regulation_tag_model(db_session):
    """Test the RegulationTag model."""
    # Create a tag
    tag = RegulationTag(
        name="Test Tag",
        description="Test tag description",
        color="#1890ff"
    )
    db_session.add(tag)
    db_session.commit()
    
    # Retrieve the tag from the database
    db_tag = db_session.query(RegulationTag).filter(RegulationTag.id == tag.id).first()
    
    # Check the tag attributes
    assert db_tag.name == "Test Tag"
    assert db_tag.description == "Test tag description"
    assert db_tag.color == "#1890ff"
    
    # Check the created_at and changed_on attributes
    assert isinstance(db_tag.created_at, datetime)
    assert isinstance(db_tag.changed_on, datetime)

def test_regulation_category_model(db_session):
    """Test the RegulationCategory model."""
    # Create a category
    category = RegulationCategory(
        name="Test Category",
        description="Test category description",
        icon="bank"
    )
    db_session.add(category)
    db_session.commit()
    
    # Retrieve the category from the database
    db_category = db_session.query(RegulationCategory).filter(RegulationCategory.id == category.id).first()
    
    # Check the category attributes
    assert db_category.name == "Test Category"
    assert db_category.description == "Test category description"
    assert db_category.icon == "bank"
    
    # Check the created_at and changed_on attributes
    assert isinstance(db_category.created_at, datetime)
    assert isinstance(db_category.changed_on, datetime)

def test_industry_model(db_session):
    """Test the Industry model."""
    # Create an industry
    industry = Industry(
        name="Test Industry",
        description="Test industry description",
        sector="Financial Services"
    )
    db_session.add(industry)
    db_session.commit()
    
    # Retrieve the industry from the database
    db_industry = db_session.query(Industry).filter(Industry.id == industry.id).first()
    
    # Check the industry attributes
    assert db_industry.name == "Test Industry"
    assert db_industry.description == "Test industry description"
    assert db_industry.sector == "Financial Services"
    
    # Check the created_at and changed_on attributes
    assert isinstance(db_industry.created_at, datetime)
    assert isinstance(db_industry.changed_on, datetime)

def test_compliance_requirement_model(db_session):
    """Test the ComplianceRequirement model."""
    # Create a category
    category = RegulationCategory(
        name="Test Category",
        description="Test category description",
        icon="bank"
    )
    db_session.add(category)
    db_session.flush()
    
    # Create a regulation
    regulation = Regulation(
        title="Test Regulation",
        description="Test regulation description",
        status="Active",
        reference_number="REG-001",
        category_id=category.id
    )
    db_session.add(regulation)
    db_session.flush()
    
    # Create a compliance requirement
    requirement = ComplianceRequirement(
        text="Test requirement",
        section="Section 1",
        priority="High",
        status="In Progress",
        regulation_id=regulation.id,
        due_date=date.today()
    )
    db_session.add(requirement)
    db_session.commit()
    
    # Retrieve the requirement from the database
    db_requirement = db_session.query(ComplianceRequirement).filter(ComplianceRequirement.id == requirement.id).first()
    
    # Check the requirement attributes
    assert db_requirement.text == "Test requirement"
    assert db_requirement.section == "Section 1"
    assert db_requirement.priority == "High"
    assert db_requirement.status == "In Progress"
    assert db_requirement.regulation_id == regulation.id
    assert db_requirement.due_date == date.today()
    
    # Check the relationship with regulation
    assert db_requirement.regulation.title == "Test Regulation"
    
    # Check the created_at and changed_on attributes
    assert isinstance(db_requirement.created_at, datetime)
    assert isinstance(db_requirement.changed_on, datetime)

def test_regulation_document_model(db_session):
    """Test the RegulationDocument model."""
    # Create a category
    category = RegulationCategory(
        name="Test Category",
        description="Test category description",
        icon="bank"
    )
    db_session.add(category)
    db_session.flush()
    
    # Create a regulation
    regulation = Regulation(
        title="Test Regulation",
        description="Test regulation description",
        status="Active",
        reference_number="REG-001",
        category_id=category.id
    )
    db_session.add(regulation)
    db_session.flush()
    
    # Create a regulation document
    document = RegulationDocument(
        name="Test Document",
        description="Test document description",
        document_type="PDF",
        file_url="https://example.com/test.pdf",
        regulation_id=regulation.id
    )
    db_session.add(document)
    db_session.commit()
    
    # Retrieve the document from the database
    db_document = db_session.query(RegulationDocument).filter(RegulationDocument.id == document.id).first()
    
    # Check the document attributes
    assert db_document.name == "Test Document"
    assert db_document.description == "Test document description"
    assert db_document.document_type == "PDF"
    assert db_document.file_url == "https://example.com/test.pdf"
    assert db_document.regulation_id == regulation.id
    
    # Check the relationship with regulation
    assert db_document.regulation.title == "Test Regulation"
    
    # Check the created_at and changed_on attributes
    assert isinstance(db_document.created_at, datetime)
    assert isinstance(db_document.changed_on, datetime)

def test_regulation_relationships(db_session):
    """Test the relationships between regulations and other entities."""
    # Create a category
    category = RegulationCategory(
        name="Test Category",
        description="Test category description",
        icon="bank"
    )
    db_session.add(category)
    db_session.flush()
    
    # Create a tag
    tag = RegulationTag(
        name="Test Tag",
        description="Test tag description",
        color="#1890ff"
    )
    db_session.add(tag)
    db_session.flush()
    
    # Create an industry
    industry = Industry(
        name="Test Industry",
        description="Test industry description",
        sector="Financial Services"
    )
    db_session.add(industry)
    db_session.flush()
    
    # Create a regulation
    regulation = Regulation(
        title="Test Regulation",
        description="Test regulation description",
        status="Active",
        reference_number="REG-001",
        category_id=category.id
    )
    
    # Add tag and industry to the regulation
    regulation.tags.append(tag)
    regulation.industries.append(industry)
    
    db_session.add(regulation)
    db_session.commit()
    
    # Retrieve the regulation from the database
    db_regulation = db_session.query(Regulation).filter(Regulation.id == regulation.id).first()
    
    # Check the relationships
    assert db_regulation.category.id == category.id
    assert len(db_regulation.tags) == 1
    assert db_regulation.tags[0].id == tag.id
    assert len(db_regulation.industries) == 1
    assert db_regulation.industries[0].id == industry.id
    
    # Check the reverse relationships
    assert tag.regulations[0].id == regulation.id
    assert industry.regulations[0].id == regulation.id
