const { test, expect } = require('@playwright/test');

test.describe('Country Management', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the countries page
    await page.goto('/countries');
  });

  test('should display the countries list', async ({ page }) => {
    // Check if the page title is displayed
    await expect(page.locator('h2').filter({ hasText: 'Countries' })).toBeVisible();
    
    // Check if the add country button is displayed
    await expect(page.getByRole('button', { name: 'Add Country' })).toBeVisible();
    
    // Check if the table is displayed
    await expect(page.locator('table')).toBeVisible();
  });

  test('should navigate to the add country page', async ({ page }) => {
    // Click the add country button
    await page.getByRole('button', { name: 'Add Country' }).click();
    
    // Check if we're on the add country page
    await expect(page.locator('h2').filter({ hasText: 'Add Country' })).toBeVisible();
    
    // Check if the form is displayed
    await expect(page.getByLabel('Country Name')).toBeVisible();
    await expect(page.getByLabel('Country Code')).toBeVisible();
  });

  test('should create a new country', async ({ page }) => {
    // Navigate to the create country page
    await page.getByRole('button', { name: 'Add Country' }).click();
    
    // Generate a unique country name and code to avoid conflicts
    const timestamp = Date.now();
    const countryName = `Test Country ${timestamp}`;
    const countryCode = `T${timestamp.toString().slice(-2)}`;
    
    // Fill out the form
    await page.getByLabel('Country Name').fill(countryName);
    await page.getByLabel('Country Code').fill(countryCode);
    await page.getByLabel('Region').click();
    await page.getByText('Europe').click();
    await page.getByLabel('Subregion').click();
    await page.getByText('Western Europe').click();
    await page.getByLabel('Flag Emoji').fill('🏳️');
    
    // Save the country
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if success message is displayed
    await expect(page.getByText('Country created successfully')).toBeVisible();
    
    // Check if we're redirected to the edit page
    await expect(page.locator('h2').filter({ hasText: 'Edit Country' })).toBeVisible();
  });

  test('should navigate to the edit country page', async ({ page }) => {
    // Assuming there's at least one country in the table
    await page.locator('table').getByRole('button', { name: 'Edit' }).first().click();
    
    // Check if we're on the edit country page
    await expect(page.locator('h2').filter({ hasText: 'Edit Country' })).toBeVisible();
    
    // Check if the form is pre-filled
    await expect(page.getByLabel('Country Name')).toHaveValue();
    await expect(page.getByLabel('Country Code')).toHaveValue();
  });

  test('should update a country', async ({ page }) => {
    // Navigate to the edit page of the first country
    await page.locator('table').getByRole('button', { name: 'Edit' }).first().click();
    
    // Get the current country name
    const currentName = await page.getByLabel('Country Name').inputValue();
    
    // Update the country name
    const updatedName = `${currentName} (Updated)`;
    await page.getByLabel('Country Name').fill(updatedName);
    
    // Save the country
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if success message is displayed
    await expect(page.getByText('Country updated successfully')).toBeVisible();
  });

  test('should navigate to the country detail page', async ({ page }) => {
    // Click on the first country name in the table
    await page.locator('table tbody tr').first().locator('td').nth(1).click();
    
    // Check if we're on the country detail page
    await expect(page.locator('h2')).toBeVisible();
    
    // Check if the details are displayed
    await expect(page.getByText('ID')).toBeVisible();
    await expect(page.getByText('Code')).toBeVisible();
    await expect(page.getByText('Region')).toBeVisible();
  });

  test('should show regulators tab on country detail page', async ({ page }) => {
    // Click on the first country name in the table
    await page.locator('table tbody tr').first().locator('td').nth(1).click();
    
    // Click on the regulators tab
    await page.getByText('Regulators').click();
    
    // Check if the regulators table is displayed
    await expect(page.locator('div[role="tabpanel"]').locator('table')).toBeVisible();
  });

  test('should search for countries', async ({ page }) => {
    // Enter search term
    await page.getByPlaceholder('Search countries...').fill('United');
    await page.getByRole('button', { name: 'Search' }).click();
    
    // Check if search results are displayed
    await expect(page.locator('table')).toBeVisible();
  });

  test('should attempt to delete a country', async ({ page }) => {
    // Click the delete button for the first country
    await page.locator('table').getByRole('button', { name: 'Delete' }).first().click();
    
    // Check if confirmation dialog is displayed
    await expect(page.getByText('Are you sure you want to delete this country?')).toBeVisible();
    
    // Cancel the deletion
    await page.getByRole('button', { name: 'No' }).click();
    
    // Check that the table still contains countries
    await expect(page.locator('table tbody tr')).toBeVisible();
  });
});
