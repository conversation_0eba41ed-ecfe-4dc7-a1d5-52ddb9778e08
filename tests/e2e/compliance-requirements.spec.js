const { test, expect } = require('@playwright/test');

test.describe('Compliance Requirements', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the compliance requirements page
    await page.goto('/compliance-requirements');
  });

  test('should display the compliance requirements list', async ({ page }) => {
    // Check if the page title is displayed
    await expect(page.locator('h2').filter({ hasText: 'Compliance Requirements' })).toBeVisible();
    
    // Check if the add requirement button is displayed
    await expect(page.getByRole('button', { name: 'Add Requirement' })).toBeVisible();
    
    // Check if the table is displayed
    await expect(page.locator('table')).toBeVisible();
  });

  test('should navigate to the add requirement page', async ({ page }) => {
    // Click the add requirement button
    await page.getByRole('button', { name: 'Add Requirement' }).click();
    
    // Check if we're on the add requirement page
    await expect(page.locator('h2').filter({ hasText: 'Add Compliance Requirement' })).toBeVisible();
    
    // Check if the form is displayed
    await expect(page.getByLabel('Requirement Text')).toBeVisible();
  });

  test('should create a new compliance requirement', async ({ page }) => {
    // Navigate to the create requirement page
    await page.getByRole('button', { name: 'Add Requirement' }).click();
    
    // Generate a unique requirement text to avoid conflicts
    const timestamp = Date.now();
    const requirementText = `Test Requirement ${timestamp}`;
    
    // Fill out the form
    await page.getByLabel('Requirement Text').fill(requirementText);
    await page.getByLabel('Section/Article Reference').fill('Section 1.2');
    await page.getByLabel('Priority').click();
    await page.getByText('High').click();
    await page.getByLabel('Status').click();
    await page.getByText('In Progress').click();
    
    // Select a regulation if available
    const regulationOptions = await page.locator('div[role="option"]').count();
    if (regulationOptions > 0) {
      await page.getByLabel('Regulation').click();
      await page.locator('div[role="option"]').first().click();
    } else {
      // If no regulations available, we need to create one first
      test.skip('No regulations available to select');
    }
    
    // Set due date
    await page.getByLabel('Due Date').click();
    await page.getByText('Today').click();
    
    // Add notes
    await page.getByLabel('Notes').fill('This is a test requirement created by Playwright');
    
    // Save the requirement
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if success message is displayed
    await expect(page.getByText('Compliance requirement created successfully')).toBeVisible();
    
    // Check if we're redirected to the edit page
    await expect(page.locator('h2').filter({ hasText: 'Edit Compliance Requirement' })).toBeVisible();
  });

  test('should navigate to the edit requirement page', async ({ page }) => {
    // Assuming there's at least one requirement in the table
    await page.locator('table').getByRole('button', { name: 'Edit' }).first().click();
    
    // Check if we're on the edit requirement page
    await expect(page.locator('h2').filter({ hasText: 'Edit Compliance Requirement' })).toBeVisible();
    
    // Check if the form is pre-filled
    await expect(page.getByLabel('Requirement Text')).toHaveValue();
  });

  test('should update a compliance requirement', async ({ page }) => {
    // Navigate to the edit page of the first requirement
    await page.locator('table').getByRole('button', { name: 'Edit' }).first().click();
    
    // Get the current requirement text
    const currentText = await page.getByLabel('Requirement Text').inputValue();
    
    // Update the requirement text
    const updatedText = `${currentText} (Updated)`;
    await page.getByLabel('Requirement Text').fill(updatedText);
    
    // Update the status
    await page.getByLabel('Status').click();
    await page.getByText('Compliant').click();
    
    // Save the requirement
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if success message is displayed
    await expect(page.getByText('Compliance requirement updated successfully')).toBeVisible();
  });

  test('should navigate to the requirement detail page', async ({ page }) => {
    // Click on the first requirement text in the table
    await page.locator('table tbody tr').first().locator('td').nth(1).click();
    
    // Check if we're on the requirement detail page
    await expect(page.locator('h2').filter({ hasText: 'Compliance Requirement' })).toBeVisible();
    
    // Check if the details are displayed
    await expect(page.getByText('Requirement Details')).toBeVisible();
  });

  test('should filter requirements by priority', async ({ page }) => {
    // Assuming there's at least one priority in the dropdown
    await page.getByPlaceholder('Filter by Priority').click();
    
    // If there are options, click the first one
    const options = await page.getByRole('option').count();
    if (options > 0) {
      await page.getByRole('option').first().click();
      
      // Check if the table is updated
      await expect(page.locator('table')).toBeVisible();
    }
  });

  test('should filter requirements by status', async ({ page }) => {
    // Assuming there's at least one status in the dropdown
    await page.getByPlaceholder('Filter by Status').click();
    
    // If there are options, click the first one
    const options = await page.getByRole('option').count();
    if (options > 0) {
      await page.getByRole('option').first().click();
      
      // Check if the table is updated
      await expect(page.locator('table')).toBeVisible();
    }
  });

  test('should filter requirements by regulation', async ({ page }) => {
    // Assuming there's at least one regulation in the dropdown
    await page.getByPlaceholder('Filter by Regulation').click();
    
    // If there are options, click the first one
    const options = await page.getByRole('option').count();
    if (options > 0) {
      await page.getByRole('option').first().click();
      
      // Check if the table is updated
      await expect(page.locator('table')).toBeVisible();
    }
  });

  test('should search for requirements', async ({ page }) => {
    // Enter search term
    await page.getByPlaceholder('Search requirements...').fill('test');
    await page.getByRole('button', { name: 'Search' }).click();
    
    // Check if search results are displayed
    await expect(page.locator('table')).toBeVisible();
  });

  test('should reset filters', async ({ page }) => {
    // Enter search term
    await page.getByPlaceholder('Search requirements...').fill('test');
    await page.getByRole('button', { name: 'Search' }).click();
    
    // Reset filters
    await page.getByRole('button', { name: 'Reset Filters' }).click();
    
    // Check that the search field is cleared
    await expect(page.getByPlaceholder('Search requirements...')).toHaveValue('');
  });

  test('should attempt to delete a requirement', async ({ page }) => {
    // Click the delete button for the first requirement
    await page.locator('table').getByRole('button', { name: 'Delete' }).first().click();
    
    // Check if confirmation dialog is displayed
    await expect(page.getByText('Are you sure you want to delete this requirement?')).toBeVisible();
    
    // Cancel the deletion
    await page.getByRole('button', { name: 'No' }).click();
    
    // Check that the table still contains requirements
    await expect(page.locator('table tbody tr')).toBeVisible();
  });
});
