const { test, expect } = require('@playwright/test');

test.describe('Regulator Management', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the regulators page
    await page.goto('/regulators');
  });

  test('should display the regulators list', async ({ page }) => {
    // Check if the page title is displayed
    await expect(page.locator('h2').filter({ hasText: 'Regulators' })).toBeVisible();
    
    // Check if the add regulator button is displayed
    await expect(page.getByRole('button', { name: 'Add Regulator' })).toBeVisible();
    
    // Check if the table is displayed
    await expect(page.locator('table')).toBeVisible();
  });

  test('should navigate to the add regulator page', async ({ page }) => {
    // Click the add regulator button
    await page.getByRole('button', { name: 'Add Regulator' }).click();
    
    // Check if we're on the add regulator page
    await expect(page.locator('h2').filter({ hasText: 'Add Regulator' })).toBeVisible();
    
    // Check if the form is displayed
    await expect(page.getByLabel('Regulator Name')).toBeVisible();
    await expect(page.getByLabel('Country')).toBeVisible();
  });

  test('should create a new regulator', async ({ page }) => {
    // Navigate to the create regulator page
    await page.getByRole('button', { name: 'Add Regulator' }).click();
    
    // Generate a unique regulator name to avoid conflicts
    const timestamp = Date.now();
    const regulatorName = `Test Regulator ${timestamp}`;
    
    // Fill out the form
    await page.getByLabel('Regulator Name').fill(regulatorName);
    
    // Select a country (assuming there's at least one country in the dropdown)
    await page.getByLabel('Country').click();
    await page.getByRole('option').first().click();
    
    // Select a regulator type
    await page.getByLabel('Regulator Type').click();
    await page.getByText('Financial').click();
    
    // Fill website
    await page.getByLabel('Website URL').fill('https://www.example.com');
    
    // Fill description
    await page.getByLabel('Description').fill('This is a test regulator description');
    
    // Save the regulator
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if success message is displayed
    await expect(page.getByText('Regulator created successfully')).toBeVisible();
    
    // Check if we're redirected to the edit page
    await expect(page.locator('h2').filter({ hasText: 'Edit Regulator' })).toBeVisible();
  });

  test('should navigate to the edit regulator page', async ({ page }) => {
    // Assuming there's at least one regulator in the table
    await page.locator('table').getByRole('button', { name: 'Edit' }).first().click();
    
    // Check if we're on the edit regulator page
    await expect(page.locator('h2').filter({ hasText: 'Edit Regulator' })).toBeVisible();
    
    // Check if the form is pre-filled
    await expect(page.getByLabel('Regulator Name')).toHaveValue();
    await expect(page.getByLabel('Country')).toBeVisible();
  });

  test('should update a regulator', async ({ page }) => {
    // Navigate to the edit page of the first regulator
    await page.locator('table').getByRole('button', { name: 'Edit' }).first().click();
    
    // Get the current regulator name
    const currentName = await page.getByLabel('Regulator Name').inputValue();
    
    // Update the regulator name
    const updatedName = `${currentName} (Updated)`;
    await page.getByLabel('Regulator Name').fill(updatedName);
    
    // Save the regulator
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if success message is displayed
    await expect(page.getByText('Regulator updated successfully')).toBeVisible();
  });

  test('should navigate to the regulator detail page', async ({ page }) => {
    // Click on the first regulator name in the table
    await page.locator('table tbody tr').first().locator('td').nth(1).click();
    
    // Check if we're on the regulator detail page
    await expect(page.locator('h2')).toBeVisible();
    
    // Check if the details are displayed
    await expect(page.getByText('ID')).toBeVisible();
    await expect(page.getByText('Type')).toBeVisible();
    await expect(page.getByText('Country')).toBeVisible();
  });

  test('should show regulation URLs tab on regulator detail page', async ({ page }) => {
    // Click on the first regulator name in the table
    await page.locator('table tbody tr').first().locator('td').nth(1).click();
    
    // Click on the regulation URLs tab
    await page.getByText('Regulation URLs').click();
    
    // Check if the regulation URLs table is displayed
    await expect(page.locator('div[role="tabpanel"]').locator('table')).toBeVisible();
  });

  test('should filter regulators by country', async ({ page }) => {
    // Assuming there's at least one country in the dropdown
    await page.getByPlaceholder('Filter by Country').click();
    await page.getByRole('option').first().click();
    
    // Check if the table is updated
    await expect(page.locator('table')).toBeVisible();
  });

  test('should filter regulators by type', async ({ page }) => {
    // Assuming there's at least one type in the dropdown
    await page.getByPlaceholder('Filter by Type').click();
    
    // If there are options, click the first one
    const options = await page.getByRole('option').count();
    if (options > 0) {
      await page.getByRole('option').first().click();
      
      // Check if the table is updated
      await expect(page.locator('table')).toBeVisible();
    }
  });

  test('should search for regulators', async ({ page }) => {
    // Enter search term
    await page.getByPlaceholder('Search regulators...').fill('Test');
    await page.getByRole('button', { name: 'Search' }).click();
    
    // Check if search results are displayed
    await expect(page.locator('table')).toBeVisible();
  });

  test('should reset filters', async ({ page }) => {
    // Enter search term
    await page.getByPlaceholder('Search regulators...').fill('Test');
    await page.getByRole('button', { name: 'Search' }).click();
    
    // Reset filters
    await page.getByRole('button', { name: 'Reset Filters' }).click();
    
    // Check that the search field is cleared
    await expect(page.getByPlaceholder('Search regulators...')).toHaveValue('');
  });

  test('should attempt to delete a regulator', async ({ page }) => {
    // Click the delete button for the first regulator
    await page.locator('table').getByRole('button', { name: 'Delete' }).first().click();
    
    // Check if confirmation dialog is displayed
    await expect(page.getByText('Are you sure you want to delete this regulator?')).toBeVisible();
    
    // Cancel the deletion
    await page.getByRole('button', { name: 'No' }).click();
    
    // Check that the table still contains regulators
    await expect(page.locator('table tbody tr')).toBeVisible();
  });
});
