[tool:pytest]
# Pytest configuration for E2E tests

# Test discovery
testpaths = tests/e2e
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Markers
markers =
    e2e: End-to-end tests
    slow: Slow running tests
    accessibility: Accessibility tests
    performance: Performance tests
    smoke: Smoke tests for basic functionality
    integration: Integration tests
    ui: User interface tests
    api: API tests
    workflow: User workflow tests

# Minimum version
minversion = 6.0

# Add options
addopts =
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --maxfail=5
    --durations=10
    --browser chromium
    --headed false

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:playwright.*

# Test timeout (in seconds)
timeout = 300

# Parallel execution
# Uncomment to enable parallel test execution
# -n auto

# Coverage (if needed)
# --cov=app
# --cov-report=html
# --cov-report=term-missing
