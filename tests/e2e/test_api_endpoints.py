"""
E2E tests for RegulationGuru API endpoints
"""
import pytest
import json
from playwright.async_api import expect


@pytest.mark.e2e
class TestAPIEndpoints:
    """Test suite for API endpoint functionality"""
    
    async def test_health_endpoint(self, page, base_url, page_helpers):
        """Test health check endpoint"""
        await page.goto(f"{base_url}/api/v1/health")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Check that health endpoint returns JSON
        content = await page.content()
        assert "application/json" in await page.evaluate("document.contentType") or \
               '"status"' in content, "Health endpoint should return JSON"
    
    async def test_countries_endpoint(self, page, base_url, page_helpers):
        """Test countries API endpoint"""
        await page.goto(f"{base_url}/api/v1/countries")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Check response status
        response = await page.evaluate("fetch('/api/v1/countries').then(r => r.status)")
        assert response == 200, "Countries endpoint should return 200"
        
        # Check JSON structure
        content = await page.content()
        if '"countries"' in content or '[' in content:
            # Valid JSON response
            pass
        else:
            pytest.skip("Countries endpoint may not have data yet")
    
    async def test_regulators_endpoint(self, page, base_url, page_helpers):
        """Test regulators API endpoint"""
        await page.goto(f"{base_url}/api/v1/regulators")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Check that page loads without error
        title = await page.title()
        assert "error" not in title.lower(), "Regulators page should not show error"
        
        # Check for JSON or HTML content
        content = await page.content()
        assert len(content) > 100, "Regulators endpoint should return substantial content"
    
    async def test_regulation_urls_endpoint(self, page, base_url, page_helpers):
        """Test regulation URLs endpoint"""
        await page.goto(f"{base_url}/api/v1/regulation-urls")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Check that endpoint is accessible
        response_status = await page.evaluate("""
            fetch('/api/v1/regulation-urls')
                .then(r => r.status)
                .catch(() => 404)
        """)
        
        assert response_status in [200, 404], "Regulation URLs endpoint should be accessible"
    
    async def test_trends_endpoint(self, page, base_url, page_helpers):
        """Test trends API endpoint"""
        await page.goto(f"{base_url}/api/v1/trends")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Check that trends endpoint responds
        content = await page.content()
        assert "Internal Server Error" not in content, "Trends endpoint should not error"
    
    async def test_worldmap_endpoint(self, page, base_url, page_helpers):
        """Test world map endpoint"""
        await page.goto(f"{base_url}/api/v1/worldmap")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Check that worldmap loads
        title = await page.title()
        assert "500" not in title, "Worldmap should not return server error"
    
    async def test_api_documentation(self, page, base_url, page_helpers):
        """Test API documentation accessibility"""
        await page.goto(f"{base_url}/api/v1/docs")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Check that Swagger UI loads
        swagger_ui = page.locator(".swagger-ui")
        if await swagger_ui.count() > 0:
            await expect(swagger_ui).to_be_visible()
        
        # Check for OpenAPI content
        content = await page.content()
        assert any(keyword in content.lower() for keyword in [
            "swagger", "openapi", "api", "documentation", "endpoints"
        ]), "API docs should contain relevant content"
    
    async def test_redoc_documentation(self, page, base_url, page_helpers):
        """Test ReDoc documentation"""
        await page.goto(f"{base_url}/api/v1/redoc")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Check that ReDoc loads
        redoc = page.locator("redoc")
        if await redoc.count() > 0:
            await expect(redoc).to_be_visible()
        
        # Check for documentation content
        content = await page.content()
        assert "redoc" in content.lower() or "api" in content.lower(), \
            "ReDoc should load API documentation"


@pytest.mark.e2e
class TestComplianceFeatures:
    """Test suite for compliance-specific features"""
    
    async def test_compliance_calendar_page(self, page, base_url, page_helpers):
        """Test compliance calendar page"""
        await page.goto(f"{base_url}/compliance-calendar")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Check that calendar page loads
        title = await page.title()
        assert "calendar" in title.lower() or "compliance" in title.lower(), \
            "Calendar page should have relevant title"
        
        # Check for calendar elements
        content = await page.content()
        calendar_indicators = ["calendar", "event", "deadline", "compliance"]
        assert any(indicator in content.lower() for indicator in calendar_indicators), \
            "Calendar page should contain calendar-related content"
    
    async def test_compliance_analytics_page(self, page, base_url, page_helpers):
        """Test compliance analytics page"""
        await page.goto(f"{base_url}/compliance-analytics")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Check that analytics page loads
        title = await page.title()
        assert "analytics" in title.lower() or "compliance" in title.lower(), \
            "Analytics page should have relevant title"
        
        # Check for analytics content
        content = await page.content()
        analytics_indicators = ["analytics", "chart", "dashboard", "metrics"]
        assert any(indicator in content.lower() for indicator in analytics_indicators), \
            "Analytics page should contain analytics-related content"
    
    async def test_document_summarizer_page(self, page, base_url, page_helpers):
        """Test AI document summarizer page"""
        await page.goto(f"{base_url}/document-summarizer")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Check that document summarizer loads
        title = await page.title()
        assert any(keyword in title.lower() for keyword in [
            "document", "summarizer", "ai", "analysis"
        ]), "Document summarizer should have relevant title"
        
        # Check for AI/document features
        content = await page.content()
        ai_indicators = ["document", "ai", "analysis", "summarize", "upload"]
        assert any(indicator in content.lower() for indicator in ai_indicators), \
            "Document summarizer should contain AI-related content"
    
    async def test_ai_regulatory_search_page(self, page, base_url, page_helpers):
        """Test AI regulatory search page"""
        await page.goto(f"{base_url}/ai-regulatory-search")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Check that AI search page loads
        title = await page.title()
        assert any(keyword in title.lower() for keyword in [
            "search", "regulatory", "ai"
        ]), "AI search page should have relevant title"
        
        # Check for search functionality
        content = await page.content()
        search_indicators = ["search", "regulatory", "ai", "query", "find"]
        assert any(indicator in content.lower() for indicator in search_indicators), \
            "AI search page should contain search-related content"


@pytest.mark.e2e
class TestAdminInterface:
    """Test suite for admin interface"""
    
    async def test_admin_panel_access(self, page, base_url, page_helpers):
        """Test admin panel accessibility"""
        await page.goto(f"{base_url}/ui/manage")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Check that admin panel loads (may require authentication)
        title = await page.title()
        content = await page.content()
        
        # Should either show admin interface or login page
        assert any(keyword in content.lower() for keyword in [
            "admin", "manage", "login", "authentication", "dashboard"
        ]), "Admin panel should be accessible or show login"
    
    async def test_regulation_urls_list_page(self, page, base_url, page_helpers):
        """Test regulation URLs list page"""
        await page.goto(f"{base_url}/regulation-urls")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Check that regulation URLs page loads
        title = await page.title()
        content = await page.content()
        
        assert any(keyword in content.lower() for keyword in [
            "regulation", "url", "list", "regulations"
        ]), "Regulation URLs page should contain relevant content"


@pytest.mark.e2e
class TestErrorHandling:
    """Test suite for error handling"""
    
    async def test_404_page(self, page, base_url, page_helpers):
        """Test 404 error page"""
        await page.goto(f"{base_url}/nonexistent-page")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Check for 404 handling
        title = await page.title()
        content = await page.content()
        
        # Should show some kind of error or redirect
        assert any(indicator in content.lower() for indicator in [
            "404", "not found", "error", "page not found"
        ]) or "regulationguru" in title.lower(), \
            "Should handle 404 errors gracefully"
    
    async def test_api_error_handling(self, page, base_url, page_helpers):
        """Test API error handling"""
        # Test invalid API endpoint
        response = await page.evaluate("""
            fetch('/api/v1/invalid-endpoint')
                .then(r => ({status: r.status, ok: r.ok}))
                .catch(e => ({error: e.message}))
        """)
        
        # Should return proper HTTP status
        assert "status" in response or "error" in response, \
            "API should handle invalid endpoints properly"


@pytest.mark.e2e
class TestDataIntegrity:
    """Test suite for data integrity and consistency"""
    
    async def test_api_response_consistency(self, page, base_url, page_helpers):
        """Test that API responses are consistent"""
        await page.goto(f"{base_url}/")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Test multiple API calls for consistency
        endpoints = [
            "/api/v1/health",
            "/api/v1/countries",
            "/api/v1/regulators"
        ]
        
        for endpoint in endpoints:
            response = await page.evaluate(f"""
                fetch('{endpoint}')
                    .then(r => r.status)
                    .catch(() => 500)
            """)
            
            # Should return consistent status codes
            assert response in [200, 404, 422, 500], \
                f"Endpoint {endpoint} should return valid HTTP status"
    
    async def test_cross_page_navigation(self, page, base_url, page_helpers):
        """Test navigation between different pages"""
        # Start at dashboard
        await page.goto(f"{base_url}/")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Navigate to different pages
        navigation_tests = [
            ("/api/v1/docs", "API Documentation"),
            ("/compliance-calendar", "Calendar"),
            ("/", "Dashboard")
        ]
        
        for url, expected_content in navigation_tests:
            await page.goto(f"{base_url}{url}")
            await page_helpers.wait_for_load_state("networkidle")
            
            # Check that navigation was successful
            current_url = page.url
            assert url in current_url, f"Should navigate to {url}"
            
            # Check that page loaded properly
            content = await page.content()
            assert len(content) > 500, f"Page {url} should have substantial content"
