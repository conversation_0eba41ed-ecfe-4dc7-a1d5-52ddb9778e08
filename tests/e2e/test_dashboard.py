"""
E2E tests for the RegulationGuru dashboard
"""
import pytest
from playwright.async_api import expect


@pytest.mark.e2e
class TestDashboard:
    """Test suite for dashboard functionality"""
    
    async def test_dashboard_loads_successfully(self, page, base_url, page_helpers):
        """Test that the dashboard loads without errors"""
        # Navigate to dashboard
        await page.goto(f"{base_url}/")
        
        # Wait for page to load
        await page_helpers.wait_for_load_state("networkidle")
        
        # Check that the page title is correct
        await expect(page).to_have_title("RegulationGuru Dashboard")
        
        # Check that main elements are present
        await expect(page.locator("nav")).to_be_visible()
        await expect(page.locator("main")).to_be_visible()
        await expect(page.locator("footer")).to_be_visible()
        
        # Check that the welcome banner is present
        await expect(page.locator(".welcome-banner")).to_be_visible()
        await expect(page.locator("h1")).to_contain_text("Welcome to RegulationGuru")
    
    async def test_enhanced_dashboard_loads(self, page, base_url, page_helpers):
        """Test that the enhanced dashboard loads with modern UI"""
        # Navigate to enhanced dashboard
        await page.goto(f"{base_url}/enhanced-dashboard")
        
        # Wait for page to load
        await page_helpers.wait_for_load_state("networkidle")
        
        # Check enhanced UI elements
        await expect(page.locator(".hero-section")).to_be_visible()
        await expect(page.locator(".stats-grid")).to_be_visible()
        await expect(page.locator(".feature-grid")).to_be_visible()
        await expect(page.locator(".quick-actions")).to_be_visible()
        
        # Check statistics cards
        stat_cards = page.locator(".stat-card")
        await expect(stat_cards).to_have_count(4)
        
        # Check feature cards
        feature_cards = page.locator(".feature-card")
        await expect(feature_cards).to_have_count(6)
    
    async def test_navigation_menu(self, page, base_url, page_helpers):
        """Test navigation menu functionality"""
        await page.goto(f"{base_url}/")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Test navigation links
        nav_links = [
            ("/", "Dashboard"),
            ("/compliance-calendar", "Calendar"),
            ("/api/v1/docs", "API"),
            ("/ui/manage", "Admin")
        ]
        
        for href, text in nav_links:
            link = page.locator(f'nav a[href="{href}"]')
            await expect(link).to_be_visible()
            await expect(link).to_contain_text(text)
    
    async def test_theme_toggle(self, page, base_url, page_helpers):
        """Test dark/light theme toggle functionality"""
        await page.goto(f"{base_url}/enhanced-dashboard")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Check initial theme (should be light)
        body = page.locator("body")
        html = page.locator("html")
        
        # Click theme toggle
        theme_toggle = page.locator("#themeToggle")
        await expect(theme_toggle).to_be_visible()
        await theme_toggle.click()
        
        # Wait for theme change
        await page.wait_for_timeout(500)
        
        # Check that dark mode is applied
        theme_attr = await html.get_attribute("data-theme")
        assert theme_attr == "dark", "Dark theme should be applied"
        
        # Click again to toggle back
        await theme_toggle.click()
        await page.wait_for_timeout(500)
        
        # Check that light mode is restored
        theme_attr = await html.get_attribute("data-theme")
        assert theme_attr == "light", "Light theme should be restored"
    
    async def test_language_selector(self, page, base_url, page_helpers):
        """Test language selector functionality"""
        await page.goto(f"{base_url}/")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Find language selector button
        lang_button = page.locator("#nav-language-button")
        await expect(lang_button).to_be_visible()
        
        # Click to open dropdown
        await lang_button.click()
        
        # Check that dropdown is visible
        dropdown = page.locator(".language-dropdown")
        await expect(dropdown).to_be_visible()
        
        # Check language options
        lang_options = dropdown.locator(".language-option")
        await expect(lang_options).to_have_count_greater_than(2)
        
        # Test selecting a language
        spanish_option = dropdown.locator('a[href*="lang=es"]')
        if await spanish_option.count() > 0:
            await spanish_option.click()
            await page_helpers.wait_for_load_state("networkidle")
            
            # Check that URL contains language parameter
            current_url = page.url
            assert "lang=es" in current_url, "URL should contain Spanish language parameter"
    
    async def test_api_endpoints_grid(self, page, base_url, page_helpers):
        """Test API endpoints grid functionality"""
        await page.goto(f"{base_url}/")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Check API endpoints section
        api_section = page.locator(".api-endpoints")
        await expect(api_section).to_be_visible()
        
        # Check API cards
        api_cards = page.locator(".api-card")
        await expect(api_cards).to_have_count_greater_than(5)
        
        # Test clicking on an API endpoint
        countries_link = page.locator('a[href="/api/v1/countries"]')
        await expect(countries_link).to_be_visible()
        
        # Click and verify navigation
        await countries_link.click()
        await page_helpers.wait_for_load_state("networkidle")
        
        # Should navigate to countries API endpoint
        assert "/api/v1/countries" in page.url
    
    async def test_quick_actions(self, page, base_url, page_helpers):
        """Test quick actions functionality on enhanced dashboard"""
        await page.goto(f"{base_url}/enhanced-dashboard")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Check quick actions section
        quick_actions = page.locator(".quick-actions")
        await expect(quick_actions).to_be_visible()
        
        # Check action buttons
        action_buttons = page.locator(".action-button")
        await expect(action_buttons).to_have_count(6)
        
        # Test health check action
        health_button = page.locator('a[href="/api/v1/health"]')
        await expect(health_button).to_be_visible()
        await health_button.click()
        await page_helpers.wait_for_load_state("networkidle")
        
        # Should show health status
        assert "/api/v1/health" in page.url
    
    async def test_modal_functionality(self, page, base_url, page_helpers):
        """Test modal functionality on enhanced dashboard"""
        await page.goto(f"{base_url}/enhanced-dashboard")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Find contact modal trigger
        contact_trigger = page.locator('[data-modal-target="contact-modal"]')
        if await contact_trigger.count() > 0:
            await contact_trigger.click()
            
            # Check that modal is visible
            modal = page.locator("#contact-modal")
            await expect(modal).to_be_visible()
            await expect(modal).to_have_class("show")
            
            # Check modal content
            await expect(modal.locator("#contact-title")).to_contain_text("Contact Us")
            
            # Test form fields
            name_field = modal.locator("#contact-name")
            email_field = modal.locator("#contact-email")
            message_field = modal.locator("#contact-message")
            
            await expect(name_field).to_be_visible()
            await expect(email_field).to_be_visible()
            await expect(message_field).to_be_visible()
            
            # Test closing modal
            close_button = modal.locator('[data-modal-close]')
            await close_button.click()
            
            # Modal should be hidden
            await expect(modal).not_to_have_class("show")
    
    async def test_responsive_design(self, page, base_url, page_helpers):
        """Test responsive design at different screen sizes"""
        await page.goto(f"{base_url}/enhanced-dashboard")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Test mobile viewport
        await page.set_viewport_size({"width": 375, "height": 667})
        await page.wait_for_timeout(500)
        
        # Navigation should still be accessible
        nav = page.locator("nav")
        await expect(nav).to_be_visible()
        
        # Stats grid should stack vertically
        stats_grid = page.locator(".stats-grid")
        await expect(stats_grid).to_be_visible()
        
        # Test tablet viewport
        await page.set_viewport_size({"width": 768, "height": 1024})
        await page.wait_for_timeout(500)
        
        # All sections should still be visible
        await expect(page.locator(".hero-section")).to_be_visible()
        await expect(page.locator(".feature-grid")).to_be_visible()
        
        # Test desktop viewport
        await page.set_viewport_size({"width": 1280, "height": 720})
        await page.wait_for_timeout(500)
        
        # All features should be fully visible
        await expect(page.locator(".quick-actions")).to_be_visible()
    
    @pytest.mark.accessibility
    async def test_accessibility_features(self, page, base_url, page_helpers):
        """Test accessibility features"""
        await page.goto(f"{base_url}/enhanced-dashboard")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Run accessibility checks
        await page_helpers.check_accessibility()
        
        # Test keyboard navigation
        await page.keyboard.press("Tab")
        
        # Check focus indicators
        focused_element = await page.evaluate("document.activeElement.tagName")
        assert focused_element in ["A", "BUTTON"], "Focus should be on interactive element"
        
        # Test skip link
        skip_link = page.locator('a[href="#main-content"]')
        await skip_link.focus()
        await expect(skip_link).to_be_visible()
        
        # Test ARIA labels
        theme_toggle = page.locator("#themeToggle")
        aria_label = await theme_toggle.get_attribute("aria-label")
        assert aria_label is not None, "Theme toggle should have aria-label"
    
    @pytest.mark.performance
    async def test_page_performance(self, page, base_url, performance_thresholds):
        """Test page performance metrics"""
        # Start performance monitoring
        await page.goto(f"{base_url}/enhanced-dashboard")
        
        # Wait for page to fully load
        await page.wait_for_load_state("networkidle")
        
        # Get performance metrics
        metrics = await page.evaluate("""
            () => {
                const navigation = performance.getEntriesByType('navigation')[0];
                return {
                    loadTime: navigation.loadEventEnd - navigation.fetchStart,
                    domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
                    firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
                    firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0
                };
            }
        """)
        
        # Assert performance thresholds
        assert metrics["loadTime"] < performance_thresholds["page_load_time"], \
            f"Page load time {metrics['loadTime']}ms exceeds threshold {performance_thresholds['page_load_time']}ms"
        
        if metrics["firstContentfulPaint"] > 0:
            assert metrics["firstContentfulPaint"] < performance_thresholds["first_contentful_paint"], \
                f"First contentful paint {metrics['firstContentfulPaint']}ms exceeds threshold"
