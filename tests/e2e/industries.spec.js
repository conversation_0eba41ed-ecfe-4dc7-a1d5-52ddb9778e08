const { test, expect } = require('@playwright/test');

test.describe('Industries', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the industries page
    await page.goto('/industries');
  });

  test('should display the industries list', async ({ page }) => {
    // Check if the page title is displayed
    await expect(page.locator('h2').filter({ hasText: 'Industries' })).toBeVisible();
    
    // Check if the add industry button is displayed
    await expect(page.getByRole('button', { name: 'Add Industry' })).toBeVisible();
    
    // Check if the table is displayed
    await expect(page.locator('table')).toBeVisible();
  });

  test('should navigate to the add industry page', async ({ page }) => {
    // Click the add industry button
    await page.getByRole('button', { name: 'Add Industry' }).click();
    
    // Check if we're on the add industry page
    await expect(page.locator('h2').filter({ hasText: 'Add Industry' })).toBeVisible();
    
    // Check if the form is displayed
    await expect(page.getByLabel('Industry Name')).toBeVisible();
  });

  test('should create a new industry', async ({ page }) => {
    // Navigate to the create industry page
    await page.getByRole('button', { name: 'Add Industry' }).click();
    
    // Generate a unique industry name to avoid conflicts
    const timestamp = Date.now();
    const industryName = `Test Industry ${timestamp}`;
    
    // Fill out the form
    await page.getByLabel('Industry Name').fill(industryName);
    await page.getByLabel('Description').fill('This is a test industry description');
    
    // Select a sector
    await page.getByLabel('Sector').click();
    await page.getByText('Financial Services').click();
    
    // Save the industry
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if success message is displayed
    await expect(page.getByText('Industry created successfully')).toBeVisible();
    
    // Check if we're redirected to the edit page
    await expect(page.locator('h2').filter({ hasText: 'Edit Industry' })).toBeVisible();
  });

  test('should navigate to the edit industry page', async ({ page }) => {
    // Assuming there's at least one industry in the table
    await page.locator('table').getByRole('button', { name: 'Edit' }).first().click();
    
    // Check if we're on the edit industry page
    await expect(page.locator('h2').filter({ hasText: 'Edit Industry' })).toBeVisible();
    
    // Check if the form is pre-filled
    await expect(page.getByLabel('Industry Name')).toHaveValue();
  });

  test('should update an industry', async ({ page }) => {
    // Navigate to the edit page of the first industry
    await page.locator('table').getByRole('button', { name: 'Edit' }).first().click();
    
    // Get the current industry name
    const currentName = await page.getByLabel('Industry Name').inputValue();
    
    // Update the industry name
    const updatedName = `${currentName} (Updated)`;
    await page.getByLabel('Industry Name').fill(updatedName);
    
    // Update the description
    await page.getByLabel('Description').fill('This description has been updated');
    
    // Save the industry
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if success message is displayed
    await expect(page.getByText('Industry updated successfully')).toBeVisible();
  });

  test('should search for industries', async ({ page }) => {
    // Enter search term
    await page.getByPlaceholder('Search industries...').fill('test');
    await page.getByRole('button', { name: 'Search' }).click();
    
    // Check if search results are displayed
    await expect(page.locator('table')).toBeVisible();
  });

  test('should attempt to delete an industry', async ({ page }) => {
    // Click the delete button for the first industry
    await page.locator('table').getByRole('button', { name: 'Delete' }).first().click();
    
    // Check if confirmation dialog is displayed
    await expect(page.getByText('Are you sure you want to delete this industry?')).toBeVisible();
    
    // Cancel the deletion
    await page.getByRole('button', { name: 'No' }).click();
    
    // Check that the table still contains industries
    await expect(page.locator('table tbody tr')).toBeVisible();
  });

  test('should navigate to regulations filtered by industry', async ({ page }) => {
    // Click the view regulations button for the first industry
    await page.locator('table').getByRole('button', { name: 'View Regulations' }).first().click();
    
    // Check if we're redirected to the regulations page with the industry filter
    await expect(page.url()).toContain('/regulations?industry_id=');
  });

  test('should show industry preview when creating/editing', async ({ page }) => {
    // Navigate to the create industry page
    await page.getByRole('button', { name: 'Add Industry' }).click();
    
    // Fill out the industry name
    await page.getByLabel('Industry Name').fill('Preview Industry');
    
    // Check if the preview is displayed
    await expect(page.getByText('Preview:')).toBeVisible();
    await expect(page.getByText('Preview Industry')).toBeVisible();
  });

  test('should validate industry name length', async ({ page }) => {
    // Navigate to the create industry page
    await page.getByRole('button', { name: 'Add Industry' }).click();
    
    // Fill out an industry name that's too long (more than 100 characters)
    await page.getByLabel('Industry Name').fill('A'.repeat(101));
    
    // Try to save
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if validation error is displayed
    await expect(page.getByText('Industry name cannot exceed 100 characters')).toBeVisible();
  });

  test('should require industry name', async ({ page }) => {
    // Navigate to the create industry page
    await page.getByRole('button', { name: 'Add Industry' }).click();
    
    // Leave industry name empty
    await page.getByLabel('Industry Name').fill('');
    
    // Try to save
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if validation error is displayed
    await expect(page.getByText('Please enter the industry name')).toBeVisible();
  });

  test('should handle sector selection', async ({ page }) => {
    // Navigate to the create industry page
    await page.getByRole('button', { name: 'Add Industry' }).click();
    
    // Click on the sector dropdown
    await page.getByLabel('Sector').click();
    
    // Check if the sector options are displayed
    await expect(page.getByText('Financial Services')).toBeVisible();
    await expect(page.getByText('Healthcare')).toBeVisible();
    
    // Select a sector
    await page.getByText('Technology').click();
    
    // Check if the preview is updated
    await expect(page.getByText('Preview:')).toBeVisible();
    await expect(page.getByText('Technology')).toBeVisible();
  });
});
