const { test, expect } = require('@playwright/test');
const path = require('path');

test.describe('Regulation Documents', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the regulation documents page
    await page.goto('/regulation-documents');
  });

  test('should display the regulation documents list', async ({ page }) => {
    // Check if the page title is displayed
    await expect(page.locator('h2').filter({ hasText: 'Regulation Documents' })).toBeVisible();
    
    // Check if the add document button is displayed
    await expect(page.getByRole('button', { name: 'Add Document' })).toBeVisible();
    
    // Check if the upload document button is displayed
    await expect(page.getByRole('button', { name: 'Upload Document' })).toBeVisible();
    
    // Check if the table is displayed
    await expect(page.locator('table')).toBeVisible();
  });

  test('should navigate to the add document page', async ({ page }) => {
    // Click the add document button
    await page.getByRole('button', { name: 'Add Document' }).click();
    
    // Check if we're on the add document page
    await expect(page.locator('h2').filter({ hasText: 'Add Regulation Document' })).toBeVisible();
    
    // Check if the form is displayed
    await expect(page.getByLabel('Document Name')).toBeVisible();
  });

  test('should navigate to the upload document page', async ({ page }) => {
    // Click the upload document button
    await page.getByRole('button', { name: 'Upload Document' }).click();
    
    // Check if we're on the upload document page
    await expect(page.locator('h2').filter({ hasText: 'Upload Regulation Document' })).toBeVisible();
    
    // Check if the upload area is displayed
    await expect(page.getByText('Click or drag file to this area to upload')).toBeVisible();
  });

  test('should create a new regulation document', async ({ page }) => {
    // Navigate to the create document page
    await page.getByRole('button', { name: 'Add Document' }).click();
    
    // Generate a unique document name to avoid conflicts
    const timestamp = Date.now();
    const documentName = `Test Document ${timestamp}`;
    
    // Fill out the form
    await page.getByLabel('Document Name').fill(documentName);
    await page.getByLabel('Document Type').click();
    await page.getByText('PDF').click();
    await page.getByLabel('Description').fill('This is a test document description');
    
    // Select a regulation if available
    const regulationOptions = await page.locator('div[role="option"]').count();
    if (regulationOptions > 0) {
      await page.getByLabel('Regulation').click();
      await page.locator('div[role="option"]').first().click();
    } else {
      // If no regulations available, we need to create one first
      test.skip('No regulations available to select');
    }
    
    // Add a file URL
    await page.getByLabel('File URL').fill('https://example.com/test-document.pdf');
    
    // Save the document
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if success message is displayed
    await expect(page.getByText('Regulation document created successfully')).toBeVisible();
    
    // Check if we're redirected to the edit page
    await expect(page.locator('h2').filter({ hasText: 'Edit Regulation Document' })).toBeVisible();
  });

  test('should navigate to the edit document page', async ({ page }) => {
    // Assuming there's at least one document in the table
    await page.locator('table').getByRole('button', { name: 'Edit' }).first().click();
    
    // Check if we're on the edit document page
    await expect(page.locator('h2').filter({ hasText: 'Edit Regulation Document' })).toBeVisible();
    
    // Check if the form is pre-filled
    await expect(page.getByLabel('Document Name')).toHaveValue();
  });

  test('should update a regulation document', async ({ page }) => {
    // Navigate to the edit page of the first document
    await page.locator('table').getByRole('button', { name: 'Edit' }).first().click();
    
    // Get the current document name
    const currentName = await page.getByLabel('Document Name').inputValue();
    
    // Update the document name
    const updatedName = `${currentName} (Updated)`;
    await page.getByLabel('Document Name').fill(updatedName);
    
    // Update the description
    await page.getByLabel('Description').fill('This description has been updated');
    
    // Save the document
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if success message is displayed
    await expect(page.getByText('Regulation document updated successfully')).toBeVisible();
  });

  test('should navigate to the document detail page', async ({ page }) => {
    // Click on the first document name in the table
    await page.locator('table tbody tr').first().locator('td').nth(1).click();
    
    // Check if we're on the document detail page
    await expect(page.locator('h2')).toBeVisible();
    
    // Check if the details are displayed
    await expect(page.getByText('Document Information')).toBeVisible();
  });

  test('should filter documents by document type', async ({ page }) => {
    // Assuming there's at least one document type in the dropdown
    await page.getByPlaceholder('Filter by Document Type').click();
    
    // If there are options, click the first one
    const options = await page.getByRole('option').count();
    if (options > 0) {
      await page.getByRole('option').first().click();
      
      // Check if the table is updated
      await expect(page.locator('table')).toBeVisible();
    }
  });

  test('should filter documents by regulation', async ({ page }) => {
    // Assuming there's at least one regulation in the dropdown
    await page.getByPlaceholder('Filter by Regulation').click();
    
    // If there are options, click the first one
    const options = await page.getByRole('option').count();
    if (options > 0) {
      await page.getByRole('option').first().click();
      
      // Check if the table is updated
      await expect(page.locator('table')).toBeVisible();
    }
  });

  test('should search for documents', async ({ page }) => {
    // Enter search term
    await page.getByPlaceholder('Search documents...').fill('test');
    await page.getByRole('button', { name: 'Search' }).click();
    
    // Check if search results are displayed
    await expect(page.locator('table')).toBeVisible();
  });

  test('should reset filters', async ({ page }) => {
    // Enter search term
    await page.getByPlaceholder('Search documents...').fill('test');
    await page.getByRole('button', { name: 'Search' }).click();
    
    // Reset filters
    await page.getByRole('button', { name: 'Reset Filters' }).click();
    
    // Check that the search field is cleared
    await expect(page.getByPlaceholder('Search documents...')).toHaveValue('');
  });

  test('should attempt to delete a document', async ({ page }) => {
    // Click the delete button for the first document
    await page.locator('table').getByRole('button', { name: 'Delete' }).first().click();
    
    // Check if confirmation dialog is displayed
    await expect(page.getByText('Are you sure you want to delete this document?')).toBeVisible();
    
    // Cancel the deletion
    await page.getByRole('button', { name: 'No' }).click();
    
    // Check that the table still contains documents
    await expect(page.locator('table tbody tr')).toBeVisible();
  });

  test('should show document preview on detail page', async ({ page }) => {
    // Click on the first document name in the table
    await page.locator('table tbody tr').first().locator('td').nth(1).click();
    
    // Check if the preview section is displayed
    await expect(page.getByText('Preview')).toBeVisible();
  });

  test('should show download button on detail page', async ({ page }) => {
    // Click on the first document name in the table
    await page.locator('table tbody tr').first().locator('td').nth(1).click();
    
    // Check if the download button is displayed (if the document has a file URL)
    const downloadButton = page.getByRole('button', { name: 'Download' });
    if (await downloadButton.count() > 0) {
      await expect(downloadButton).toBeVisible();
    }
  });

  // This test is more complex and might need to be adjusted based on your actual implementation
  test.skip('should upload a document file', async ({ page }) => {
    // Navigate to the upload document page
    await page.getByRole('button', { name: 'Upload Document' }).click();
    
    // Generate a unique document name
    const timestamp = Date.now();
    const documentName = `Uploaded Document ${timestamp}`;
    
    // Create a test file path (this would need to be a real file on your system)
    const filePath = path.join(__dirname, '../fixtures/test-document.pdf');
    
    // Upload the file
    await page.setInputFiles('input[type="file"]', filePath);
    
    // Fill out the form
    await page.getByLabel('Document Name').fill(documentName);
    
    // Select a regulation if available
    const regulationOptions = await page.locator('div[role="option"]').count();
    if (regulationOptions > 0) {
      await page.getByLabel('Regulation').click();
      await page.locator('div[role="option"]').first().click();
    } else {
      // If no regulations available, we need to create one first
      test.skip('No regulations available to select');
    }
    
    // Click the upload button
    await page.getByRole('button', { name: 'Upload' }).click();
    
    // Check if success message is displayed
    await expect(page.getByText('File uploaded successfully')).toBeVisible();
    
    // Check if we're redirected to the document detail page
    await expect(page.locator('h2')).toBeVisible();
  });
});
