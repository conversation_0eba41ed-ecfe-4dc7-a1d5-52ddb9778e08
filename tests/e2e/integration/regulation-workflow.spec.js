const { test, expect } = require('@playwright/test');

test.describe('Regulation Management Workflow', () => {
  // Generate unique identifiers for test data
  const timestamp = Date.now();
  const regulationTitle = `Test Regulation ${timestamp}`;
  const tagName = `Test Tag ${timestamp}`;
  const categoryName = `Test Category ${timestamp}`;
  const industryName = `Test Industry ${timestamp}`;
  const requirementText = `Test Requirement ${timestamp}`;
  const documentName = `Test Document ${timestamp}`;
  
  // Store IDs for created entities
  let regulationId;
  let tagId;
  let categoryId;
  let industryId;
  
  test.beforeAll(async ({ browser }) => {
    // Create a context for setup operations
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // Create a tag first
    await page.goto('/regulation-tags/new');
    await page.getByLabel('Tag Name').fill(tagName);
    await page.getByLabel('Description').fill('Test tag description');
    await page.getByRole('button', { name: 'Save' }).click();
    await expect(page.getByText('Regulation tag created successfully')).toBeVisible();
    
    // Extract tag ID from URL
    const tagUrl = page.url();
    tagId = parseInt(tagUrl.split('/').pop().split('/')[0]);
    
    // Create a category
    await page.goto('/regulation-categories/new');
    await page.getByLabel('Category Name').fill(categoryName);
    await page.getByLabel('Description').fill('Test category description');
    await page.getByLabel('Icon').click();
    await page.getByText('Banking & Finance').click();
    await page.getByRole('button', { name: 'Save' }).click();
    await expect(page.getByText('Regulation category created successfully')).toBeVisible();
    
    // Extract category ID from URL
    const categoryUrl = page.url();
    categoryId = parseInt(categoryUrl.split('/').pop().split('/')[0]);
    
    // Create an industry
    await page.goto('/industries/new');
    await page.getByLabel('Industry Name').fill(industryName);
    await page.getByLabel('Description').fill('Test industry description');
    await page.getByLabel('Sector').click();
    await page.getByText('Financial Services').click();
    await page.getByRole('button', { name: 'Save' }).click();
    await expect(page.getByText('Industry created successfully')).toBeVisible();
    
    // Extract industry ID from URL
    const industryUrl = page.url();
    industryId = parseInt(industryUrl.split('/').pop().split('/')[0]);
    
    // Close the context after setup
    await context.close();
  });
  
  test('should create a regulation and associate it with tags, categories, and industries', async ({ page }) => {
    // Navigate to the create regulation page
    await page.goto('/regulations/new');
    
    // Fill out the basic information tab
    await page.getByLabel('Title').fill(regulationTitle);
    await page.getByLabel('Description').fill('Test regulation description');
    await page.getByLabel('Status').click();
    await page.getByText('Active').click();
    await page.getByLabel('Reference Number').fill('REG-001');
    
    // Go to the classification tab
    await page.getByRole('tab', { name: 'Classification' }).click();
    
    // Select the category
    await page.getByLabel('Category').click();
    await page.getByText(categoryName).click();
    
    // Select tags
    await page.getByLabel('Tags').click();
    await page.getByText(tagName).click();
    await page.press('body', 'Escape'); // Close the dropdown
    
    // Select industries
    await page.getByLabel('Industries').click();
    await page.getByText(industryName).click();
    await page.press('body', 'Escape'); // Close the dropdown
    
    // Save the regulation
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if success message is displayed
    await expect(page.getByText('Regulation created successfully')).toBeVisible();
    
    // Extract regulation ID from URL
    const regulationUrl = page.url();
    regulationId = parseInt(regulationUrl.split('/').pop().split('/')[0]);
  });
  
  test('should add a compliance requirement to the regulation', async ({ page }) => {
    // Navigate to the create compliance requirement page
    await page.goto(`/compliance-requirements/new?regulation_id=${regulationId}`);
    
    // Fill out the form
    await page.getByLabel('Requirement Text').fill(requirementText);
    await page.getByLabel('Section/Article Reference').fill('Section 1.2');
    await page.getByLabel('Priority').click();
    await page.getByText('High').click();
    await page.getByLabel('Status').click();
    await page.getByText('In Progress').click();
    
    // Verify regulation is pre-selected
    await expect(page.getByLabel('Regulation')).toHaveValue();
    
    // Save the requirement
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if success message is displayed
    await expect(page.getByText('Compliance requirement created successfully')).toBeVisible();
  });
  
  test('should add a document to the regulation', async ({ page }) => {
    // Navigate to the create document page
    await page.goto(`/regulation-documents/new?regulation_id=${regulationId}`);
    
    // Fill out the form
    await page.getByLabel('Document Name').fill(documentName);
    await page.getByLabel('Description').fill('Test document description');
    await page.getByLabel('Document Type').click();
    await page.getByText('PDF').click();
    
    // Verify regulation is pre-selected
    await expect(page.getByLabel('Regulation')).toHaveValue();
    
    // Add a file URL
    await page.getByLabel('File URL').fill('https://example.com/test-document.pdf');
    
    // Save the document
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if success message is displayed
    await expect(page.getByText('Regulation document created successfully')).toBeVisible();
  });
  
  test('should view the regulation with all related entities', async ({ page }) => {
    // Navigate to the regulation detail page
    await page.goto(`/regulations/${regulationId}`);
    
    // Check if the regulation title is displayed
    await expect(page.locator('h2').filter({ hasText: regulationTitle })).toBeVisible();
    
    // Check if the tag is displayed
    await expect(page.getByText(tagName)).toBeVisible();
    
    // Check if the category is displayed
    await expect(page.getByText(categoryName)).toBeVisible();
    
    // Check if the industry is displayed
    await expect(page.getByText(industryName)).toBeVisible();
    
    // Navigate to the Requirements tab
    await page.getByRole('tab', { name: 'Requirements' }).click();
    
    // Check if the requirement is displayed
    await expect(page.getByText(requirementText)).toBeVisible();
    
    // Navigate to the Documents tab
    await page.getByRole('tab', { name: 'Documents' }).click();
    
    // Check if the document is displayed
    await expect(page.getByText(documentName)).toBeVisible();
  });
  
  test('should filter regulations by tag', async ({ page }) => {
    // Navigate to the regulations page with tag filter
    await page.goto(`/regulations?tag_id=${tagId}`);
    
    // Check if the regulation is displayed in the filtered list
    await expect(page.getByText(regulationTitle)).toBeVisible();
  });
  
  test('should filter regulations by category', async ({ page }) => {
    // Navigate to the regulations page with category filter
    await page.goto(`/regulations?category_id=${categoryId}`);
    
    // Check if the regulation is displayed in the filtered list
    await expect(page.getByText(regulationTitle)).toBeVisible();
  });
  
  test('should filter regulations by industry', async ({ page }) => {
    // Navigate to the regulations page with industry filter
    await page.goto(`/regulations?industry_id=${industryId}`);
    
    // Check if the regulation is displayed in the filtered list
    await expect(page.getByText(regulationTitle)).toBeVisible();
  });
  
  test('should update the regulation', async ({ page }) => {
    // Navigate to the edit regulation page
    await page.goto(`/regulations/${regulationId}/edit`);
    
    // Update the title
    const updatedTitle = `${regulationTitle} (Updated)`;
    await page.getByLabel('Title').fill(updatedTitle);
    
    // Update the status
    await page.getByLabel('Status').click();
    await page.getByText('Draft').click();
    
    // Save the regulation
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if success message is displayed
    await expect(page.getByText('Regulation updated successfully')).toBeVisible();
    
    // Navigate to the regulation detail page
    await page.goto(`/regulations/${regulationId}`);
    
    // Check if the updated title is displayed
    await expect(page.locator('h2').filter({ hasText: updatedTitle })).toBeVisible();
    
    // Check if the updated status is displayed
    await expect(page.getByText('Draft')).toBeVisible();
  });
});
