"""
E2E tests for user workflows and interactions
"""
import pytest
from playwright.async_api import expect


@pytest.mark.e2e
class TestUserWorkflows:
    """Test suite for complete user workflows"""
    
    async def test_new_user_onboarding_flow(self, page, base_url, page_helpers):
        """Test the experience of a new user visiting the site"""
        # Navigate to homepage
        await page.goto(f"{base_url}/")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Check welcome message
        welcome_banner = page.locator(".welcome-banner")
        await expect(welcome_banner).to_be_visible()
        
        # User explores API documentation
        api_link = page.locator('a[href="/api/v1/docs"]')
        await api_link.click()
        await page_helpers.wait_for_load_state("networkidle")
        
        # Should reach API docs
        assert "/api/v1/docs" in page.url
        
        # User goes back to explore features
        await page.go_back()
        await page_helpers.wait_for_load_state("networkidle")
        
        # User checks compliance calendar
        calendar_link = page.locator('a[href="/compliance-calendar"]')
        if await calendar_link.count() > 0:
            await calendar_link.click()
            await page_helpers.wait_for_load_state("networkidle")
            assert "/compliance-calendar" in page.url
    
    async def test_compliance_professional_workflow(self, page, base_url, page_helpers):
        """Test workflow for a compliance professional"""
        # Start at enhanced dashboard
        await page.goto(f"{base_url}/enhanced-dashboard")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Check compliance statistics
        stat_cards = page.locator(".stat-card")
        await expect(stat_cards).to_have_count(4)
        
        # Navigate to compliance analytics
        analytics_button = page.locator('a[href="/compliance-analytics"]')
        if await analytics_button.count() > 0:
            await analytics_button.click()
            await page_helpers.wait_for_load_state("networkidle")
        
        # Go back and check regulatory search
        await page.goto(f"{base_url}/enhanced-dashboard")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Use AI regulatory search
        ai_search_link = page.locator('a[href="/ai-regulatory-search"]')
        if await ai_search_link.count() > 0:
            await ai_search_link.click()
            await page_helpers.wait_for_load_state("networkidle")
    
    async def test_developer_api_exploration_workflow(self, page, base_url, page_helpers):
        """Test workflow for a developer exploring the API"""
        # Start at dashboard
        await page.goto(f"{base_url}/")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Navigate to API documentation
        api_docs_link = page.locator('a[href="/api/v1/docs"]')
        await api_docs_link.click()
        await page_helpers.wait_for_load_state("networkidle")
        
        # Check API documentation loads
        assert "/api/v1/docs" in page.url
        
        # Test different API endpoints
        api_endpoints = [
            "/api/v1/health",
            "/api/v1/countries",
            "/api/v1/regulators"
        ]
        
        for endpoint in api_endpoints:
            await page.goto(f"{base_url}{endpoint}")
            await page_helpers.wait_for_load_state("networkidle")
            
            # Check that endpoint responds
            content = await page.content()
            assert len(content) > 10, f"Endpoint {endpoint} should return content"
    
    async def test_admin_management_workflow(self, page, base_url, page_helpers):
        """Test workflow for an admin user"""
        # Navigate to admin panel
        await page.goto(f"{base_url}/ui/manage")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Check admin interface (may require authentication)
        title = await page.title()
        content = await page.content()
        
        # Should show admin interface or login
        assert any(keyword in content.lower() for keyword in [
            "admin", "manage", "login", "dashboard"
        ]), "Should show admin interface or login"
        
        # Test regulation URLs management
        await page.goto(f"{base_url}/regulation-urls")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Should show regulation URLs list
        content = await page.content()
        assert "regulation" in content.lower(), "Should show regulation-related content"


@pytest.mark.e2e
class TestFormInteractions:
    """Test suite for form interactions"""
    
    async def test_contact_form_interaction(self, page, base_url, page_helpers):
        """Test contact form functionality"""
        await page.goto(f"{base_url}/enhanced-dashboard")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Find and click contact modal trigger
        contact_trigger = page.locator('[data-modal-target="contact-modal"]')
        if await contact_trigger.count() > 0:
            await contact_trigger.click()
            
            # Wait for modal to appear
            modal = page.locator("#contact-modal")
            await expect(modal).to_be_visible()
            
            # Fill out form
            await page.fill("#contact-name", "Test User")
            await page.fill("#contact-email", "<EMAIL>")
            await page.fill("#contact-message", "This is a test message for E2E testing.")
            
            # Check form validation
            name_field = page.locator("#contact-name")
            email_field = page.locator("#contact-email")
            message_field = page.locator("#contact-message")
            
            name_value = await name_field.input_value()
            email_value = await email_field.input_value()
            message_value = await message_field.input_value()
            
            assert name_value == "Test User"
            assert email_value == "<EMAIL>"
            assert "test message" in message_value
            
            # Close modal
            close_button = modal.locator('[data-modal-close]')
            await close_button.click()
            
            # Modal should be hidden
            await expect(modal).not_to_have_class("show")
    
    async def test_search_functionality(self, page, base_url, page_helpers):
        """Test search functionality if available"""
        await page.goto(f"{base_url}/ai-regulatory-search")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Look for search input
        search_inputs = await page.query_selector_all('input[type="search"], input[placeholder*="search"], input[name*="search"]')
        
        if search_inputs:
            search_input = page.locator('input[type="search"], input[placeholder*="search"], input[name*="search"]').first
            
            # Test search input
            await search_input.fill("GDPR")
            await page.keyboard.press("Enter")
            await page_helpers.wait_for_load_state("networkidle")
            
            # Check that search was performed
            content = await page.content()
            # Search results or no results message should appear
            assert len(content) > 1000, "Search should return some content"
    
    async def test_language_selection_persistence(self, page, base_url, page_helpers):
        """Test that language selection persists across pages"""
        await page.goto(f"{base_url}/")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Select a language
        lang_button = page.locator("#nav-language-button")
        if await lang_button.count() > 0:
            await lang_button.click()
            
            # Select Spanish if available
            spanish_option = page.locator('a[href*="lang=es"]')
            if await spanish_option.count() > 0:
                await spanish_option.click()
                await page_helpers.wait_for_load_state("networkidle")
                
                # Navigate to another page
                await page.goto(f"{base_url}/compliance-calendar")
                await page_helpers.wait_for_load_state("networkidle")
                
                # Language should persist
                current_url = page.url
                assert "lang=es" in current_url, "Language selection should persist"
    
    async def test_theme_persistence(self, page, base_url, page_helpers):
        """Test that theme selection persists across page reloads"""
        await page.goto(f"{base_url}/enhanced-dashboard")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Switch to dark theme
        theme_toggle = page.locator("#themeToggle")
        await theme_toggle.click()
        await page.wait_for_timeout(500)
        
        # Check dark theme is applied
        html = page.locator("html")
        theme_attr = await html.get_attribute("data-theme")
        assert theme_attr == "dark"
        
        # Reload page
        await page.reload()
        await page_helpers.wait_for_load_state("networkidle")
        
        # Theme should persist
        theme_attr = await html.get_attribute("data-theme")
        assert theme_attr == "dark", "Dark theme should persist after reload"


@pytest.mark.e2e
class TestAccessibilityWorkflows:
    """Test suite for accessibility workflows"""
    
    @pytest.mark.accessibility
    async def test_keyboard_navigation_workflow(self, page, base_url, page_helpers):
        """Test complete keyboard navigation workflow"""
        await page.goto(f"{base_url}/enhanced-dashboard")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Start keyboard navigation
        await page.keyboard.press("Tab")
        
        # Should focus on skip link first
        focused_element = await page.evaluate("document.activeElement")
        
        # Continue tabbing through interactive elements
        interactive_elements = []
        for _ in range(10):  # Tab through first 10 elements
            await page.keyboard.press("Tab")
            element_info = await page.evaluate("""
                {
                    tagName: document.activeElement.tagName,
                    type: document.activeElement.type || '',
                    href: document.activeElement.href || '',
                    textContent: document.activeElement.textContent?.slice(0, 50) || ''
                }
            """)
            interactive_elements.append(element_info)
        
        # Should have focused on various interactive elements
        tag_names = [elem["tagName"] for elem in interactive_elements]
        assert any(tag in tag_names for tag in ["A", "BUTTON", "INPUT"]), \
            "Should focus on interactive elements"
    
    @pytest.mark.accessibility
    async def test_screen_reader_workflow(self, page, base_url, page_helpers):
        """Test screen reader compatibility"""
        await page.goto(f"{base_url}/enhanced-dashboard")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Check for proper heading structure
        headings = await page.evaluate("""
            Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'))
                .map(h => ({level: parseInt(h.tagName[1]), text: h.textContent.slice(0, 50)}))
        """)
        
        # Should have proper heading hierarchy
        assert len(headings) > 0, "Page should have headings"
        assert any(h["level"] == 1 for h in headings), "Page should have h1 element"
        
        # Check for ARIA landmarks
        landmarks = await page.evaluate("""
            Array.from(document.querySelectorAll('[role], main, nav, header, footer, aside, section'))
                .map(el => el.tagName.toLowerCase() + (el.getAttribute('role') ? `[${el.getAttribute('role')}]` : ''))
        """)
        
        assert len(landmarks) > 0, "Page should have ARIA landmarks"
        assert any("main" in landmark for landmark in landmarks), "Page should have main landmark"
    
    @pytest.mark.accessibility
    async def test_high_contrast_mode(self, page, base_url, page_helpers):
        """Test high contrast mode compatibility"""
        await page.goto(f"{base_url}/enhanced-dashboard")
        await page_helpers.wait_for_load_state("networkidle")
        
        # Simulate high contrast mode
        await page.add_style_tag(content="""
            @media (prefers-contrast: high) {
                * { 
                    background: black !important; 
                    color: white !important; 
                    border-color: white !important; 
                }
            }
        """)
        
        # Check that content is still visible
        visible_elements = await page.evaluate("""
            Array.from(document.querySelectorAll('h1, h2, h3, p, a, button'))
                .filter(el => {
                    const style = window.getComputedStyle(el);
                    return style.display !== 'none' && style.visibility !== 'hidden';
                }).length
        """)
        
        assert visible_elements > 10, "Content should remain visible in high contrast mode"


@pytest.mark.e2e
class TestPerformanceWorkflows:
    """Test suite for performance-related workflows"""
    
    @pytest.mark.performance
    async def test_page_load_performance_workflow(self, page, base_url, performance_thresholds):
        """Test page load performance across different pages"""
        pages_to_test = [
            "/",
            "/enhanced-dashboard",
            "/api/v1/docs",
            "/compliance-calendar"
        ]
        
        for page_url in pages_to_test:
            # Navigate to page
            start_time = await page.evaluate("performance.now()")
            await page.goto(f"{base_url}{page_url}")
            await page.wait_for_load_state("networkidle")
            end_time = await page.evaluate("performance.now()")
            
            load_time = end_time - start_time
            
            # Check load time threshold
            assert load_time < performance_thresholds["page_load_time"], \
                f"Page {page_url} load time {load_time}ms exceeds threshold"
    
    @pytest.mark.performance
    async def test_resource_loading_workflow(self, page, base_url):
        """Test resource loading performance"""
        await page.goto(f"{base_url}/enhanced-dashboard")
        await page.wait_for_load_state("networkidle")
        
        # Check resource loading
        resources = await page.evaluate("""
            performance.getEntriesByType('resource').map(r => ({
                name: r.name,
                duration: r.duration,
                size: r.transferSize || 0
            }))
        """)
        
        # Check that critical resources loaded quickly
        css_resources = [r for r in resources if r["name"].endswith(".css")]
        js_resources = [r for r in resources if r["name"].endswith(".js")]
        
        if css_resources:
            avg_css_load = sum(r["duration"] for r in css_resources) / len(css_resources)
            assert avg_css_load < 1000, "CSS resources should load quickly"
        
        if js_resources:
            avg_js_load = sum(r["duration"] for r in js_resources) / len(js_resources)
            assert avg_js_load < 2000, "JS resources should load reasonably quickly"
