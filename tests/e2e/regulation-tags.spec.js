const { test, expect } = require('@playwright/test');

test.describe('Regulation Tags', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the regulation tags page
    await page.goto('/regulation-tags');
  });

  test('should display the regulation tags list', async ({ page }) => {
    // Check if the page title is displayed
    await expect(page.locator('h2').filter({ hasText: 'Regulation Tags' })).toBeVisible();
    
    // Check if the add tag button is displayed
    await expect(page.getByRole('button', { name: 'Add Tag' })).toBeVisible();
    
    // Check if the table is displayed
    await expect(page.locator('table')).toBeVisible();
  });

  test('should navigate to the add tag page', async ({ page }) => {
    // Click the add tag button
    await page.getByRole('button', { name: 'Add Tag' }).click();
    
    // Check if we're on the add tag page
    await expect(page.locator('h2').filter({ hasText: 'Add Regulation Tag' })).toBeVisible();
    
    // Check if the form is displayed
    await expect(page.getByLabel('Tag Name')).toBeVisible();
  });

  test('should create a new regulation tag', async ({ page }) => {
    // Navigate to the create tag page
    await page.getByRole('button', { name: 'Add Tag' }).click();
    
    // Generate a unique tag name to avoid conflicts
    const timestamp = Date.now();
    const tagName = `Test Tag ${timestamp}`;
    
    // Fill out the form
    await page.getByLabel('Tag Name').fill(tagName);
    await page.getByLabel('Description').fill('This is a test tag description');
    
    // Save the tag
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if success message is displayed
    await expect(page.getByText('Regulation tag created successfully')).toBeVisible();
    
    // Check if we're redirected to the edit page
    await expect(page.locator('h2').filter({ hasText: 'Edit Regulation Tag' })).toBeVisible();
  });

  test('should navigate to the edit tag page', async ({ page }) => {
    // Assuming there's at least one tag in the table
    await page.locator('table').getByRole('button', { name: 'Edit' }).first().click();
    
    // Check if we're on the edit tag page
    await expect(page.locator('h2').filter({ hasText: 'Edit Regulation Tag' })).toBeVisible();
    
    // Check if the form is pre-filled
    await expect(page.getByLabel('Tag Name')).toHaveValue();
  });

  test('should update a regulation tag', async ({ page }) => {
    // Navigate to the edit page of the first tag
    await page.locator('table').getByRole('button', { name: 'Edit' }).first().click();
    
    // Get the current tag name
    const currentName = await page.getByLabel('Tag Name').inputValue();
    
    // Update the tag name
    const updatedName = `${currentName} (Updated)`;
    await page.getByLabel('Tag Name').fill(updatedName);
    
    // Update the description
    await page.getByLabel('Description').fill('This description has been updated');
    
    // Save the tag
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if success message is displayed
    await expect(page.getByText('Regulation tag updated successfully')).toBeVisible();
  });

  test('should search for tags', async ({ page }) => {
    // Enter search term
    await page.getByPlaceholder('Search tags...').fill('test');
    await page.getByRole('button', { name: 'Search' }).click();
    
    // Check if search results are displayed
    await expect(page.locator('table')).toBeVisible();
  });

  test('should attempt to delete a tag', async ({ page }) => {
    // Click the delete button for the first tag
    await page.locator('table').getByRole('button', { name: 'Delete' }).first().click();
    
    // Check if confirmation dialog is displayed
    await expect(page.getByText('Are you sure you want to delete this tag?')).toBeVisible();
    
    // Cancel the deletion
    await page.getByRole('button', { name: 'No' }).click();
    
    // Check that the table still contains tags
    await expect(page.locator('table tbody tr')).toBeVisible();
  });

  test('should navigate to regulations filtered by tag', async ({ page }) => {
    // Click the view regulations button for the first tag
    await page.locator('table').getByRole('button', { name: 'View Regulations' }).first().click();
    
    // Check if we're redirected to the regulations page with the tag filter
    await expect(page.url()).toContain('/regulations?tag_id=');
  });

  test('should show tag preview when creating/editing', async ({ page }) => {
    // Navigate to the create tag page
    await page.getByRole('button', { name: 'Add Tag' }).click();
    
    // Fill out the tag name
    await page.getByLabel('Tag Name').fill('Preview Tag');
    
    // Check if the preview is displayed
    await expect(page.getByText('Preview:')).toBeVisible();
    await expect(page.getByText('Preview Tag')).toBeVisible();
  });

  test('should validate tag name length', async ({ page }) => {
    // Navigate to the create tag page
    await page.getByRole('button', { name: 'Add Tag' }).click();
    
    // Fill out a tag name that's too long (more than 50 characters)
    await page.getByLabel('Tag Name').fill('A'.repeat(51));
    
    // Try to save
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if validation error is displayed
    await expect(page.getByText('Tag name cannot exceed 50 characters')).toBeVisible();
  });

  test('should require tag name', async ({ page }) => {
    // Navigate to the create tag page
    await page.getByRole('button', { name: 'Add Tag' }).click();
    
    // Leave tag name empty
    await page.getByLabel('Tag Name').fill('');
    
    // Try to save
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if validation error is displayed
    await expect(page.getByText('Please enter the tag name')).toBeVisible();
  });

  test('should handle color picker', async ({ page }) => {
    // Navigate to the create tag page
    await page.getByRole('button', { name: 'Add Tag' }).click();
    
    // Check if color picker is displayed
    await expect(page.getByLabel('Tag Color')).toBeVisible();
    
    // Click on the color picker
    await page.getByLabel('Tag Color').click();
    
    // This is a simplified test as interacting with the color picker is complex
    // Just verify that clicking it opens the color picker panel
    await expect(page.locator('.ant-color-picker-panel')).toBeVisible();
  });
});
