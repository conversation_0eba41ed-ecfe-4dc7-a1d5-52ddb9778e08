const { test, expect } = require('@playwright/test');

test.describe('Regulation Categories', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the regulation categories page
    await page.goto('/regulation-categories');
  });

  test('should display the regulation categories list', async ({ page }) => {
    // Check if the page title is displayed
    await expect(page.locator('h2').filter({ hasText: 'Regulation Categories' })).toBeVisible();
    
    // Check if the add category button is displayed
    await expect(page.getByRole('button', { name: 'Add Category' })).toBeVisible();
    
    // Check if the table is displayed
    await expect(page.locator('table')).toBeVisible();
  });

  test('should navigate to the add category page', async ({ page }) => {
    // Click the add category button
    await page.getByRole('button', { name: 'Add Category' }).click();
    
    // Check if we're on the add category page
    await expect(page.locator('h2').filter({ hasText: 'Add Regulation Category' })).toBeVisible();
    
    // Check if the form is displayed
    await expect(page.getByLabel('Category Name')).toBeVisible();
  });

  test('should create a new regulation category', async ({ page }) => {
    // Navigate to the create category page
    await page.getByRole('button', { name: 'Add Category' }).click();
    
    // Generate a unique category name to avoid conflicts
    const timestamp = Date.now();
    const categoryName = `Test Category ${timestamp}`;
    
    // Fill out the form
    await page.getByLabel('Category Name').fill(categoryName);
    await page.getByLabel('Description').fill('This is a test category description');
    
    // Select an icon
    await page.getByLabel('Icon').click();
    await page.getByText('Banking & Finance').click();
    
    // Save the category
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if success message is displayed
    await expect(page.getByText('Regulation category created successfully')).toBeVisible();
    
    // Check if we're redirected to the edit page
    await expect(page.locator('h2').filter({ hasText: 'Edit Regulation Category' })).toBeVisible();
  });

  test('should navigate to the edit category page', async ({ page }) => {
    // Assuming there's at least one category in the table
    await page.locator('table').getByRole('button', { name: 'Edit' }).first().click();
    
    // Check if we're on the edit category page
    await expect(page.locator('h2').filter({ hasText: 'Edit Regulation Category' })).toBeVisible();
    
    // Check if the form is pre-filled
    await expect(page.getByLabel('Category Name')).toHaveValue();
  });

  test('should update a regulation category', async ({ page }) => {
    // Navigate to the edit page of the first category
    await page.locator('table').getByRole('button', { name: 'Edit' }).first().click();
    
    // Get the current category name
    const currentName = await page.getByLabel('Category Name').inputValue();
    
    // Update the category name
    const updatedName = `${currentName} (Updated)`;
    await page.getByLabel('Category Name').fill(updatedName);
    
    // Update the description
    await page.getByLabel('Description').fill('This description has been updated');
    
    // Save the category
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if success message is displayed
    await expect(page.getByText('Regulation category updated successfully')).toBeVisible();
  });

  test('should search for categories', async ({ page }) => {
    // Enter search term
    await page.getByPlaceholder('Search categories...').fill('test');
    await page.getByRole('button', { name: 'Search' }).click();
    
    // Check if search results are displayed
    await expect(page.locator('table')).toBeVisible();
  });

  test('should attempt to delete a category', async ({ page }) => {
    // Click the delete button for the first category
    await page.locator('table').getByRole('button', { name: 'Delete' }).first().click();
    
    // Check if confirmation dialog is displayed
    await expect(page.getByText('Are you sure you want to delete this category?')).toBeVisible();
    
    // Cancel the deletion
    await page.getByRole('button', { name: 'No' }).click();
    
    // Check that the table still contains categories
    await expect(page.locator('table tbody tr')).toBeVisible();
  });

  test('should navigate to regulations filtered by category', async ({ page }) => {
    // Click the view regulations button for the first category
    await page.locator('table').getByRole('button', { name: 'View Regulations' }).first().click();
    
    // Check if we're redirected to the regulations page with the category filter
    await expect(page.url()).toContain('/regulations?category_id=');
  });

  test('should show category preview when creating/editing', async ({ page }) => {
    // Navigate to the create category page
    await page.getByRole('button', { name: 'Add Category' }).click();
    
    // Fill out the category name
    await page.getByLabel('Category Name').fill('Preview Category');
    
    // Check if the preview is displayed
    await expect(page.getByText('Preview:')).toBeVisible();
    await expect(page.getByText('Preview Category')).toBeVisible();
  });

  test('should validate category name length', async ({ page }) => {
    // Navigate to the create category page
    await page.getByRole('button', { name: 'Add Category' }).click();
    
    // Fill out a category name that's too long (more than 100 characters)
    await page.getByLabel('Category Name').fill('A'.repeat(101));
    
    // Try to save
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if validation error is displayed
    await expect(page.getByText('Category name cannot exceed 100 characters')).toBeVisible();
  });

  test('should require category name', async ({ page }) => {
    // Navigate to the create category page
    await page.getByRole('button', { name: 'Add Category' }).click();
    
    // Leave category name empty
    await page.getByLabel('Category Name').fill('');
    
    // Try to save
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if validation error is displayed
    await expect(page.getByText('Please enter the category name')).toBeVisible();
  });

  test('should handle icon selection', async ({ page }) => {
    // Navigate to the create category page
    await page.getByRole('button', { name: 'Add Category' }).click();
    
    // Click on the icon dropdown
    await page.getByLabel('Icon').click();
    
    // Check if the icon options are displayed
    await expect(page.getByText('Banking & Finance')).toBeVisible();
    await expect(page.getByText('Healthcare')).toBeVisible();
    
    // Select an icon
    await page.getByText('Environmental').click();
    
    // Check if the preview is updated
    await expect(page.getByText('Preview:')).toBeVisible();
  });
});
