const { test, expect } = require('@playwright/test');

test.describe('Regulation URL Management', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the regulation URLs page
    await page.goto('/regulation-urls');
  });

  test('should display the regulation URLs list', async ({ page }) => {
    // Check if the page title is displayed
    await expect(page.locator('h2').filter({ hasText: 'Regulation URLs' })).toBeVisible();
    
    // Check if the add regulation URL button is displayed
    await expect(page.getByRole('button', { name: 'Add Regulation URL' })).toBeVisible();
    
    // Check if the table is displayed
    await expect(page.locator('table')).toBeVisible();
  });

  test('should navigate to the add regulation URL page', async ({ page }) => {
    // Click the add regulation URL button
    await page.getByRole('button', { name: 'Add Regulation URL' }).click();
    
    // Check if we're on the add regulation URL page
    await expect(page.locator('h2').filter({ hasText: 'Add Regulation URL' })).toBeVisible();
    
    // Check if the form is displayed
    await expect(page.getByLabel('URL')).toBeVisible();
    await expect(page.getByLabel('Domain')).toBeVisible();
  });

  test('should create a new regulation URL', async ({ page }) => {
    // Navigate to the create regulation URL page
    await page.getByRole('button', { name: 'Add Regulation URL' }).click();
    
    // Generate a unique URL to avoid conflicts
    const timestamp = Date.now();
    const testUrl = `https://www.example.com/regulation-${timestamp}.pdf`;
    
    // Fill out the form
    await page.getByLabel('URL').fill(testUrl);
    await page.getByLabel('Title').fill(`Test Regulation ${timestamp}`);
    
    // Select a category
    await page.getByLabel('Category').click();
    await page.getByText('Financial Regulations').click();
    
    // Select a regulator if available
    const regulatorOptions = await page.locator('div[role="option"]').count();
    if (regulatorOptions > 0) {
      await page.getByLabel('Regulator').click();
      await page.locator('div[role="option"]').first().click();
    }
    
    // Set confidence level
    await page.getByLabel('Confidence Level').fill('90%');
    
    // Save the regulation URL
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if success message is displayed
    await expect(page.getByText('Regulation URL created successfully')).toBeVisible();
    
    // Check if we're redirected to the edit page
    await expect(page.locator('h2').filter({ hasText: 'Edit Regulation URL' })).toBeVisible();
  });

  test('should navigate to the edit regulation URL page', async ({ page }) => {
    // Assuming there's at least one regulation URL in the table
    await page.locator('table').getByRole('button', { name: 'Edit' }).first().click();
    
    // Check if we're on the edit regulation URL page
    await expect(page.locator('h2').filter({ hasText: 'Edit Regulation URL' })).toBeVisible();
    
    // Check if the form is pre-filled
    await expect(page.getByLabel('URL')).toHaveValue();
    await expect(page.getByLabel('Domain')).toHaveValue();
  });

  test('should update a regulation URL', async ({ page }) => {
    // Navigate to the edit page of the first regulation URL
    await page.locator('table').getByRole('button', { name: 'Edit' }).first().click();
    
    // Get the current title
    const currentTitle = await page.getByLabel('Title').inputValue();
    
    // Update the title
    const updatedTitle = `${currentTitle || 'Regulation'} (Updated)`;
    await page.getByLabel('Title').fill(updatedTitle);
    
    // Save the regulation URL
    await page.getByRole('button', { name: 'Save' }).click();
    
    // Check if success message is displayed
    await expect(page.getByText('Regulation URL updated successfully')).toBeVisible();
  });

  test('should navigate to the regulation URL detail page', async ({ page }) => {
    // Click on the first regulation URL title in the table
    await page.locator('table tbody tr').first().locator('td').nth(1).click();
    
    // Check if we're on the regulation URL detail page
    await expect(page.locator('h2')).toBeVisible();
    
    // Check if the details are displayed
    await expect(page.getByText('ID')).toBeVisible();
    await expect(page.getByText('URL')).toBeVisible();
    await expect(page.getByText('Domain')).toBeVisible();
  });

  test('should open the URL in a new tab', async ({ page, context }) => {
    // Click on the first regulation URL title in the table
    await page.locator('table tbody tr').first().locator('td').nth(1).click();
    
    // Check if the Open URL button is displayed
    await expect(page.getByRole('button', { name: 'Open URL' })).toBeVisible();
    
    // We can't fully test opening in a new tab, but we can check the button has the right attributes
    const openUrlButton = page.getByRole('button', { name: 'Open URL' });
    await expect(openUrlButton).toHaveAttribute('href');
    await expect(openUrlButton).toHaveAttribute('target', '_blank');
  });

  test('should filter regulation URLs by regulator', async ({ page }) => {
    // Assuming there's at least one regulator in the dropdown
    await page.getByPlaceholder('Filter by Regulator').click();
    
    // If there are options, click the first one
    const options = await page.getByRole('option').count();
    if (options > 0) {
      await page.getByRole('option').first().click();
      
      // Check if the table is updated
      await expect(page.locator('table')).toBeVisible();
    }
  });

  test('should filter regulation URLs by country', async ({ page }) => {
    // Assuming there's at least one country in the dropdown
    await page.getByPlaceholder('Filter by Country').click();
    
    // If there are options, click the first one
    const options = await page.getByRole('option').count();
    if (options > 0) {
      await page.getByRole('option').first().click();
      
      // Check if the table is updated
      await expect(page.locator('table')).toBeVisible();
    }
  });

  test('should filter regulation URLs by category', async ({ page }) => {
    // Assuming there's at least one category in the dropdown
    await page.getByPlaceholder('Filter by Category').click();
    
    // If there are options, click the first one
    const options = await page.getByRole('option').count();
    if (options > 0) {
      await page.getByRole('option').first().click();
      
      // Check if the table is updated
      await expect(page.locator('table')).toBeVisible();
    }
  });

  test('should search for regulation URLs', async ({ page }) => {
    // Enter search term
    await page.getByPlaceholder('Search regulation URLs...').fill('test');
    await page.getByRole('button', { name: 'Search' }).click();
    
    // Check if search results are displayed
    await expect(page.locator('table')).toBeVisible();
  });

  test('should reset filters', async ({ page }) => {
    // Enter search term
    await page.getByPlaceholder('Search regulation URLs...').fill('test');
    await page.getByRole('button', { name: 'Search' }).click();
    
    // Reset filters
    await page.getByRole('button', { name: 'Reset Filters' }).click();
    
    // Check that the search field is cleared
    await expect(page.getByPlaceholder('Search regulation URLs...')).toHaveValue('');
  });

  test('should attempt to delete a regulation URL', async ({ page }) => {
    // Click the delete button for the first regulation URL
    await page.locator('table').getByRole('button', { name: 'Delete' }).first().click();
    
    // Check if confirmation dialog is displayed
    await expect(page.getByText('Are you sure you want to delete this regulation URL?')).toBeVisible();
    
    // Cancel the deletion
    await page.getByRole('button', { name: 'No' }).click();
    
    // Check that the table still contains regulation URLs
    await expect(page.locator('table tbody tr')).toBeVisible();
  });
});
