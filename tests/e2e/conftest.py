"""
Pytest configuration for E2E tests with <PERSON><PERSON>
"""
import pytest
import asyncio
import uvicorn
import threading
import time
import sys
import os
from pathlib import Path
from playwright.async_api import async_playwright

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.main import app


class TestServer:
    """Test server manager for E2E tests"""
    
    def __init__(self, host="127.0.0.1", port=8001):
        self.host = host
        self.port = port
        self.server = None
        self.thread = None
        
    def start(self):
        """Start the test server in a separate thread"""
        def run_server():
            config = uvicorn.Config(
                app=app,
                host=self.host,
                port=self.port,
                log_level="error",  # Reduce noise in tests
                access_log=False
            )
            self.server = uvicorn.Server(config)
            asyncio.run(self.server.serve())
        
        self.thread = threading.Thread(target=run_server, daemon=True)
        self.thread.start()
        
        # Wait for server to start
        max_retries = 30
        for _ in range(max_retries):
            try:
                import requests
                response = requests.get(f"http://{self.host}:{self.port}/api/v1/health", timeout=1)
                if response.status_code == 200:
                    break
            except:
                pass
            time.sleep(0.1)
        else:
            raise RuntimeError("Test server failed to start")
    
    def stop(self):
        """Stop the test server"""
        if self.server:
            self.server.should_exit = True
        if self.thread:
            self.thread.join(timeout=5)
    
    @property
    def base_url(self):
        return f"http://{self.host}:{self.port}"


@pytest.fixture(scope="session")
def test_server():
    """Fixture to provide a test server for E2E tests"""
    server = TestServer()
    server.start()
    yield server
    server.stop()


# Use pytest-playwright's built-in fixtures
# These are automatically provided by pytest-playwright plugin


@pytest.fixture(scope="session")
def base_url(test_server):
    """Fixture to provide the base URL for tests"""
    return test_server.base_url


@pytest.fixture
async def authenticated_page(page, base_url):
    """Fixture to provide an authenticated page (if authentication is implemented)"""
    # For now, just return the page as-is
    # In the future, this could handle login flows
    yield page


class PageHelpers:
    """Helper methods for common page interactions"""
    
    def __init__(self, page):
        self.page = page
    
    async def wait_for_load_state(self, state="networkidle", timeout=30000):
        """Wait for page to reach a specific load state"""
        await self.page.wait_for_load_state(state, timeout=timeout)
    
    async def wait_for_selector_and_click(self, selector, timeout=10000):
        """Wait for selector to be visible and click it"""
        await self.page.wait_for_selector(selector, timeout=timeout)
        await self.page.click(selector)
    
    async def fill_form_field(self, selector, value, timeout=10000):
        """Fill a form field with a value"""
        await self.page.wait_for_selector(selector, timeout=timeout)
        await self.page.fill(selector, value)
    
    async def take_screenshot(self, name):
        """Take a screenshot for debugging"""
        await self.page.screenshot(path=f"tests/e2e/screenshots/{name}.png")
    
    async def check_accessibility(self):
        """Basic accessibility checks"""
        # Check for skip link
        skip_link = await self.page.query_selector('a[href="#main-content"]')
        assert skip_link is not None, "Skip link should be present"
        
        # Check for main landmark
        main_element = await self.page.query_selector('main')
        assert main_element is not None, "Main landmark should be present"
        
        # Check for heading hierarchy
        h1_elements = await self.page.query_selector_all('h1')
        assert len(h1_elements) >= 1, "Page should have at least one h1 element"
    
    async def check_responsive_design(self):
        """Check responsive design at different viewport sizes"""
        viewports = [
            {"width": 320, "height": 568},   # Mobile
            {"width": 768, "height": 1024},  # Tablet
            {"width": 1280, "height": 720},  # Desktop
        ]
        
        for viewport in viewports:
            await self.page.set_viewport_size(viewport)
            await self.page.wait_for_load_state("networkidle")
            
            # Check that navigation is accessible
            nav = await self.page.query_selector('nav')
            assert nav is not None, f"Navigation should be present at {viewport['width']}px"


@pytest.fixture
def page_helpers(page):
    """Fixture to provide page helper methods"""
    return PageHelpers(page)


# Test data fixtures
@pytest.fixture
def sample_regulation_data():
    """Sample regulation data for testing"""
    return {
        "title": "Test Regulation",
        "description": "A test regulation for E2E testing",
        "url": "https://example.com/test-regulation",
        "country": "US",
        "regulator": "Test Regulator"
    }


@pytest.fixture
def sample_calendar_event():
    """Sample calendar event data for testing"""
    return {
        "title": "Test Compliance Deadline",
        "description": "A test compliance deadline",
        "start_date": "2025-12-31",
        "end_date": "2025-12-31",
        "event_type": "deadline",
        "priority": "high"
    }


# Performance testing fixtures
@pytest.fixture
def performance_thresholds():
    """Performance thresholds for testing"""
    return {
        "page_load_time": 3000,  # 3 seconds
        "first_contentful_paint": 1500,  # 1.5 seconds
        "largest_contentful_paint": 2500,  # 2.5 seconds
        "cumulative_layout_shift": 0.1,
        "first_input_delay": 100  # 100ms
    }


# Setup and teardown
@pytest.fixture(autouse=True)
def setup_test_environment():
    """Setup test environment before each test"""
    # Create screenshots directory if it doesn't exist
    import os
    os.makedirs("tests/e2e/screenshots", exist_ok=True)

    yield

    # Cleanup after test
    # Any cleanup code can go here


# Pytest configuration
def pytest_configure(config):
    """Configure pytest for E2E tests"""
    config.addinivalue_line(
        "markers", "e2e: mark test as end-to-end test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "accessibility: mark test as accessibility test"
    )
    config.addinivalue_line(
        "markers", "performance: mark test as performance test"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers"""
    for item in items:
        # Add e2e marker to all tests in e2e directory
        if "e2e" in str(item.fspath):
            item.add_marker(pytest.mark.e2e)
