
import pytest
from translate import translate_text, detect_language, available_languages, get_supported_languages

def test_translate_text():
    """Test that text can be translated between languages."""
    input_text = "Hello"
    result = translate_text(input_text, source_lang="en", target_lang="es")
    assert result != input_text
    assert isinstance(result, str)
    
def test_detect_language():
    """Test language detection functionality."""
    result = detect_language("Hello world")
    assert isinstance(result, str)
    assert len(result) == 2  # ISO language code
    
    # Test with different language
    result_fr = detect_language("Bonjour le monde")
    assert isinstance(result_fr, str)
    
def test_available_languages():
    """Test that available languages function returns a dictionary."""
    langs = available_languages()
    assert isinstance(langs, dict)
    assert len(langs) > 0
    # Check that common languages are included
    assert "en" in langs
    assert "es" in langs
    assert "fr" in langs
    
def test_get_supported_languages():
    """Test that get_supported_languages returns a list of language codes."""
    langs = get_supported_languages()
    assert isinstance(langs, list)
    assert len(langs) > 0
    # Check that each language code is a string of length 2
    for lang in langs:
        assert isinstance(lang, str)
        assert len(lang) == 2
