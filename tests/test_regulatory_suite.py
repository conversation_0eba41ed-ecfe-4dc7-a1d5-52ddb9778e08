
"""
Comprehensive test suite for regulatory document parsing and relationship mapping.
This suite tests the integration of the various components.
"""
import os
import pytest
from unittest.mock import patch, MagicMock
import tempfile
import networkx as nx

from app.utils.regulatory_parser import RegulatoryParser
from app.utils.relationship_mapper import RelationshipMapper
from app.utils.pdf_analyzer import (
    extract_text_from_pdf,
    identify_regulatory_sections,
    extract_entities,
    analyze_pdf_document
)

class TestRegulatoryDocumentSuite:
    """Test suite for regulatory document parsing and analysis."""
    
    @pytest.fixture
    def sample_regulatory_text(self):
        """Provide sample regulatory text for testing."""
        return """
        Article 1. Scope
        This regulation applies to all organizations processing personal data.
        
        Article 2. Requirements
        2.1. Organizations shall implement appropriate security measures.
        2.2. Data breaches must be reported within 72 hours.
        
        Section 3. Penalties
        3.1. Violations may result in fines up to $10,000,000 or 2% of annual revenue.
        3.2. Serious violations may result in imprisonment of up to 5 years.
        """
    
    @pytest.fixture
    def sample_documents(self):
        """Provide sample document analysis results for testing."""
        return [
            {
                "id": "doc1",
                "title": "General Data Protection Regulation",
                "file_name": "gdpr.pdf",
                "confidence_score": 0.95,
                "sections": {
                    "articles": [
                        "Article 1: This Regulation protects personal data of individuals.",
                        "Article 2: This Regulation applies to all organizations."
                    ],
                    "sections": []
                },
                "requirements": [
                    {
                        "text": "Organizations shall implement appropriate security measures.",
                        "modal_verb": "shall",
                        "subject": "Organizations",
                        "action": "implement",
                        "priority": "high"
                    },
                    {
                        "text": "Data breaches must be reported within 72 hours.",
                        "modal_verb": "must",
                        "subject": "breaches",
                        "action": "reported",
                        "priority": "high"
                    }
                ],
                "entities": {
                    "organizations": ["Data Protection Authority"],
                    "regulatory_terms": ["personal data", "data protection"]
                }
            },
            {
                "id": "doc2",
                "title": "GDPR Implementation Act",
                "file_name": "gdpr_implementation.pdf",
                "confidence_score": 0.85,
                "sections": {
                    "articles": [
                        "Article 1: This Act implements the General Data Protection Regulation.",
                        "Article 3: Refer to Article 2 of the GDPR for scope."
                    ],
                    "sections": []
                },
                "requirements": [
                    {
                        "text": "The data protection officer must be independent.",
                        "modal_verb": "must",
                        "subject": "officer",
                        "action": "be",
                        "priority": "high"
                    }
                ],
                "entities": {
                    "organizations": ["Data Protection Authority", "Ministry of Justice"],
                    "regulatory_terms": ["data protection officer", "data protection"]
                }
            }
        ]
    
    @pytest.fixture
    def regulatory_parser(self):
        """Initialize a RegulatoryParser instance for testing."""
        return RegulatoryParser()
    
    @pytest.fixture
    def relationship_mapper(self):
        """Initialize a RelationshipMapper instance for testing."""
        return RelationshipMapper()
    
    def test_regulatory_parser_extraction(self, regulatory_parser, sample_regulatory_text):
        """Test regulatory parser extraction capabilities."""
        # Test section identification
        sections = regulatory_parser.identify_regulatory_sections(sample_regulatory_text)
        assert len(sections["articles"]) == 2
        assert "Article 1" in sections["articles"][0]
        assert "Article 2" in sections["articles"][1]
        
        # Test entity extraction
        entities = regulatory_parser.extract_entities(sample_regulatory_text)
        assert "regulatory_terms" in entities
        
        # Test requirements extraction
        requirements = regulatory_parser.extract_requirements(sample_regulatory_text)
        assert len(requirements) >= 2
        assert any("implement" in req["text"] for req in requirements)
        
        # Test penalties extraction
        penalties = regulatory_parser.extract_penalties(sample_regulatory_text)
        assert len(penalties) >= 1
        assert any("$10,000,000" in penalty["text"] for penalty in penalties)
    
    def test_relationship_mapping(self, relationship_mapper, sample_documents):
        """Test relationship mapping between documents."""
        result = relationship_mapper.map_document_relationships(sample_documents)
        
        # Verify result structure
        assert "documents" in result
        assert "hierarchical_relationships" in result
        assert "cross_references" in result
        assert "framework_mappings" in result
        
        # Verify documents are included
        assert len(result["documents"]) == 2
    
    @patch('app.utils.pdf_analyzer.PyPDF2.PdfReader')
    def test_pdf_analysis_integration(self, mock_pdf_reader, regulatory_parser, sample_regulatory_text, tmp_path):
        """Test integration of PDF analysis with regulatory parser."""
        # Create mock PDF reader
        mock_reader = MagicMock()
        mock_page = MagicMock()
        mock_page.extract_text.return_value = sample_regulatory_text
        mock_reader.pages = [mock_page]
        mock_pdf_reader.return_value = mock_reader
        
        # Create temp file
        pdf_path = os.path.join(tmp_path, "test.pdf")
        with open(pdf_path, 'wb') as f:
            f.write(b'Dummy PDF content')
        
        # Test extraction and analysis
        text = regulatory_parser.extract_text_from_pdf(pdf_path)
        analysis = regulatory_parser.analyze_regulatory_text(text)
        
        # Verify analysis results
        assert "sections" in analysis
        assert "entities" in analysis
        assert "requirements" in analysis
        assert "penalties" in analysis
        assert "confidence_score" in analysis
        assert analysis["confidence_score"] > 0.5
    
    def test_end_to_end_document_processing(self, regulatory_parser, relationship_mapper, 
                                            sample_regulatory_text, tmp_path):
        """Test end-to-end document processing and relationship mapping."""
        # Mock document extraction and analysis
        documents = []
        
        with patch.object(regulatory_parser, 'extract_text_from_pdf', 
                         return_value=sample_regulatory_text):
            # Create two sample documents
            for i in range(2):
                pdf_path = os.path.join(tmp_path, f"test{i}.pdf")
                with open(pdf_path, 'wb') as f:
                    f.write(b'Dummy PDF content')
                
                # Parse document
                doc = regulatory_parser.parse_document(pdf_path)
                doc["id"] = f"doc{i}"
                doc["title"] = f"Regulation {i}"
                doc["file_name"] = f"test{i}.pdf"
                documents.append(doc)
        
        # Test relationship mapping
        result = relationship_mapper.map_document_relationships(documents)
        
        # Verify mapping results
        assert len(result["documents"]) == 2
        # The documents may or may not have relationships depending on content
        assert "hierarchical_relationships" in result
        assert "cross_references" in result
        assert "framework_mappings" in result
    
    def test_compliance_mapping(self, relationship_mapper, sample_documents):
        """Test mapping regulatory requirements to compliance frameworks."""
        # Add test requirements with keywords that should match frameworks
        sample_documents[0]["requirements"].append({
            "text": "Organizations shall implement strong encryption for all data transfers.",
            "modal_verb": "shall",
            "subject": "Organizations",
            "action": "implement",
            "priority": "high"
        })
        
        sample_documents[0]["requirements"].append({
            "text": "All system access must be logged and monitored for suspicious activity.",
            "modal_verb": "must",
            "subject": "system access",
            "action": "logged",
            "priority": "high"
        })
        
        # Map relationships
        result = relationship_mapper.map_document_relationships(sample_documents)
        
        # Verify framework mappings exist
        all_mappings = []
        for framework, mappings in result["framework_mappings"].items():
            all_mappings.extend(mappings)
        
        # We should have at least some mappings given our test data
        assert len(all_mappings) > 0
