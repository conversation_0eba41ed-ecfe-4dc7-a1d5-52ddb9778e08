
"""Tests for the Regulatory Alerts API endpoints."""
import pytest
from fastapi.testclient import TestClient
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

from app.main import app
from app.db.models import RegulationURL, Regulator, Country, AlertSubscription

client = TestClient(app)

class TestAlertsAPI:
    @patch('app.db.database.get_db')
    def test_get_recent_alerts(self, mock_get_db):
        """Test getting recent alerts."""
        # Setup mock data
        mock_session = MagicMock()
        mock_get_db.return_value.__enter__.return_value = mock_session
        
        # Create mock alert data
        country = MagicMock(id=1, code='US', name='United States')
        regulator = MagicMock(id=1, name='SEC', country=country)
        
        mock_alerts = [
            MagicMock(
                id=1, 
                title='New Regulation', 
                url='https://example.com/reg1',
                importance=8,
                discovery_date=datetime.now() - timedelta(days=5),
                regulator=regulator
            ),
            MagicMock(
                id=2, 
                title='Updated Policy', 
                url='https://example.com/reg2',
                importance=6,
                discovery_date=datetime.now() - timedelta(days=10),
                regulator=regulator
            )
        ]
        
        # Setup query mock
        mock_query = MagicMock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.join.return_value = mock_query
        mock_query.count.return_value = len(mock_alerts)
        mock_query.order_by.return_value.offset.return_value.limit.return_value.all.return_value = mock_alerts
        
        # Test endpoint
        response = client.get("/api/v1/alerts/recent")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert data[0]["id"] == 1
        assert data[0]["importance"] == 8
        assert data[0]["country_code"] == "US"
        
        # Test with filters
        response = client.get("/api/v1/alerts/recent?country_code=US&importance=7")
        assert response.status_code == 200
    
    @patch('app.db.database.get_db')
    def test_subscribe_to_alerts(self, mock_get_db):
        """Test creating an alert subscription."""
        # Setup mock data
        mock_session = MagicMock()
        mock_get_db.return_value.__enter__.return_value = mock_session
        
        # Setup add and commit mocks
        mock_session.add = MagicMock()
        mock_session.commit = MagicMock()
        mock_session.refresh = MagicMock()
        
        # Create test subscription data
        subscription_data = {
            "webhook_url": "https://example.com/webhook",
            "countries": ["US", "GB"],
            "min_importance": 5,
            "description": "Test subscription"
        }
        
        # Mock the new subscription
        mock_new_sub = MagicMock(
            id=1,
            webhook_url="https://example.com/webhook",
            countries="US,GB",
            min_importance=5,
            description="Test subscription",
            created_at=datetime.now()
        )
        
        # Set the refresh side effect to populate id
        def refresh_side_effect(obj):
            obj.id = 1
            obj.created_at = datetime.now()
        
        mock_session.refresh.side_effect = refresh_side_effect
        
        # Test endpoint
        response = client.post("/api/v1/alerts/subscribe", json=subscription_data)
        assert response.status_code == 201
        assert response.json()["webhook_url"] == "https://example.com/webhook"
        assert "id" in response.json()
        
        # Verify session methods were called
        assert mock_session.add.called
        assert mock_session.commit.called
        assert mock_session.refresh.called
    
    @patch('app.db.database.get_db')
    def test_delete_subscription(self, mock_get_db):
        """Test deleting an alert subscription."""
        # Setup mock data
        mock_session = MagicMock()
        mock_get_db.return_value.__enter__.return_value = mock_session
        
        # Mock the subscription query
        mock_sub = MagicMock(id=1)
        mock_query = MagicMock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_sub
        
        # Test successful deletion
        response = client.delete("/api/v1/alerts/subscriptions/1")
        assert response.status_code == 204
        
        # Verify delete and commit were called
        assert mock_session.delete.called
        assert mock_session.commit.called
        
        # Test subscription not found
        mock_query.first.return_value = None
        response = client.delete("/api/v1/alerts/subscriptions/999")
        assert response.status_code == 404
