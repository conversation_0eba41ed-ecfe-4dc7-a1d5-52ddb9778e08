
import pytest
from fastapi.testclient import TestClient
from app.main import app
from unittest.mock import patch, MagicMock

client = TestClient(app)

class TestAPIEndpoints:
    def test_health_check(self):
        """Test the health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        assert response.json() == {"status": "ok"}
    
    @patch('app.main.get_db_status')
    def test_health_check_detailed(self, mock_db_status):
        """Test the detailed health check endpoint."""
        # Mock database status
        mock_db_status.return_value = {"status": "connected", "version": "SQLite 3.x"}
        
        response = client.get("/health/detailed")
        assert response.status_code == 200
        assert "database" in response.json()
        assert response.json()["database"]["status"] == "connected"
    
    @patch('app.main.get_countries')
    def test_get_countries(self, mock_get_countries):
        """Test the countries endpoint."""
        # Mock countries data
        mock_countries = [
            {"id": 1, "name": "United States", "code": "US"},
            {"id": 2, "name": "Canada", "code": "CA"},
            {"id": 3, "name": "United Kingdom", "code": "GB"}
        ]
        mock_get_countries.return_value = mock_countries
        
        response = client.get("/api/v1/countries")
        assert response.status_code == 200
        assert response.json() == mock_countries
    
    @patch('app.main.get_country_by_id')
    def test_get_country_by_id(self, mock_get_country):
        """Test retrieving a specific country by ID."""
        # Mock country data
        mock_country = {"id": 1, "name": "United States", "code": "US"}
        mock_get_country.return_value = mock_country
        
        response = client.get("/api/v1/countries/1")
        assert response.status_code == 200
        assert response.json() == mock_country
    
    @patch('app.main.get_country_by_id')
    def test_get_country_not_found(self, mock_get_country):
        """Test behavior when country is not found."""
        # Mock country not found
        mock_get_country.return_value = None
        
        response = client.get("/api/v1/countries/999")
        assert response.status_code == 404
        assert "detail" in response.json()
    
    @patch('app.main.create_country')
    def test_create_country(self, mock_create):
        """Test country creation endpoint."""
        # Mock create country
        new_country = {"name": "Germany", "code": "DE"}
        created_country = {"id": 4, "name": "Germany", "code": "DE"}
        mock_create.return_value = created_country
        
        response = client.post("/api/v1/countries", json=new_country)
        assert response.status_code == 201
        assert response.json() == created_country
    
    @patch('app.main.update_country')
    def test_update_country(self, mock_update):
        """Test country update endpoint."""
        # Mock update country
        updated_data = {"name": "Federal Republic of Germany"}
        updated_country = {"id": 4, "name": "Federal Republic of Germany", "code": "DE"}
        mock_update.return_value = updated_country
        
        response = client.patch("/api/v1/countries/4", json=updated_data)
        assert response.status_code == 200
        assert response.json() == updated_country
    
    @patch('app.main.delete_country')
    def test_delete_country(self, mock_delete):
        """Test country deletion endpoint."""
        # Mock successful deletion
        mock_delete.return_value = True
        
        response = client.delete("/api/v1/countries/4")
        assert response.status_code == 204
        
        # Test deletion failure
        mock_delete.return_value = False
        
        response = client.delete("/api/v1/countries/999")
        assert response.status_code == 404
