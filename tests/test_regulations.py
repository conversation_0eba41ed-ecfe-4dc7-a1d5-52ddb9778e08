
"""Test cases for the regulations module."""
import pytest
from app.api import (
    get_regulations_by_category,
    get_all_regulations,
    get_categories,
    REGULATIONS
)


def test_get_regulations_by_category() -> None:
    """Test retrieving regulations by category."""
    # Test valid category
    privacy_regs = get_regulations_by_category("privacy_data_protection")
    assert isinstance(privacy_regs, list)
    assert len(privacy_regs) > 0
    assert all(isinstance(url, str) for url in privacy_regs)
    
    # Test invalid category
    invalid_regs = get_regulations_by_category("non_existent_category")
    assert isinstance(invalid_regs, list)
    assert len(invalid_regs) == 0


def test_get_all_regulations() -> None:
    """Test retrieving all regulations."""
    all_regs = get_all_regulations()
    assert isinstance(all_regs, list)
    
    # Count total regulations in the dictionary
    total_regs = sum(len(regs) for regs in REGULATIONS.values())
    assert len(all_regs) == total_regs
    
    # Check that all entries are strings
    assert all(isinstance(url, str) for url in all_regs)


def test_get_categories() -> None:
    """Test retrieving all regulation categories."""
    categories = get_categories()
    assert isinstance(categories, list)
    assert set(categories) == set(REGULATIONS.keys())
    assert len(categories) > 0
