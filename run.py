
#!/usr/bin/env python3
"""
Script to run the FastAPI application
"""
import uvicorn
import logging
import sys
import time
import os
from threading import Thread
import webbrowser
import argparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

def run_api_server():
    """Run the FastAPI server"""
    try:
        # Always bind to 0.0.0.0 for Replit
        host = "0.0.0.0"
        port = 8000

        logger.info(f"Starting FastAPI server on {host}:{port}")
        logger.info(f"API documentation will be available at: http://{host}:{port}/docs")
        logger.info(f"Alternative API documentation at: http://{host}:{port}/redoc")

        # Import app here to avoid circular imports
        from app.main import app
        
        # Configure uvicorn with explicit settings
        config = uvicorn.Config(
            app=app,
            host=host,
            port=port,
            log_level="info",
            reload=True
        )
        
        server = uvicorn.Server(config)
        server.run()
        
    except Exception as e:
        logger.error(f"Critical error starting API server: {str(e)}", exc_info=True)
        sys.exit(1)

def take_screenshot():
    """Take a screenshot of the web interface using Playwright"""
    try:
        import asyncio
        from playwright.async_api import async_playwright
        
        async def capture_screenshot():
            logger.info("Taking screenshot of the web interface...")
            async with async_playwright() as p:
                browser = await p.chromium.launch()
                page = await browser.new_page()
                await page.goto("http://0.0.0.0:8000/")
                # Wait for page to fully load
                await page.wait_for_load_state("networkidle")
                # Additional wait to ensure everything is rendered
                await asyncio.sleep(2)
                # Create screenshots directory if it doesn't exist
                os.makedirs("screenshots", exist_ok=True)
                # Take screenshot and save
                await page.screenshot(path="screenshots/dashboard_screenshot.png", full_page=True)
                logger.info("Screenshot saved to screenshots/dashboard_screenshot.png")
                await browser.close()
        
        # Run the async function
        asyncio.run(capture_screenshot())
    except ImportError:
        logger.warning("Playwright not installed. To take screenshots, install with: pip install playwright")
        logger.warning("Then run: playwright install chromium")
    except Exception as e:
        logger.error(f"Error taking screenshot: {str(e)}")

def open_browser():
    """Open browser to view the application"""
    time.sleep(2)  # Wait for the server to start
    webbrowser.open("http://0.0.0.0:8000/")
    logger.info("Browser opened to application URL")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run the RegulationGuru application")
    parser.add_argument("--screenshot", action="store_true", help="Take a screenshot of the interface")
    parser.add_argument("--no-browser", action="store_true", help="Don't open browser automatically")
    
    args = parser.parse_args()
    
    # Start the server in a thread
    server_thread = Thread(target=run_api_server, daemon=True)
    server_thread.start()
    
    # Open browser unless specified not to
    if not args.no_browser:
        browser_thread = Thread(target=open_browser)
        browser_thread.start()
    
    # Take screenshot if requested
    if args.screenshot:
        # Wait for the server and browser to start
        time.sleep(5)
        take_screenshot()
        
    # Keep the main thread running
    try:
        while server_thread.is_alive():
            time.sleep(0.1)
    except KeyboardInterrupt:
        logger.info("Server shutdown requested. Exiting.")
        sys.exit(0)
