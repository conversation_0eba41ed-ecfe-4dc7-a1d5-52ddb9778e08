[tool.poetry]
name = "python-template"
version = "0.1.0"
description = ""
authors = ["Your Name <<EMAIL>>"]

[tool.poetry.dependencies]
python = ">=3.10.0,<3.12"
fastapi = "^0.112.0"
uvicorn = "0.23.2"
pytest = "^8.3.4"
pytest-cov = "^6.0.0"
selenium = "^4.29.0"
requests = "^2.32.3"
httpx = "^0.28.1"
pytest-xdist = "^3.6.1"
pydantic = "^2.10.6"
python-dotenv = "^1.0.1"
sqlalchemy = "^2.0.38"
babel = "^2.17.0"
folium = "^0.19.4"
jinja2 = "^3.1.6"
pandas = "^2.2.3"
beautifulsoup4 = "4.12.3"
psycopg2-binary = "^2.9.10"
psutil = "^7.0.0"
playwright = "^1.50.0"
python-i18n = "^0.3.9"

[tool.pyright]
# https://github.com/microsoft/pyright/blob/main/docs/configuration.md
useLibraryCodeForTypes = true
exclude = [".cache"]

[tool.ruff]
# https://beta.ruff.rs/docs/configuration/
select = ['E', 'W', 'F', 'I', 'B', 'C4', 'ARG', 'SIM']
ignore = ['W291', 'W292', 'W293']

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"