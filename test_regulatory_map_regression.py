#!/usr/bin/env python3
"""Regression tests for Enhanced Regulatory Map feature."""
import sys
import os
sys.path.append('.')

from datetime import datetime
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.db.models import (
    Base, RegulatoryEntity, RegulatoryRelationship, RegulatoryComplianceStatus,
    EntityType, RelationshipType, ComplianceStatusEnum, ImplementationStatusEnum
)

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_regulatory_map_regression.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def setup_database():
    """Set up test database."""
    Base.metadata.create_all(bind=engine)

def cleanup_database():
    """Clean up test database."""
    Base.metadata.drop_all(bind=engine)
    if os.path.exists("./test_regulatory_map_regression.db"):
        os.remove("./test_regulatory_map_regression.db")

def test_database_schema_integrity():
    """Test that the new tables don't break existing schema."""
    print("Testing database schema integrity...")
    
    db = TestingSessionLocal()
    try:
        # Test that all tables exist
        tables = [
            'regulatory_entities',
            'regulatory_relationships', 
            'regulatory_compliance_status',
            'regulatory_views',
            'regulatory_annotations'
        ]
        
        for table in tables:
            result = db.execute(text(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'"))
            assert result.fetchone() is not None, f"Table {table} does not exist"
        
        print("✓ Database schema integrity test passed")
    finally:
        db.close()

def test_foreign_key_constraints():
    """Test foreign key constraints work correctly."""
    print("Testing foreign key constraints...")
    
    db = TestingSessionLocal()
    try:
        # Create parent entity
        parent = RegulatoryEntity(
            name="Parent Entity",
            type=EntityType.REGULATION,
            jurisdiction="US"
        )
        db.add(parent)
        db.commit()
        db.refresh(parent)
        
        # Create child entity with valid parent_id
        child = RegulatoryEntity(
            name="Child Entity",
            type=EntityType.REQUIREMENT,
            parent_id=parent.id,
            jurisdiction="US"
        )
        db.add(child)
        db.commit()
        db.refresh(child)
        
        # Verify relationship works
        assert child.parent_id == parent.id
        assert child.parent == parent
        
        # Create relationship between entities
        relationship = RegulatoryRelationship(
            source_id=parent.id,
            target_id=child.id,
            relationship_type=RelationshipType.CONTAINS
        )
        db.add(relationship)
        db.commit()
        db.refresh(relationship)
        
        # Verify relationship references work
        assert relationship.source_entity == parent
        assert relationship.target_entity == child
        
        print("✓ Foreign key constraints test passed")
    finally:
        db.close()

def test_data_consistency():
    """Test data consistency across related tables."""
    print("Testing data consistency...")
    
    db = TestingSessionLocal()
    try:
        # Create entity
        entity = RegulatoryEntity(
            name="Consistency Test Entity",
            type=EntityType.REGULATION,
            jurisdiction="US"
        )
        db.add(entity)
        db.commit()
        db.refresh(entity)
        
        # Create compliance status
        status = RegulatoryComplianceStatus(
            entity_id=entity.id,
            compliance_status=ComplianceStatusEnum.COMPLIANT,
            assessment_date=datetime.utcnow()
        )
        db.add(status)
        db.commit()
        db.refresh(status)
        
        # Verify consistency
        assert status.entity == entity
        assert entity in [s.entity for s in db.query(RegulatoryComplianceStatus).all()]
        
        # Test soft delete doesn't break relationships
        entity.soft_delete()
        db.commit()
        
        # Status should still reference the entity
        db.refresh(status)
        assert status.entity == entity
        assert status.entity.is_deleted == True
        
        print("✓ Data consistency test passed")
    finally:
        db.close()

def test_performance_with_large_dataset():
    """Test performance with a larger dataset."""
    print("Testing performance with large dataset...")
    
    db = TestingSessionLocal()
    try:
        start_time = datetime.utcnow()
        
        # Create 100 entities
        entities = []
        for i in range(100):
            entity = RegulatoryEntity(
                name=f"Entity {i}",
                type=EntityType.REGULATION if i % 2 == 0 else EntityType.REQUIREMENT,
                jurisdiction="US" if i % 3 == 0 else "EU",
                status="active"
            )
            entities.append(entity)
        
        db.add_all(entities)
        db.commit()

        # Refresh entities to get their IDs
        for entity in entities:
            db.refresh(entity)

        # Create relationships between entities
        relationships = []
        for i in range(0, 99, 2):
            relationship = RegulatoryRelationship(
                source_id=entities[i].id,
                target_id=entities[i + 1].id,
                relationship_type=RelationshipType.CONTAINS
            )
            relationships.append(relationship)

        db.add_all(relationships)
        db.commit()
        
        # Test query performance
        query_start = datetime.utcnow()
        
        # Query all entities
        all_entities = db.query(RegulatoryEntity).filter(
            RegulatoryEntity.is_deleted == False
        ).all()
        print(f"  Found {len(all_entities)} entities (expected 100)")

        # Debug: check if entities were created properly
        total_entities = db.query(RegulatoryEntity).count()
        print(f"  Total entities in database: {total_entities}")

        assert len(all_entities) >= 100, f"Expected at least 100 entities, got {len(all_entities)}"

        # Query with filters
        us_entities = db.query(RegulatoryEntity).filter(
            RegulatoryEntity.jurisdiction == "US",
            RegulatoryEntity.is_deleted == False
        ).all()
        assert len(us_entities) > 0

        # Query relationships
        all_relationships = db.query(RegulatoryRelationship).filter(
            RegulatoryRelationship.is_deleted == False
        ).all()
        print(f"  Found {len(all_relationships)} relationships")
        assert len(all_relationships) >= 49, f"Expected at least 49 relationships, got {len(all_relationships)}"
        
        query_end = datetime.utcnow()
        query_time = (query_end - query_start).total_seconds()
        
        end_time = datetime.utcnow()
        total_time = (end_time - start_time).total_seconds()
        
        print(f"  Created 100 entities and 49 relationships in {total_time:.2f}s")
        print(f"  Query performance: {query_time:.2f}s")
        
        # Performance should be reasonable (less than 5 seconds for this test)
        assert total_time < 5.0, f"Performance test failed: took {total_time:.2f}s"
        assert query_time < 1.0, f"Query performance test failed: took {query_time:.2f}s"
        
        print("✓ Performance test passed")
    finally:
        db.close()

def test_concurrent_operations():
    """Test concurrent operations don't cause issues."""
    print("Testing concurrent operations...")
    
    db1 = TestingSessionLocal()
    db2 = TestingSessionLocal()
    
    try:
        # Create entity in first session
        entity1 = RegulatoryEntity(
            name="Concurrent Entity 1",
            type=EntityType.REGULATION,
            jurisdiction="US"
        )
        db1.add(entity1)
        db1.commit()
        db1.refresh(entity1)
        
        # Create entity in second session
        entity2 = RegulatoryEntity(
            name="Concurrent Entity 2",
            type=EntityType.REQUIREMENT,
            jurisdiction="US"
        )
        db2.add(entity2)
        db2.commit()
        db2.refresh(entity2)
        
        # Create relationship using both entities
        relationship = RegulatoryRelationship(
            source_id=entity1.id,
            target_id=entity2.id,
            relationship_type=RelationshipType.REFERENCES
        )
        db1.add(relationship)
        db1.commit()
        db1.refresh(relationship)
        
        # Verify from second session
        db2_relationship = db2.query(RegulatoryRelationship).filter(
            RegulatoryRelationship.id == relationship.id
        ).first()
        assert db2_relationship is not None
        
        print("✓ Concurrent operations test passed")
    finally:
        db1.close()
        db2.close()

def test_edge_cases():
    """Test edge cases and boundary conditions."""
    print("Testing edge cases...")
    
    db = TestingSessionLocal()
    try:
        # Test entity with minimal data
        minimal_entity = RegulatoryEntity(
            name="Minimal",
            type=EntityType.REGULATION
        )
        db.add(minimal_entity)
        db.commit()
        db.refresh(minimal_entity)
        
        assert minimal_entity.id is not None
        assert minimal_entity.name == "Minimal"
        assert minimal_entity.jurisdiction is None
        
        # Test entity with maximum length strings
        max_entity = RegulatoryEntity(
            name="A" * 255,  # Maximum name length
            description="B" * 1000,  # Long description
            type=EntityType.REGULATION,
            jurisdiction="C" * 100,  # Maximum jurisdiction length
            status="D" * 50,  # Maximum status length
            version="E" * 50,  # Maximum version length
            source_url="https://example.com/" + "F" * 2000,  # Long URL
            external_id="G" * 255  # Maximum external ID length
        )
        db.add(max_entity)
        db.commit()
        db.refresh(max_entity)
        
        assert len(max_entity.name) == 255
        assert len(max_entity.jurisdiction) == 100
        
        # Test relationship with edge values
        edge_relationship = RegulatoryRelationship(
            source_id=minimal_entity.id,
            target_id=max_entity.id,
            relationship_type=RelationshipType.CONTAINS,
            strength=0.0  # Minimum strength
        )
        db.add(edge_relationship)
        db.commit()
        db.refresh(edge_relationship)
        
        assert edge_relationship.strength == 0.0
        
        # Test relationship with maximum strength
        max_relationship = RegulatoryRelationship(
            source_id=max_entity.id,
            target_id=minimal_entity.id,
            relationship_type=RelationshipType.REFERENCES,
            strength=1.0  # Maximum strength
        )
        db.add(max_relationship)
        db.commit()
        db.refresh(max_relationship)
        
        assert max_relationship.strength == 1.0
        
        print("✓ Edge cases test passed")
    finally:
        db.close()

def test_data_migration_compatibility():
    """Test that new schema is compatible with potential data migrations."""
    print("Testing data migration compatibility...")
    
    db = TestingSessionLocal()
    try:
        # Simulate existing data that might exist before migration
        entities = []
        for i in range(10):
            entity = RegulatoryEntity(
                name=f"Migration Test Entity {i}",
                type=EntityType.REGULATION,
                jurisdiction="US",
                status="active",
                created_at=datetime.utcnow(),
                changed_on=datetime.utcnow(),
                is_deleted=False
            )
            entities.append(entity)
        
        db.add_all(entities)
        db.commit()
        
        # Test that all timestamp fields are properly set
        for entity in entities:
            db.refresh(entity)
            assert entity.created_at is not None
            assert entity.changed_on is not None
            assert entity.is_deleted == False
            assert entity.deleted_at is None
        
        # Test soft delete migration scenario
        entities[0].soft_delete()
        db.commit()
        db.refresh(entities[0])
        
        assert entities[0].is_deleted == True
        assert entities[0].deleted_at is not None
        
        # Test that queries still work correctly
        active_entities = db.query(RegulatoryEntity).filter(
            RegulatoryEntity.is_deleted == False
        ).all()
        print(f"  Found {len(active_entities)} active entities from migration test")

        # Count only the entities we created in this test
        migration_entities = [e for e in active_entities if "Migration Test Entity" in e.name]
        assert len(migration_entities) == 9, f"Expected 9 migration test entities, got {len(migration_entities)}"
        
        print("✓ Data migration compatibility test passed")
    finally:
        db.close()

def main():
    """Run all regression tests."""
    print("🔄 Starting Enhanced Regulatory Map Regression Tests")
    print("=" * 60)
    
    try:
        # Setup
        setup_database()
        
        # Run regression tests
        test_database_schema_integrity()
        test_foreign_key_constraints()
        test_data_consistency()
        test_performance_with_large_dataset()
        test_concurrent_operations()
        test_edge_cases()
        test_data_migration_compatibility()
        
        print("=" * 60)
        print("🎉 All regression tests passed successfully!")
        print("✅ Enhanced Regulatory Map is ready for production!")
        
    except Exception as e:
        print(f"❌ Regression test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        # Cleanup
        cleanup_database()
    
    return 0

if __name__ == "__main__":
    exit(main())
