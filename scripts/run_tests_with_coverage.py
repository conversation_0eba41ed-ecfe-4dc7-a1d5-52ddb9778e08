
#!/usr/bin/env python3
"""Script to run tests with coverage and generate reports."""

import os
import sys
import subprocess
from pathlib import Path


def run_tests_with_coverage():
    """Run the tests with coverage and generate reports."""
    print("Running tests with coverage...")

    # Make sure the coverage directory exists
    os.makedirs("coverage/html", exist_ok=True)

    # Run pytest with coverage
    cmd = [
        "python", "-m", "pytest",
        "--cov=.",
        "--cov-report=term",
        "--cov-report=html:coverage/html",
        "--cov-report=xml:coverage/coverage.xml",
        "--cov-config=.coveragerc",
        "-v"
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    # Print the output
    print("\nTest results:")
    print(result.stdout)
    
    if result.stderr:
        print("Errors:", file=sys.stderr)
        print(result.stderr, file=sys.stderr)
    
    # Check if tests passed
    if result.returncode != 0:
        print(f"Tests failed with return code {result.returncode}")
        return result.returncode
    
    # Extract and display overall coverage information
    try:
        coverage_info = {}
        
        # Parse coverage data from stdout
        for line in result.stdout.split('\n'):
            if "TOTAL" in line and "%" in line:
                parts = line.split()
                for part in parts:
                    if "%" in part:
                        overall_coverage = part.strip()
                        coverage_info["overall"] = overall_coverage
                        break
        
        # Display coverage summary
        if coverage_info:
            print("\nCoverage Summary:")
            print(f"Overall coverage: {coverage_info.get('overall', 'N/A')}")
            
            # Check coverage of main modules
            modules = ["main.py", "models.py", "schemas.py", "database.py", "regulations.py", "worldmap.py"]
            print("\nCoverage by main module:")
            
            for module in modules:
                html_file = f"coverage/html/{module.replace('.', '_')}.html"
                if os.path.exists(html_file):
                    # Extract coverage percentage from HTML title
                    with open(html_file, 'r') as f:
                        content = f.read()
                        import re
                        match = re.search(r'<title>Coverage for .+?: (\d+)%</title>', content)
                        if match:
                            module_coverage = match.group(1) + "%"
                            coverage_info[module] = module_coverage
                            print(f"  {module}: {module_coverage}")
                        else:
                            print(f"  {module}: Could not extract coverage")
                else:
                    print(f"  {module}: No coverage data")
            
            # Write coverage data to a JSON file for the health check endpoint
            import json
            with open("coverage/coverage_summary.json", "w") as f:
                json.dump(coverage_info, f, indent=2)
                
        # Open coverage report in browser if available
        index_path = Path("coverage/html/index.html")
        if index_path.exists():
            print("\nOpening coverage report in browser...")
            try:
                import webbrowser
                webbrowser.open(index_path.as_uri())
            except Exception as e:
                print(f"Could not open browser: {e}")
                print(f"You can manually open the report at: {index_path.absolute()}")
    
    except Exception as e:
        print(f"Error processing coverage data: {e}")
    
    return 0


if __name__ == "__main__":
    sys.exit(run_tests_with_coverage())
