
#!/usr/bin/env python3
"""
Script to kill all user processes in the Replit environment.
Use with caution as it will terminate all running Python processes.
"""
import os
import signal
import psutil
import logging
import sys

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

def kill_processes():
    """Kill all Python processes owned by the current user except this script."""
    current_pid = os.getpid()
    user_id = os.getuid()
    killed_count = 0
    
    logger.info("Starting process termination...")
    
    for proc in psutil.process_iter(['pid', 'name', 'username', 'cmdline']):
        try:
            # Skip the current process
            if proc.info['pid'] == current_pid:
                continue
                
            # Focus on Python processes and uvicorn servers
            if (('python' in proc.info['name'].lower() or 
                 'uvicorn' in ' '.join(proc.info['cmdline'] or []).lower()) and
                proc.info['pid'] != current_pid):
                
                logger.info(f"Terminating process: {proc.info['pid']} - {proc.info['name']} - {' '.join(proc.info['cmdline'] or [])}")
                
                # Send SIGTERM first for graceful shutdown
                os.kill(proc.info['pid'], signal.SIGTERM)
                killed_count += 1
                
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess) as e:
            logger.warning(f"Error accessing process {proc.info['pid']}: {e}")
            continue
    
    logger.info(f"Process termination complete. Terminated {killed_count} processes.")
    return killed_count

if __name__ == "__main__":
    try:
        count = kill_processes()
        print(f"Successfully terminated {count} processes.")
    except Exception as e:
        logger.error(f"Failed to terminate processes: {e}", exc_info=True)
        sys.exit(1)
