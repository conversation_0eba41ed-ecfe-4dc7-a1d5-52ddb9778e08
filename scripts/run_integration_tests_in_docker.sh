#!/bin/bash

# Run integration tests in Docker
echo "Running integration tests in Docker..."

# Start the backend and database containers
docker-compose up -d regulationguru_api regulationguru_db

# Wait for the backend to be ready
echo "Waiting for backend to be ready..."
sleep 15

# Run the frontend tests in a separate container
docker run --rm \
  --network RegulationGuru_Network \
  -v $(pwd)/frontend:/app \
  -w /app \
  node:14 \
  npm test -- --coverage

# Capture the test result
TEST_RESULT=$?

# Stop the containers
echo "Stopping containers..."
docker-compose down

# Exit with the test result
exit $TEST_RESULT
