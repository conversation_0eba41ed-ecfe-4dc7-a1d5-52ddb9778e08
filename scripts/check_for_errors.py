
#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to check for common errors in the codebase, especially in areas with low test coverage.
"""
import importlib
import inspect
import os
import sys
import traceback
from types import ModuleType
from typing import Dict, List, Set, Tuple

# Add the root directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def check_imports(module_name: str) -> List[str]:
    """Check if a module can be imported without errors."""
    errors = []
    try:
        importlib.import_module(module_name)
    except Exception as e:
        errors.append(f"Error importing {module_name}: {str(e)}")
        # Get traceback for more details
        tb = traceback.format_exc()
        errors.append(tb)
    
    return errors

def scan_directory(directory: str, package_prefix: str = "") -> Tuple[List[str], Dict[str, List[str]]]:
    """
    Recursively scan a directory for Python files and check for import errors.
    
    Args:
        directory: Directory to scan
        package_prefix: Package prefix for import paths
        
    Returns:
        Tuple of (all_modules, modules_with_errors)
    """
    all_modules = []
    modules_with_errors = {}
    
    for root, dirs, files in os.walk(directory):
        # Skip directories that start with a dot or "__"
        dirs[:] = [d for d in dirs if not d.startswith('.') and not d.startswith('__')]
        
        for file in files:
            if file.endswith('.py') and not file.startswith('__'):
                # Construct the module path
                rel_path = os.path.relpath(root, os.path.dirname(directory))
                if rel_path == '.':
                    rel_path = ''
                
                rel_path = rel_path.replace(os.sep, '.')
                if rel_path:
                    module_path = f"{package_prefix}.{rel_path}.{file[:-3]}" if package_prefix else f"{rel_path}.{file[:-3]}"
                else:
                    module_path = f"{package_prefix}.{file[:-3]}" if package_prefix else file[:-3]
                
                # Remove leading dots
                module_path = module_path.lstrip('.')
                
                all_modules.append(module_path)
                
                # Check for import errors
                errors = check_imports(module_path)
                if errors:
                    modules_with_errors[module_path] = errors
    
    return all_modules, modules_with_errors

def check_api_endpoints(app_module: ModuleType) -> List[str]:
    """Check API endpoints for potential issues."""
    issues = []
    
    try:
        # Try to access the app routes
        routes = app_module.app.routes
        
        # Check each route
        for route in routes:
            route_path = getattr(route, "path", "Unknown path")
            handler = getattr(route, "endpoint", None)
            
            if handler:
                # Check for error handling in the handler
                source = inspect.getsource(handler)
                if "try:" not in source and "except" not in source:
                    issues.append(f"Route {route_path} lacks error handling")
                
                # Check for validation
                if "validate_" not in source and "Depends" not in source and route.methods != {"GET"}:
                    issues.append(f"Route {route_path} may lack proper input validation")
                
                # Check for database operations without proper cleanup
                if "get_db()" in source and "with get_db()" not in source:
                    issues.append(f"Route {route_path} might not properly close database connections")
                
                # Check for possible security issues
                if route_path.startswith("/api/") and "auth" not in source and "@requires_auth" not in source:
                    issues.append(f"Route {route_path} may lack authentication")
    except Exception as e:
        issues.append(f"Error analyzing API endpoints: {str(e)}")
    
    return issues

def check_javascript_files() -> List[str]:
    """Check JavaScript in template files for common issues."""
    issues = []
    template_dir = "templates"
    
    if not os.path.exists(template_dir):
        return [f"Template directory {template_dir} not found"]
    
    for root, _, files in os.walk(template_dir):
        for file in files:
            if file.endswith(".html"):
                file_path = os.path.join(root, file)
                
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        content = f.read()
                        
                        # Check for incomplete JavaScript functions
                    if "async fun" in content and "async function" not in content:
                        issues.append(f"{file_path}: Contains incomplete async function declaration")
                        
                    # Check for fetch without error handling
                    if "fetch(" in content and ("catch" not in content or "try" not in content):
                        issues.append(f"{file_path}: Contains fetch() without proper error handling")
                    
                    # Check for unescaped template literals in script tags
                    if "<script" in content and "${" in content and "\\${" not in content:
                        issues.append(f"{file_path}: May contain unescaped template literals")
                    
                except Exception as e:
                    issues.append(f"Error analyzing {file_path}: {str(e)}")
    
    return issues

def main():
    """Main entry point for the error checker."""
    print("Checking for errors in the codebase...\n")
    
    # Check app directory
    print("Scanning app directory...")
    app_modules, app_errors = scan_directory("app", "app")
    
    print(f"Found {len(app_modules)} modules in app directory")
    print(f"Found {len(app_errors)} modules with import errors\n")
    
    # Print errors
    if app_errors:
        print("Modules with import errors:")
        for module, errors in app_errors.items():
            print(f"\n{module}:")
            for error in errors:
                print(f"  {error}")
    else:
        print("No import errors found in app modules.\n")
    
    # Check API endpoints
    print("\nChecking API endpoints...")
    try:
        from app.main import app
        api_issues = check_api_endpoints(app)
        
        if api_issues:
            print("\nPotential API issues:")
            for issue in api_issues:
                print(f"  {issue}")
        else:
            print("No obvious API issues found.")
    except Exception as e:
        print(f"Error checking API endpoints: {str(e)}")
    
    # Check database connections
    print("\nChecking database connections...")
    try:
        from app.db.database import get_db
        with get_db() as db:
            print("Database connection successful")
    except Exception as e:
        print(f"Database connection error: {str(e)}")
    
    # Check JavaScript files
    print("\nChecking JavaScript in templates...")
    js_issues = check_javascript_files()
    if js_issues:
        print("\nPotential JavaScript issues:")
        for issue in js_issues:
            print(f"  {issue}")
    else:
        print("No obvious JavaScript issues found.")
    
    print("\nError check complete!")

if __name__ == "__main__":
    main()
