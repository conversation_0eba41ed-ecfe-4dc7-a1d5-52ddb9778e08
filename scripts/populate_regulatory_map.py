#!/usr/bin/env python3
"""Script to populate the Enhanced Regulatory Map with sample data."""
import sys
import os
sys.path.append('.')

from datetime import datetime, timedelta
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import random

from app.db.models import (
    Base, RegulatoryEntity, RegulatoryRelationship, RegulatoryComplianceStatus,
    RegulatoryView, RegulatoryAnnotation, EntityType, RelationshipType,
    ComplianceStatusEnum, ImplementationStatusEnum
)

# Database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./regulation_guru.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def create_sample_entities():
    """Create sample regulatory entities."""
    entities = []
    
    # Sample regulations
    regulations = [
        {
            "name": "GDPR - General Data Protection Regulation",
            "description": "EU regulation on data protection and privacy",
            "type": EntityType.REGULATION,
            "jurisdiction": "EU",
            "status": "active",
            "version": "2018.1",
            "effective_date": datetime(2018, 5, 25),
            "source_url": "https://gdpr.eu/",
            "external_id": "EU-GDPR-2016/679"
        },
        {
            "name": "SOX - Sarbanes-Oxley Act",
            "description": "US federal law on corporate financial reporting",
            "type": EntityType.REGULATION,
            "jurisdiction": "US",
            "status": "active",
            "version": "2002.1",
            "effective_date": datetime(2002, 7, 30),
            "source_url": "https://www.sec.gov/about/laws/soa2002.pdf",
            "external_id": "US-SOX-2002"
        },
        {
            "name": "PCI DSS - Payment Card Industry Data Security Standard",
            "description": "Security standard for organizations that handle credit cards",
            "type": EntityType.REGULATION,
            "jurisdiction": "Global",
            "status": "active",
            "version": "4.0",
            "effective_date": datetime(2022, 3, 31),
            "source_url": "https://www.pcisecuritystandards.org/",
            "external_id": "PCI-DSS-4.0"
        },
        {
            "name": "HIPAA - Health Insurance Portability and Accountability Act",
            "description": "US law on healthcare data privacy and security",
            "type": EntityType.REGULATION,
            "jurisdiction": "US",
            "status": "active",
            "version": "1996.1",
            "effective_date": datetime(1996, 8, 21),
            "source_url": "https://www.hhs.gov/hipaa/",
            "external_id": "US-HIPAA-1996"
        }
    ]
    
    # Sample requirements
    requirements = [
        {
            "name": "Data Subject Rights",
            "description": "Individuals have rights regarding their personal data",
            "type": EntityType.REQUIREMENT,
            "jurisdiction": "EU",
            "status": "active"
        },
        {
            "name": "Data Breach Notification",
            "description": "Requirement to notify authorities of data breaches within 72 hours",
            "type": EntityType.REQUIREMENT,
            "jurisdiction": "EU",
            "status": "active"
        },
        {
            "name": "Internal Controls Assessment",
            "description": "Management must assess internal controls over financial reporting",
            "type": EntityType.REQUIREMENT,
            "jurisdiction": "US",
            "status": "active"
        },
        {
            "name": "Network Segmentation",
            "description": "Isolate cardholder data environment from other networks",
            "type": EntityType.REQUIREMENT,
            "jurisdiction": "Global",
            "status": "active"
        },
        {
            "name": "Access Controls",
            "description": "Restrict access to cardholder data by business need-to-know",
            "type": EntityType.REQUIREMENT,
            "jurisdiction": "Global",
            "status": "active"
        }
    ]
    
    # Sample controls
    controls = [
        {
            "name": "Encryption at Rest",
            "description": "Encrypt sensitive data when stored",
            "type": EntityType.CONTROL,
            "jurisdiction": "Global",
            "status": "active"
        },
        {
            "name": "Multi-Factor Authentication",
            "description": "Require multiple forms of authentication",
            "type": EntityType.CONTROL,
            "jurisdiction": "Global",
            "status": "active"
        },
        {
            "name": "Audit Logging",
            "description": "Log and monitor access to sensitive systems",
            "type": EntityType.CONTROL,
            "jurisdiction": "Global",
            "status": "active"
        },
        {
            "name": "Data Loss Prevention",
            "description": "Prevent unauthorized data exfiltration",
            "type": EntityType.CONTROL,
            "jurisdiction": "Global",
            "status": "active"
        }
    ]
    
    # Sample business processes
    processes = [
        {
            "name": "Customer Data Processing",
            "description": "Process for handling customer personal data",
            "type": EntityType.BUSINESS_PROCESS,
            "jurisdiction": "EU",
            "status": "active"
        },
        {
            "name": "Financial Reporting",
            "description": "Process for preparing and reviewing financial statements",
            "type": EntityType.BUSINESS_PROCESS,
            "jurisdiction": "US",
            "status": "active"
        },
        {
            "name": "Payment Processing",
            "description": "Process for handling credit card transactions",
            "type": EntityType.BUSINESS_PROCESS,
            "jurisdiction": "Global",
            "status": "active"
        }
    ]
    
    # Sample systems
    systems = [
        {
            "name": "Customer Database",
            "description": "Database containing customer personal information",
            "type": EntityType.SYSTEM,
            "jurisdiction": "EU",
            "status": "active"
        },
        {
            "name": "Financial Reporting System",
            "description": "System for generating financial reports",
            "type": EntityType.SYSTEM,
            "jurisdiction": "US",
            "status": "active"
        },
        {
            "name": "Payment Gateway",
            "description": "System for processing credit card payments",
            "type": EntityType.SYSTEM,
            "jurisdiction": "Global",
            "status": "active"
        }
    ]
    
    all_entities = regulations + requirements + controls + processes + systems
    
    for entity_data in all_entities:
        entity = RegulatoryEntity(**entity_data)
        entities.append(entity)
    
    return entities

def create_sample_relationships(entities):
    """Create sample relationships between entities."""
    relationships = []
    
    # Create a mapping of entities by name for easier reference
    entity_map = {entity.name: entity for entity in entities}
    
    # Define relationships
    relationship_definitions = [
        # GDPR relationships
        ("GDPR - General Data Protection Regulation", "Data Subject Rights", RelationshipType.CONTAINS),
        ("GDPR - General Data Protection Regulation", "Data Breach Notification", RelationshipType.CONTAINS),
        ("Data Subject Rights", "Customer Data Processing", RelationshipType.IMPACTS),
        ("Data Breach Notification", "Audit Logging", RelationshipType.IMPLEMENTS),
        ("Customer Data Processing", "Customer Database", RelationshipType.USES),
        ("Customer Database", "Encryption at Rest", RelationshipType.IMPLEMENTS),
        ("Customer Database", "Multi-Factor Authentication", RelationshipType.IMPLEMENTS),
        
        # SOX relationships
        ("SOX - Sarbanes-Oxley Act", "Internal Controls Assessment", RelationshipType.CONTAINS),
        ("Internal Controls Assessment", "Financial Reporting", RelationshipType.IMPACTS),
        ("Financial Reporting", "Financial Reporting System", RelationshipType.USES),
        ("Financial Reporting System", "Audit Logging", RelationshipType.IMPLEMENTS),
        
        # PCI DSS relationships
        ("PCI DSS - Payment Card Industry Data Security Standard", "Network Segmentation", RelationshipType.CONTAINS),
        ("PCI DSS - Payment Card Industry Data Security Standard", "Access Controls", RelationshipType.CONTAINS),
        ("Network Segmentation", "Payment Processing", RelationshipType.IMPACTS),
        ("Access Controls", "Payment Gateway", RelationshipType.IMPACTS),
        ("Payment Processing", "Payment Gateway", RelationshipType.USES),
        ("Payment Gateway", "Encryption at Rest", RelationshipType.IMPLEMENTS),
        ("Payment Gateway", "Multi-Factor Authentication", RelationshipType.IMPLEMENTS),
        
        # Cross-cutting relationships
        ("Data Loss Prevention", "Customer Database", RelationshipType.IMPLEMENTS),
        ("Data Loss Prevention", "Payment Gateway", RelationshipType.IMPLEMENTS),
        ("Audit Logging", "Customer Database", RelationshipType.IMPLEMENTS),
        ("Audit Logging", "Financial Reporting System", RelationshipType.IMPLEMENTS),
    ]
    
    for source_name, target_name, rel_type in relationship_definitions:
        if source_name in entity_map and target_name in entity_map:
            relationship = RegulatoryRelationship(
                source_id=entity_map[source_name].id,
                target_id=entity_map[target_name].id,
                relationship_type=rel_type,
                strength=random.uniform(0.5, 1.0),
                description=f"{source_name} {rel_type.value} {target_name}"
            )
            relationships.append(relationship)
    
    return relationships

def create_sample_compliance_status(entities):
    """Create sample compliance status records."""
    statuses = []
    
    compliance_options = [
        ComplianceStatusEnum.COMPLIANT,
        ComplianceStatusEnum.AT_RISK,
        ComplianceStatusEnum.NON_COMPLIANT,
        ComplianceStatusEnum.UNKNOWN
    ]
    
    implementation_options = [
        ImplementationStatusEnum.IMPLEMENTED,
        ImplementationStatusEnum.IN_PROGRESS,
        ImplementationStatusEnum.NOT_STARTED,
        ImplementationStatusEnum.VERIFIED
    ]
    
    assessors = ["John Smith", "Jane Doe", "Mike Johnson", "Sarah Wilson", "David Brown"]
    
    for entity in entities:
        # Create compliance status for about 70% of entities
        if random.random() < 0.7:
            compliance_status = random.choice(compliance_options)
            implementation_status = random.choice(implementation_options)
            
            # Generate realistic scores based on compliance status
            if compliance_status == ComplianceStatusEnum.COMPLIANT:
                compliance_score = random.uniform(85, 100)
                risk_score = random.uniform(0, 20)
            elif compliance_status == ComplianceStatusEnum.AT_RISK:
                compliance_score = random.uniform(60, 84)
                risk_score = random.uniform(20, 50)
            elif compliance_status == ComplianceStatusEnum.NON_COMPLIANT:
                compliance_score = random.uniform(0, 59)
                risk_score = random.uniform(50, 100)
            else:  # UNKNOWN
                compliance_score = None
                risk_score = None
            
            status = RegulatoryComplianceStatus(
                entity_id=entity.id,
                compliance_status=compliance_status,
                implementation_status=implementation_status,
                compliance_score=compliance_score,
                risk_score=risk_score,
                assessment_date=datetime.utcnow() - timedelta(days=random.randint(1, 90)),
                assessed_by=random.choice(assessors),
                notes=f"Assessment for {entity.name}"
            )
            statuses.append(status)
    
    return statuses

def main():
    """Populate the regulatory map with sample data."""
    print("🚀 Populating Enhanced Regulatory Map with sample data...")

    # Create tables if they don't exist
    print("Creating database tables...")
    Base.metadata.create_all(bind=engine)
    print("✓ Database tables created")

    db = SessionLocal()
    try:
        # Create sample entities
        print("Creating sample entities...")
        entities = create_sample_entities()
        db.add_all(entities)
        db.commit()
        
        # Refresh entities to get their IDs
        for entity in entities:
            db.refresh(entity)
        
        print(f"✓ Created {len(entities)} entities")
        
        # Create sample relationships
        print("Creating sample relationships...")
        relationships = create_sample_relationships(entities)
        db.add_all(relationships)
        db.commit()
        
        print(f"✓ Created {len(relationships)} relationships")
        
        # Create sample compliance status
        print("Creating sample compliance status...")
        statuses = create_sample_compliance_status(entities)
        db.add_all(statuses)
        db.commit()
        
        print(f"✓ Created {len(statuses)} compliance status records")
        
        print("=" * 50)
        print("🎉 Sample data population completed successfully!")
        print(f"📊 Summary:")
        print(f"   - {len(entities)} regulatory entities")
        print(f"   - {len(relationships)} relationships")
        print(f"   - {len(statuses)} compliance status records")
        print("🌐 You can now view the Enhanced Regulatory Map at /regulatory-map")
        
    except Exception as e:
        print(f"❌ Error populating data: {e}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        db.close()
    
    return 0

if __name__ == "__main__":
    exit(main())
