#!/usr/bin/env python3
"""
Performance testing script for RegulationGuru application.
Establishes performance baselines and detects regressions after merges.
"""

import time
import requests
import statistics
import json
import sys
import os
from datetime import datetime
from typing import Dict, List, Any
import concurrent.futures


BASE_URL = os.getenv("BASE_URL", "http://localhost:8000")
TIMEOUT = 30


class PerformanceTester:
    """Performance testing and baseline establishment"""
    
    def __init__(self):
        self.results = {}
        self.baseline_file = "performance_baseline.json"
    
    def test_response_times(self) -> Dict[str, Any]:
        """Test API response times to establish baseline"""
        
        endpoints = [
            "/health",
            "/api/v1/regulations",
            "/api/v1/countries",
            "/api/v1/calendar",
            "/docs"
        ]
        
        print("Testing response times...")
        results = {}
        
        for endpoint in endpoints:
            print(f"Testing {endpoint}...")
            times = []
            errors = 0
            
            for i in range(10):
                try:
                    start = time.time()
                    response = requests.get(f"{BASE_URL}{endpoint}", timeout=TIMEOUT)
                    end = time.time()
                    
                    if response.status_code in [200, 401, 403]:
                        times.append(end - start)
                    else:
                        errors += 1
                        print(f"  Warning: {endpoint} returned {response.status_code}")
                        
                except requests.RequestException as e:
                    errors += 1
                    print(f"  Error: {endpoint} failed: {e}")
            
            if times:
                results[endpoint] = {
                    'avg': statistics.mean(times),
                    'median': statistics.median(times),
                    'min': min(times),
                    'max': max(times),
                    'std_dev': statistics.stdev(times) if len(times) > 1 else 0,
                    'success_rate': len(times) / (len(times) + errors),
                    'sample_size': len(times)
                }
                
                print(f"  ✅ {endpoint}: avg={results[endpoint]['avg']:.3f}s, "
                      f"median={results[endpoint]['median']:.3f}s, "
                      f"max={results[endpoint]['max']:.3f}s")
            else:
                print(f"  ❌ {endpoint}: All requests failed")
                results[endpoint] = {
                    'avg': None,
                    'median': None,
                    'min': None,
                    'max': None,
                    'std_dev': None,
                    'success_rate': 0,
                    'sample_size': 0
                }
        
        return results
    
    def test_concurrent_load(self) -> Dict[str, Any]:
        """Test concurrent request handling"""
        
        print("Testing concurrent load handling...")
        
        def make_request():
            try:
                start = time.time()
                response = requests.get(f"{BASE_URL}/health", timeout=TIMEOUT)
                end = time.time()
                return {
                    'success': response.status_code == 200,
                    'time': end - start,
                    'status_code': response.status_code
                }
            except Exception as e:
                return {
                    'success': False,
                    'time': None,
                    'error': str(e)
                }
        
        # Test with different concurrency levels
        concurrency_levels = [1, 5, 10, 20]
        results = {}
        
        for concurrency in concurrency_levels:
            print(f"  Testing with {concurrency} concurrent requests...")
            
            start_time = time.time()
            with concurrent.futures.ThreadPoolExecutor(max_workers=concurrency) as executor:
                futures = [executor.submit(make_request) for _ in range(concurrency)]
                request_results = [future.result() for future in concurrent.futures.as_completed(futures)]
            end_time = time.time()
            
            successful_requests = [r for r in request_results if r['success']]
            response_times = [r['time'] for r in successful_requests if r['time'] is not None]
            
            results[f"concurrent_{concurrency}"] = {
                'total_time': end_time - start_time,
                'success_rate': len(successful_requests) / len(request_results),
                'avg_response_time': statistics.mean(response_times) if response_times else None,
                'requests_per_second': len(successful_requests) / (end_time - start_time),
                'total_requests': len(request_results),
                'successful_requests': len(successful_requests)
            }
            
            print(f"    Success rate: {results[f'concurrent_{concurrency}']['success_rate']:.2%}")
            print(f"    Requests/sec: {results[f'concurrent_{concurrency}']['requests_per_second']:.2f}")
        
        return results
    
    def test_memory_stability(self) -> Dict[str, Any]:
        """Test memory stability under sustained load"""
        
        print("Testing memory stability...")
        
        # Make sustained requests to check for memory leaks
        request_count = 100
        batch_size = 10
        
        batch_times = []
        errors = 0
        
        for batch in range(0, request_count, batch_size):
            batch_start = time.time()
            
            for _ in range(batch_size):
                try:
                    response = requests.get(f"{BASE_URL}/health", timeout=TIMEOUT)
                    if response.status_code != 200:
                        errors += 1
                except Exception:
                    errors += 1
            
            batch_end = time.time()
            batch_times.append(batch_end - batch_start)
        
        # Check if response times are increasing (potential memory leak indicator)
        if len(batch_times) > 1:
            first_half = batch_times[:len(batch_times)//2]
            second_half = batch_times[len(batch_times)//2:]
            
            first_avg = statistics.mean(first_half)
            second_avg = statistics.mean(second_half)
            
            degradation = (second_avg - first_avg) / first_avg if first_avg > 0 else 0
        else:
            degradation = 0
        
        results = {
            'total_requests': request_count,
            'error_rate': errors / request_count,
            'performance_degradation': degradation,
            'avg_batch_time': statistics.mean(batch_times),
            'stable': degradation < 0.1  # Less than 10% degradation considered stable
        }
        
        print(f"  Error rate: {results['error_rate']:.2%}")
        print(f"  Performance degradation: {results['performance_degradation']:.2%}")
        print(f"  Memory stability: {'✅ Stable' if results['stable'] else '⚠️ Unstable'}")
        
        return results
    
    def save_baseline(self, results: Dict[str, Any]):
        """Save performance results as baseline"""
        
        baseline_data = {
            'timestamp': datetime.now().isoformat(),
            'results': results
        }
        
        try:
            with open(self.baseline_file, 'w') as f:
                json.dump(baseline_data, f, indent=2)
            print(f"✅ Baseline saved to {self.baseline_file}")
        except Exception as e:
            print(f"❌ Failed to save baseline: {e}")
    
    def load_baseline(self) -> Dict[str, Any]:
        """Load existing performance baseline"""
        
        try:
            with open(self.baseline_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"ℹ️  No existing baseline found at {self.baseline_file}")
            return None
        except Exception as e:
            print(f"❌ Failed to load baseline: {e}")
            return None
    
    def compare_with_baseline(self, current_results: Dict[str, Any]) -> bool:
        """Compare current results with baseline"""
        
        baseline = self.load_baseline()
        if not baseline:
            print("ℹ️  No baseline available for comparison")
            return True
        
        print("Comparing with baseline...")
        baseline_results = baseline['results']
        
        # Compare response times
        if 'response_times' in baseline_results and 'response_times' in current_results:
            for endpoint in baseline_results['response_times']:
                if endpoint in current_results['response_times']:
                    baseline_avg = baseline_results['response_times'][endpoint]['avg']
                    current_avg = current_results['response_times'][endpoint]['avg']
                    
                    if baseline_avg and current_avg:
                        change = (current_avg - baseline_avg) / baseline_avg
                        
                        if change > 0.5:  # 50% slower
                            print(f"⚠️  {endpoint} is {change:.1%} slower than baseline")
                            return False
                        elif change > 0.2:  # 20% slower
                            print(f"⚠️  {endpoint} is {change:.1%} slower than baseline")
                        else:
                            print(f"✅ {endpoint} performance is acceptable")
        
        print("✅ Performance comparison completed")
        return True
    
    def run_all_tests(self) -> bool:
        """Run all performance tests"""
        
        print("=== PERFORMANCE TESTING STARTED ===")
        print(f"Target URL: {BASE_URL}")
        print(f"Started at: {datetime.now()}")
        
        all_results = {}
        
        try:
            # Test response times
            all_results['response_times'] = self.test_response_times()
            
            # Test concurrent load
            all_results['concurrent_load'] = self.test_concurrent_load()
            
            # Test memory stability
            all_results['memory_stability'] = self.test_memory_stability()
            
            # Compare with baseline
            performance_acceptable = self.compare_with_baseline(all_results)
            
            # Save new baseline if this is the first run or if performance is acceptable
            baseline = self.load_baseline()
            if not baseline or performance_acceptable:
                self.save_baseline(all_results)
            
            print("=== PERFORMANCE TESTING COMPLETED ===")
            return performance_acceptable
            
        except Exception as e:
            print(f"❌ Performance testing failed: {e}")
            return False


def main():
    """Main function"""
    tester = PerformanceTester()
    success = tester.run_all_tests()
    
    if success:
        print("✅ All performance tests passed")
        sys.exit(0)
    else:
        print("❌ Performance tests failed or showed regression")
        sys.exit(1)


if __name__ == "__main__":
    main()
