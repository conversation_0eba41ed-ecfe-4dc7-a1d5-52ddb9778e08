
"""
Analyze and generate detailed test coverage report.
"""
import os
import re
import glob
import json
from typing import Dict, List, <PERSON><PERSON>

def get_coverage_from_html(file_path: str) -> Tuple[str, int]:
    """
    Extract coverage percentage from HTML title.
    
    Args:
        file_path (str): Path to HTML coverage file
        
    Returns:
        Tuple[str, int]: Module name and coverage percentage
    """
    pattern = r'<title>Coverage for (.+?): (\d+)%</title>'
    
    with open(file_path, 'r') as f:
        content = f.read()
        match = re.search(pattern, content)
        if match:
            module_name = match.group(1)
            coverage_pct = int(match.group(2))
            return module_name, coverage_pct
    return None, 0

def analyze_coverage() -> Dict:
    """
    Analyze test coverage from HTML files.
    
    Returns:
        Dict: Coverage statistics
    """
    results = {
        "modules": {},
        "summary": {
            "total_modules": 0,
            "high_coverage": 0,
            "medium_coverage": 0,
            "low_coverage": 0,
            "overall_percentage": 0
        }
    }
    
    html_dir = "coverage/html"
    if not os.path.exists(html_dir):
        return {"error": "Coverage directory not found. Run tests with coverage first."}
    
    # Get all HTML files except index.html
    html_files = glob.glob(os.path.join(html_dir, "*_py.html"))
    
    if not html_files:
        return {"error": "No coverage files found"}
    
    total_coverage = 0
    
    for file_path in html_files:
        module_name, coverage_pct = get_coverage_from_html(file_path)
        if module_name:
            results["modules"][module_name] = coverage_pct
            total_coverage += coverage_pct
            
            # Categorize coverage
            if coverage_pct >= 90:
                results["summary"]["high_coverage"] += 1
            elif coverage_pct >= 60:
                results["summary"]["medium_coverage"] += 1
            else:
                results["summary"]["low_coverage"] += 1
    
    # Calculate summary
    module_count = len(results["modules"])
    results["summary"]["total_modules"] = module_count
    
    if module_count > 0:
        total_coverage = sum(results["modules"].values())
        results["summary"]["overall_percentage"] = round(total_coverage / module_count, 1)
    
    # Sort modules by coverage percentage (descending)
    results["modules"] = dict(sorted(
        results["modules"].items(),
        key=lambda item: item[1],
        reverse=True
    ))
    
    return results
    results["modules"] = dict(sorted(
        results["modules"].items(), 
        key=lambda item: item[1], 
        reverse=True
    ))
    
    return results

def print_report(results: Dict) -> None:
    """
    Print formatted coverage report.
    
    Args:
        results (Dict): Coverage analysis results
    """
    print("\n===== TEST COVERAGE REPORT =====\n")
    
    if "error" in results:
        print(f"Error: {results['error']}")
        return
    
    # Print summary
    summary = results["summary"]
    print(f"Overall Coverage: {summary['overall_percentage']}%")
    print(f"Total Modules: {summary['total_modules']}")
    print(f"Coverage Distribution:")
    print(f"  - High (≥90%): {summary['high_coverage']} modules")
    print(f"  - Medium (60-89%): {summary['medium_coverage']} modules")
    print(f"  - Low (<60%): {summary['low_coverage']} modules")
    
    # Print detailed module coverage
    print("\nDetailed Module Coverage:")
    for module, coverage in results["modules"].items():
        # Create a visual bar
        bar_length = 30
        filled_length = int(bar_length * coverage / 100)
        bar = '█' * filled_length + '░' * (bar_length - filled_length)
        
        print(f"  {module:<25} {coverage:>3}% {bar}")

def main():
    """Run the coverage analysis."""
    results = analyze_coverage()
    print_report(results)
    
    # Save results to JSON file
    with open("coverage_report.json", "w") as f:
        json.dump(results, f, indent=2)
    
    print("\nDetailed report saved to coverage_report.json")

if __name__ == "__main__":
    main()
