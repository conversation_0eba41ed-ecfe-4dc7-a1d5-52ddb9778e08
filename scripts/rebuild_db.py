"""
Database rebuild script.
Creates tables based on SQLAlchemy models.
"""
import os
import sys
from datetime import datetime
import logging

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"logs/db_rebuild_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger(__name__)

# Import database components
from app.db.database import engine
from app.db.models import Base

def rebuild_database():
    """Rebuild the database schema based on current models."""
    try:
        logger.info("Starting database rebuild...")

        # Create all tables
        Base.metadata.create_all(bind=engine)

        logger.info("Database rebuild completed successfully!")
        return True
    except Exception as e:
        logger.error(f"Database rebuild failed: {str(e)}")
        return False

def main():
    """Main entry point."""
    print("This script will rebuild your database schema based on the current SQLAlchemy models.")
    print("This will DROP all existing tables and recreate them! All data will be lost.")

    choice = input("Do you want to continue? [y/N]: ")

    if choice.lower() == 'y':
        success = rebuild_database()
        if success:
            print("Database has been successfully rebuilt!")
        else:
            print("Database rebuild failed. Check the logs for details.")
    else:
        print("Operation cancelled.")

if __name__ == "__main__":
    main()