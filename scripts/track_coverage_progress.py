
#!/usr/bin/env python3
"""Script to track coverage progress over time."""

import os
import json
import datetime
import re
from pathlib import Path


def extract_coverage_from_html(html_file):
    """Extract coverage percentage from a coverage HTML file."""
    if not os.path.exists(html_file):
        return None
    
    with open(html_file, 'r') as f:
        content = f.read()
        match = re.search(r'<title>Coverage for .+?: (\d+)%</title>', content)
        if match:
            return int(match.group(1))
    return None


def get_module_coverage(coverage_dir="coverage/html"):
    """Get coverage data for all modules."""
    if not os.path.exists(coverage_dir):
        return {"error": "Coverage directory not found"}
    
    coverage_data = {}
    
    # Read all HTML files in the coverage directory
    for filename in os.listdir(coverage_dir):
        if filename.endswith('.html'):
            file_path = os.path.join(coverage_dir, filename)
            
            # Parse module name from filename
            module_name = filename.replace('_py.html', '.py')
            if module_name.startswith('z_'):
                # Handle the mangled filenames with path info
                match = re.search(r'<title>Coverage for (.+?): \d+%</title>', open(file_path).read())
                if match:
                    module_name = match.group(1)
            
            # Extract coverage percentage
            coverage_pct = extract_coverage_from_html(file_path)
            if coverage_pct is not None:
                coverage_data[module_name] = coverage_pct
    
    # Calculate average coverage
    if coverage_data:
        coverage_data['average'] = round(sum(
            v for k, v in coverage_data.items() if k != 'average'
        ) / len([k for k in coverage_data if k != 'average']), 2)
    
    return coverage_data


def save_coverage_history(coverage_data, history_file="coverage/coverage_history.json"):
    """Save coverage data to history file."""
    history = []
    
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(history_file), exist_ok=True)
    
    # Load existing history if available
    if os.path.exists(history_file):
        with open(history_file, 'r') as f:
            try:
                history = json.load(f)
            except json.JSONDecodeError:
                history = []
    
    # Add new entry
    entry = {
        "date": datetime.datetime.now().isoformat(),
        "average": coverage_data.get("average", 0),
        "modules": {k: v for k, v in coverage_data.items() if k != "average" and k != "error"}
    }
    history.append(entry)
    
    # Save updated history
    with open(history_file, 'w') as f:
        json.dump(history, f, indent=2)
    
    return history


def print_coverage_report(coverage_data):
    """Print a formatted coverage report."""
    if "error" in coverage_data:
        print(f"Error: {coverage_data['error']}")
        return
    
    print("\n=== Coverage Report ===")
    
    # Print modules sorted by coverage (ascending)
    modules = [(k, v) for k, v in coverage_data.items() if k != "average"]
    modules.sort(key=lambda x: x[1])
    
    print("\nLow coverage modules (under 50%):")
    low_coverage = [m for m in modules if m[1] < 50]
    if low_coverage:
        for module, coverage in low_coverage:
            print(f"  {module}: {coverage}%")
    else:
        print("  None")
    
    print("\nMedium coverage modules (50% - 80%):")
    med_coverage = [m for m in modules if 50 <= m[1] < 80]
    if med_coverage:
        for module, coverage in med_coverage:
            print(f"  {module}: {coverage}%")
    else:
        print("  None")
    
    print("\nHigh coverage modules (80% - 99%):")
    high_coverage = [m for m in modules if 80 <= m[1] < 100]
    if high_coverage:
        for module, coverage in high_coverage:
            print(f"  {module}: {coverage}%")
    else:
        print("  None")
    
    print("\nFully covered modules (100%):")
    full_coverage = [m for m in modules if m[1] == 100]
    if full_coverage:
        for module, coverage in full_coverage:
            print(f"  {module}: {coverage}%")
    else:
        print("  None")
    
    print(f"\nAverage coverage: {coverage_data.get('average', 0)}%")
    print(f"Total modules: {len(modules)}")
    print(f"Low coverage modules: {len(low_coverage)} ({len(low_coverage)/len(modules)*100:.1f}%)")
    print(f"Medium coverage modules: {len(med_coverage)} ({len(med_coverage)/len(modules)*100:.1f}%)")
    print(f"High coverage modules: {len(high_coverage)} ({len(high_coverage)/len(modules)*100:.1f}%)")
    print(f"Fully covered modules: {len(full_coverage)} ({len(full_coverage)/len(modules)*100:.1f}%)")


def print_coverage_trend(history_file="coverage/coverage_history.json"):
    """Print coverage trend over time."""
    if not os.path.exists(history_file):
        print("No coverage history found.")
        return
    
    with open(history_file, 'r') as f:
        history = json.load(f)
    
    if not history:
        print("Coverage history is empty.")
        return
    
    print("\n=== Coverage Trend ===")
    print(f"History entries: {len(history)}")
    
    first = history[0]
    last = history[-1]
    
    print(f"\nFirst measurement ({first['date'].split('T')[0]}):")
    print(f"  Average coverage: {first['average']}%")
    
    print(f"\nLatest measurement ({last['date'].split('T')[0]}):")
    print(f"  Average coverage: {last['average']}%")
    
    if len(history) >= 2:
        change = last['average'] - first['average']
        print(f"\nChange over time: {change:+.2f}%")
        
        # Show modules with biggest improvement
        module_changes = []
        for module in last['modules']:
            if module in first['modules']:
                change = last['modules'][module] - first['modules'][module]
                module_changes.append((module, change))
        
        if module_changes:
            module_changes.sort(key=lambda x: x[1], reverse=True)
            
            print("\nTop 5 most improved modules:")
            for module, change in module_changes[:5]:
                if change > 0:
                    print(f"  {module}: {change:+.1f}%")
                    
            print("\nModules needing attention:")
            for module, change in module_changes[-5:]:
                if change <= 0:
                    print(f"  {module}: {change:+.1f}%")


def main():
    """Main function."""
    coverage_data = get_module_coverage()
    print_coverage_report(coverage_data)
    
    # Save to history
    save_coverage_history(coverage_data)
    
    # Print trend
    print_coverage_trend()
    
    return 0


if __name__ == "__main__":
    main()
