#!/bin/bash

# Setup /etc/hosts entries for RegulationGuru Traefik integration

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Hosts entries to add
HOSTS_ENTRIES="
# RegulationGuru Traefik Integration
127.0.0.1 api.guru.localhost
127.0.0.1 docs.guru.localhost
127.0.0.1 admin.guru.localhost"

print_status "Setting up /etc/hosts entries for RegulationGuru..."

# Check if entries already exist
if grep -q "api.guru.localhost" /etc/hosts; then
    print_warning "RegulationGuru entries already exist in /etc/hosts"
    print_status "Current entries:"
    grep "guru.localhost" /etc/hosts
else
    print_status "Adding entries to /etc/hosts..."
    
    # Backup current hosts file
    sudo cp /etc/hosts /etc/hosts.backup.$(date +%Y%m%d_%H%M%S)
    print_success "Backed up /etc/hosts"
    
    # Add entries
    echo "$HOSTS_ENTRIES" | sudo tee -a /etc/hosts > /dev/null
    print_success "Added RegulationGuru entries to /etc/hosts"
fi

print_status "Verifying /etc/hosts entries..."
echo ""
echo "Current RegulationGuru entries in /etc/hosts:"
grep "guru.localhost" /etc/hosts || print_warning "No entries found"

echo ""
print_success "✅ /etc/hosts setup complete!"
echo ""
echo "You can now access:"
echo "   🔧 API Service:      http://api.guru.localhost"
echo "   📚 Documentation:    http://docs.guru.localhost"
echo "   ⚙️  Admin Interface:  http://admin.guru.localhost"
