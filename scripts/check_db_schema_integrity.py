
"""
Database schema integrity checker.
Validates the actual database schema against the SQLAlchemy models.
"""
import os
import sys
from typing import List, Dict, Any

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import database components
from app.db.database import engine, get_db
from app.db.models import Base
from sqlalchemy import inspect, MetaData, Table

def check_schema_consistency() -> List[str]:
    """Check if the database schema matches the SQLAlchemy models."""
    issues = []
    
    # Get the inspector
    inspector = inspect(engine)
    
    # Get all tables in the database
    db_tables = inspector.get_table_names()
    
    # Get all tables defined in the models
    model_tables = [table_name for table_name in Base.metadata.tables.keys()]
    
    # Check for tables in model but not in database
    for table_name in model_tables:
        if table_name not in db_tables:
            issues.append(f"Table '{table_name}' is defined in models but does not exist in the database")
    
    # Check for tables in database but not in model
    for table_name in db_tables:
        # Skip SQLite internal tables
        if table_name.startswith('sqlite_'):
            continue
            
        if table_name not in model_tables:
            issues.append(f"Table '{table_name}' exists in the database but is not defined in models")
    
    # Check table columns
    for table_name in model_tables:
        if table_name not in db_tables:
            continue
            
        # Get model columns
        model_table = Base.metadata.tables[table_name]
        model_columns = {col.name: col for col in model_table.columns}
        
        # Get database columns
        db_columns = {col['name']: col for col in inspector.get_columns(table_name)}
        
        # Check for columns in model but not in database
        for col_name in model_columns:
            if col_name not in db_columns:
                issues.append(f"Column '{col_name}' in table '{table_name}' is defined in model but not in database")
        
        # Check for columns in database but not in model
        for col_name in db_columns:
            if col_name not in model_columns:
                issues.append(f"Column '{col_name}' in table '{table_name}' exists in database but not in model")
    
    # Check foreign keys
    for table_name in model_tables:
        if table_name not in db_tables:
            continue
            
        # Get model foreign keys
        model_table = Base.metadata.tables[table_name]
        model_fks = {fk.parent.name: (fk.target_fullname, fk.name) for fk in model_table.foreign_keys}
        
        # Get database foreign keys
        db_fks = {}
        for fk in inspector.get_foreign_keys(table_name):
            for col_name in fk['constrained_columns']:
                db_fks[col_name] = (f"{fk['referred_table']}.{fk['referred_columns'][0]}", fk.get('name'))
        
        # Check for foreign keys in model but not in database
        for col_name, (target, name) in model_fks.items():
            if col_name not in db_fks:
                issues.append(f"Foreign key on '{table_name}.{col_name}' pointing to '{target}' is defined in model but not in database")
            elif db_fks[col_name][0] != target:
                issues.append(f"Foreign key on '{table_name}.{col_name}' points to '{db_fks[col_name][0]}' in database but to '{target}' in model")
        
        # Check for foreign keys in database but not in model
        for col_name, (target, name) in db_fks.items():
            if col_name not in model_fks:
                issues.append(f"Foreign key on '{table_name}.{col_name}' pointing to '{target}' exists in database but not in model")
    
    return issues

def main():
    """Main entry point."""
    print("Checking database schema integrity...")
    
    issues = check_schema_consistency()
    
    if issues:
        print("\nSchema integrity issues found:")
        for issue in issues:
            print(f"  - {issue}")
        
        print("\nYou should consider recreating your database or running migrations.")
    else:
        print("No schema integrity issues found! Database schema matches the models.")

if __name__ == "__main__":
    main()
