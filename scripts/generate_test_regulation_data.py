#!/usr/bin/env python3
"""
Test data generator for local regulation importer testing.

This script generates various types of CSV files for testing the local
regulation import functionality, including valid files, invalid files,
and edge cases.
"""

import csv
import random
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class TestDataGenerator:
    """Generator for test regulation data."""
    
    def __init__(self, output_dir: str = "local_storage/imports"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Sample data for generation
        self.countries = [
            ("United States", "US"),
            ("Canada", "CA"),
            ("United Kingdom", "GB"),
            ("Germany", "DE"),
            ("France", "FR"),
            ("Japan", "JP"),
            ("Australia", "AU"),
            ("Brazil", "BR"),
            ("India", "IN"),
            ("China", "CN")
        ]
        
        self.document_types = [
            "Federal Law",
            "State Law",
            "Provincial Law",
            "National Law",
            "EU Regulation",
            "EU Directive",
            "Federal Regulation",
            "Financial Regulation",
            "Administrative Regulation"
        ]
        
        self.legal_statuses = [
            "Binding",
            "Non-binding guidance",
            "Pending",
            "Draft"
        ]
        
        self.languages = [
            "English",
            "French",
            "German",
            "Spanish",
            "Japanese",
            "Chinese",
            "Portuguese"
        ]
        
        # CSV headers
        self.headers = [
            "Country_Name", "Country_Code", "Document_Title", "Document_Type",
            "Issuing_Authority", "Publication_Date", "Effective_Date", "Legal_Status",
            "Document_URL", "Language", "Scope_Application", "Key_Compliance_Requirements",
            "Enforcement_Mechanisms", "Penalties", "Cross_Border_Elements",
            "Data_Protection_Provisions", "Incident_Reporting_Requirements",
            "Risk_Management_Mandates", "Third_Party_Requirements", "Audit_Obligations",
            "Certification_Requirements", "Implementation_Timeline",
            "International_Standards_Alignment", "Extraterritorial_Reach",
            "Safe_Harbor_Provisions", "Industry_Specific_Provisions",
            "Technology_Specific_Provisions"
        ]
    
    def generate_random_date(self, start_year: int = 2020, end_year: int = 2025) -> str:
        """Generate a random date string."""
        start_date = datetime(start_year, 1, 1)
        end_date = datetime(end_year, 12, 31)
        
        time_between = end_date - start_date
        days_between = time_between.days
        random_days = random.randrange(days_between)
        
        random_date = start_date + timedelta(days=random_days)
        return random_date.strftime("%Y-%m-%d")
    
    def generate_regulation_record(self, record_id: int) -> Dict[str, str]:
        """Generate a single regulation record."""
        country_name, country_code = random.choice(self.countries)
        document_type = random.choice(self.document_types)
        legal_status = random.choice(self.legal_statuses)
        language = random.choice(self.languages)
        
        pub_date = self.generate_random_date(2020, 2024)
        eff_date = self.generate_random_date(2020, 2025)
        
        return {
            "Country_Name": country_name,
            "Country_Code": country_code,
            "Document_Title": f"Regulation {record_id:04d} - {document_type}",
            "Document_Type": document_type,
            "Issuing_Authority": f"{country_name} Regulatory Authority",
            "Publication_Date": pub_date,
            "Effective_Date": eff_date,
            "Legal_Status": legal_status,
            "Document_URL": f"https://regulations.{country_code.lower()}.gov/reg{record_id:04d}",
            "Language": language,
            "Scope_Application": f"Applies to all entities operating in {country_name} financial sector",
            "Key_Compliance_Requirements": f"Must comply with {document_type} requirements including reporting, risk management, and audit obligations",
            "Enforcement_Mechanisms": f"{country_name} Financial Supervisory Authority with powers of investigation and enforcement",
            "Penalties": f"Fines up to ${random.randint(100000, 10000000):,} and/or suspension of operations",
            "Cross_Border_Elements": f"Applies to cross-border transactions involving {country_name} entities",
            "Data_Protection_Provisions": f"Must comply with {country_name} data protection laws and international standards",
            "Incident_Reporting_Requirements": f"Report incidents within {random.randint(24, 72)} hours to regulatory authority",
            "Risk_Management_Mandates": f"Implement comprehensive risk management framework as per {country_name} standards",
            "Third_Party_Requirements": f"Due diligence required for all third-party service providers",
            "Audit_Obligations": f"Annual audit by certified auditors required, report due within {random.randint(90, 180)} days",
            "Certification_Requirements": f"Certification required for key personnel and systems",
            "Implementation_Timeline": f"Full implementation required within {random.randint(6, 24)} months",
            "International_Standards_Alignment": f"Aligned with international standards including Basel III and FATF recommendations",
            "Extraterritorial_Reach": f"Applies to {country_name} entities operating internationally",
            "Safe_Harbor_Provisions": f"Safe harbor available for good faith compliance efforts",
            "Industry_Specific_Provisions": f"Specific requirements for banking, insurance, and investment sectors",
            "Technology_Specific_Provisions": f"Requirements for digital platforms, AI systems, and cybersecurity measures"
        }
    
    def generate_valid_csv(self, filename: str, record_count: int = 50) -> Path:
        """Generate a valid CSV file with specified number of records."""
        file_path = self.output_dir / filename
        
        with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=self.headers)
            writer.writeheader()
            
            for i in range(1, record_count + 1):
                record = self.generate_regulation_record(i)
                writer.writerow(record)
        
        print(f"✅ Generated valid CSV: {file_path} ({record_count} records)")
        return file_path
    
    def generate_invalid_csv_missing_columns(self, filename: str) -> Path:
        """Generate an invalid CSV file with missing required columns."""
        file_path = self.output_dir / filename
        
        # Only include some columns
        limited_headers = ["Country_Name", "Country_Code", "Document_Title"]
        
        with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=limited_headers)
            writer.writeheader()
            
            for i in range(1, 6):
                country_name, country_code = random.choice(self.countries)
                writer.writerow({
                    "Country_Name": country_name,
                    "Country_Code": country_code,
                    "Document_Title": f"Incomplete Regulation {i}"
                })
        
        print(f"❌ Generated invalid CSV (missing columns): {file_path}")
        return file_path
    
    def generate_invalid_csv_malformed(self, filename: str) -> Path:
        """Generate a malformed CSV file."""
        file_path = self.output_dir / filename
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("This is not a proper CSV file\n")
            f.write("It has irregular structure\n")
            f.write("Some lines have commas, others don't\n")
            f.write("Country,Code,Title,Type,Authority\n")  # Header with wrong count
            f.write("US,United States,Test\n")  # Wrong number of fields
            f.write("CA,Canada,Test,Law,Authority,Extra,Fields\n")  # Too many fields
        
        print(f"❌ Generated malformed CSV: {file_path}")
        return file_path
    
    def generate_empty_csv(self, filename: str) -> Path:
        """Generate an empty CSV file."""
        file_path = self.output_dir / filename
        file_path.touch()
        
        print(f"❌ Generated empty CSV: {file_path}")
        return file_path
    
    def generate_unicode_csv(self, filename: str) -> Path:
        """Generate a CSV file with Unicode content."""
        file_path = self.output_dir / filename
        
        unicode_data = [
            {
                "Country_Name": "中国",
                "Country_Code": "CN",
                "Document_Title": "金融监管条例 2024",
                "Document_Type": "National Law",
                "Issuing_Authority": "中国人民银行",
                "Publication_Date": "2024-01-01",
                "Effective_Date": "2024-01-01",
                "Legal_Status": "Binding",
                "Document_URL": "https://regulations.cn.gov/reg0001",
                "Language": "Chinese",
                "Scope_Application": "适用于所有在中国境内经营的金融机构",
                "Key_Compliance_Requirements": "必须遵守中国金融监管要求",
                "Enforcement_Mechanisms": "中国银行保险监督管理委员会执法",
                "Penalties": "罚款最高1000万元人民币",
                "Cross_Border_Elements": "适用于跨境金融交易",
                "Data_Protection_Provisions": "必须遵守中国数据保护法",
                "Incident_Reporting_Requirements": "24小时内报告重大事件",
                "Risk_Management_Mandates": "实施全面风险管理框架",
                "Third_Party_Requirements": "第三方服务商尽职调查",
                "Audit_Obligations": "年度审计要求",
                "Certification_Requirements": "关键人员认证要求",
                "Implementation_Timeline": "12个月内全面实施",
                "International_Standards_Alignment": "与国际标准保持一致",
                "Extraterritorial_Reach": "适用于境外中国金融机构",
                "Safe_Harbor_Provisions": "善意合规安全港条款",
                "Industry_Specific_Provisions": "银行业特定要求",
                "Technology_Specific_Provisions": "金融科技特定要求"
            },
            {
                "Country_Name": "France",
                "Country_Code": "FR",
                "Document_Title": "Règlement Financier Français 2024",
                "Document_Type": "National Law",
                "Issuing_Authority": "Autorité de Contrôle Prudentiel et de Résolution",
                "Publication_Date": "2024-01-01",
                "Effective_Date": "2024-01-01",
                "Legal_Status": "Binding",
                "Document_URL": "https://regulations.fr.gov/reg0001",
                "Language": "Français",
                "Scope_Application": "S'applique à toutes les institutions financières opérant en France",
                "Key_Compliance_Requirements": "Doit se conformer aux exigences réglementaires françaises",
                "Enforcement_Mechanisms": "Autorité de Contrôle Prudentiel et de Résolution",
                "Penalties": "Amendes jusqu'à 10 millions d'euros",
                "Cross_Border_Elements": "S'applique aux transactions transfrontalières",
                "Data_Protection_Provisions": "Doit se conformer au RGPD",
                "Incident_Reporting_Requirements": "Signalement dans les 24 heures",
                "Risk_Management_Mandates": "Cadre de gestion des risques complet",
                "Third_Party_Requirements": "Diligence raisonnable des tiers",
                "Audit_Obligations": "Audit annuel requis",
                "Certification_Requirements": "Certification du personnel clé",
                "Implementation_Timeline": "Mise en œuvre complète dans 12 mois",
                "International_Standards_Alignment": "Aligné sur les normes internationales",
                "Extraterritorial_Reach": "S'applique aux entités françaises à l'étranger",
                "Safe_Harbor_Provisions": "Dispositions de refuge de bonne foi",
                "Industry_Specific_Provisions": "Exigences spécifiques au secteur bancaire",
                "Technology_Specific_Provisions": "Exigences spécifiques à la fintech"
            }
        ]
        
        with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=self.headers)
            writer.writeheader()
            
            for record in unicode_data:
                writer.writerow(record)
        
        print(f"🌍 Generated Unicode CSV: {file_path}")
        return file_path
    
    def generate_large_csv(self, filename: str, record_count: int = 1000) -> Path:
        """Generate a large CSV file for performance testing."""
        file_path = self.output_dir / filename
        
        print(f"📊 Generating large CSV with {record_count} records...")
        
        with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=self.headers)
            writer.writeheader()
            
            for i in range(1, record_count + 1):
                record = self.generate_regulation_record(i)
                writer.writerow(record)
                
                if i % 100 == 0:
                    print(f"  Generated {i}/{record_count} records...")
        
        print(f"✅ Generated large CSV: {file_path} ({record_count} records)")
        return file_path
    
    def generate_all_test_files(self):
        """Generate all types of test files."""
        print("🏭 Generating comprehensive test dataset...")
        print("=" * 50)
        
        # Valid files
        self.generate_valid_csv("valid_small.csv", 10)
        self.generate_valid_csv("valid_medium.csv", 50)
        self.generate_valid_csv("valid_large.csv", 200)
        
        # Invalid files
        self.generate_invalid_csv_missing_columns("invalid_missing_columns.csv")
        self.generate_invalid_csv_malformed("invalid_malformed.csv")
        self.generate_empty_csv("invalid_empty.csv")
        
        # Special cases
        self.generate_unicode_csv("unicode_content.csv")
        
        # Performance testing
        self.generate_large_csv("performance_test.csv", 1000)
        
        print("\n🎉 Test data generation completed!")
        print(f"📁 Files generated in: {self.output_dir}")
        
        # List generated files
        files = list(self.output_dir.glob("*.csv"))
        print(f"📋 Generated {len(files)} test files:")
        for file_path in sorted(files):
            size_mb = file_path.stat().st_size / (1024 * 1024)
            print(f"  - {file_path.name} ({size_mb:.2f} MB)")


def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Generate test data for local regulation importer")
    parser.add_argument("--output-dir", default="local_storage/imports", 
                       help="Output directory for generated files")
    parser.add_argument("--type", choices=["all", "valid", "invalid", "unicode", "large"],
                       default="all", help="Type of test data to generate")
    parser.add_argument("--count", type=int, default=50,
                       help="Number of records for valid files")
    
    args = parser.parse_args()
    
    generator = TestDataGenerator(args.output_dir)
    
    if args.type == "all":
        generator.generate_all_test_files()
    elif args.type == "valid":
        generator.generate_valid_csv("test_valid.csv", args.count)
    elif args.type == "invalid":
        generator.generate_invalid_csv_missing_columns("test_invalid.csv")
    elif args.type == "unicode":
        generator.generate_unicode_csv("test_unicode.csv")
    elif args.type == "large":
        generator.generate_large_csv("test_large.csv", args.count)


if __name__ == "__main__":
    main()
