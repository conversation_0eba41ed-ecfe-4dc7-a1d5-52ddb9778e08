
"""
Generate test coverage reports in multiple formats.
"""
import os
import subprocess
import sys
from pathlib import Path

def run_command(cmd):
    """Run a shell command and return the exit code."""
    try:
        result = subprocess.run(cmd, shell=True, check=True)
        return result.returncode
    except subprocess.CalledProcessError as e:
        print(f"Error running: {cmd}")
        print(f"Error details: {e}")
        return e.returncode

def main():
    """Run tests with coverage and generate reports."""
    # Create coverage directory if it doesn't exist
    os.makedirs("coverage", exist_ok=True)
    
    # Run pytest with coverage
    cmd = "python -m pytest --cov=. --cov-report=term --cov-report=html:coverage/html --cov-report=xml:coverage/coverage.xml --cov-config=.coveragerc"
    result = run_command(cmd)
    
    if result != 0:
        print("Tests failed, but still generating coverage reports")
    
    # Check if coverage reports were generated
    coverage_html = Path("coverage/html/index.html")
    coverage_xml = Path("coverage/coverage.xml")
    
    if coverage_html.exists():
        print(f"HTML coverage report generated at: {coverage_html}")
    else:
        print("HTML coverage report generation failed")
        
    if coverage_xml.exists():
        print(f"XML coverage report generated at: {coverage_xml}")
    else:
        print("XML coverage report generation failed")
    
    return result

if __name__ == "__main__":
    sys.exit(main())
