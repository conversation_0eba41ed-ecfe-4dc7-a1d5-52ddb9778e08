
"""URL import script to process and store URLs in the database."""
import os
import argparse
import logging
from urllib.parse import urlparse
from typing import List, Dict, Optional, Tuple

from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from app.db import models
from app.db import engine, get_db
from app.utils import (
    load_urls_from_file, 
    extract_domain_and_tld,
    identify_country_from_url,
    identify_regulator_from_url,
    find_regulation_category
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Create database tables if they don't exist
models.Base.metadata.create_all(bind=engine)


def normalize_url(url: str) -> str:
    """
    Normalize URL for storage and comparison.
    
    Args:
        url (str): URL to normalize
        
    Returns:
        str: Normalized URL
    """
    # Basic normalization - could be enhanced with more rules
    url = url.strip().lower()
    
    # Remove trailing slashes
    if url.endswith('/'):
        url = url[:-1]
    
    # Ensure protocol
    if not url.startswith(('http://', 'https://')):
        url = 'https://' + url
        
    return url


def get_or_create_country(db: Session, country_name: str) -> models.Country:
    """
    Get or create a country record.
    
    Args:
        db (Session): Database session
        country_name (str): Name of the country
        
    Returns:
        models.Country: Country record
    """
    if country_name == "Unknown":
        country_name = "Other"
        
    # Check if country already exists
    country = db.query(models.Country).filter(models.Country.name == country_name).first()
    
    if not country:
        # Create new country
        country = models.Country(name=country_name)
        db.add(country)
        try:
            db.commit()
            db.refresh(country)
            logging.info(f"Created new country: {country_name}")
        except IntegrityError:
            db.rollback()
            # Try again - it might have been created by another process
            country = db.query(models.Country).filter(models.Country.name == country_name).first()
            if not country:
                # Create with a unique name if there's still an issue
                country = models.Country(name=f"{country_name} {id(country_name)}")
                db.add(country)
                db.commit()
                db.refresh(country)
    
    return country


def get_or_create_regulator(db: Session, regulator_name: str, country_id: int) -> models.Regulator:
    """
    Get or create a regulator record.
    
    Args:
        db (Session): Database session
        regulator_name (str): Name of the regulator
        country_id (int): ID of the country the regulator belongs to
        
    Returns:
        models.Regulator: Regulator record
    """
    if regulator_name == "Unknown":
        regulator_name = f"Unidentified Regulator"
        
    # Check if regulator already exists for this country
    regulator = db.query(models.Regulator).filter(
        models.Regulator.name == regulator_name,
        models.Regulator.country_id == country_id
    ).first()
    
    if not regulator:
        # Create new regulator
        regulator = models.Regulator(
            name=regulator_name,
            country_id=country_id
        )
        db.add(regulator)
        try:
            db.commit()
            db.refresh(regulator)
            logging.info(f"Created new regulator: {regulator_name}")
        except IntegrityError:
            db.rollback()
            # Try again - it might have been created by another process
            regulator = db.query(models.Regulator).filter(
                models.Regulator.name == regulator_name,
                models.Regulator.country_id == country_id
            ).first()
            if not regulator:
                # Create with a unique name if there's still an issue
                regulator = models.Regulator(
                    name=f"{regulator_name} {id(regulator_name)}",
                    country_id=country_id
                )
                db.add(regulator)
                db.commit()
                db.refresh(regulator)
    
    return regulator


def calculate_confidence(url: str, country: str, regulator: str, category: Optional[str]) -> float:
    """
    Calculate confidence level for the URL-regulator relationship.
    
    Args:
        url (str): The URL
        country (str): Identified country
        regulator (str): Identified regulator
        category (Optional[str]): Regulation category
        
    Returns:
        float: Confidence level (0.0 to 1.0)
    """
    confidence = 0.0
    
    # Base confidence levels
    if country != "Unknown":
        confidence += 0.3
    
    if regulator != "Unknown":
        confidence += 0.3
    
    if category and category != "Unknown":
        confidence += 0.2
    
    # Domain-specific confidence
    domain = urlparse(url).netloc
    if domain:
        # Government domains generally have higher confidence
        if '.gov' in domain:
            confidence += 0.1
        
        # Known regulator domains
        if domain in [
            'mas.gov.sg', 'sec.gov', 'ecfr.gov', 'finma.ch', 
            'eur-lex.europa.eu', 'legislation.gov.uk', 'osfi-bsif.gc.ca'
        ]:
            confidence += 0.1
    
    # Cap at 1.0
    return min(confidence, 1.0)


def process_and_import_urls(file_path: str, db: Session) -> List[Dict]:
    """
    Process URLs from a file and import them into the database.
    
    Args:
        file_path (str): Path to the file containing URLs
        db (Session): Database session
        
    Returns:
        List[Dict]: Results of processing
    """
    urls = load_urls_from_file(file_path)
    results = []
    
    for url in urls:
        logging.info(f"Processing URL: {url}")
        
        try:
            # Normalize URL for storage
            normalized_url = normalize_url(url)
            
            # Check if URL already exists in database
            existing_url = db.query(models.RegulationURL).filter(
                models.RegulationURL.url == url
            ).first()
            
            if existing_url:
                logging.info(f"URL already exists in database: {url}")
                results.append({
                    "url": url,
                    "status": "exists",
                    "id": existing_url.id
                })
                continue
            
            # Process URL
            domain, tld = extract_domain_and_tld(url)
            category = find_regulation_category(url)
            country_name = identify_country_from_url(url, domain, tld)
            regulator_name = identify_regulator_from_url(url, domain, category)
            
            # Calculate confidence
            confidence = calculate_confidence(url, country_name, regulator_name, category)
            
            # Get or create country
            country = get_or_create_country(db, country_name)
            
            # Get or create regulator
            regulator = get_or_create_regulator(db, regulator_name, country.id)
            
            # Create URL record
            regulation_url = models.RegulationURL(
                url=url,
                domain=domain,
                normalized_url=normalized_url,
                category=category if category else "Unknown",
                confidence_level=confidence,
                regulator_id=regulator.id
            )
            
            db.add(regulation_url)
            db.commit()
            db.refresh(regulation_url)
            
            logging.info(f"Imported URL: {url} | Country: {country_name} | Regulator: {regulator_name} | Confidence: {confidence:.2f}")
            
            results.append({
                "url": url,
                "status": "imported",
                "id": regulation_url.id,
                "country": country_name,
                "regulator": regulator_name,
                "confidence": confidence
            })
            
        except Exception as e:
            logging.error(f"Error processing URL {url}: {e}")
            results.append({
                "url": url,
                "status": "error",
                "error": str(e)
            })
    
    return results


def main():
    """Main function to run the URL processor."""
    parser = argparse.ArgumentParser(description='Process URLs and import them into the database.')
    parser.add_argument('file', help='Path to the text file containing URLs')
    
    args = parser.parse_args()
    
    # Initialize DB session
    db = next(get_db())
    
    try:
        results = process_and_import_urls(args.file, db)
        
        # Print summary
        total = len(results)
        imported = sum(1 for r in results if r["status"] == "imported")
        existing = sum(1 for r in results if r["status"] == "exists")
        errors = sum(1 for r in results if r["status"] == "error")
        
        logging.info(f"Import summary: Total: {total}, Imported: {imported}, Already existed: {existing}, Errors: {errors}")
        
    except Exception as e:
        logging.error(f"Error during import process: {e}")
    finally:
        db.close()


if __name__ == "__main__":
    main()
