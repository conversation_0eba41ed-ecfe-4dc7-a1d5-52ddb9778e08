
"""
Analyze the results of the URL processor to provide statistics
on regulator and country detection rates with confidence scores.
"""
import csv
import sys
import os
from typing import Dict, List, <PERSON>ple

def analyze_results(csv_file: str) -> Dict:
    """
    Analyze the CSV results file from URL processor.
    
    Args:
        csv_file (str): Path to the CSV results file
        
    Returns:
        Dict: Statistics about the processing results
    """
    if not os.path.exists(csv_file):
        print(f"Error: Results file {csv_file} not found.")
        return {}
        
    stats = {
        "total_urls": 0,
        "identified_countries": 0,
        "identified_regulators": 0,
        "identified_categories": 0,
        "country_distribution": {},
        "regulator_distribution": {},
        "category_distribution": {},
        "confidence_sum": 0,
        "confidence_count": 0
    }
    
    try:
        with open(csv_file, 'r', newline='') as file:
            reader = csv.DictReader(file)
            for row in reader:
                stats["total_urls"] += 1
                
                # Count country identifications
                if row.get('country') and row.get('country') != "Unknown" and row.get('country') != "Error":
                    stats["identified_countries"] += 1
                    country = row.get('country')
                    stats["country_distribution"][country] = stats["country_distribution"].get(country, 0) + 1
                
                # Count regulator identifications
                if row.get('regulator') and row.get('regulator') != "Unknown" and row.get('regulator') != "Error":
                    stats["identified_regulators"] += 1
                    regulator = row.get('regulator')
                    stats["regulator_distribution"][regulator] = stats["regulator_distribution"].get(regulator, 0) + 1
                
                # Count category identifications
                if row.get('category') and row.get('category') != "Unknown" and row.get('category') != "Error":
                    stats["identified_categories"] += 1
                    category = row.get('category')
                    stats["category_distribution"][category] = stats["category_distribution"].get(category, 0) + 1
                
        # Now import the results as well to get confidence levels
        import_stats = analyze_imported_results()
        stats.update(import_stats)
                
        return stats
    except Exception as e:
        print(f"Error analyzing results: {e}")
        return {}

def analyze_imported_results() -> Dict:
    """
    Analyze results from the database after import
    to get confidence levels.
    
    Returns:
        Dict: Statistics about confidence levels
    """
    try:
        from sqlalchemy import func, create_engine
        from sqlalchemy.orm import sessionmaker
        from app.db import models
        from app.db import get_db, engine
        
        stats = {
            "confidence_sum": 0,
            "confidence_count": 0,
            "confidence_levels": {
                "high": 0,  # 0.7-1.0
                "medium": 0,  # 0.4-0.7
                "low": 0  # 0-0.4
            }
        }
        
        # Create a session and query for confidence stats
        Session = sessionmaker(bind=engine)
        session = Session()
        
        # Get average confidence
        avg_confidence = session.query(func.avg(models.RegulationURL.confidence_level)).scalar()
        if avg_confidence is not None:
            stats["avg_confidence"] = float(avg_confidence)
        
        # Get count of URLs by confidence level
        total = session.query(models.RegulationURL).count()
        stats["imported_urls"] = total
        
        # Count by confidence bands
        high = session.query(models.RegulationURL).filter(
            models.RegulationURL.confidence_level >= 0.7
        ).count()
        
        medium = session.query(models.RegulationURL).filter(
            models.RegulationURL.confidence_level >= 0.4,
            models.RegulationURL.confidence_level < 0.7
        ).count()
        
        low = session.query(models.RegulationURL).filter(
            models.RegulationURL.confidence_level < 0.4
        ).count()
        
        stats["confidence_levels"] = {
            "high": high,
            "medium": medium,
            "low": low
        }
        
        session.close()
        return stats
    except Exception as e:
        print(f"Error analyzing imported results: {e}")
        return {}

def print_report(stats: Dict) -> None:
    """
    Print a formatted report of the analysis.
    
    Args:
        stats (Dict): Statistics from analyze_results
    """
    print("\n===== URL PROCESSOR ANALYSIS REPORT =====\n")
    
    print(f"Total URLs processed: {stats.get('total_urls', 0)}")
    
    if stats.get('total_urls', 0) > 0:
        country_pct = (stats.get('identified_countries', 0) / stats.get('total_urls', 0)) * 100
        print(f"Countries identified: {stats.get('identified_countries', 0)} ({country_pct:.1f}%)")
        
        regulator_pct = (stats.get('identified_regulators', 0) / stats.get('total_urls', 0)) * 100
        print(f"Regulators identified: {stats.get('identified_regulators', 0)} ({regulator_pct:.1f}%)")
        
        category_pct = (stats.get('identified_categories', 0) / stats.get('total_urls', 0)) * 100
        print(f"Categories identified: {stats.get('identified_categories', 0)} ({category_pct:.1f}%)")
    
    # Print confidence information if available from import
    if 'avg_confidence' in stats:
        print(f"\nAverage confidence level: {stats.get('avg_confidence', 0):.2f}")
        print("\nConfidence distribution:")
        total = sum(stats.get('confidence_levels', {}).values())
        if total > 0:
            for level, count in stats.get('confidence_levels', {}).items():
                pct = (count / total) * 100
                print(f"  - {level.capitalize()}: {count} ({pct:.1f}%)")
    
    # Print top 5 countries
    countries = sorted(
        stats.get('country_distribution', {}).items(), 
        key=lambda x: x[1], 
        reverse=True
    )[:5]
    
    if countries:
        print("\nTop countries:")
        for country, count in countries:
            print(f"  - {country}: {count}")
    
    # Print top 5 regulators
    regulators = sorted(
        stats.get('regulator_distribution', {}).items(), 
        key=lambda x: x[1], 
        reverse=True
    )[:5]
    
    if regulators:
        print("\nTop regulators:")
        for regulator, count in regulators:
            print(f"  - {regulator}: {count}")

def main():
    """Run the analysis on the results file."""
    results_file = "regulatory_analysis.csv"
    if len(sys.argv) > 1:
        results_file = sys.argv[1]
    
    stats = analyze_results(results_file)
    if stats:
        print_report(stats)
    else:
        print("No analysis results to display.")

if __name__ == "__main__":
    main()
