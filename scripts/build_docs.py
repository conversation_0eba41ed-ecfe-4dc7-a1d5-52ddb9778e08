#!/usr/bin/env python3
"""
Documentation build script for RegulationGuru.

This script builds the Sphinx documentation with proper theme integration
and verifies that all theme files are correctly included.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def main():
    """Main build function."""
    print("🏗️  Building RegulationGuru Documentation")
    print("=" * 50)
    
    # Get project root
    project_root = Path(__file__).parent.parent
    docs_dir = project_root / "docs"
    
    print(f"📁 Project root: {project_root}")
    print(f"📚 Documentation directory: {docs_dir}")
    
    # Check if docs directory exists
    if not docs_dir.exists():
        print("❌ Documentation directory not found!")
        return 1
    
    # Change to docs directory
    os.chdir(docs_dir)
    
    # Check for required files
    required_files = [
        "conf.py",
        "_static/custom-theme.css",
        "_static/doc-theme-sync.js",
        "_templates/layout.html"
    ]
    
    print("\n🔍 Checking required files...")
    missing_files = []
    for file_path in required_files:
        if not (docs_dir / file_path).exists():
            missing_files.append(file_path)
            print(f"❌ Missing: {file_path}")
        else:
            print(f"✅ Found: {file_path}")
    
    if missing_files:
        print(f"\n❌ Missing {len(missing_files)} required files!")
        return 1
    
    # Clean previous build
    print("\n🧹 Cleaning previous build...")
    build_dir = docs_dir / "_build"
    if build_dir.exists():
        shutil.rmtree(build_dir)
        print("✅ Cleaned _build directory")
    
    # Build HTML documentation
    print("\n🔨 Building HTML documentation...")
    try:
        result = subprocess.run(
            ["sphinx-build", "-b", "html", ".", "_build/html"],
            cwd=docs_dir,
            capture_output=True,
            text=True,
            timeout=300  # 5 minutes timeout
        )
        
        if result.returncode == 0:
            print("✅ Documentation built successfully!")
            print("\nBuild output:")
            print(result.stdout)
            
            if result.stderr:
                print("\nWarnings/Notes:")
                print(result.stderr)
        else:
            print("❌ Documentation build failed!")
            print("Error output:")
            print(result.stderr)
            print("Standard output:")
            print(result.stdout)
            return 1
            
    except subprocess.TimeoutExpired:
        print("❌ Documentation build timed out!")
        return 1
    except FileNotFoundError:
        print("❌ sphinx-build command not found!")
        print("Please install Sphinx: pip install sphinx sphinx-rtd-theme")
        return 1
    except Exception as e:
        print(f"❌ Unexpected error during build: {e}")
        return 1
    
    # Verify build output
    print("\n🔍 Verifying build output...")
    html_dir = docs_dir / "_build" / "html"
    
    expected_files = [
        "index.html",
        "_static/custom-theme.css",
        "_static/doc-theme-sync.js",
        "_static/css/theme.css"  # RTD theme CSS
    ]
    
    missing_output = []
    for file_path in expected_files:
        if not (html_dir / file_path).exists():
            missing_output.append(file_path)
            print(f"❌ Missing output: {file_path}")
        else:
            print(f"✅ Generated: {file_path}")
    
    if missing_output:
        print(f"\n⚠️  Warning: {len(missing_output)} expected files missing from output")
    
    # Check file sizes
    print("\n📊 Build statistics:")
    total_size = 0
    file_count = 0
    
    for file_path in html_dir.rglob("*"):
        if file_path.is_file():
            size = file_path.stat().st_size
            total_size += size
            file_count += 1
    
    print(f"📄 Total files: {file_count}")
    print(f"💾 Total size: {total_size / (1024*1024):.2f} MB")
    
    # Verify theme integration
    print("\n🎨 Verifying theme integration...")
    index_file = html_dir / "index.html"
    
    if index_file.exists():
        with open(index_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        theme_checks = [
            ('custom-theme.css', 'custom-theme.css' in content),
            ('doc-theme-sync.js', 'doc-theme-sync.js' in content),
            ('data-theme attribute', 'data-theme' in content),
            ('Font Awesome', 'font-awesome' in content.lower() or 'fas fa-' in content),
        ]
        
        for check_name, check_result in theme_checks:
            if check_result:
                print(f"✅ {check_name} integrated")
            else:
                print(f"❌ {check_name} missing")
    
    # Generate access instructions
    print("\n🌐 Access Instructions:")
    print("=" * 30)
    print("1. Start the FastAPI application:")
    print("   python -m uvicorn app.main:app --reload")
    print()
    print("2. Access documentation at:")
    print("   http://localhost:8000/docs/")
    print()
    print("3. Or view locally:")
    print(f"   file://{html_dir / 'index.html'}")
    print()
    print("4. Theme features:")
    print("   - Automatic OS dark mode detection")
    print("   - Manual theme toggle button")
    print("   - Synchronized with main app theme")
    print("   - Persistent theme preferences")
    
    # Check for common issues
    print("\n🔧 Troubleshooting:")
    print("=" * 20)
    
    # Check if FastAPI app can serve the docs
    app_main = project_root / "app" / "main.py"
    if app_main.exists():
        with open(app_main, 'r') as f:
            app_content = f.read()
        
        if 'mount("/docs"' in app_content:
            print("✅ FastAPI configured to serve documentation")
        else:
            print("❌ FastAPI not configured to serve documentation")
            print("   Add this to app/main.py:")
            print('   app.mount("/docs", StaticFiles(directory="docs/_build/html", html=True), name="docs")')
    
    # Check theme files
    theme_css = html_dir / "_static" / "custom-theme.css"
    if theme_css.exists():
        size = theme_css.stat().st_size
        print(f"✅ Theme CSS file: {size} bytes")
    else:
        print("❌ Theme CSS file missing")
    
    theme_js = html_dir / "_static" / "doc-theme-sync.js"
    if theme_js.exists():
        size = theme_js.stat().st_size
        print(f"✅ Theme JS file: {size} bytes")
    else:
        print("❌ Theme JS file missing")
    
    print("\n🎉 Documentation build completed!")
    return 0


if __name__ == "__main__":
    sys.exit(main())
