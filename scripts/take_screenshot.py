
#!/usr/bin/env python3
"""
Utility script to take screenshots of the application interface
"""
import os
import sys
import asyncio
import logging
from playwright.async_api import async_playwright

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

async def capture_screenshot(url="http://0.0.0.0:8000/", filename="dashboard_screenshot.png", wait_time=2):
    """
    Take a screenshot of the specified URL
    
    Args:
        url: The URL to screenshot
        filename: The filename to save the screenshot as
        wait_time: Additional seconds to wait for rendering
    """
    logger.info(f"Taking screenshot of {url}...")
    
    async with async_playwright() as p:
        # Launch browser
        browser = await p.chromium.launch()
        # Create a new page
        page = await browser.new_page()
        
        # Navigate to the URL
        await page.goto(url)
        
        # Wait for page to fully load
        await page.wait_for_load_state("networkidle")
        
        # Additional wait to ensure everything is rendered
        await asyncio.sleep(wait_time)
        
        # Create screenshots directory if it doesn't exist
        os.makedirs("screenshots", exist_ok=True)
        
        # Take screenshot and save
        full_path = os.path.join("screenshots", filename)
        await page.screenshot(path=full_path, full_page=True)
        
        logger.info(f"Screenshot saved to {full_path}")
        
        # Close the browser
        await browser.close()

async def main():
    """Main function to run the screenshot utility"""
    # Screenshot the main dashboard
    await capture_screenshot(
        url="http://0.0.0.0:8000/",
        filename="dashboard_dark_mode.png"
    )
    
    # Screenshot API docs
    await capture_screenshot(
        url="http://0.0.0.0:8000/api/v1/docs",
        filename="api_docs_dark_mode.png"
    )
    
    # Screenshot admin interface if available
    await capture_screenshot(
        url="http://0.0.0.0:8000/ui/manage",
        filename="admin_dark_mode.png",
        wait_time=3  # Admin UI might need more time to load
    )
    
    logger.info("All screenshots completed!")

if __name__ == "__main__":
    # Install Playwright browsers if needed
    try:
        import subprocess
        subprocess.run(["playwright", "install", "chromium"], check=True)
    except Exception as e:
        logger.warning(f"Could not automatically install Playwright browser: {e}")
        logger.warning("If screenshot fails, run: playwright install chromium")
    
    # Run the async main function
    asyncio.run(main())
