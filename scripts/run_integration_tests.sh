#!/bin/bash

# Start the backend server in the background
echo "Starting backend server..."
python run.py --no-browser &
BACKEND_PID=$!

# Wait for the server to start
echo "Waiting for server to start..."
sleep 10

# Run the frontend tests
echo "Running frontend integration tests..."
cd frontend
npm test -- --coverage

# Capture the test result
TEST_RESULT=$?

# Shutdown the backend server
echo "Shutting down backend server..."
kill $BACKEND_PID

# Exit with the test result
exit $TEST_RESULT
