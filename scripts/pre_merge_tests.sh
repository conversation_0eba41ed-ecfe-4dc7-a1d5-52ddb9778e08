#!/bin/bash
# pre_merge_tests.sh - Comprehensive testing before merging a branch

set -e  # Exit on any error

BRANCH_NAME=$1
CURRENT_BRANCH=$(git branch --show-current)

if [ -z "$BRANCH_NAME" ]; then
    echo "Usage: ./pre_merge_tests.sh <branch_name>"
    echo "Available branches:"
    git branch -a | grep -E "(dependabot|feature|regulatory)" | head -10
    exit 1
fi

echo "=== PRE-MERGE TESTING FOR BRANCH: $BRANCH_NAME ==="
echo "Current branch: $CURRENT_BRANCH"
echo "Target branch: $BRANCH_NAME"
echo "Started at: $(date)"

# 1. Verify branch exists
if ! git show-ref --verify --quiet refs/heads/$BRANCH_NAME && ! git show-ref --verify --quiet refs/remotes/origin/$BRANCH_NAME; then
    echo "❌ Error: Branch $BRANCH_NAME does not exist"
    exit 1
fi

# 2. Create database backup
echo "Step 1: Creating database backup..."
./scripts/backup_database.sh
BACKUP_CREATED=$?
if [ $BACKUP_CREATED -ne 0 ]; then
    echo "❌ Error: Database backup failed"
    exit 1
fi

# 3. Check for merge conflicts (dry run)
echo "Step 2: Checking for merge conflicts..."
git merge --no-commit --no-ff $BRANCH_NAME
MERGE_STATUS=$?

if [ $MERGE_STATUS -ne 0 ]; then
    echo "❌ Error: Merge conflicts detected"
    git merge --abort
    exit 1
else
    echo "✅ No merge conflicts detected"
    git merge --abort  # Abort the dry run merge
fi

# 4. Perform actual merge
echo "Step 3: Performing merge..."
git merge $BRANCH_NAME --no-edit

if [ $? -ne 0 ]; then
    echo "❌ Error: Merge failed"
    exit 1
fi

# 5. Test database migrations
echo "Step 4: Testing database migrations..."
if [ -f "alembic.ini" ]; then
    python -m alembic upgrade head
    if [ $? -ne 0 ]; then
        echo "❌ Error: Database migration failed"
        git reset --hard HEAD~1  # Rollback merge
        exit 1
    fi
    echo "✅ Database migrations successful"
else
    echo "ℹ️  No alembic.ini found, skipping migration test"
fi

# 6. Restart application containers
echo "Step 5: Restarting application..."
docker-compose restart app 2>/dev/null || docker restart regulationguru-app
sleep 15

# 7. Health check
echo "Step 6: Application health check..."
for i in {1..30}; do
    if curl -f http://localhost:8000/health >/dev/null 2>&1; then
        echo "✅ Application health check passed"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ Error: Application health check failed"
        git reset --hard HEAD~1  # Rollback merge
        exit 1
    fi
    sleep 2
done

# 8. API endpoint tests
echo "Step 7: Testing API endpoints..."
python -c "
import requests
import sys

base_url = 'http://localhost:8000'
endpoints = ['/health', '/api/v1/regulations', '/api/v1/countries']

for endpoint in endpoints:
    try:
        response = requests.get(f'{base_url}{endpoint}', timeout=10)
        if response.status_code in [200, 401, 403]:
            print(f'✅ {endpoint}: {response.status_code}')
        else:
            print(f'❌ {endpoint}: {response.status_code}')
            sys.exit(1)
    except Exception as e:
        print(f'❌ {endpoint}: {e}')
        sys.exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ Error: API endpoint tests failed"
    git reset --hard HEAD~1  # Rollback merge
    exit 1
fi

# 9. Database integrity tests
echo "Step 8: Testing database integrity..."
python -c "
from app.database import engine
import sys

try:
    with engine.connect() as conn:
        result = conn.execute('SELECT 1').scalar()
        if result == 1:
            print('✅ Database connectivity test passed')
        else:
            print('❌ Database connectivity test failed')
            sys.exit(1)
except Exception as e:
    print(f'❌ Database test failed: {e}')
    sys.exit(1)
" 2>/dev/null

if [ $? -ne 0 ]; then
    echo "❌ Error: Database integrity tests failed"
    git reset --hard HEAD~1  # Rollback merge
    exit 1
fi

# 10. Run pytest if available
echo "Step 9: Running automated tests..."
if [ -d "tests" ] && command -v pytest >/dev/null 2>&1; then
    python -m pytest tests/ -x --tb=short
    if [ $? -ne 0 ]; then
        echo "❌ Error: Automated tests failed"
        git reset --hard HEAD~1  # Rollback merge
        exit 1
    fi
    echo "✅ Automated tests passed"
else
    echo "ℹ️  No tests directory or pytest not available, skipping automated tests"
fi

echo "=== ALL PRE-MERGE TESTS PASSED FOR BRANCH: $BRANCH_NAME ==="
echo "Completed at: $(date)"
echo "✅ Branch $BRANCH_NAME successfully merged and validated"
