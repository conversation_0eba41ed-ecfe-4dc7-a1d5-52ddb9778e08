#!/bin/bash
# restore_database.sh - Restore database from backup

set -e  # Exit on any error

BACKUP_FILE=$1
DB_NAME="regulationguru"
CONTAINER_NAME="regulationguru-db"
APP_CONTAINER="regulationguru-app"

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: ./restore_database.sh <backup_file>"
    echo "Available backups:"
    ls -la backups/backup_*.sql.gz 2>/dev/null || echo "No backups found"
    exit 1
fi

if [ ! -f "$BACKUP_FILE" ]; then
    echo "❌ Error: Backup file $BACKUP_FILE not found"
    exit 1
fi

echo "=== DATABASE RESTORE STARTING ==="
echo "Backup file: $BACKUP_FILE"
echo "Database: $DB_NAME"
echo "Container: $CONTAINER_NAME"

# Stop application container
echo "Stopping application container..."
if docker ps | grep -q $APP_CONTAINER; then
    docker stop $APP_CONTAINER
    echo "✅ Application container stopped"
else
    echo "ℹ️  Application container not running"
fi

# Check if database container is running
if ! docker ps | grep -q $CONTAINER_NAME; then
    echo "❌ Error: Database container $CONTAINER_NAME is not running"
    exit 1
fi

# Drop existing connections
echo "Dropping existing database connections..."
docker exec $CONTAINER_NAME psql -U postgres -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '$DB_NAME' AND pid <> pg_backend_pid();"

# Drop and recreate database
echo "Recreating database..."
docker exec $CONTAINER_NAME psql -U postgres -c "DROP DATABASE IF EXISTS $DB_NAME;"
docker exec $CONTAINER_NAME psql -U postgres -c "CREATE DATABASE $DB_NAME;"

# Restore database
echo "Restoring database from backup..."
if [[ "$BACKUP_FILE" == *.gz ]]; then
    gunzip -c $BACKUP_FILE | docker exec -i $CONTAINER_NAME psql -U postgres -d $DB_NAME
else
    cat $BACKUP_FILE | docker exec -i $CONTAINER_NAME psql -U postgres -d $DB_NAME
fi

if [ $? -eq 0 ]; then
    echo "✅ Database restored successfully"
else
    echo "❌ Error: Database restore failed"
    exit 1
fi

# Start application container
echo "Starting application container..."
docker start $APP_CONTAINER

# Wait for application to start
echo "Waiting for application to start..."
sleep 10

# Test database connectivity
echo "Testing database connectivity..."
if docker exec $CONTAINER_NAME pg_isready -U postgres; then
    echo "✅ Database is ready"
else
    echo "❌ Error: Database is not ready"
    exit 1
fi

# Test application health
echo "Testing application health..."
for i in {1..30}; do
    if curl -f http://localhost:8000/health >/dev/null 2>&1; then
        echo "✅ Application is healthy"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ Error: Application health check failed"
        exit 1
    fi
    sleep 2
done

echo "=== DATABASE RESTORE COMPLETED ==="
