#!/bin/bash
# backup_database.sh - Create database backup before merge operations

set -e  # Exit on any error

TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="./backups"
DB_NAME="regulationguru"
CONTAINER_NAME="regulationguru-db"

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

echo "=== DATABASE BACKUP STARTING ==="
echo "Timestamp: $TIMESTAMP"
echo "Database: $DB_NAME"
echo "Container: $CONTAINER_NAME"

# Check if database container is running
if ! docker ps | grep -q $CONTAINER_NAME; then
    echo "❌ Error: Database container $CONTAINER_NAME is not running"
    exit 1
fi

# Create database backup
echo "Creating database backup..."
docker exec $CONTAINER_NAME pg_dump -U postgres $DB_NAME > $BACKUP_DIR/backup_${TIMESTAMP}.sql

if [ $? -eq 0 ]; then
    echo "✅ Database backup created successfully"
else
    echo "❌ Error: Database backup failed"
    exit 1
fi

# Compress backup
echo "Compressing backup..."
gzip $BACKUP_DIR/backup_${TIMESTAMP}.sql

if [ $? -eq 0 ]; then
    echo "✅ Backup compressed successfully"
    BACKUP_FILE="$BACKUP_DIR/backup_${TIMESTAMP}.sql.gz"
    BACKUP_SIZE=$(du -h "$BACKUP_FILE" | cut -f1)
    echo "📁 Backup file: $BACKUP_FILE"
    echo "📊 Backup size: $BACKUP_SIZE"
else
    echo "❌ Error: Backup compression failed"
    exit 1
fi

# Clean up old backups (keep last 10)
echo "Cleaning up old backups..."
cd $BACKUP_DIR
ls -t backup_*.sql.gz | tail -n +11 | xargs -r rm
echo "✅ Old backups cleaned up"

echo "=== DATABASE BACKUP COMPLETED ==="
echo "Backup file: $BACKUP_FILE"
