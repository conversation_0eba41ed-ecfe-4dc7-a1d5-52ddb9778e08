
"""
Database model integrity checker.
Verifies SQLAlchemy models for complete bidirectional relationships.
"""
import os
import sys
import inspect
from typing import List, Dict, Any, Tuple, Set

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import models
from app.db.models import Base
from sqlalchemy import inspect as sqlalchemy_inspect
from sqlalchemy.orm import relationship, RelationshipProperty

def check_model_relationships() -> List[str]:
    """Check for model relationship consistency issues."""
    issues = []
    
    # Get all model classes
    model_classes = []
    for cls_name in dir(Base.metadata.tables):
        for cls in Base.__subclasses__():
            if cls.__tablename__ == cls_name:
                model_classes.append(cls)
    
    # Check each model's relationships
    for cls in model_classes:
        # Get relationship properties
        relationships = {}
        for key, prop in inspect.getmembers(cls):
            if isinstance(prop, RelationshipProperty):
                relationships[key] = prop
        
        # Skip if no relationships
        if not relationships:
            continue
        
        # Check each relationship
        for rel_name, rel_prop in relationships.items():
            target_cls = rel_prop.entity.class_
            back_populates = rel_prop.back_populates
            
            # Check if back_populates is specified
            if not back_populates:
                issues.append(f"Model {cls.__name__} has relationship '{rel_name}' without back_populates")
                continue
            
            # Check if the target has the reciprocal relationship
            target_relationships = {}
            for key, prop in inspect.getmembers(target_cls):
                if isinstance(prop, RelationshipProperty):
                    target_relationships[key] = prop
            
            # Check if the back_populates exists in the target
            if back_populates not in target_relationships:
                issues.append(f"Model {cls.__name__} has relationship '{rel_name}' with back_populates='{back_populates}' but {target_cls.__name__} has no such attribute")
                continue
            
            # Check if the back_populates points back
            target_rel_prop = target_relationships[back_populates]
            if target_rel_prop.back_populates != rel_name:
                issues.append(f"Model {cls.__name__}.{rel_name} and {target_cls.__name__}.{back_populates} have mismatched back_populates")
    
    return issues

def check_foreign_keys() -> List[str]:
    """Check for foreign key consistency issues."""
    issues = []
    
    for cls in Base.__subclasses__():
        # Get SQLAlchemy inspector
        mapper = sqlalchemy_inspect(cls)
        
        # Get relationships
        for rel in mapper.relationships:
            # Skip if no foreign keys
            if not rel.local_remote_pairs:
                continue
            
            # Check each foreign key
            for local_col, remote_col in rel.local_remote_pairs:
                local_name = local_col.name
                remote_name = remote_col.name
                
                # Check if the local column exists
                if local_name not in [c.name for c in mapper.columns]:
                    issues.append(f"Model {cls.__name__} references non-existent local column {local_name} in relationship {rel.key}")
    
    return issues

def check_model_integrity() -> List[str]:
    """Run all model integrity checks."""
    issues = []
    
    # Check relationships
    relationship_issues = check_model_relationships()
    if relationship_issues:
        issues.extend(relationship_issues)
    
    # Check foreign keys
    foreign_key_issues = check_foreign_keys()
    if foreign_key_issues:
        issues.extend(foreign_key_issues)
    
    return issues

def main():
    """Main entry point."""
    print("Checking database model integrity...")
    
    issues = check_model_integrity()
    
    if issues:
        print("\nModel integrity issues found:")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("No model integrity issues found! All models look good.")

if __name__ == "__main__":
    main()
