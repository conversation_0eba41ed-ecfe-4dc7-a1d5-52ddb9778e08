#!/bin/bash
# docker_health_check.sh - Comprehensive Docker environment health check

set -e  # Exit on any error

echo "=== DOCKER ENVIRONMENT HEALTH CHECK ==="
echo "Started at: $(date)"

# 1. Check Docker daemon
echo "Step 1: Checking Docker daemon..."
if ! docker info >/dev/null 2>&1; then
    echo "❌ Error: Docker daemon is not running"
    exit 1
fi
echo "✅ Docker daemon is running"

# 2. Check all RegulationGuru containers
echo "Step 2: Checking RegulationGuru containers..."
echo "Container Status:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(regulationguru|NAMES)"

# Check specific containers
CONTAINERS=("regulationguru-db" "regulationguru-redis" "regulationguru-app")
for container in "${CONTAINERS[@]}"; do
    if docker ps | grep -q $container; then
        echo "✅ $container is running"
    else
        echo "❌ $container is not running"
        echo "Attempting to start $container..."
        docker start $container 2>/dev/null || echo "Failed to start $container"
    fi
done

# 3. Check database connectivity
echo "Step 3: Checking database connectivity..."
if docker exec regulationguru-db pg_isready -U postgres >/dev/null 2>&1; then
    echo "✅ Database is ready and accepting connections"
else
    echo "❌ Database is not ready"
    exit 1
fi

# Test database query
echo "Testing database query..."
if docker exec regulationguru-db psql -U postgres -d regulationguru -c "SELECT 1;" >/dev/null 2>&1; then
    echo "✅ Database query test passed"
else
    echo "❌ Database query test failed"
    exit 1
fi

# 4. Check Redis connectivity
echo "Step 4: Checking Redis connectivity..."
if docker exec regulationguru-redis redis-cli ping | grep -q "PONG"; then
    echo "✅ Redis is responding to ping"
else
    echo "❌ Redis is not responding"
    exit 1
fi

# Test Redis operations
echo "Testing Redis operations..."
docker exec regulationguru-redis redis-cli set test_key "test_value" >/dev/null
if docker exec regulationguru-redis redis-cli get test_key | grep -q "test_value"; then
    echo "✅ Redis read/write test passed"
    docker exec regulationguru-redis redis-cli del test_key >/dev/null
else
    echo "❌ Redis read/write test failed"
    exit 1
fi

# 5. Check application health
echo "Step 5: Checking application health..."
for i in {1..30}; do
    if curl -f http://localhost:8000/health >/dev/null 2>&1; then
        echo "✅ Application health endpoint is responding"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ Application health endpoint is not responding"
        exit 1
    fi
    sleep 2
done

# Test specific health endpoints
echo "Testing specific health endpoints..."
HEALTH_ENDPOINTS=("/health" "/health/db" "/health/redis")
for endpoint in "${HEALTH_ENDPOINTS[@]}"; do
    if curl -f "http://localhost:8000$endpoint" >/dev/null 2>&1; then
        echo "✅ $endpoint is healthy"
    else
        echo "❌ $endpoint is not healthy"
    fi
done

# 6. Check container logs for errors
echo "Step 6: Checking container logs for errors..."
for container in "${CONTAINERS[@]}"; do
    echo "Checking $container logs..."
    ERROR_COUNT=$(docker logs $container --tail 50 2>&1 | grep -i error | wc -l)
    if [ $ERROR_COUNT -eq 0 ]; then
        echo "✅ No errors in $container logs"
    else
        echo "⚠️  Found $ERROR_COUNT errors in $container logs"
        echo "Recent errors:"
        docker logs $container --tail 10 2>&1 | grep -i error | head -3
    fi
done

# 7. Check network connectivity
echo "Step 7: Checking network connectivity..."
if docker network ls | grep -q regulationguru; then
    echo "✅ RegulationGuru network exists"
    
    # Check network connectivity between containers
    if docker exec regulationguru-app ping -c 1 regulationguru-db >/dev/null 2>&1; then
        echo "✅ App can reach database"
    else
        echo "❌ App cannot reach database"
    fi
    
    if docker exec regulationguru-app ping -c 1 regulationguru-redis >/dev/null 2>&1; then
        echo "✅ App can reach Redis"
    else
        echo "❌ App cannot reach Redis"
    fi
else
    echo "❌ RegulationGuru network does not exist"
fi

# 8. Check disk space
echo "Step 8: Checking disk space..."
DISK_USAGE=$(df -h . | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -lt 90 ]; then
    echo "✅ Disk space is adequate ($DISK_USAGE% used)"
else
    echo "⚠️  Disk space is running low ($DISK_USAGE% used)"
fi

# 9. Check memory usage
echo "Step 9: Checking memory usage..."
for container in "${CONTAINERS[@]}"; do
    MEMORY_USAGE=$(docker stats $container --no-stream --format "{{.MemPerc}}" | sed 's/%//')
    if (( $(echo "$MEMORY_USAGE < 80" | bc -l) )); then
        echo "✅ $container memory usage is normal ($MEMORY_USAGE%)"
    else
        echo "⚠️  $container memory usage is high ($MEMORY_USAGE%)"
    fi
done

echo "=== DOCKER HEALTH CHECK COMPLETED ==="
echo "Completed at: $(date)"
echo "✅ Docker environment is healthy and ready for operations"
