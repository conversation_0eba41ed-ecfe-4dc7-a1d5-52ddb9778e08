#!/usr/bin/env python3
"""
Test script for regulations CSV import functionality.

This script tests the CSV import system with the existing regulations_list.csv file
and demonstrates the soft-delete functionality.
"""

import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.db.models import Base
from app.db.models.regulations_csv import RegulationCSVRecord, RegulationCSVImportLog
from app.utils.regulations_csv_utils import RegulationCSVProcessor
from app.schemas.regulations_csv import RegulationCSVExportRequest


def setup_test_database():
    """Set up a test database for CSV operations."""
    # Use SQLite for testing
    engine = create_engine("sqlite:///test_regulations_csv.db", echo=True)
    
    # Create all tables
    Base.metadata.create_all(bind=engine)
    
    # Create session
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return SessionLocal()


def test_csv_import(db_session):
    """Test importing the regulations_list.csv file."""
    print("Testing CSV import functionality...")
    
    # Path to the regulations CSV file
    csv_file_path = project_root / "regulations_list.csv"
    
    if not csv_file_path.exists():
        print(f"Error: CSV file not found at {csv_file_path}")
        return False
    
    # Create processor and import
    processor = RegulationCSVProcessor(db_session)
    
    try:
        result = processor.import_csv_file(
            file_path=str(csv_file_path),
            batch_id="test_import_001",
            user_id="test_user"
        )
        
        print(f"Import completed successfully!")
        print(f"  Total records: {result.total_records}")
        print(f"  Successful imports: {result.successful_imports}")
        print(f"  Failed imports: {result.failed_imports}")
        print(f"  Updated records: {result.updated_records}")
        print(f"  Processing time: {result.processing_time:.2f} seconds")
        
        if result.errors:
            print(f"  Errors encountered: {len(result.errors)}")
            for error in result.errors[:5]:  # Show first 5 errors
                print(f"    Row {error['row']}: {error['error']}")
        
        return True
        
    except Exception as e:
        print(f"Import failed: {e}")
        return False


def test_soft_delete(db_session):
    """Test soft delete functionality."""
    print("\nTesting soft delete functionality...")
    
    # Get a record to test with
    record = db_session.query(RegulationCSVRecord).filter(
        RegulationCSVRecord.is_deleted == False
    ).first()
    
    if not record:
        print("No records found to test soft delete")
        return False
    
    print(f"Testing with record: {record.country_code} - {record.document_title[:50]}...")
    
    # Test soft delete
    original_id = record.id
    record.soft_delete()
    db_session.commit()
    
    # Verify record is soft deleted
    deleted_record = db_session.query(RegulationCSVRecord).filter(
        RegulationCSVRecord.id == original_id
    ).first()
    
    if deleted_record.is_deleted and deleted_record.deleted_at:
        print("✓ Soft delete successful")
        
        # Test restoration
        deleted_record.is_deleted = False
        deleted_record.deleted_at = None
        db_session.commit()
        
        restored_record = db_session.query(RegulationCSVRecord).filter(
            RegulationCSVRecord.id == original_id,
            RegulationCSVRecord.is_deleted == False
        ).first()
        
        if restored_record:
            print("✓ Record restoration successful")
            return True
        else:
            print("✗ Record restoration failed")
            return False
    else:
        print("✗ Soft delete failed")
        return False


def test_csv_export(db_session):
    """Test CSV export functionality."""
    print("\nTesting CSV export functionality...")
    
    # Create export request
    export_request = RegulationCSVExportRequest(
        filters={"country_code": "US"},
        include_deleted=False
    )
    
    # Export to temporary file
    export_file_path = project_root / "test_export.csv"
    
    processor = RegulationCSVProcessor(db_session)
    
    try:
        export_id = processor.export_csv_file(
            file_path=str(export_file_path),
            export_request=export_request,
            user_id="test_user"
        )
        
        print(f"Export completed successfully!")
        print(f"  Export ID: {export_id}")
        print(f"  File path: {export_file_path}")
        
        # Check if file exists and has content
        if export_file_path.exists():
            file_size = export_file_path.stat().st_size
            print(f"  File size: {file_size} bytes")
            
            # Clean up
            export_file_path.unlink()
            return True
        else:
            print("✗ Export file not created")
            return False
            
    except Exception as e:
        print(f"Export failed: {e}")
        return False


def test_database_queries(db_session):
    """Test various database queries."""
    print("\nTesting database queries...")
    
    # Count total records
    total_count = db_session.query(RegulationCSVRecord).filter(
        RegulationCSVRecord.is_deleted == False
    ).count()
    print(f"Total active records: {total_count}")
    
    # Count by country
    from sqlalchemy import func
    country_counts = db_session.query(
        RegulationCSVRecord.country_code,
        func.count(RegulationCSVRecord.id).label('count')
    ).filter(
        RegulationCSVRecord.is_deleted == False
    ).group_by(RegulationCSVRecord.country_code).all()
    
    print("Records by country:")
    for country, count in country_counts:
        print(f"  {country}: {count}")
    
    # Count by document type
    type_counts = db_session.query(
        RegulationCSVRecord.document_type,
        func.count(RegulationCSVRecord.id).label('count')
    ).filter(
        RegulationCSVRecord.is_deleted == False
    ).group_by(RegulationCSVRecord.document_type).all()
    
    print("Records by document type:")
    for doc_type, count in type_counts:
        print(f"  {doc_type}: {count}")
    
    return True


def main():
    """Main test function."""
    print("Starting regulations CSV system tests...")
    
    # Set up test database
    db_session = setup_test_database()
    
    try:
        # Run tests
        tests = [
            ("CSV Import", lambda: test_csv_import(db_session)),
            ("Soft Delete", lambda: test_soft_delete(db_session)),
            ("CSV Export", lambda: test_csv_export(db_session)),
            ("Database Queries", lambda: test_database_queries(db_session)),
        ]
        
        results = []
        for test_name, test_func in tests:
            print(f"\n{'='*50}")
            print(f"Running test: {test_name}")
            print('='*50)
            
            try:
                success = test_func()
                results.append((test_name, success))
                print(f"Test {test_name}: {'PASSED' if success else 'FAILED'}")
            except Exception as e:
                print(f"Test {test_name}: FAILED with exception: {e}")
                results.append((test_name, False))
        
        # Print summary
        print(f"\n{'='*50}")
        print("TEST SUMMARY")
        print('='*50)
        
        passed = sum(1 for _, success in results if success)
        total = len(results)
        
        for test_name, success in results:
            status = "PASSED" if success else "FAILED"
            print(f"{test_name}: {status}")
        
        print(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed!")
            return 0
        else:
            print("❌ Some tests failed")
            return 1
            
    finally:
        db_session.close()
        
        # Clean up test database
        test_db_path = Path("test_regulations_csv.db")
        if test_db_path.exists():
            test_db_path.unlink()
            print("\nTest database cleaned up")


if __name__ == "__main__":
    sys.exit(main())
