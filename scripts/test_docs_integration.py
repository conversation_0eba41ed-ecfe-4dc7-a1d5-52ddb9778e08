#!/usr/bin/env python3
"""
Test script for documentation integration and dark mode functionality.

This script tests the complete integration between the FastAPI application,
Sphinx documentation, and dark mode theme system.
"""

import os
import sys
import time
import requests
import subprocess
from pathlib import Path
from urllib.parse import urljoin


def test_documentation_serving():
    """Test that documentation is properly served by FastAPI."""
    print("🌐 Testing Documentation Serving")
    print("=" * 40)
    
    base_url = "http://localhost:8000"
    
    # Test endpoints
    endpoints_to_test = [
        ("/docs/", "Documentation index"),
        ("/docs/index.html", "Documentation index HTML"),
        ("/docs/_static/custom-theme.css", "Custom theme CSS"),
        ("/docs/_static/doc-theme-sync.js", "Theme sync JavaScript"),
        ("/documentation", "Documentation redirect"),
    ]
    
    results = []
    
    for endpoint, description in endpoints_to_test:
        try:
            url = urljoin(base_url, endpoint)
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ {description}: {response.status_code}")
                results.append((endpoint, True, response.status_code, None))
            elif response.status_code in [301, 302, 307, 308]:
                print(f"🔄 {description}: {response.status_code} (redirect)")
                results.append((endpoint, True, response.status_code, "redirect"))
            else:
                print(f"❌ {description}: {response.status_code}")
                results.append((endpoint, False, response.status_code, None))
                
        except requests.exceptions.RequestException as e:
            print(f"❌ {description}: Connection error - {e}")
            results.append((endpoint, False, None, str(e)))
    
    return results


def test_theme_files():
    """Test that theme files exist and have content."""
    print("\n🎨 Testing Theme Files")
    print("=" * 30)
    
    project_root = Path(__file__).parent.parent
    
    files_to_check = [
        ("statics/js/theme-manager.js", "Theme Manager JavaScript"),
        ("statics/css/main.css", "Main CSS with dark mode"),
        ("docs/_static/custom-theme.css", "Documentation theme CSS"),
        ("docs/_static/doc-theme-sync.js", "Documentation theme sync JS"),
        ("docs/_templates/layout.html", "Custom documentation layout"),
    ]
    
    results = []
    
    for file_path, description in files_to_check:
        full_path = project_root / file_path
        
        if full_path.exists():
            size = full_path.stat().st_size
            print(f"✅ {description}: {size} bytes")
            
            # Check for key content
            if file_path.endswith('.js'):
                with open(full_path, 'r') as f:
                    content = f.read()
                if 'dark-mode' in content or 'prefers-color-scheme' in content:
                    print(f"   ✅ Contains dark mode logic")
                else:
                    print(f"   ⚠️  Missing dark mode logic")
            
            results.append((file_path, True, size))
        else:
            print(f"❌ {description}: File not found")
            results.append((file_path, False, 0))
    
    return results


def test_documentation_build():
    """Test that documentation can be built successfully."""
    print("\n🏗️  Testing Documentation Build")
    print("=" * 35)
    
    project_root = Path(__file__).parent.parent
    docs_dir = project_root / "docs"
    
    if not docs_dir.exists():
        print("❌ Documentation directory not found")
        return False
    
    try:
        # Run the build script
        build_script = project_root / "scripts" / "build_docs.py"
        
        if build_script.exists():
            print("📋 Running build script...")
            result = subprocess.run(
                [sys.executable, str(build_script)],
                cwd=project_root,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            if result.returncode == 0:
                print("✅ Documentation build successful")
                return True
            else:
                print("❌ Documentation build failed")
                print("Error:", result.stderr)
                return False
        else:
            print("⚠️  Build script not found, trying direct sphinx-build...")
            
            result = subprocess.run(
                ["sphinx-build", "-b", "html", ".", "_build/html"],
                cwd=docs_dir,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            if result.returncode == 0:
                print("✅ Direct sphinx-build successful")
                return True
            else:
                print("❌ Direct sphinx-build failed")
                print("Error:", result.stderr)
                return False
                
    except subprocess.TimeoutExpired:
        print("❌ Documentation build timed out")
        return False
    except FileNotFoundError:
        print("❌ sphinx-build command not found")
        print("Please install Sphinx: pip install sphinx sphinx-rtd-theme")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def test_fastapi_integration():
    """Test FastAPI integration with documentation serving."""
    print("\n🚀 Testing FastAPI Integration")
    print("=" * 35)
    
    try:
        # Check if FastAPI app is running
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ FastAPI application is running")
        else:
            print("⚠️  FastAPI health check returned:", response.status_code)
    except requests.exceptions.RequestException:
        print("❌ FastAPI application not accessible")
        print("Please start the application: python -m uvicorn app.main:app --reload")
        return False
    
    # Test main application routes
    routes_to_test = [
        ("/", "Main dashboard"),
        ("/api/v1/docs", "API documentation"),
        ("/static/js/theme-manager.js", "Theme manager script"),
        ("/static/css/main.css", "Main CSS file"),
    ]
    
    for route, description in routes_to_test:
        try:
            response = requests.get(f"http://localhost:8000{route}", timeout=10)
            if response.status_code == 200:
                print(f"✅ {description}: Available")
            else:
                print(f"❌ {description}: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ {description}: {e}")
    
    return True


def test_theme_detection():
    """Test theme detection logic."""
    print("\n🌙 Testing Theme Detection Logic")
    print("=" * 35)
    
    project_root = Path(__file__).parent.parent
    theme_manager_path = project_root / "statics" / "js" / "theme-manager.js"
    
    if not theme_manager_path.exists():
        print("❌ Theme manager file not found")
        return False
    
    with open(theme_manager_path, 'r') as f:
        content = f.read()
    
    # Check for key features
    features_to_check = [
        ("prefers-color-scheme", "OS dark mode detection"),
        ("localStorage", "Theme preference storage"),
        ("matchMedia", "Media query support"),
        ("addEventListener", "Event handling"),
        ("dark-mode", "Dark mode class handling"),
        ("ThemeManager", "Main theme manager class"),
    ]
    
    for feature, description in features_to_check:
        if feature in content:
            print(f"✅ {description}: Found")
        else:
            print(f"❌ {description}: Missing")
    
    return True


def generate_test_report(results):
    """Generate a comprehensive test report."""
    print("\n📊 Test Report Summary")
    print("=" * 30)
    
    total_tests = 0
    passed_tests = 0
    
    for test_name, test_results in results.items():
        if isinstance(test_results, list):
            test_count = len(test_results)
            passed_count = sum(1 for result in test_results if result[1])
        elif isinstance(test_results, bool):
            test_count = 1
            passed_count = 1 if test_results else 0
        else:
            continue
        
        total_tests += test_count
        passed_tests += passed_count
        
        print(f"{test_name}: {passed_count}/{test_count} passed")
    
    print(f"\nOverall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed!")
        return True
    else:
        print("❌ Some tests failed")
        return False


def main():
    """Main test function."""
    print("🧪 RegulationGuru Documentation & Theme Integration Tests")
    print("=" * 60)
    print(f"📅 Started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run all tests
    results = {}
    
    print("\n" + "="*60)
    results["Theme Files"] = test_theme_files()
    
    print("\n" + "="*60)
    results["Documentation Build"] = test_documentation_build()
    
    print("\n" + "="*60)
    results["FastAPI Integration"] = test_fastapi_integration()
    
    print("\n" + "="*60)
    results["Theme Detection"] = test_theme_detection()
    
    print("\n" + "="*60)
    results["Documentation Serving"] = test_documentation_serving()
    
    # Generate final report
    print("\n" + "="*60)
    success = generate_test_report(results)
    
    # Provide usage instructions
    print("\n📋 Usage Instructions:")
    print("=" * 25)
    print("1. Start the application:")
    print("   python -m uvicorn app.main:app --reload")
    print()
    print("2. Access documentation:")
    print("   http://localhost:8000/docs/")
    print()
    print("3. Test dark mode:")
    print("   - Toggle with button in top-right corner")
    print("   - Change OS theme preference")
    print("   - Check browser developer tools")
    print()
    print("4. Build documentation:")
    print("   python scripts/build_docs.py")
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
