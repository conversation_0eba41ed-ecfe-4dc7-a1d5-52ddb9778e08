#!/usr/bin/env python3
"""
Comprehensive test runner for local regulation importer.

This script runs both unit tests and BDD tests for the local regulation
import functionality, providing detailed reporting and coverage analysis.
"""

import sys
import os
import subprocess
import tempfile
import shutil
from pathlib import Path
from datetime import datetime

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_unit_tests():
    """Run unit tests for local regulation importer."""
    print("🧪 Running Unit Tests")
    print("=" * 50)
    
    test_files = [
        "tests/test_local_regulation_importer.py",
        "tests/test_local_regulation_importer_edge_cases.py"
    ]
    
    results = {}
    
    for test_file in test_files:
        print(f"\n📋 Running {test_file}")
        print("-" * 30)
        
        try:
            # Run pytest with verbose output and coverage
            cmd = [
                sys.executable, "-m", "pytest", 
                test_file,
                "-v",
                "--tb=short",
                "--cov=app.services.local_regulation_importer",
                "--cov-report=term-missing"
            ]
            
            result = subprocess.run(
                cmd,
                cwd=project_root,
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes timeout
            )
            
            results[test_file] = {
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
            if result.returncode == 0:
                print(f"✅ {test_file} PASSED")
            else:
                print(f"❌ {test_file} FAILED")
                print("STDOUT:", result.stdout)
                print("STDERR:", result.stderr)
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {test_file} TIMED OUT")
            results[test_file] = {
                'returncode': -1,
                'stdout': '',
                'stderr': 'Test timed out'
            }
        except Exception as e:
            print(f"💥 {test_file} ERROR: {e}")
            results[test_file] = {
                'returncode': -2,
                'stdout': '',
                'stderr': str(e)
            }
    
    return results


def run_behave_tests():
    """Run BDD tests using Behave."""
    print("\n🎭 Running BDD Tests (Behave)")
    print("=" * 50)
    
    try:
        # Run behave tests
        cmd = [
            sys.executable, "-m", "behave",
            "features/local_regulation_import.feature",
            "--verbose",
            "--no-capture",
            "--format=pretty"
        ]
        
        result = subprocess.run(
            cmd,
            cwd=project_root,
            capture_output=True,
            text=True,
            timeout=600  # 10 minutes timeout
        )
        
        print("STDOUT:", result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        if result.returncode == 0:
            print("✅ BDD Tests PASSED")
        else:
            print("❌ BDD Tests FAILED")
        
        return {
            'returncode': result.returncode,
            'stdout': result.stdout,
            'stderr': result.stderr
        }
        
    except subprocess.TimeoutExpired:
        print("⏰ BDD Tests TIMED OUT")
        return {
            'returncode': -1,
            'stdout': '',
            'stderr': 'BDD tests timed out'
        }
    except Exception as e:
        print(f"💥 BDD Tests ERROR: {e}")
        return {
            'returncode': -2,
            'stdout': '',
            'stderr': str(e)
        }


def run_integration_test():
    """Run a simple integration test."""
    print("\n🔗 Running Integration Test")
    print("=" * 50)
    
    try:
        # Create a temporary test environment
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"📁 Using temporary directory: {temp_dir}")
            
            # Import required modules
            from sqlalchemy import create_engine
            from sqlalchemy.orm import sessionmaker
            from app.db.models import Base
            from app.services.local_regulation_importer import LocalRegulationImporter, LocalStorageConfig
            
            # Set up test database
            engine = create_engine("sqlite:///:memory:")
            Base.metadata.create_all(engine)
            SessionLocal = sessionmaker(bind=engine)
            db = SessionLocal()
            
            # Set up local storage
            storage_config = LocalStorageConfig(temp_dir)
            importer = LocalRegulationImporter(db, storage_config)
            
            # Create a test CSV file
            test_csv_content = """Country_Name,Country_Code,Document_Title,Document_Type,Issuing_Authority,Publication_Date,Effective_Date,Legal_Status,Document_URL,Language,Scope_Application,Key_Compliance_Requirements,Enforcement_Mechanisms,Penalties,Cross_Border_Elements,Data_Protection_Provisions,Incident_Reporting_Requirements,Risk_Management_Mandates,Third_Party_Requirements,Audit_Obligations,Certification_Requirements,Implementation_Timeline,International_Standards_Alignment,Extraterritorial_Reach,Safe_Harbor_Provisions,Industry_Specific_Provisions,Technology_Specific_Provisions
United States,US,Integration Test Regulation,Federal Law,Test Authority,2024-01-01,2024-01-01,Binding,https://example.com,English,Test scope,Test requirements,Test enforcement,Test penalties,Test cross-border,Test data protection,Test incident reporting,Test risk management,Test third party,Test audit,Test certification,Test timeline,Test standards,Test extraterritorial,Test safe harbor,Test industry,Test technology"""
            
            test_file = storage_config.imports_path / "integration_test.csv"
            test_file.write_text(test_csv_content)
            
            print("📝 Created test CSV file")
            
            # Test file discovery
            discovered_files = importer.discover_import_files()
            assert len(discovered_files) == 1, f"Expected 1 file, found {len(discovered_files)}"
            print("✅ File discovery works")
            
            # Test file validation
            is_valid, errors = importer.validate_file(test_file)
            assert is_valid, f"File validation failed: {errors}"
            print("✅ File validation works")
            
            # Test storage statistics
            stats = importer.get_storage_stats()
            assert stats['imports']['file_count'] == 1, "Storage stats incorrect"
            print("✅ Storage statistics work")
            
            # Test backup creation
            backup_path = importer.backup_file(test_file, "integration_test")
            assert backup_path.exists(), "Backup file not created"
            print("✅ File backup works")
            
            # Test file movement
            moved_path = importer.move_file_to_processed(test_file, success=True)
            assert moved_path.exists(), "File not moved correctly"
            assert not test_file.exists(), "Original file still exists"
            print("✅ File movement works")
            
            # Clean up
            db.close()
            
            print("🎉 Integration test PASSED")
            return {'returncode': 0, 'message': 'Integration test passed'}
            
    except Exception as e:
        print(f"💥 Integration test FAILED: {e}")
        return {'returncode': 1, 'message': f'Integration test failed: {e}'}


def generate_test_report(unit_results, behave_result, integration_result):
    """Generate a comprehensive test report."""
    print("\n📊 Test Report")
    print("=" * 50)
    
    # Count unit test results
    unit_passed = sum(1 for result in unit_results.values() if result['returncode'] == 0)
    unit_total = len(unit_results)
    
    # BDD test result
    behave_passed = behave_result['returncode'] == 0
    
    # Integration test result
    integration_passed = integration_result['returncode'] == 0
    
    print(f"📋 Unit Tests: {unit_passed}/{unit_total} passed")
    for test_file, result in unit_results.items():
        status = "✅ PASS" if result['returncode'] == 0 else "❌ FAIL"
        print(f"  - {test_file}: {status}")
    
    print(f"\n🎭 BDD Tests: {'✅ PASS' if behave_passed else '❌ FAIL'}")
    
    print(f"\n🔗 Integration Test: {'✅ PASS' if integration_passed else '❌ FAIL'}")
    
    # Overall result
    all_passed = unit_passed == unit_total and behave_passed and integration_passed
    
    print(f"\n🎯 Overall Result: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    # Generate summary
    total_tests = unit_total + 1 + 1  # unit tests + behave + integration
    passed_tests = unit_passed + (1 if behave_passed else 0) + (1 if integration_passed else 0)
    
    print(f"\n📈 Summary: {passed_tests}/{total_tests} test suites passed")
    print(f"📅 Test run completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return all_passed


def main():
    """Main test runner function."""
    print("🚀 Local Regulation Importer Test Suite")
    print("=" * 60)
    print(f"📅 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 Project root: {project_root}")
    
    # Check if required dependencies are available
    try:
        import pytest
        import behave
        print("✅ Test dependencies available")
    except ImportError as e:
        print(f"❌ Missing test dependency: {e}")
        print("Please install test dependencies:")
        print("  pip install pytest pytest-cov behave")
        return 1
    
    # Run all test suites
    unit_results = run_unit_tests()
    behave_result = run_behave_tests()
    integration_result = run_integration_test()
    
    # Generate report
    all_passed = generate_test_report(unit_results, behave_result, integration_result)
    
    # Return appropriate exit code
    return 0 if all_passed else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
