
"""
Translation utility script.

This script helps with extracting messages for translation and compiling 
translation files for use with Babel.

Usage:
  - Extract messages: python translate.py extract
  - Update translations: python translate.py update
  - Compile translations: python translate.py compile
"""
import sys
import os
import subprocess
from typing import List


def run_command(cmd: List[str]) -> None:
    """
    Run a shell command with proper error handling.
    
    Args:
        cmd (List[str]): Command and arguments to run
    """
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {' '.join(cmd)}")
        print(f"Error details: {e}")
        sys.exit(1)


def extract_messages() -> None:
    """Extract translatable messages from Python files and templates."""
    cmd = [
        "pybabel", "extract",
        "--input-dirs=.", 
        "--output-file=lang/messages.pot",
        "--project=FastAPI-Application",
        "--version=1.0",
        "--mapping-file=babel.cfg"
    ]
    run_command(cmd)
    print("Messages extracted to lang/messages.pot")


def update_translations() -> None:
    """Update translation files for all languages."""
    languages = ["en_US", "en_GB", "af", "de", "zu", "ro", "gsw"]
    
    for lang in languages:
        lang_dir = f"lang/{lang}/LC_MESSAGES"
        os.makedirs(lang_dir, exist_ok=True)
        
        po_file = f"{lang_dir}/messages.po"
        
        # If the .po file doesn't exist, initialize it
        if not os.path.exists(po_file):
            cmd = [
                "pybabel", "init",
                "--input-file=lang/messages.pot",
                f"--output-dir=lang",
                f"--locale={lang}"
            ]
            run_command(cmd)
            print(f"Created new translation file for {lang}")
        else:
            # Update existing translation file
            cmd = [
                "pybabel", "update",
                "--input-file=lang/messages.pot",
                f"--output-dir=lang",
                f"--locale={lang}"
            ]
            run_command(cmd)
            print(f"Updated translation file for {lang}")


def compile_translations() -> None:
    """Compile .po files to .mo files for all languages."""
    cmd = [
        "pybabel", "compile",
        "--directory=lang"
    ]
    run_command(cmd)
    print("Compiled all translation files")


if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python translate.py [extract|update|compile]")
        sys.exit(1)
        
    action = sys.argv[1]
    
    if action == "extract":
        extract_messages()
    elif action == "update":
        extract_messages()
        update_translations()
    elif action == "compile":
        compile_translations()
    else:
        print(f"Unknown action: {action}")
        print("Available actions: extract, update, compile")
        sys.exit(1)
