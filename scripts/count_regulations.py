
#!/usr/bin/env python3
"""
Script to count the number of regulation URLs in the database.
"""
import os
import sys
import logging
from pathlib import Path

# Add the parent directory to sys.path
script_dir = Path(__file__).parent.absolute()
project_root = script_dir.parent
print(f"Script directory: {script_dir}")
print(f"Project root: {project_root}")
sys.path.append(str(project_root))

# Set up Python path
print(f"Python path: {sys.path}")

# Import database modules
print("Importing database modules...")
try:
    from sqlalchemy import create_engine
    print("Database engine imported successfully")
    from sqlalchemy.orm import sessionmaker, Session
    from app.db.models import RegulationURL
    print("RegulationURL model imported successfully")
    from app.db.database import get_db, SessionLocal
    print("SQLAlchemy Session imported successfully")
except ImportError as e:
    print(f"Error importing database modules: {e}")
    sys.exit(1)

def count_regulation_urls():
    """
    Count the number of regulation URLs in the database.
    """
    print("Starting count_regulation_urls function")
    db = None
    try:
        # Get database session
        print("Getting database session...")
        db = SessionLocal()
        print("Database session obtained successfully")
        
        # Count total regulation URLs
        print("Counting total regulation URLs...")
        try:
            count = db.query(RegulationURL).count()
            print(f"Total regulation URLs: {count}")
            
            # Count by category
            print("Counting regulation URLs by category...")
            category_counts = db.query(
                RegulationURL.category, 
                func.count(RegulationURL.id)
            ).group_by(RegulationURL.category).all()
            
            print("\nRegulation URLs by category:")
            for category, count in category_counts:
                category_name = category if category else "Uncategorized"
                print(f"  {category_name}: {count}")
                
            # Count by confidence level ranges
            print("\nRegulation URLs by confidence level:")
            high_confidence = db.query(RegulationURL).filter(RegulationURL.confidence_level >= 0.8).count()
            medium_confidence = db.query(RegulationURL).filter(
                RegulationURL.confidence_level >= 0.5,
                RegulationURL.confidence_level < 0.8
            ).count()
            low_confidence = db.query(RegulationURL).filter(RegulationURL.confidence_level < 0.5).count()
            
            print(f"  High confidence (≥0.8): {high_confidence}")
            print(f"  Medium confidence (0.5-0.8): {medium_confidence}")
            print(f"  Low confidence (<0.5): {low_confidence}")
            
            return count
            
        except Exception as e:
            print(f"Error querying database: {e}")
            return None
            
    except Exception as e:
        print(f"Error in count_regulation_urls: {e}")
        return None
    finally:
        # Close database session
        if db:
            print("Closing database session")
            db.close()

if __name__ == "__main__":
    try:
        from sqlalchemy import func
        count = count_regulation_urls()
        if count is not None:
            print(f"\nSummary: Found {count} regulation URLs in the database.")
        else:
            print("\nFailed to count regulation URLs.")
            sys.exit(1)
    except Exception as e:
        print(f"Unhandled error: {e}")
        sys.exit(1)
