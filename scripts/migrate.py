
#!/usr/bin/env python
"""
Migration management script for Alembic.
Provides a simple CLI interface for common migration tasks.
"""
import sys
import subprocess
import argparse


def run_command(command):
    """Run an Alembic command."""
    print(f"Executing: {' '.join(command)}")
    subprocess.run(command, check=True)


def create_migration(message):
    """Create a new migration."""
    run_command(["alembic", "revision", "--autogenerate", "-m", message])


def upgrade(revision="head"):
    """Upgrade the database to a specific revision."""
    run_command(["alembic", "upgrade", revision])


def downgrade(revision="-1"):
    """Downgrade the database to a specific revision."""
    run_command(["alembic", "downgrade", revision])


def show_history():
    """Show migration history."""
    run_command(["alembic", "history"])


def show_current():
    """Show current migration revision."""
    run_command(["alembic", "current"])


def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Alembic migration manager")
    subparsers = parser.add_subparsers(dest="command", help="Command to run")

    # Create migration command
    create_parser = subparsers.add_parser("create", help="Create a new migration")
    create_parser.add_argument("message", help="Migration message")

    # Upgrade command
    upgrade_parser = subparsers.add_parser("upgrade", help="Upgrade the database")
    upgrade_parser.add_argument(
        "revision", nargs="?", default="head", help="Revision to upgrade to (default: head)"
    )

    # Downgrade command
    downgrade_parser = subparsers.add_parser("downgrade", help="Downgrade the database")
    downgrade_parser.add_argument(
        "revision", nargs="?", default="-1", help="Revision to downgrade to (default: -1)"
    )

    # History command
    subparsers.add_parser("history", help="Show migration history")

    # Current command
    subparsers.add_parser("current", help="Show current migration revision")

    args = parser.parse_args()

    if args.command == "create":
        create_migration(args.message)
    elif args.command == "upgrade":
        upgrade(args.revision)
    elif args.command == "downgrade":
        downgrade(args.revision)
    elif args.command == "history":
        show_history()
    elif args.command == "current":
        show_current()
    else:
        parser.print_help()
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
