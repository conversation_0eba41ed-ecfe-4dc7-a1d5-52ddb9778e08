#!/bin/bash

# Test Traefik integration with just the API service

set -e

echo "🧪 Testing Traefik integration with API service..."

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Create Traefik network if it doesn't exist
if ! docker network ls | grep -q "traefik"; then
    print_status "Creating Traefik network..."
    docker network create traefik
    print_success "Traefik network created"
fi

# Stop any existing containers
print_status "Stopping existing containers..."
docker compose down 2>/dev/null || true

# Create a minimal docker-compose for testing
cat > docker-compose.minimal.yml << 'EOF'
version: '3.8'

services:
  traefik:
    image: traefik:v3.0
    container_name: regulationguru-traefik-minimal
    command:
      - "--api.dashboard=false"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--log.level=INFO"
      - "--ping=true"
    ports:
      - "80:80"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - traefik
    labels:
      - "traefik.enable=true"

  db:
    image: postgres:14-alpine
    container_name: regulationguru-db-minimal
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: regulationguru
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - backend
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: regulationguru-api-minimal
    depends_on:
      db:
        condition: service_healthy
    environment:
      DATABASE_URL: **************************************/regulationguru
      SECRET_KEY: test-secret-key
      ENVIRONMENT: development
      LOG_LEVEL: info
    volumes:
      - ./app:/app/app
      - ./templates:/app/templates
      - ./statics:/app/statics
    networks:
      - traefik
      - backend
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.api-minimal.rule=Host(\`api.guru.localhost\`)"
      - "traefik.http.routers.api-minimal.service=api-minimal"
      - "traefik.http.services.api-minimal.loadbalancer.server.port=8000"
      - "traefik.http.services.api-minimal.loadbalancer.healthcheck.path=/api/v1/health"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  traefik:
    external: true
    name: traefik
  backend:
    name: regulationguru-backend-minimal
    driver: bridge

volumes:
  postgres_data:
    name: regulationguru_postgres_data_minimal
EOF

print_status "Starting minimal Traefik + API setup..."
docker compose -f docker-compose.minimal.yml up -d --build

print_status "Waiting for services to be ready..."
sleep 15

# Test the API endpoint
print_status "Testing API endpoint..."
if curl -s -f "http://api.guru.localhost/api/v1/health" > /dev/null 2>&1; then
    print_success "✅ API is accessible at http://api.guru.localhost/api/v1/health"
    
    # Get the actual response
    response=$(curl -s "http://api.guru.localhost/api/v1/health")
    echo "Response: $response"
else
    print_warning "❌ API is not accessible. Checking container logs..."
    echo ""
    echo "=== Traefik logs ==="
    docker logs regulationguru-traefik-minimal --tail 20
    echo ""
    echo "=== API logs ==="
    docker logs regulationguru-api-minimal --tail 20
fi

echo ""
print_status "Container status:"
docker compose -f docker-compose.minimal.yml ps

echo ""
print_status "To stop the test setup:"
echo "docker compose -f docker-compose.minimal.yml down"
