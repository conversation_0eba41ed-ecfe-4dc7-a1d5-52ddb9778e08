
"""
Analyze the confidence levels of regulatory URL assignments in the database.
"""
import logging
from typing import Dict, List

from sqlalchemy import func
from sqlalchemy.orm import Session

from app.db import models
from app.db import engine, get_db

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

from typing import Dict, List
from sqlalchemy import func
import logging
from app.db import get_db
from app.db import models

def analyze_confidence() -> Dict:
    """
    Analyze confidence levels for URLs in the database.
    
    Returns:
        Dict: Statistics about confidence levels
    """
    stats = {}
    
    # Get a database session
    db = next(get_db())
    
    try:
        # Get count of URLs
        total_urls = db.query(models.RegulationURL).count()
        stats["total_urls"] = total_urls
        
        if total_urls == 0:
            logging.info("No URLs found in database.")
            return stats
        
        # Get average confidence
        avg_confidence = db.query(func.avg(models.RegulationURL.confidence_level)).scalar()
        stats["average_confidence"] = float(avg_confidence) if avg_confidence is not None else 0.0
        
        # Get confidence distribution
        high_confidence = db.query(models.RegulationURL).filter(
            models.RegulationURL.confidence_level >= 0.7
        ).count()
        
        medium_confidence = db.query(models.RegulationURL).filter(
            models.RegulationURL.confidence_level >= 0.4,
            models.RegulationURL.confidence_level < 0.7
        ).count()
        
        low_confidence = db.query(models.RegulationURL).filter(
            models.RegulationURL.confidence_level < 0.4
        ).count()
        
        stats["confidence_distribution"] = {
            "high": high_confidence,
            "medium": medium_confidence,
            "low": low_confidence
        }
        
        # Get country counts
        countries = db.query(
            models.Country.name, 
            func.count(models.RegulationURL.id)
        ).join(
            models.Regulator, 
            models.Country.id == models.Regulator.country_id
        ).join(
            models.RegulationURL, 
            models.Regulator.id == models.RegulationURL.regulator_id
        ).group_by(
            models.Country.name
        ).order_by(
            func.count(models.RegulationURL.id).desc()
        ).limit(10).all()
        
        stats["top_countries"] = [{"name": name, "count": count} for name, count in countries]
        
        # Get regulator counts
        regulators = db.query(
            models.Regulator.name, 
            func.count(models.RegulationURL.id)
        ).join(
            models.RegulationURL, 
            models.Regulator.id == models.RegulationURL.regulator_id
        ).group_by(
            models.Regulator.name
        ).order_by(
            func.count(models.RegulationURL.id).desc()
        ).limit(10).all()
        
        stats["top_regulators"] = [{"name": name, "count": count} for name, count in regulators]
        
        return stats
        
    except Exception as e:
        logging.error(f"Error analyzing confidence levels: {e}")
        return {"error": str(e)}
    finally:
        db.close()

def print_report(stats: Dict) -> None:
    """
    Print a formatted report of the confidence analysis.
    
    Args:
        stats (Dict): Statistics from analyze_confidence
    """
    print("\n===== REGULATORY URL CONFIDENCE REPORT =====\n")
    
    if "error" in stats:
        print(f"Error: {stats['error']}")
        return
    
    print(f"Total URLs in database: {stats.get('total_urls', 0)}")
    
    if stats.get('total_urls', 0) > 0:
        print(f"\nAverage confidence level: {stats.get('average_confidence', 0):.2f}")
        
        print("\nConfidence distribution:")
        for level, count in stats.get('confidence_distribution', {}).items():
            pct = (count / stats.get('total_urls', 1)) * 100
            print(f"  - {level.capitalize()}: {count} ({pct:.1f}%)")
        
        if stats.get('top_countries'):
            print("\nTop countries:")
            for country in stats.get('top_countries', []):
                print(f"  - {country['name']}: {country['count']}")
        
        if stats.get('top_regulators'):
            print("\nTop regulators:")
            for regulator in stats.get('top_regulators', []):
                print(f"  - {regulator['name']}: {regulator['count']}")

def main():
    """Run the confidence analysis."""
    logging.info("Analyzing confidence levels for regulatory URLs...")
    stats = analyze_confidence()
    print_report(stats)

if __name__ == "__main__":
    main()
