#!/usr/bin/env python3
"""
Setup script for documentation and theme integration.

This script ensures that all components are properly configured for
serving Sphinx documentation with dark mode support.
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path


def check_dependencies():
    """Check if required dependencies are installed."""
    print("🔍 Checking Dependencies")
    print("=" * 30)
    
    dependencies = [
        ("sphinx", "Sphinx documentation generator"),
        ("sphinx_rtd_theme", "Read the Docs theme"),
        ("fastapi", "FastAPI web framework"),
        ("uvicorn", "ASGI server"),
    ]
    
    missing_deps = []
    
    for dep, description in dependencies:
        try:
            __import__(dep)
            print(f"✅ {description}: Available")
        except ImportError:
            print(f"❌ {description}: Missing")
            missing_deps.append(dep)
    
    if missing_deps:
        print(f"\n📦 Install missing dependencies:")
        print(f"pip install {' '.join(missing_deps)}")
        return False
    
    return True


def setup_documentation():
    """Set up documentation structure and build."""
    print("\n📚 Setting Up Documentation")
    print("=" * 35)
    
    project_root = Path(__file__).parent.parent
    docs_dir = project_root / "docs"
    
    # Ensure docs directory exists
    if not docs_dir.exists():
        print("❌ Documentation directory not found")
        return False
    
    # Check required files
    required_files = [
        "_static/custom-theme.css",
        "_static/doc-theme-sync.js",
        "_templates/layout.html",
        "conf.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = docs_dir / file_path
        if not full_path.exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing required files: {missing_files}")
        return False
    
    print("✅ All required documentation files present")
    
    # Build documentation
    print("🔨 Building documentation...")
    try:
        result = subprocess.run(
            ["sphinx-build", "-b", "html", ".", "_build/html"],
            cwd=docs_dir,
            capture_output=True,
            text=True,
            timeout=300
        )
        
        if result.returncode == 0:
            print("✅ Documentation built successfully")
            return True
        else:
            print("❌ Documentation build failed")
            print("Error:", result.stderr)
            return False
            
    except FileNotFoundError:
        print("❌ sphinx-build command not found")
        print("Install Sphinx: pip install sphinx sphinx-rtd-theme")
        return False
    except Exception as e:
        print(f"❌ Build error: {e}")
        return False


def setup_static_files():
    """Set up static files for theme support."""
    print("\n🎨 Setting Up Static Files")
    print("=" * 30)
    
    project_root = Path(__file__).parent.parent
    statics_dir = project_root / "statics"
    
    # Ensure statics directory exists
    statics_dir.mkdir(exist_ok=True)
    (statics_dir / "js").mkdir(exist_ok=True)
    (statics_dir / "css").mkdir(exist_ok=True)
    
    # Check theme files
    theme_files = [
        "js/theme-manager.js",
        "css/main.css",
    ]
    
    for file_path in theme_files:
        full_path = statics_dir / file_path
        if full_path.exists():
            size = full_path.stat().st_size
            print(f"✅ {file_path}: {size} bytes")
        else:
            print(f"❌ {file_path}: Missing")
            return False
    
    return True


def verify_fastapi_config():
    """Verify FastAPI configuration for documentation serving."""
    print("\n🚀 Verifying FastAPI Configuration")
    print("=" * 40)
    
    project_root = Path(__file__).parent.parent
    main_py = project_root / "app" / "main.py"
    
    if not main_py.exists():
        print("❌ app/main.py not found")
        return False
    
    with open(main_py, 'r') as f:
        content = f.read()
    
    # Check for documentation mounting
    if 'mount("/docs"' in content:
        print("✅ Documentation mounting configured")
    else:
        print("❌ Documentation mounting not configured")
        print("Add this to app/main.py:")
        print('app.mount("/docs", StaticFiles(directory="docs/_build/html", html=True), name="docs")')
        return False
    
    # Check for static files mounting
    if 'mount("/static"' in content:
        print("✅ Static files mounting configured")
    else:
        print("❌ Static files mounting not configured")
        return False
    
    return True


def create_gitignore_entries():
    """Ensure proper .gitignore entries for documentation builds."""
    print("\n📝 Updating .gitignore")
    print("=" * 25)
    
    project_root = Path(__file__).parent.parent
    gitignore_path = project_root / ".gitignore"
    
    entries_to_add = [
        "# Documentation builds",
        "docs/_build/",
        "docs/_static/.doctrees/",
        "",
        "# Local storage (already added)",
        "local_storage/",
        "data/imports/",
        "data/exports/",
        "data/temp/",
        "data/backups/",
        "storage/",
        "temp_files/",
        "uploaded_files/",
        "processed_files/",
    ]
    
    if gitignore_path.exists():
        with open(gitignore_path, 'r') as f:
            existing_content = f.read()
        
        # Check if documentation entries exist
        if "docs/_build/" in existing_content:
            print("✅ Documentation build directories already ignored")
        else:
            print("📝 Adding documentation build entries to .gitignore")
            with open(gitignore_path, 'a') as f:
                f.write("\n" + "\n".join(entries_to_add) + "\n")
            print("✅ .gitignore updated")
    else:
        print("📝 Creating .gitignore file")
        with open(gitignore_path, 'w') as f:
            f.write("\n".join(entries_to_add) + "\n")
        print("✅ .gitignore created")
    
    return True


def test_integration():
    """Test the complete integration."""
    print("\n🧪 Testing Integration")
    print("=" * 25)
    
    project_root = Path(__file__).parent.parent
    
    # Check if documentation was built
    docs_html = project_root / "docs" / "_build" / "html" / "index.html"
    if docs_html.exists():
        print("✅ Documentation HTML generated")
    else:
        print("❌ Documentation HTML not found")
        return False
    
    # Check if theme files are in the build
    theme_css = project_root / "docs" / "_build" / "html" / "_static" / "custom-theme.css"
    theme_js = project_root / "docs" / "_build" / "html" / "_static" / "doc-theme-sync.js"
    
    if theme_css.exists():
        print("✅ Theme CSS included in build")
    else:
        print("❌ Theme CSS not included in build")
        return False
    
    if theme_js.exists():
        print("✅ Theme JavaScript included in build")
    else:
        print("❌ Theme JavaScript not included in build")
        return False
    
    return True


def main():
    """Main setup function."""
    print("🛠️  RegulationGuru Documentation & Theme Setup")
    print("=" * 50)
    print("This script will set up documentation serving and dark mode theme integration.")
    print()
    
    # Run setup steps
    steps = [
        ("Dependencies", check_dependencies),
        ("Static Files", setup_static_files),
        ("FastAPI Config", verify_fastapi_config),
        ("Documentation", setup_documentation),
        ("GitIgnore", create_gitignore_entries),
        ("Integration Test", test_integration),
    ]
    
    failed_steps = []
    
    for step_name, step_func in steps:
        print("\n" + "="*60)
        try:
            if not step_func():
                failed_steps.append(step_name)
        except Exception as e:
            print(f"❌ {step_name} failed with error: {e}")
            failed_steps.append(step_name)
    
    # Final report
    print("\n" + "="*60)
    print("📊 Setup Summary")
    print("=" * 20)
    
    if failed_steps:
        print(f"❌ Failed steps: {', '.join(failed_steps)}")
        print("\nPlease resolve the issues above and run the setup again.")
        return 1
    else:
        print("🎉 All setup steps completed successfully!")
        
        print("\n📋 Next Steps:")
        print("=" * 15)
        print("1. Start the application:")
        print("   python -m uvicorn app.main:app --reload")
        print()
        print("2. Access the documentation:")
        print("   http://localhost:8000/docs/")
        print()
        print("3. Test dark mode:")
        print("   - Use the theme toggle button")
        print("   - Change your OS theme preference")
        print("   - Check browser developer tools")
        print()
        print("4. Run integration tests:")
        print("   python scripts/test_docs_integration.py")
        
        return 0


if __name__ == "__main__":
    sys.exit(main())
