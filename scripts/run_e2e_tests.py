#!/usr/bin/env python3
"""
E2E Test Runner for RegulationGuru
Comprehensive test runner with different test suites and reporting
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path


class E2ETestRunner:
    """E2E Test Runner with multiple test suites"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.test_dir = self.project_root / "tests" / "e2e"
        self.screenshots_dir = self.test_dir / "screenshots"
        self.reports_dir = self.project_root / "test-reports"
        
        # Ensure directories exist
        self.screenshots_dir.mkdir(parents=True, exist_ok=True)
        self.reports_dir.mkdir(parents=True, exist_ok=True)
    
    def run_smoke_tests(self):
        """Run smoke tests for basic functionality"""
        print("🔥 Running Smoke Tests...")
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir),
            "-m", "smoke or (e2e and not slow)",
            "--maxfail=3",
            "-v"
        ]
        return self._run_command(cmd)
    
    def run_full_e2e_tests(self):
        """Run complete E2E test suite"""
        print("🚀 Running Full E2E Test Suite...")
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir),
            "-m", "e2e",
            "--html=" + str(self.reports_dir / "e2e-report.html"),
            "--self-contained-html",
            "-v"
        ]
        return self._run_command(cmd)
    
    def run_accessibility_tests(self):
        """Run accessibility-focused tests"""
        print("♿ Running Accessibility Tests...")
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir),
            "-m", "accessibility",
            "--html=" + str(self.reports_dir / "accessibility-report.html"),
            "--self-contained-html",
            "-v"
        ]
        return self._run_command(cmd)
    
    def run_performance_tests(self):
        """Run performance tests"""
        print("⚡ Running Performance Tests...")
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir),
            "-m", "performance",
            "--html=" + str(self.reports_dir / "performance-report.html"),
            "--self-contained-html",
            "-v"
        ]
        return self._run_command(cmd)
    
    def run_api_tests(self):
        """Run API-focused tests"""
        print("🔌 Running API Tests...")
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir / "test_api_endpoints.py"),
            "--html=" + str(self.reports_dir / "api-report.html"),
            "--self-contained-html",
            "-v"
        ]
        return self._run_command(cmd)
    
    def run_ui_tests(self):
        """Run UI-focused tests"""
        print("🎨 Running UI Tests...")
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir / "test_dashboard.py"),
            str(self.test_dir / "test_user_workflows.py"),
            "--html=" + str(self.reports_dir / "ui-report.html"),
            "--self-contained-html",
            "-v"
        ]
        return self._run_command(cmd)
    
    def run_workflow_tests(self):
        """Run user workflow tests"""
        print("👤 Running User Workflow Tests...")
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir / "test_user_workflows.py"),
            "-m", "workflow or not (accessibility or performance)",
            "--html=" + str(self.reports_dir / "workflow-report.html"),
            "--self-contained-html",
            "-v"
        ]
        return self._run_command(cmd)
    
    def run_cross_browser_tests(self):
        """Run tests across multiple browsers"""
        print("🌐 Running Cross-Browser Tests...")
        browsers = ["chromium", "firefox", "webkit"]
        results = []
        
        for browser in browsers:
            print(f"Testing with {browser}...")
            cmd = [
                "python", "-m", "pytest",
                str(self.test_dir / "test_dashboard.py"),
                "--browser", browser,
                "--html=" + str(self.reports_dir / f"{browser}-report.html"),
                "--self-contained-html",
                "-v"
            ]
            result = self._run_command(cmd, capture_output=True)
            results.append((browser, result))
        
        return all(result for _, result in results)
    
    def run_parallel_tests(self):
        """Run tests in parallel for faster execution"""
        print("⚡ Running Parallel Tests...")
        try:
            # Install pytest-xdist if not available
            subprocess.run(["pip", "install", "pytest-xdist"], check=True, capture_output=True)
            
            cmd = [
                "python", "-m", "pytest",
                str(self.test_dir),
                "-n", "auto",
                "--html=" + str(self.reports_dir / "parallel-report.html"),
                "--self-contained-html",
                "-v"
            ]
            return self._run_command(cmd)
        except subprocess.CalledProcessError:
            print("⚠️  Could not install pytest-xdist, running sequential tests...")
            return self.run_full_e2e_tests()
    
    def run_custom_tests(self, test_pattern, markers=None):
        """Run custom test pattern"""
        print(f"🎯 Running Custom Tests: {test_pattern}")
        cmd = [
            "python", "-m", "pytest",
            str(self.test_dir),
            "-k", test_pattern,
            "-v"
        ]
        
        if markers:
            cmd.extend(["-m", markers])
        
        return self._run_command(cmd)
    
    def _run_command(self, cmd, capture_output=False):
        """Run command and return success status"""
        try:
            if capture_output:
                result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
                return result.returncode == 0
            else:
                result = subprocess.run(cmd, cwd=self.project_root)
                return result.returncode == 0
        except Exception as e:
            print(f"❌ Error running command: {e}")
            return False
    
    def cleanup_screenshots(self):
        """Clean up old screenshots"""
        print("🧹 Cleaning up old screenshots...")
        if self.screenshots_dir.exists():
            for screenshot in self.screenshots_dir.glob("*.png"):
                screenshot.unlink()
    
    def generate_summary_report(self):
        """Generate a summary report of all test runs"""
        print("📊 Generating Summary Report...")
        
        report_files = list(self.reports_dir.glob("*-report.html"))
        if not report_files:
            print("No test reports found.")
            return
        
        summary_html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>RegulationGuru E2E Test Summary</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .header { background: #2196f3; color: white; padding: 20px; border-radius: 8px; }
                .report-list { margin: 20px 0; }
                .report-item { 
                    background: #f5f5f5; 
                    padding: 15px; 
                    margin: 10px 0; 
                    border-radius: 4px; 
                    border-left: 4px solid #2196f3;
                }
                .report-item a { color: #1976d2; text-decoration: none; font-weight: bold; }
                .report-item a:hover { text-decoration: underline; }
                .timestamp { color: #666; font-size: 0.9em; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🛡️ RegulationGuru E2E Test Summary</h1>
                <p>Comprehensive test results for all test suites</p>
            </div>
            
            <div class="report-list">
                <h2>Available Test Reports</h2>
        """
        
        for report_file in sorted(report_files):
            report_name = report_file.stem.replace("-report", "").title()
            summary_html += f"""
                <div class="report-item">
                    <a href="{report_file.name}">{report_name} Test Report</a>
                    <div class="timestamp">Generated: {time.ctime(report_file.stat().st_mtime)}</div>
                </div>
            """
        
        summary_html += """
            </div>
            
            <div style="margin-top: 40px; padding: 20px; background: #e8f5e8; border-radius: 4px;">
                <h3>🚀 Quick Start Guide</h3>
                <ul>
                    <li><strong>Smoke Tests:</strong> Quick validation of core functionality</li>
                    <li><strong>Full E2E:</strong> Comprehensive end-to-end testing</li>
                    <li><strong>Accessibility:</strong> WCAG compliance and screen reader compatibility</li>
                    <li><strong>Performance:</strong> Page load times and resource optimization</li>
                    <li><strong>API Tests:</strong> Backend API endpoint validation</li>
                    <li><strong>UI Tests:</strong> Frontend user interface testing</li>
                </ul>
            </div>
        </body>
        </html>
        """
        
        summary_file = self.reports_dir / "index.html"
        summary_file.write_text(summary_html)
        print(f"📋 Summary report generated: {summary_file}")


def main():
    """Main entry point for E2E test runner"""
    parser = argparse.ArgumentParser(description="RegulationGuru E2E Test Runner")
    parser.add_argument("--suite", choices=[
        "smoke", "full", "accessibility", "performance", "api", "ui", 
        "workflow", "cross-browser", "parallel"
    ], default="smoke", help="Test suite to run")
    parser.add_argument("--custom", help="Custom test pattern to run")
    parser.add_argument("--markers", help="Pytest markers to filter tests")
    parser.add_argument("--cleanup", action="store_true", help="Clean up screenshots before running")
    parser.add_argument("--summary", action="store_true", help="Generate summary report only")
    
    args = parser.parse_args()
    
    runner = E2ETestRunner()
    
    if args.cleanup:
        runner.cleanup_screenshots()
    
    if args.summary:
        runner.generate_summary_report()
        return
    
    print("🛡️ RegulationGuru E2E Test Runner")
    print("=" * 50)
    
    success = False
    
    if args.custom:
        success = runner.run_custom_tests(args.custom, args.markers)
    elif args.suite == "smoke":
        success = runner.run_smoke_tests()
    elif args.suite == "full":
        success = runner.run_full_e2e_tests()
    elif args.suite == "accessibility":
        success = runner.run_accessibility_tests()
    elif args.suite == "performance":
        success = runner.run_performance_tests()
    elif args.suite == "api":
        success = runner.run_api_tests()
    elif args.suite == "ui":
        success = runner.run_ui_tests()
    elif args.suite == "workflow":
        success = runner.run_workflow_tests()
    elif args.suite == "cross-browser":
        success = runner.run_cross_browser_tests()
    elif args.suite == "parallel":
        success = runner.run_parallel_tests()
    
    # Generate summary report
    runner.generate_summary_report()
    
    if success:
        print("✅ Tests completed successfully!")
        sys.exit(0)
    else:
        print("❌ Some tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
