#!/bin/bash

# RegulationGuru Traefik Setup Script
# This script sets up the Traefik integration for local development

set -e

echo "🚀 Setting up RegulationGuru with Traefik integration..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

print_success "Docker is running"

# Create Traefik network if it doesn't exist
if ! docker network ls | grep -q "traefik"; then
    print_status "Creating Traefik network..."
    docker network create traefik
    print_success "Traefik network created"
else
    print_warning "Traefik network already exists"
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p traefik/logs
mkdir -p docs/_build/html
mkdir -p nginx
print_success "Directories created"

# Build Sphinx documentation
print_status "Building Sphinx documentation..."
if [ -f "docs/conf.py" ]; then
    cd docs
    if command -v sphinx-build &> /dev/null; then
        sphinx-build -b html . _build/html
        print_success "Documentation built with sphinx-build"
    elif python3 -c "import sphinx" &> /dev/null; then
        python3 -m sphinx -b html . _build/html
        print_success "Documentation built with python3 -m sphinx"
    else
        print_warning "Sphinx not found, creating placeholder documentation"
        mkdir -p _build/html
        cat > _build/html/index.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>RegulationGuru Documentation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>RegulationGuru Documentation</h1>
            <p>Welcome to the RegulationGuru documentation portal.</p>
            <p><strong>Note:</strong> This is a placeholder page. To build the full documentation, install Sphinx and run the setup script again.</p>
        </div>
        <h2>Quick Links</h2>
        <ul>
            <li><a href="http://api.guru.localhost">API Service</a></li>
            <li><a href="http://admin.guru.localhost">Admin Interface</a></li>
            <li><a href="http://api.guru.localhost/docs">API Documentation</a></li>
        </ul>
    </div>
</body>
</html>
EOF
        # Create health endpoint
        echo "healthy" > _build/html/health
    fi
    cd ..
else
    print_warning "docs/conf.py not found, creating minimal documentation structure"
    mkdir -p docs/_build/html
    echo "<h1>RegulationGuru Documentation</h1><p>Documentation will be available here.</p>" > docs/_build/html/index.html
    echo "healthy" > docs/_build/html/health
fi

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    print_status "Creating .env file..."
    cat > .env << 'EOF'
# RegulationGuru Environment Configuration

# Database Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=regulationguru

# Application Configuration
SECRET_KEY=your-secret-key-change-in-production
ENVIRONMENT=development
LOG_LEVEL=info

# Admin Configuration
ADMIN_USER=admin
ADMIN_PASSWORD=admin

# API Configuration
ALLOWED_ORIGINS=http://api.guru.localhost,http://docs.guru.localhost,http://admin.guru.localhost

# External API Keys (optional)
GEMINI_API_KEY=

# Grafana Configuration (for future use)
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=admin
EOF
    print_success ".env file created"
else
    print_warning ".env file already exists"
fi

# Validate docker-compose.yml
print_status "Validating docker-compose.yml..."
if docker compose config > /dev/null 2>&1; then
    print_success "docker-compose.yml is valid"
else
    print_error "docker-compose.yml has errors. Please check the configuration."
    exit 1
fi

# Build the application image
print_status "Building application images..."
docker compose build --no-cache
print_success "Application images built"

# Start the services
print_status "Starting services with Traefik..."
docker compose up -d

# Wait for services to be ready
print_status "Waiting for services to be ready..."
sleep 10

# Check service health
print_status "Checking service health..."

# Function to check if a URL is accessible
check_url() {
    local url=$1
    local name=$2
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "$url" > /dev/null 2>&1; then
            print_success "$name is accessible at $url"
            return 0
        fi
        print_status "Waiting for $name... (attempt $attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    print_error "$name is not accessible at $url after $max_attempts attempts"
    return 1
}

# Check each service
check_url "http://api.guru.localhost/api/v1/health" "API Service"
check_url "http://docs.guru.localhost/health" "Documentation Service"
check_url "http://admin.guru.localhost/health" "Admin Interface"

# Display service URLs
echo ""
print_success "🎉 RegulationGuru with Traefik is now running!"
echo ""
echo "📋 Service URLs:"
echo "   🔧 API Service:      http://api.guru.localhost"
echo "   📚 Documentation:    http://docs.guru.localhost"
echo "   ⚙️  Admin Interface:  http://admin.guru.localhost"
echo ""
echo "📖 API Documentation: http://api.guru.localhost/docs"
echo "🔍 API Health Check:  http://api.guru.localhost/api/v1/health"
echo ""
echo "🔐 Admin Credentials:"
echo "   Username: admin"
echo "   Password: admin"
echo ""
echo "🐳 Docker Commands:"
echo "   View logs:    docker compose logs -f"
echo "   Stop services: docker compose down"
echo "   Restart:      docker compose restart"
echo ""
print_warning "Note: Make sure your /etc/hosts file includes:"
print_warning "127.0.0.1 api.guru.localhost docs.guru.localhost admin.guru.localhost"
