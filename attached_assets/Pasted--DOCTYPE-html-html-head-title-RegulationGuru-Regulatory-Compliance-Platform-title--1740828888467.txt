
<!DOCTYPE html>
<html>
<head>
    <title>RegulationGuru - Regulatory Compliance Platform</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --bg-color: #ffffff;
            --bg-secondary: #f8f9fa;
            --header-bg: #ffffff;
            --text-color: #2d3748;
            --text-secondary: #4a5568;
            --primary-color: #3182ce;
            --primary-dark: #2c5282;
            --border-color: #e2e8f0;
            --box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            --endpoint-bg: #ffffff;
            --card-bg: #ffffff;
            --code-bg: #f7fafc;
            --method-get-bg: #63b3ed;
            --method-post-bg: #68d391;
            --method-put-bg: #f6ad55;
            --method-delete-bg: #fc8181;
            --greeting-bg: #ebf8ff;
            --greeting-border: #3182ce;
            --health-good: #48bb78;
            --health-bad: #f56565;
            --transition: all 0.3s ease;
        }
        
        [data-theme="dark"] {
            --bg-color: #1a202c;
            --bg-secondary: #2d3748;
            --header-bg: #2d3748;
            --text-color: #e2e8f0;
            --text-secondary: #cbd5e0;
            --primary-color: #63b3ed;
            --primary-dark: #90cdf4;
            --border-color: #4a5568;
            --box-shadow: 0 4px 6px rgba(0,0,0,0.3);
            --endpoint-bg: #2d3748;
            --card-bg: #323a4a;
            --code-bg: #2d3748;
            --greeting-bg: #2c3e50;
            --greeting-border: #63b3ed;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--bg-color);
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            transition: var(--transition);
        }
        
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            margin-bottom: 30px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .title-area {
            display: flex;
            align-items: center;
        }
        
        .logo {
            font-size: 24px;
            margin-right: 15px;
            color: var(--primary-color);
        }
        
        .theme-toggle {
            background: none;
            border: none;
            color: var(--text-color);
            cursor: pointer;
            font-size: 20px;
            padding: 8px;
            border-radius: 50%;
            transition: var(--transition);
        }
        
        .theme-toggle:hover {
            background-color: var(--bg-secondary);
        }
        
        .toggle-icon {
            transition: transform 0.3s;
        }
        
        h1, h2, h3 {
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
            line-height: 1.25;
        }
        
        h1 {
            color: var(--primary-color);
            padding-bottom: 0.3em;
            font-size: 2em;
            margin-top: 0;
        }
        
        h2 {
            color: var(--text-color);
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 0.3em;
        }
        
        code {
            font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
            background-color: var(--code-bg);
            border-radius: 4px;
            padding: 0.2em 0.4em;
            font-size: 85%;
        }
        
        pre {
            background-color: var(--code-bg);
            border-radius: 8px;
            padding: 16px;
            overflow: auto;
            line-height: 1.45;
        }
        
        pre code {
            background-color: transparent;
            padding: 0;
        }
        
        .endpoint {
            margin-bottom: 20px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            background-color: var(--endpoint-bg);
            box-shadow: var(--box-shadow);
            transition: var(--transition);
        }
        
        .endpoint:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.08);
        }
        
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: 600;
            margin-right: 10px;
            min-width: 60px;
            text-align: center;
            font-size: 14px;
        }
        
        .get {
            background-color: var(--method-get-bg);
            color: white;
        }
        
        .post {
            background-color: var(--method-post-bg);
            color: white;
        }
        
        .put {
            background-color: var(--method-put-bg);
            color: white;
        }
        
        .delete {
            background-color: var(--method-delete-bg);
            color: white;
        }
        
        .path {
            font-weight: 600;
            font-size: 16px;
        }
        
        .nav {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .nav-item {
            text-decoration: none;
            color: var(--primary-color);
            background-color: var(--card-bg);
            padding: 12px 18px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            box-shadow: var(--box-shadow);
            transition: var(--transition);
            border: 1px solid var(--border-color);
            font-weight: 500;
        }
        
        .nav-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.08);
            color: var(--primary-dark);
        }
        
        .nav-icon {
            margin-right: 10px;
        }
        
        .greeting {
            margin: 30px 0;
            padding: 25px;
            background-color: var(--greeting-bg);
            border-left: 4px solid var(--greeting-border);
            border-radius: 0 8px 8px 0;
            font-size: 1.1em;
            line-height: 1.7;
        }

        .card-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .card {
            background-color: var(--card-bg);
            border-radius: 8px;
            box-shadow: var(--box-shadow);
            padding: 25px;
            border: 1px solid var(--border-color);
            transition: var(--transition);
        }
        
        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.08);
        }
        
        .card h3 {
            margin-top: 0;
            display: flex;
            align-items: center;
            color: var(--primary-color);
        }
        
        .card-icon {
            margin-right: 12px;
            color: var(--primary-color);
        }
        
        .health-status {
            display: none;
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            box-shadow: var(--box-shadow);
        }
        
        .health-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .health-good {
            background-color: var(--health-good);
        }
        
        .health-bad {
            background-color: var(--health-bad);
        }
        
        #worldMapContainer {
            width: 100%;
            height: 500px;
            margin-top: 30px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--box-shadow);
            border: 1px solid var(--border-color);
        }
        
        .hidden {
            display: none;
        }
        
        #mapToggle {
            margin-bottom: 15px;
            padding: 10px 18px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
        }
        
        #mapToggle:hover {
            background-color: var(--primary-dark);
        }
        
        @media (max-width: 768px) {
            .card-container {
                grid-template-columns: 1fr;
            }
            
            .nav {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="title-area">
            <div class="logo"><i class="fas fa-globe"></i></div>
            <h1>RegulationGuru</h1>
        </div>
        <button class="theme-toggle" id="themeToggle" aria-label="Toggle dark/light mode">
            <i class="fas fa-moon toggle-icon"></i>
        </button>
    </header>
    
    <p>Regulatory compliance intelligence platform with global coverage</p>
    
    <div class="nav">
        <a href="/docs" class="nav-item">
            <i class="fas fa-book nav-icon"></i> API Documentation
        </a>
        <a href="/redoc" class="nav-item">
            <i class="fas fa-file-alt nav-icon"></i> ReDoc
        </a>
        <a href="/openapi.json" class="nav-item">
            <i class="fas fa-code nav-icon"></i> OpenAPI JSON
        </a>
        <a href="#" class="nav-item" id="healthCheckBtn">
            <i class="fas fa-heartbeat nav-icon"></i> Health Check
        </a>
        <a href="/analytics" class="nav-item">
            <i class="fas fa-chart-bar nav-icon"></i> Analytics
        </a>
        <a href="/regulatory" class="nav-item">
            <i class="fas fa-clipboard-check nav-icon"></i> Compliance
        </a>
    </div>
    
    <div class="greeting">
        <p>Welcome to RegulationGuru Dashboard</p>
    </div>
    
    <div class="card-container">
        <div class="card">
            <h3><i class="fas fa-globe card-icon"></i> Global Coverage</h3>
            <p>Access regulatory information from countries around the world with our comprehensive database.</p>
        </div>
        <div class="card">
            <h3><i class="fas fa-search card-icon"></i> Intelligent Search</h3>
            <p>Find relevant regulations quickly with our powerful search capabilities and AI-powered recommendations.</p>
        </div>
        <div class="card">
            <h3><i class="fas fa-shield-alt card-icon"></i> Compliance Tracking</h3>
            <p>Monitor regulatory changes and ensure your organization stays compliant with automated alerts.</p>
        </div>
        <div class="card">
            <h3><i class="fas fa-chart-line card-icon"></i> Analytics</h3>
            <p>Gain insights from comprehensive analytics and visualize your compliance status across regions.</p>
        </div>
    </div>
    
    <div id="healthStatus" class="health-status">
        <h3><i class="fas fa-heartbeat card-icon"></i> System Health</h3>
        <div id="healthContent">Loading health information...</div>
    </div>
    
    <button id="mapToggle">Show World Map</button>
    <div id="worldMapContainer" class="hidden"></div>

    <script>
        // Theme switcher
        const themeToggleBtn = document.getElementById('themeToggle');
        const icon = themeToggleBtn.querySelector('.toggle-icon');
        
        // Check for saved theme preference or prefer-color-scheme
        const savedTheme = localStorage.getItem('theme');
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        
        if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
            document.documentElement.setAttribute('data-theme', 'dark');
            icon.classList.remove('fa-moon');
            icon.classList.add('fa-sun');
        }
        
        themeToggleBtn.addEventListener('click', () => {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            
            if (newTheme === 'dark') {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
            } else {
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
            }
        });
        
        // Health check functionality
        const healthCheckBtn = document.getElementById('healthCheckBtn');
        const healthStatus = document.getElementById('healthStatus');
        const healthContent = document.getElementById('healthContent');
        
        healthCheckBtn.addEventListener('click', (e) => {
            e.preventDefault();
            healthStatus.style.display = 'block';
            healthContent.innerHTML = 'Loading health information...';
            
            fetch('/health')
                .then(response => response.json())
                .then(data => {
                    let html = `
                        <div>
                            <p><strong>Status:</strong> 
                                <span class="health-indicator ${data.status === 'healthy' ? 'health-good' : 'health-bad'}"></span>
                                ${data.status}
                            </p>
                            <p><strong>Database:</strong> 
                                <span class="health-indicator ${data.database === 'healthy' ? 'health-good' : 'health-bad'}"></span>
                                ${data.database}
                            </p>
                            <p><strong>Timestamp:</strong> ${new Date(data.timestamp).toLocaleString()}</p>
                        </div>
                    `;
                    
                    if (data.test_coverage && Object.keys(data.test_coverage).length > 0) {
                        html += `<h4>Test Coverage:</h4><ul>`;
                        for (const [key, value] of Object.entries(data.test_coverage)) {
                            html += `<li>${key}: ${value}%</li>`;
                        }
                        html += `</ul>`;
                    }
                    
                    healthContent.innerHTML = html;
                })
                .catch(error => {
                    healthContent.innerHTML = `<p class="error">Error fetching health data: ${error.message}</p>`;
                });
        });
        
        // World map toggle
        const mapToggle = document.getElementById('mapToggle');
        const mapContainer = document.getElementById('worldMapContainer');
        let mapLoaded = false;
        
        mapToggle.addEventListener('click', () => {
            if (mapContainer.classList.contains('hidden')) {
                mapContainer.classList.remove('hidden');
                mapToggle.textContent = 'Hide World Map';
                
                if (!mapLoaded) {
                    // Load the map the first time it's shown
                    fetch('/api/v1/worldmap?lat=20&lon=0&zoom=2')
                        .then(response => response.text())
                        .then(html => {
                            // Extract just the map HTML from the response
                            const parser = new DOMParser();
                            const doc = parser.parseFromString(html, 'text/html');
                            const mapElement = doc.body.firstChild;
                            
                            if (mapElement) {
                                mapContainer.innerHTML = '';
                                mapContainer.appendChild(mapElement);
                                mapLoaded = true;
                            }
                        })
                        .catch(error => {
                            mapContainer.innerHTML = `<p>Error loading map: ${error.message}</p>`;
                        });
                }
            } else {
                mapContainer.classList.add('hidden');
                mapToggle.textContent = 'Show World Map';
            }
        });
    </script>
</body>
</html>