

<!DOCTYPE html>
<html lang="en_US">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RegulationGuru - Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/main.css">
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2ecc71;
            --info-color: #00BCD4;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --text-light: #f8f9fa;
            --text-dark: #212529;
            --card-bg-light: #ffffff;
            --card-bg-dark: #2d2d2d;
        }
        
        body {
            font-family: 'Roboto', Arial, sans-serif;
            margin: 0;
            padding: 0;
            color: var(--text-dark);
            background-color: #f5f5f5;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        
        body.dark-mode {
            background-color: #121212;
            color: var(--text-light);
        }
        
        header {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        body.dark-mode header {
            background-color: #1e1e1e;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .title-area {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .logo {
            font-size: 1.5rem;
            color: var(--primary-color);
        }
        
        h1 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 700;
        }
        
        main {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        footer {
            text-align: center;
            padding: 1rem;
            background-color: white;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
        }
        
        body.dark-mode footer {
            background-color: #1e1e1e;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.3);
        }
        
        /* Header controls */
        .header-controls {
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .theme-toggle {
            background: none;
            border: none;
            color: inherit;
            cursor: pointer;
            font-size: 1.2rem;
            padding: 0.5rem;
            border-radius: 50%;
            transition: background-color 0.3s;
        }
        
        .theme-toggle:hover {
            background-color: rgba(0,0,0,0.1);
        }
        
        body.dark-mode .theme-toggle:hover {
            background-color: rgba(255,255,255,0.1);
        }
        
        /* Language selector */
        #language-selector-container {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1000;
            background-color: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            max-width: 80%;
            width: 400px;
        }
        
        body.dark-mode #language-selector-container {
            background-color: #2d2d2d;
            box-shadow: 0 4px 12px rgba(0,0,0,0.4);
        }
        
        #language-selector-background {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0,0,0,0.5);
            z-index: 999;
        }
    </style>
    
</head>
<body>
    <header>
        <div class="title-area">
            <div class="logo"><i class="fas fa-globe"></i></div>
            <h1>RegulationGuru</h1>
        </div>
        <div class="header-controls">
            
<div class="language-nav-selector">
  <button id="nav-language-button" aria-label="Select language">
    <span class="flag">🇺🇸</span>
    <span class="language-name">English (US)</span>
    <span class="dropdown-arrow">▼</span>
  </button>
  <div class="language-dropdown">
    
      
        <a href="?lang=en_US" class="language-option active">
          <span class="flag">🇺🇸</span>
          <span class="language-name">English (US)</span>
        </a>
      
        <a href="?lang=en_GB" class="language-option ">
          <span class="flag">🇬🇧</span>
          <span class="language-name">English (UK)</span>
        </a>
      
        <a href="?lang=es" class="language-option ">
          <span class="flag">🇪🇸</span>
          <span class="language-name">Español</span>
        </a>
      
        <a href="?lang=de" class="language-option ">
          <span class="flag">🇩🇪</span>
          <span class="language-name">Deutsch</span>
        </a>
      
        <a href="?lang=af" class="language-option ">
          <span class="flag">🇿🇦</span>
          <span class="language-name">Afrikaans</span>
        </a>
      
        <a href="?lang=zu" class="language-option ">
          <span class="flag">🇿🇦</span>
          <span class="language-name">isiZulu</span>
        </a>
      
    
  </div>
</div>
            <button class="theme-toggle" id="themeToggle" aria-label="Toggle dark/light mode">
                <i class="fas fa-moon toggle-icon"></i>
            </button>
        </div>
    </header>

    <main>
        <p>Regulatory compliance intelligence platform with global coverage</p>
        
<main class="container">
    <h1></h1>
    
    <div class="dashboard-grid">
        <div class="dashboard-card">
            <h2><i class="fas fa-globe"></i> Global Regulatory Overview</h2>
            <p>Track regulatory changes across multiple jurisdictions</p>
            <a href="/regulatory_dashboard" class="dashboard-link">View Details</a>
        </div>
        
        <div class="dashboard-card">
            <h2><i class="fas fa-calendar-alt"></i> Compliance Calendar</h2>
            <p>Stay updated with upcoming regulatory deadlines</p>
            <a href="/compliance_calendar" class="dashboard-link">View Calendar</a>
        </div>
        
        <div class="dashboard-card">
            <h2><i class="fas fa-file-upload"></i> Document Import</h2>
            <p>Import and analyze regulatory documents</p>
            <a href="/document-import" class="dashboard-link">Upload Documents</a>
        </div>
        
        <div class="dashboard-card">
            <h2><i class="fas fa-chart-bar"></i> Analytics</h2>
            <p>Visualize regulatory trends and impacts</p>
            <a href="/analytics_dashboard" class="dashboard-link">View Analytics</a>
        </div>
    </div>
    
    
</main>

    </main>

    <footer>
        <p>© 2024 RegulationGuru</p>
    </footer>

    <!-- Language Selector -->
    <div id="language-selector-container">
        <div class="language-selector">
  <button id="language-button" aria-label="Select language">
    <span class="flag">🇺🇸</span>
    <span class="language-name">English (US)</span>
    <span class="dropdown-arrow">▼</span>
  </button>
  <div class="language-dropdown">
    
      <a href="?lang=en_US" class="language-option active">
        <span class="flag">🇺🇸</span>
        <span class="language-name">English (US)</span>
      </a>
    
      <a href="?lang=en_GB" class="language-option ">
        <span class="flag">🇬🇧</span>
        <span class="language-name">English (UK)</span>
      </a>
    
      <a href="?lang=es" class="language-option ">
        <span class="flag">🇪🇸</span>
        <span class="language-name">Español</span>
      </a>
    
      <a href="?lang=de" class="language-option ">
        <span class="flag">🇩🇪</span>
        <span class="language-name">Deutsch</span>
      </a>
    
      <a href="?lang=af" class="language-option ">
        <span class="flag">🇿🇦</span>
        <span class="language-name">Afrikaans</span>
      </a>
    
      <a href="?lang=zu" class="language-option ">
        <span class="flag">🇿🇦</span>
        <span class="language-name">isiZulu</span>
      </a>
    
  </div>
</div>

<style>
  .language-selector {
    position: relative;
    display: inline-block;
  }

  #language-button {
    background: none;
    border: none;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    color: var(--text-color);
    padding: 6px 10px;
    font-size: 14px;
    border-radius: 6px;
  }

  #language-button:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .language-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    min-width: 150px;
    display: none;
    flex-direction: column;
    padding: 8px 0;
  }

  .language-option {
    padding: 8px 16px;
    text-decoration: none;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .language-option:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .language-option.active {
    background-color: rgba(0, 0, 0, 0.07);
    font-weight: 500;
  }

  .dropdown-arrow {
    font-size: 8px;
    transition: transform 0.2s ease;
  }

  .language-selector.open .language-dropdown {
    display: flex;
  }

  .language-selector.open .dropdown-arrow {
    transform: rotate(180deg);
  }
</style>

<script>
  console.log('Language selector script loaded');
  document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing language selector');
    const languageButton = document.getElementById('language-button');
    const languageSelector = document.querySelector('.language-selector');

    if (!languageButton || !languageSelector) {
      console.error('Language selector elements not found');
      return;
    }
    console.log('Language selector elements found');

    // Toggle dropdown when button is clicked
    languageButton.addEventListener('click', function(e) {
      e.preventDefault();
      languageSelector.classList.toggle('open');
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
      if (languageSelector && !languageSelector.contains(e.target)) {
        languageSelector.classList.remove('open');
      }
    });

    // Log that the language selector is ready
    console.log('Language selector initialized');

    // Add transition for smoother animation
    const style = document.createElement('style');
    style.textContent = `
      .language-dropdown {
        transition: opacity 0.2s ease, transform 0.2s ease;
        opacity: 0;
        transform: translateY(10px);
      }
      .language-selector.open .language-dropdown {
        display: flex;
        opacity: 1;
        transform: translateY(0);
      }
    `;
    document.head.appendChild(style);
  });
</script>
    </div>
    <div id="language-selector-background"></div>

    <script src="/static/js/main.js"></script>
    <script>
        // Dark mode toggle
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggle = document.getElementById('themeToggle');
            const toggleIcon = themeToggle.querySelector('.toggle-icon');

            // Check for saved theme preference or prefer-color-scheme
            const savedTheme = localStorage.getItem('theme');
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

            if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
                document.body.classList.add('dark-mode');
                toggleIcon.classList.remove('fa-moon');
                toggleIcon.classList.add('fa-sun');
            }

            // Theme toggle functionality
            themeToggle.addEventListener('click', function() {
                document.body.classList.toggle('dark-mode');

                if (document.body.classList.contains('dark-mode')) {
                    toggleIcon.classList.remove('fa-moon');
                    toggleIcon.classList.add('fa-sun');
                    localStorage.setItem('theme', 'dark');
                } else {
                    toggleIcon.classList.remove('fa-sun');
                    toggleIcon.classList.add('fa-moon');
                    localStorage.setItem('theme', 'light');
                }
            });
        });
    </script>
    
</body>
</html>