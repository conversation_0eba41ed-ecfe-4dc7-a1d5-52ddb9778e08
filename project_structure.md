# RegulationGuru Project Structure

## Component Architecture

```mermaid
graph TD
    subgraph Frontend
        UI[React UI]
        Charts[Recharts Visualizations]
        Dashboard[Regulatory Dashboard]
    end
    
    subgraph Backend
        API[FastAPI Backend]
        DB[(Database)]
        Parser[Regulatory Parser]
        Mapper[Relationship Mapper]
        PDFAnalyzer[PDF Analyzer]
    end
    
    subgraph AI_Integration
        Gemini[Gemini API]
        RateLimiter[Rate Limiter]
        Enrichment[Data Enrichment]
    end
    
    subgraph Testing
        UnitTests[Unit Tests]
        IntegrationTests[Integration Tests]
        UITests[Playwright UI Tests]
    end
    
    UI --> API
    Charts --> API
    Dashboard --> API
    
    API --> DB
    API --> Parser
    API --> Mapper
    API --> PDFAnalyzer
    API --> Gemini
    
    Gemini --> RateLimiter
    RateLimiter --> Enrichment
    
    Parser --> IntegrationTests
    Mapper --> IntegrationTests
    PDFAnalyzer --> IntegrationTests
    API --> IntegrationTests
    UI --> UITests
end
```

## Data Flow

```mermaid
sequenceDiagram
    participant User
    participant UI as React UI
    participant API as FastAPI Backend
    participant Parser as Regulatory Parser
    participant Mapper as Relationship Mapper
    participant Gemini as Gemini API
    participant DB as Database
    
    User->>UI: Upload regulatory document
    UI->>API: POST /api/document/upload
    API->>Parser: Parse document
    Parser->>API: Return parsed content
    API->>Mapper: Map relationships
    Mapper->>API: Return relationships
    API->>Gemini: Enrich data (rate limited)
    Gemini->>API: Return enriched data
    API->>DB: Store document and analysis
    API->>UI: Return success
    UI->>User: Display analysis results
```

## Integration Test Flow

```mermaid
flowchart TD
    A[Start Tests] --> B{Check Dependencies}
    B -->|Missing| C[Install Dependencies]
    C --> D[Setup Test Environment]
    B -->|Present| D
    D --> E[Run Basic Tests]
    E --> F{Tests Pass?}
    F -->|Yes| G[Run Integration Tests]
    F -->|No| H[Fix Issues]
    H --> E
    G --> I{Integration Tests Pass?}
    I -->|Yes| J[Generate Coverage Report]
    I -->|No| K[Debug Integration Issues]
    K --> G
    J --> L[End Tests]
```
