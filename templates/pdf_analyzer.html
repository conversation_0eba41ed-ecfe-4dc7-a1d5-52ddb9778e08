
{% extends "base.html" %}

{% block title %}PDF Document Analyzer{% endblock %}

{% block content %}
<div class="container my-4">
  <h1 class="mb-4">PDF Document Analyzer</h1>
  
  <div class="card mb-4">
    <div class="card-header">
      <h5 class="mb-0">Upload Regulatory Document</h5>
    </div>
    <div class="card-body">
      <form id="pdf-upload-form" enctype="multipart/form-data">
        <div class="mb-3">
          <label for="pdf-file" class="form-label">PDF Document</label>
          <input type="file" class="form-control" id="pdf-file" name="document" accept=".pdf" required>
          <div class="form-text">Upload a PDF document containing regulatory text for analysis.</div>
        </div>
        
        <div class="mb-3">
          <label for="country" class="form-label">Country (Optional)</label>
          <select class="form-select" id="country" name="country">
            <option value="">All Countries</option>
            <option value="US">United States</option>
            <option value="EU">European Union</option>
            <option value="UK">United Kingdom</option>
            <option value="CA">Canada</option>
            <option value="AU">Australia</option>
            <option value="JP">Japan</option>
            <option value="Other">Other</option>
          </select>
        </div>
        
        <button type="submit" class="btn btn-primary">Analyze Document</button>
      </form>
    </div>
  </div>
  
  <div id="status-container" class="d-none alert alert-info">
    <div class="d-flex align-items-center">
      <div class="spinner-border spinner-border-sm me-2" role="status"></div>
      <div>Analyzing document... <span id="status-message"></span></div>
    </div>
  </div>
  
  <div id="results-container" class="d-none">
    <h2 class="mb-3">Analysis Results</h2>
    
    <div class="row">
      <div class="col-md-6">
        <div class="card mb-3">
          <div class="card-header">
            <h5 class="mb-0">Document Summary</h5>
          </div>
          <div class="card-body">
            <p id="doc-summary" class="mb-0"></p>
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="card mb-3">
          <div class="card-header">
            <h5 class="mb-0">Confidence Score</h5>
          </div>
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="progress flex-grow-1 me-3" style="height: 30px;">
                <div id="confidence-bar" class="progress-bar" role="progressbar"></div>
              </div>
              <div id="confidence-score" class="fs-5 fw-bold"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-md-6">
        <div class="card mb-3">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Regulatory Requirements</h5>
            <span id="requirements-count" class="badge bg-primary"></span>
          </div>
          <div class="card-body">
            <ul id="requirements-list" class="list-group"></ul>
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="card mb-3">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Penalties & Enforcement</h5>
            <span id="penalties-count" class="badge bg-warning text-dark"></span>
          </div>
          <div class="card-body">
            <ul id="penalties-list" class="list-group"></ul>
          </div>
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-md-6">
        <div class="card mb-3">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Articles & Sections</h5>
            <span id="sections-count" class="badge bg-secondary"></span>
          </div>
          <div class="card-body">
            <div class="accordion" id="sections-accordion"></div>
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="card mb-3">
          <div class="card-header">
            <h5 class="mb-0">Key Entities</h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6 mb-3">
                <h6>Organizations</h6>
                <ul id="organizations-list" class="list-unstyled"></ul>
              </div>
              <div class="col-md-6 mb-3">
                <h6>Regulatory Terms</h6>
                <ul id="terms-list" class="list-unstyled"></ul>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6 mb-3">
                <h6>Monetary Values</h6>
                <ul id="money-list" class="list-unstyled"></ul>
              </div>
              <div class="col-md-6 mb-3">
                <h6>Dates</h6>
                <ul id="dates-list" class="list-unstyled"></ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('pdf-upload-form');
    const statusContainer = document.getElementById('status-container');
    const statusMessage = document.getElementById('status-message');
    const resultsContainer = document.getElementById('results-container');
    
    let jobId = null;
    let statusCheckInterval = null;
    
    form.addEventListener('submit', async function(e) {
      e.preventDefault();
      
      // Reset UI
      resetResults();
      
      // Show status
      statusContainer.classList.remove('d-none');
      statusMessage.textContent = 'Starting analysis...';
      
      // Submit form
      const formData = new FormData(form);
      
      try {
        const response = await fetch('/pdf/analyze', {
          method: 'POST',
          body: formData
        });
        
        if (!response.ok) {
          throw new Error(`Server error: ${response.status}`);
        }
        
        const data = await response.json();
        jobId = data.job_id;
        
        // Start checking status
        statusMessage.textContent = 'Processing document...';
        statusCheckInterval = setInterval(checkStatus, 2000);
        
      } catch (error) {
        statusContainer.classList.add('alert-danger');
        statusContainer.classList.remove('alert-info');
        statusMessage.textContent = `Error: ${error.message}`;
      }
    });
    
    async function checkStatus() {
      if (!jobId) return;
      
      try {
        const response = await fetch(`/pdf/status/${jobId}`);
        if (!response.ok) {
          throw new Error(`Server error: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.status === 'completed') {
          clearInterval(statusCheckInterval);
          statusContainer.classList.add('d-none');
          displayResults(data.result);
        } else if (data.status === 'failed') {
          clearInterval(statusCheckInterval);
          statusContainer.classList.add('alert-danger');
          statusContainer.classList.remove('alert-info');
          statusMessage.textContent = `Analysis failed: ${data.error || 'Unknown error'}`;
        }
        
      } catch (error) {
        clearInterval(statusCheckInterval);
        statusContainer.classList.add('alert-danger');
        statusContainer.classList.remove('alert-info');
        statusMessage.textContent = `Error checking status: ${error.message}`;
      }
    }
    
    function resetResults() {
      resultsContainer.classList.add('d-none');
      statusContainer.classList.add('alert-info');
      statusContainer.classList.remove('alert-danger');
      
      // Clear previous interval if any
      if (statusCheckInterval) {
        clearInterval(statusCheckInterval);
      }
      
      // Reset job ID
      jobId = null;
    }
    
    function displayResults(result) {
      // Show results container
      resultsContainer.classList.remove('d-none');
      
      // Document summary
      document.getElementById('doc-summary').textContent = result.summary;
      
      // Confidence score
      const confidencePercent = Math.round(result.confidence_score * 100);
      document.getElementById('confidence-score').textContent = `${confidencePercent}%`;
      const confidenceBar = document.getElementById('confidence-bar');
      confidenceBar.style.width = `${confidencePercent}%`;
      
      // Set color based on confidence
      if (confidencePercent >= 70) {
        confidenceBar.classList.add('bg-success');
      } else if (confidencePercent >= 40) {
        confidenceBar.classList.add('bg-warning');
      } else {
        confidenceBar.classList.add('bg-danger');
      }
      
      // Requirements
      const requirementsList = document.getElementById('requirements-list');
      const requirements = result.regulatory_sections.requirements || [];
      document.getElementById('requirements-count').textContent = requirements.length;
      
      requirementsList.innerHTML = '';
      requirements.forEach(requirement => {
        const li = document.createElement('li');
        li.className = 'list-group-item';
        li.textContent = requirement;
        requirementsList.appendChild(li);
      });
      
      // Penalties
      const penaltiesList = document.getElementById('penalties-list');
      const penalties = result.regulatory_sections.penalties || [];
      document.getElementById('penalties-count').textContent = penalties.length;
      
      penaltiesList.innerHTML = '';
      penalties.forEach(penalty => {
        const li = document.createElement('li');
        li.className = 'list-group-item';
        li.textContent = penalty;
        penaltiesList.appendChild(li);
      });
      
      // Articles & Sections
      const sectionsAccordion = document.getElementById('sections-accordion');
      const articles = result.regulatory_sections.articles || [];
      const sections = result.regulatory_sections.sections || [];
      document.getElementById('sections-count').textContent = articles.length + sections.length;
      
      sectionsAccordion.innerHTML = '';
      
      // Add articles
      articles.forEach((article, index) => {
        addAccordionItem(sectionsAccordion, `article-${index}`, article);
      });
      
      // Add sections
      sections.forEach((section, index) => {
        addAccordionItem(sectionsAccordion, `section-${index}`, section);
      });
      
      // Organizations
      const organizationsList = document.getElementById('organizations-list');
      organizationsList.innerHTML = '';
      (result.entities.organizations || []).forEach(org => {
        addEntityItem(organizationsList, org);
      });
      
      // Regulatory terms
      const termsList = document.getElementById('terms-list');
      termsList.innerHTML = '';
      (result.entities.regulatory_terms || []).forEach(term => {
        addEntityItem(termsList, term);
      });
      
      // Money values
      const moneyList = document.getElementById('money-list');
      moneyList.innerHTML = '';
      (result.entities.money || []).forEach(money => {
        addEntityItem(moneyList, money);
      });
      
      // Dates
      const datesList = document.getElementById('dates-list');
      datesList.innerHTML = '';
      (result.entities.dates || []).forEach(date => {
        addEntityItem(datesList, date);
      });
    }
    
    function addAccordionItem(accordion, id, content) {
      const title = content.split(':')[0];
      const body = content.substring(content.indexOf(':') + 1).trim();
      
      const item = document.createElement('div');
      item.className = 'accordion-item';
      
      item.innerHTML = `
        <h2 class="accordion-header">
          <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#${id}">
            ${title}
          </button>
        </h2>
        <div id="${id}" class="accordion-collapse collapse">
          <div class="accordion-body">${body}</div>
        </div>
      `;
      
      accordion.appendChild(item);
    }
    
    function addEntityItem(list, text) {
      const li = document.createElement('li');
      li.innerHTML = `<span class="badge bg-light text-dark me-1">${text}</span>`;
      list.appendChild(li);
    }
  });
</script>
{% endblock %}
