
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Regulatory Compliance Calendar</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css">
    <style>
        :root {
            --bg-color: #ffffff;
            --bg-secondary: #f3f4f6;
            --header-bg: #ffffff;
            --text-color: #333;
            --text-secondary: #555;
            --primary-color: #1e48aa;
            --border-color: #e1e4e8;
            --box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            --card-bg: #ffffff;
            --event-finance: #4285F4;
            --event-data: #34A853;
            --event-healthcare: #EA4335;
            --event-general: #FBBC05;
        }
        
        [data-theme="dark"] {
            --bg-color: #1a1a1a;
            --bg-secondary: #252525;
            --header-bg: #252525;
            --text-color: #e0e0e0;
            --text-secondary: #aaaaaa;
            --primary-color: #4f83f7;
            --border-color: #444444;
            --box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            --card-bg: #2d2d2d;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--bg-color);
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            transition: background-color 0.3s, color 0.3s;
        }
        
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 10px;
            margin-bottom: 20px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .title-area {
            display: flex;
            align-items: center;
        }
        
        .logo {
            font-size: 24px;
            margin-right: 10px;
            color: var(--primary-color);
        }
        
        .theme-toggle {
            background: none;
            border: none;
            color: var(--text-color);
            cursor: pointer;
            font-size: 20px;
            padding: 5px;
        }
        
        h1, h2, h3 {
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 500;
            line-height: 1.25;
        }
        
        h1 {
            color: var(--primary-color);
            padding-bottom: 0.3em;
            font-size: 2.2em;
            margin-top: 0;
        }
        
        .filter-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .filter-btn {
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 5px 10px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .filter-btn.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        #calendar {
            background-color: var(--card-bg);
            border-radius: 8px;
            box-shadow: var(--box-shadow);
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .fc-event {
            cursor: pointer;
        }
        
        .fc-event-title {
            font-weight: 500;
        }
        
        .color-dot {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        
        .finance-dot {
            background-color: var(--event-finance);
        }
        
        .data-dot {
            background-color: var(--event-data);
        }
        
        .healthcare-dot {
            background-color: var(--event-healthcare);
        }
        
        .general-dot {
            background-color: var(--event-general);
        }
        
        .calendar-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .calendar-views {
            display: flex;
            gap: 10px;
        }
        
        .view-btn {
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 5px 10px;
            cursor: pointer;
        }
        
        .view-btn.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        #event-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal-content {
            position: relative;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: var(--bg-color);
            padding: 20px;
            border-radius: 8px;
            max-width: 500px;
            width: 90%;
        }
        
        .close-modal {
            position: absolute;
            top: 10px;
            right: 10px;
            cursor: pointer;
            font-size: 20px;
        }
        
        .modal-header {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .modal-body p {
            margin-bottom: 10px;
        }
        
        .modal-footer {
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
        }
        
        .modal-btn {
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .primary-btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
        }
        
        .secondary-btn {
            background-color: transparent;
            border: 1px solid var(--border-color);
            margin-right: 10px;
        }
        
        @media (max-width: 768px) {
            .filter-container {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .calendar-controls {
                flex-direction: column;
                gap: 10px;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="title-area">
            <div class="logo"><i class="fas fa-calendar-alt"></i></div>
            <h1>Regulatory Compliance Calendar</h1>
        </div>
        <button class="theme-toggle" id="themeToggle" aria-label="Toggle dark/light mode">
            <i class="fas fa-moon toggle-icon"></i>
        </button>
    </header>
    
    <div class="calendar-controls">
        <div class="calendar-views">
            <button class="view-btn active" data-view="dayGridMonth">Month</button>
            <button class="view-btn" data-view="timeGridWeek">Week</button>
            <button class="view-btn" data-view="listMonth">List</button>
        </div>
        
        <div class="filter-container">
            <button class="filter-btn active" data-category="all">
                All Categories
            </button>
            <button class="filter-btn" data-category="finance">
                <span class="color-dot finance-dot"></span> Financial
            </button>
            <button class="filter-btn" data-category="data">
                <span class="color-dot data-dot"></span> Data Protection
            </button>
            <button class="filter-btn" data-category="healthcare">
                <span class="color-dot healthcare-dot"></span> Healthcare
            </button>
            <button class="filter-btn" data-category="general">
                <span class="color-dot general-dot"></span> General
            </button>
        </div>
    </div>
    
    <div id="calendar"></div>
    
    <div id="event-modal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <div class="modal-header">
                <h3 id="modal-title">Event Title</h3>
            </div>
            <div class="modal-body">
                <p><strong>Date:</strong> <span id="modal-date"></span></p>
                <p><strong>Category:</strong> <span id="modal-category"></span></p>
                <p><strong>Jurisdiction:</strong> <span id="modal-jurisdiction"></span></p>
                <p><strong>Description:</strong> <span id="modal-description"></span></p>
                <p><strong>Deadline Type:</strong> <span id="modal-deadline-type"></span></p>
                <p><strong>Action Required:</strong> <span id="modal-action"></span></p>
            </div>
            <div class="modal-footer">
                <button class="modal-btn secondary-btn" id="close-btn">Close</button>
                <button class="modal-btn primary-btn" id="details-btn">View Full Details</button>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Theme toggle functionality
            const themeToggleBtn = document.getElementById('themeToggle');
            const icon = themeToggleBtn.querySelector('.toggle-icon');
            
            // Always default to dark mode
            document.documentElement.setAttribute('data-theme', 'dark');
            icon.classList.remove('fa-moon');
            icon.classList.add('fa-sun');
            localStorage.setItem('theme', 'dark');
            
            themeToggleBtn.addEventListener('click', () => {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                
                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);
                
                if (newTheme === 'dark') {
                    icon.classList.remove('fa-moon');
                    icon.classList.add('fa-sun');
                } else {
                    icon.classList.remove('fa-sun');
                    icon.classList.add('fa-moon');
                }
                
                // Refresh calendar to update colors
                calendar.render();
            });
            
            // Modal functionality
            const modal = document.getElementById('event-modal');
            const closeModal = document.querySelector('.close-modal');
            const closeBtn = document.getElementById('close-btn');
            const detailsBtn = document.getElementById('details-btn');
            
            closeModal.onclick = function() {
                modal.style.display = "none";
            }
            
            closeBtn.onclick = function() {
                modal.style.display = "none";
            }
            
            detailsBtn.onclick = function() {
                // This would navigate to a detailed view of the regulation
                const regulationId = detailsBtn.getAttribute('data-regulation-id');
                window.location.href = `/api/v1/regulations/${regulationId}`;
            }
            
            window.onclick = function(event) {
                if (event.target == modal) {
                    modal.style.display = "none";
                }
            }
            
            // Calendar view buttons
            const viewButtons = document.querySelectorAll('.view-btn');
            viewButtons.forEach(button => {
                button.addEventListener('click', () => {
                    viewButtons.forEach(btn => btn.classList.remove('active'));
                    button.classList.add('active');
                    const view = button.getAttribute('data-view');
                    calendar.changeView(view);
                });
            });
            
            // Filter buttons
            const filterButtons = document.querySelectorAll('.filter-btn');
            let activeFilters = ['all'];
            
            filterButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const category = button.getAttribute('data-category');
                    
                    if (category === 'all') {
                        // If "All" is clicked, deactivate other filters
                        filterButtons.forEach(btn => btn.classList.remove('active'));
                        button.classList.add('active');
                        activeFilters = ['all'];
                    } else {
                        // If a specific category is clicked
                        const allButton = document.querySelector('.filter-btn[data-category="all"]');
                        allButton.classList.remove('active');
                        
                        // Toggle this category
                        if (button.classList.contains('active')) {
                            button.classList.remove('active');
                            activeFilters = activeFilters.filter(filter => filter !== category);
                            
                            // If no categories are selected, reactivate "All"
                            if (activeFilters.length === 0 || (activeFilters.length === 1 && activeFilters[0] === 'all')) {
                                allButton.classList.add('active');
                                activeFilters = ['all'];
                            }
                        } else {
                            button.classList.add('active');
                            // Remove 'all' if it exists
                            activeFilters = activeFilters.filter(filter => filter !== 'all');
                            activeFilters.push(category);
                        }
                    }
                    
                    // Reapply filters
                    calendar.getEvents().forEach(event => {
                        if (activeFilters.includes('all') || activeFilters.includes(event.extendedProps.category)) {
                            event.setProp('display', 'auto');
                        } else {
                            event.setProp('display', 'none');
                        }
                    });
                });
            });
            
            // Initialize FullCalendar
            const calendarEl = document.getElementById('calendar');
            const calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: ''  // We handle views with separate buttons
                },
                events: function(info, successCallback, failureCallback) {
                    // Fetch events from API
                    fetch('/api/v1/calendar/events')
                        .then(response => response.json())
                        .then(data => {
                            successCallback(data);
                        })
                        .catch(error => {
                            console.error('Error fetching calendar events:', error);
                            // Provide demo data if API fails
                            const demoEvents = getDemoEvents();
                            successCallback(demoEvents);
                        });
                },
                eventClick: function(info) {
                    // Show event details in modal
                    document.getElementById('modal-title').textContent = info.event.title;
                    document.getElementById('modal-date').textContent = info.event.start.toLocaleDateString();
                    document.getElementById('modal-category').textContent = capitalizeFirstLetter(info.event.extendedProps.category);
                    document.getElementById('modal-jurisdiction').textContent = info.event.extendedProps.jurisdiction;
                    document.getElementById('modal-description').textContent = info.event.extendedProps.description;
                    document.getElementById('modal-deadline-type').textContent = info.event.extendedProps.deadlineType;
                    document.getElementById('modal-action').textContent = info.event.extendedProps.action;
                    
                    // Set regulation ID for the details button
                    document.getElementById('details-btn').setAttribute('data-regulation-id', info.event.extendedProps.regulationId);
                    
                    // Show the modal
                    modal.style.display = "block";
                },
                eventClassNames: function(arg) {
                    // Add category-specific class
                    return ['event-' + arg.event.extendedProps.category];
                },
                eventDidMount: function(info) {
                    // Add category color indicator
                    const dotEl = document.createElement('span');
                    dotEl.classList.add('color-dot');
                    dotEl.classList.add(info.event.extendedProps.category + '-dot');
                    
                    if (info.view.type.includes('dayGrid') || info.view.type.includes('timeGrid')) {
                        const titleEl = info.el.querySelector('.fc-event-title');
                        if (titleEl) {
                            titleEl.prepend(dotEl);
                        }
                    } else if (info.view.type.includes('list')) {
                        const dotContainer = document.createElement('span');
                        dotContainer.style.display = 'inline-block';
                        dotContainer.style.margin = '0 5px';
                        dotContainer.appendChild(dotEl);
                        
                        const titleEl = info.el.querySelector('.fc-list-event-title a');
                        if (titleEl) {
                            titleEl.prepend(dotContainer);
                        }
                    }
                },
                dayMaxEvents: true,
                eventTimeFormat: {
                    hour: 'numeric',
                    minute: '2-digit',
                    meridiem: 'short'
                }
            });
            
            calendar.render();
            
            // Helper function for demo data
            function getDemoEvents() {
                return [
                    {
                        title: 'GDPR Annual Assessment',
                        start: '2024-03-15',
                        end: '2024-03-15',
                        backgroundColor: '#34A853',
                        borderColor: '#34A853',
                        extendedProps: {
                            category: 'data',
                            jurisdiction: 'European Union',
                            description: 'Annual data protection impact assessment required under GDPR Article 35',
                            deadlineType: 'Compliance Assessment',
                            action: 'Complete DPIA for all high-risk processing activities',
                            regulationId: 101
                        }
                    },
                    {
                        title: 'SEC Filing Deadline',
                        start: '2024-04-01',
                        end: '2024-04-01',
                        backgroundColor: '#4285F4',
                        borderColor: '#4285F4',
                        extendedProps: {
                            category: 'finance',
                            jurisdiction: 'United States',
                            description: 'Quarterly report filing deadline for Q1 2024',
                            deadlineType: 'Regulatory Filing',
                            action: 'Submit Form 10-Q to SEC',
                            regulationId: 202
                        }
                    },
                    {
                        title: 'HIPAA Security Review',
                        start: '2024-03-10',
                        end: '2024-03-12',
                        backgroundColor: '#EA4335',
                        borderColor: '#EA4335',
                        extendedProps: {
                            category: 'healthcare',
                            jurisdiction: 'United States',
                            description: 'Annual security risk assessment for HIPAA compliance',
                            deadlineType: 'Security Audit',
                            action: 'Complete security risk assessment and documentation',
                            regulationId: 303
                        }
                    },
                    {
                        title: 'Brazil LGPD Implementation',
                        start: '2024-03-20',
                        end: '2024-03-20',
                        backgroundColor: '#34A853',
                        borderColor: '#34A853',
                        extendedProps: {
                            category: 'data',
                            jurisdiction: 'Brazil',
                            description: 'Implementation deadline for LGPD data subject rights procedures',
                            deadlineType: 'Implementation Deadline',
                            action: 'Finalize data subject rights management processes',
                            regulationId: 404
                        }
                    },
                    {
                        title: 'AML Report Filing',
                        start: '2024-04-15',
                        end: '2024-04-15',
                        backgroundColor: '#4285F4',
                        borderColor: '#4285F4',
                        extendedProps: {
                            category: 'finance',
                            jurisdiction: 'United Kingdom',
                            description: 'Anti-Money Laundering compliance report due',
                            deadlineType: 'Regulatory Filing',
                            action: 'Submit AML compliance report to FCA',
                            regulationId: 505
                        }
                    },
                    {
                        title: 'ESG Disclosure Deadline',
                        start: '2024-03-31',
                        end: '2024-03-31',
                        backgroundColor: '#FBBC05',
                        borderColor: '#FBBC05',
                        extendedProps: {
                            category: 'general',
                            jurisdiction: 'EU Directive',
                            description: 'Environmental, Social, and Governance disclosure requirements',
                            deadlineType: 'Disclosure',
                            action: 'Publish ESG report with required metrics',
                            regulationId: 606
                        }
                    },
                    {
                        title: 'Data Breach Notification Test',
                        start: '2024-04-05',
                        end: '2024-04-05',
                        backgroundColor: '#34A853',
                        borderColor: '#34A853',
                        extendedProps: {
                            category: 'data',
                            jurisdiction: 'Global',
                            description: 'Quarterly test of data breach notification procedures',
                            deadlineType: 'Process Test',
                            action: 'Conduct simulation test of breach notification process',
                            regulationId: 707
                        }
                    },
                    {
                        title: 'PCI DSS Compliance Audit',
                        start: '2024-04-20',
                        end: '2024-04-22',
                        backgroundColor: '#4285F4',
                        borderColor: '#4285F4',
                        extendedProps: {
                            category: 'finance',
                            jurisdiction: 'Global Standard',
                            description: 'Annual Payment Card Industry Data Security Standard audit',
                            deadlineType: 'Compliance Audit',
                            action: 'Complete PCI DSS compliance assessment with QSA',
                            regulationId: 808
                        }
                    },
                    {
                        title: 'CCPA Consumer Request Report',
                        start: '2024-03-25',
                        end: '2024-03-25',
                        backgroundColor: '#34A853',
                        borderColor: '#34A853',
                        extendedProps: {
                            category: 'data',
                            jurisdiction: 'California, USA',
                            description: 'Quarterly report on CCPA consumer requests',
                            deadlineType: 'Internal Reporting',
                            action: 'Generate and review report on consumer data requests',
                            regulationId: 909
                        }
                    },
                    {
                        title: 'Corporate Governance Review',
                        start: '2024-04-10',
                        end: '2024-04-10',
                        backgroundColor: '#FBBC05',
                        borderColor: '#FBBC05',
                        extendedProps: {
                            category: 'general',
                            jurisdiction: 'Internal',
                            description: 'Quarterly review of corporate governance policies',
                            deadlineType: 'Internal Review',
                            action: 'Review and update governance documentation',
                            regulationId: 1010
                        }
                    }
                ];
            }
            
            // Helper function to capitalize first letter
            function capitalizeFirstLetter(string) {
                return string.charAt(0).toUpperCase() + string.slice(1);
            }
        });
    </script>
</body>
</html>
