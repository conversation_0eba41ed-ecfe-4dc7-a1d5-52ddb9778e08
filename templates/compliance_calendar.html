
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Regulatory Compliance Calendar</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css">
    <style>
        :root {
            --bg-color: #ffffff;
            --bg-secondary: #f3f4f6;
            --header-bg: #ffffff;
            --text-color: #333;
            --text-secondary: #555;
            --primary-color: #1e48aa;
            --border-color: #e1e4e8;
            --box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            --card-bg: #ffffff;
            --event-finance: #4285F4;
            --event-data: #34A853;
            --event-healthcare: #EA4335;
            --event-general: #FBBC05;
        }

        [data-theme="dark"] {
            --bg-color: #1a1a1a;
            --bg-secondary: #252525;
            --header-bg: #252525;
            --text-color: #e0e0e0;
            --text-secondary: #aaaaaa;
            --primary-color: #4f83f7;
            --border-color: #444444;
            --box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            --card-bg: #2d2d2d;
        }

        body {
            font-family: 'Roboto', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--bg-color);
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            transition: background-color 0.3s, color 0.3s;
        }

        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 10px;
            margin-bottom: 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .title-area {
            display: flex;
            align-items: center;
        }

        .logo {
            font-size: 24px;
            margin-right: 10px;
            color: var(--primary-color);
        }

        .theme-toggle {
            background: none;
            border: none;
            color: var(--text-color);
            cursor: pointer;
            font-size: 20px;
            padding: 5px;
        }

        h1, h2, h3 {
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 500;
            line-height: 1.25;
        }

        h1 {
            color: var(--primary-color);
            padding-bottom: 0.3em;
            font-size: 2.2em;
            margin-top: 0;
        }

        .filter-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }

        .filter-btn {
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 5px 10px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .filter-btn.active {
            background-color: var(--primary-color);
            color: white;
        }

        #calendar {
            background-color: var(--card-bg);
            border-radius: 8px;
            box-shadow: var(--box-shadow);
            padding: 20px;
            margin-bottom: 30px;
        }

        .fc-event {
            cursor: pointer;
        }

        .fc-event-title {
            font-weight: 500;
        }

        .color-dot {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .finance-dot {
            background-color: var(--event-finance);
        }

        .data-dot {
            background-color: var(--event-data);
        }

        .healthcare-dot {
            background-color: var(--event-healthcare);
        }

        .general-dot {
            background-color: var(--event-general);
        }

        .calendar-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .calendar-views {
            display: flex;
            gap: 10px;
        }

        .view-btn {
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 5px 10px;
            cursor: pointer;
        }

        .view-btn.active {
            background-color: var(--primary-color);
            color: white;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            position: relative;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: var(--bg-color);
            padding: 20px;
            border-radius: 8px;
            max-width: 600px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }

        .close-modal {
            position: absolute;
            top: 10px;
            right: 10px;
            cursor: pointer;
            font-size: 20px;
        }

        .modal-header {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body p {
            margin-bottom: 10px;
        }

        .modal-footer {
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .modal-btn {
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
        }

        .primary-btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
        }

        .secondary-btn {
            background-color: transparent;
            border: 1px solid var(--border-color);
        }

        .edit-btn {
            background-color: #34A853;
            color: white;
            border: none;
        }

        .delete-btn {
            background-color: #EA4335;
            color: white;
            border: none;
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 15px;
        }

        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        input[type="text"],
        input[type="datetime-local"],
        input[type="number"],
        select,
        textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background-color: var(--bg-color);
            color: var(--text-color);
        }

        .form-section {
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid var(--border-color);
        }

        /* Reminder Styles */
        .reminder-item {
            position: relative;
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            margin-bottom: 10px;
            background-color: var(--bg-secondary);
        }

        .remove-reminder-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            color: #EA4335;
            cursor: pointer;
            font-size: 16px;
        }

        .time-before-container {
            display: flex;
            gap: 5px;
        }

        .time-before {
            width: 60px;
        }

        /* Action Buttons */
        .action-btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 15px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .calendar-actions {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
        }

        /* Export Dropdown */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: var(--bg-color);
            min-width: 160px;
            box-shadow: var(--box-shadow);
            z-index: 1;
            border-radius: 4px;
            border: 1px solid var(--border-color);
        }

        .dropdown-content a {
            color: var(--text-color);
            padding: 12px 16px;
            text-decoration: none;
            display: block;
        }

        .dropdown-content a:hover {
            background-color: var(--bg-secondary);
        }

        /* Priority and Status Badges */
        .priority-badge, .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .priority-badge.low {
            background-color: #34A853;
            color: white;
        }

        .priority-badge.medium {
            background-color: #FBBC05;
            color: black;
        }

        .priority-badge.high {
            background-color: #EA4335;
            color: white;
        }

        .priority-badge.critical {
            background-color: #9C27B0;
            color: white;
        }

        .status-badge.scheduled {
            background-color: #4285F4;
            color: white;
        }

        .status-badge.in_progress {
            background-color: #FBBC05;
            color: black;
        }

        .status-badge.completed {
            background-color: #34A853;
            color: white;
        }

        .status-badge.cancelled {
            background-color: #9E9E9E;
            color: white;
        }

        /* Reminders Section */
        .reminders-section {
            margin-top: 15px;
            padding-top: 10px;
            border-top: 1px solid var(--border-color);
        }

        #modal-reminders-list {
            list-style: none;
            padding: 0;
        }

        #modal-reminders-list .reminder-item {
            padding: 8px 12px;
            margin-bottom: 5px;
        }

        @media (max-width: 768px) {
            .filter-container {
                flex-direction: column;
                align-items: flex-start;
            }

            .calendar-controls {
                flex-direction: column;
                gap: 10px;
                align-items: flex-start;
            }

            .calendar-actions {
                width: 100%;
            }

            .action-buttons {
                width: 100%;
                justify-content: space-between;
            }

            .form-row {
                flex-direction: column;
                gap: 10px;
            }

            .modal-content {
                width: 95%;
                max-height: 85vh;
            }

            .modal-footer {
                flex-wrap: wrap;
                gap: 5px;
            }

            .modal-btn {
                flex: 1;
                min-width: 80px;
                text-align: center;
                padding: 8px 5px;
            }

            .time-before-container {
                flex-direction: column;
                gap: 5px;
            }

            .time-before {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="title-area">
            <div class="logo"><i class="fas fa-calendar-alt"></i></div>
            <h1>Regulatory Compliance Calendar</h1>
        </div>
        <button class="theme-toggle" id="themeToggle" aria-label="Toggle dark/light mode">
            <i class="fas fa-moon toggle-icon"></i>
        </button>
    </header>

    <div class="calendar-controls">
        <div class="calendar-actions">
            <div class="calendar-views">
                <button class="view-btn active" data-view="dayGridMonth">Month</button>
                <button class="view-btn" data-view="timeGridWeek">Week</button>
                <button class="view-btn" data-view="listMonth">List</button>
            </div>
            <div class="action-buttons">
                <button class="action-btn create-event-btn" id="createEventBtn">
                    <i class="fas fa-plus"></i> Create Event
                </button>
                <div class="dropdown">
                    <button class="action-btn export-btn" id="exportBtn">
                        <i class="fas fa-download"></i> Export
                    </button>
                    <div class="dropdown-content" id="exportDropdown">
                        <a href="#" data-format="ical">iCalendar (.ics)</a>
                        <a href="#" data-format="csv">CSV File (.csv)</a>
                        <a href="#" data-format="json">JSON Data (.json)</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="filter-container">
            <button class="filter-btn active" data-category="all">
                All Categories
            </button>
            <button class="filter-btn" data-category="finance">
                <span class="color-dot finance-dot"></span> Financial
            </button>
            <button class="filter-btn" data-category="data">
                <span class="color-dot data-dot"></span> Data Protection
            </button>
            <button class="filter-btn" data-category="healthcare">
                <span class="color-dot healthcare-dot"></span> Healthcare
            </button>
            <button class="filter-btn" data-category="general">
                <span class="color-dot general-dot"></span> General
            </button>
        </div>
    </div>

    <div id="calendar"></div>

    <div id="event-modal" class="modal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <div class="modal-header">
                <h3 id="modal-title">Event Title</h3>
                <span class="priority-badge" id="modal-priority">Medium</span>
            </div>
            <div class="modal-body">
                <p><strong>Date:</strong> <span id="modal-date"></span></p>
                <p><strong>Category:</strong> <span id="modal-category"></span></p>
                <p><strong>Jurisdiction:</strong> <span id="modal-jurisdiction"></span></p>
                <p><strong>Status:</strong> <span id="modal-status" class="status-badge">Scheduled</span></p>
                <p><strong>Description:</strong> <span id="modal-description"></span></p>
                <p><strong>Deadline Type:</strong> <span id="modal-deadline-type"></span></p>
                <p><strong>Action Required:</strong> <span id="modal-action"></span></p>

                <div class="reminders-section" id="modal-reminders">
                    <h4>Reminders</h4>
                    <ul id="modal-reminders-list">
                        <!-- Reminders will be populated here -->
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn delete-btn" id="delete-btn">Delete</button>
                <button class="modal-btn edit-btn" id="edit-btn">Edit</button>
                <button class="modal-btn secondary-btn" id="close-btn">Close</button>
                <button class="modal-btn primary-btn" id="details-btn">View Full Details</button>
            </div>
        </div>
    </div>

    <!-- Event Creation/Edit Modal -->
    <div id="event-form-modal" class="modal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <div class="modal-header">
                <h3 id="form-title">Create New Event</h3>
            </div>
            <div class="modal-body">
                <form id="event-form">
                    <input type="hidden" id="event-id" name="event-id">

                    <div class="form-group">
                        <label for="title">Title *</label>
                        <input type="text" id="form-event-title" name="title" required>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="start-date">Start Date *</label>
                            <input type="datetime-local" id="form-start-date" name="start-date" required>
                        </div>
                        <div class="form-group">
                            <label for="end-date">End Date *</label>
                            <input type="datetime-local" id="form-end-date" name="end-date" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="all-day">
                            <input type="checkbox" id="form-all-day" name="all-day">
                            All Day Event
                        </label>
                    </div>

                    <div class="form-group">
                        <label for="location">Location</label>
                        <input type="text" id="form-location" name="location">
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="event-type">Event Type *</label>
                            <select id="form-event-type" name="event-type" required>
                                <option value="deadline">Deadline</option>
                                <option value="review">Review</option>
                                <option value="audit">Audit</option>
                                <option value="training">Training</option>
                                <option value="meeting">Meeting</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="priority">Priority</label>
                            <select id="form-priority" name="priority">
                                <option value="low">Low</option>
                                <option value="medium" selected>Medium</option>
                                <option value="high">High</option>
                                <option value="critical">Critical</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="status">Status</label>
                        <select id="form-status" name="status">
                            <option value="scheduled" selected>Scheduled</option>
                            <option value="in_progress">In Progress</option>
                            <option value="completed">Completed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="jurisdiction">Jurisdiction</label>
                        <input type="text" id="form-jurisdiction" name="jurisdiction">
                    </div>

                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="form-description" name="description" rows="4"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="action">Action Required</label>
                        <textarea id="form-action" name="action" rows="2"></textarea>
                    </div>

                    <div class="form-section">
                        <h4>Reminders</h4>
                        <div id="reminders-container">
                            <!-- Reminder items will be added here -->
                        </div>
                        <button type="button" id="add-reminder-btn" class="action-btn">
                            <i class="fas fa-plus"></i> Add Reminder
                        </button>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="cancel-form-btn" class="modal-btn secondary-btn">Cancel</button>
                <button type="button" id="save-event-btn" class="modal-btn primary-btn">Save Event</button>
            </div>
        </div>
    </div>

    <!-- Reminder Template (hidden, used for cloning) -->
    <template id="reminder-template">
        <div class="reminder-item">
            <div class="form-row">
                <div class="form-group">
                    <label for="reminder-type">Type</label>
                    <select name="reminder-type" class="reminder-type">
                        <option value="email">Email</option>
                        <option value="sms">SMS</option>
                        <option value="in_app">In-App</option>
                        <option value="webhook">Webhook</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="time-before">Time Before</label>
                    <div class="time-before-container">
                        <input type="number" name="time-before" class="time-before" min="1" value="1">
                        <select name="time-unit" class="time-unit">
                            <option value="minutes">Minutes</option>
                            <option value="hours">Hours</option>
                            <option value="days" selected>Days</option>
                            <option value="weeks">Weeks</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label for="recipient">Recipient</label>
                <input type="text" name="recipient" class="recipient" placeholder="Email, phone, or webhook URL">
            </div>
            <button type="button" class="remove-reminder-btn">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </template>

    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Theme toggle functionality
            const themeToggleBtn = document.getElementById('themeToggle');
            const icon = themeToggleBtn.querySelector('.toggle-icon');

            // Always default to dark mode
            document.documentElement.setAttribute('data-theme', 'dark');
            icon.classList.remove('fa-moon');
            icon.classList.add('fa-sun');
            localStorage.setItem('theme', 'dark');

            themeToggleBtn.addEventListener('click', () => {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);

                if (newTheme === 'dark') {
                    icon.classList.remove('fa-moon');
                    icon.classList.add('fa-sun');
                } else {
                    icon.classList.remove('fa-sun');
                    icon.classList.add('fa-moon');
                }

                // Refresh calendar to update colors
                calendar.render();
            });

            // Modal functionality
            const eventModal = document.getElementById('event-modal');
            const eventFormModal = document.getElementById('event-form-modal');
            const closeModalBtns = document.querySelectorAll('.close-modal');
            const closeBtn = document.getElementById('close-btn');
            const detailsBtn = document.getElementById('details-btn');
            const editBtn = document.getElementById('edit-btn');
            const deleteBtn = document.getElementById('delete-btn');
            const createEventBtn = document.getElementById('createEventBtn');
            const cancelFormBtn = document.getElementById('cancel-form-btn');
            const saveEventBtn = document.getElementById('save-event-btn');
            const addReminderBtn = document.getElementById('add-reminder-btn');
            const exportBtn = document.getElementById('exportBtn');
            const exportDropdown = document.getElementById('exportDropdown');

            // Close modal buttons
            closeModalBtns.forEach(btn => {
                btn.onclick = function() {
                    eventModal.style.display = "none";
                    eventFormModal.style.display = "none";
                }
            });

            closeBtn.onclick = function() {
                eventModal.style.display = "none";
            }

            cancelFormBtn.onclick = function() {
                eventFormModal.style.display = "none";
            }

            detailsBtn.onclick = function() {
                // This would navigate to a detailed view of the regulation
                const regulationId = detailsBtn.getAttribute('data-regulation-id');
                window.location.href = `/api/v1/regulations/${regulationId}`;
            }

            // Create Event button
            createEventBtn.onclick = function() {
                // Reset form
                document.getElementById('event-form').reset();
                document.getElementById('event-id').value = '';
                document.getElementById('form-title').textContent = 'Create New Event';

                // Set default dates
                const now = new Date();
                const tomorrow = new Date(now);
                tomorrow.setDate(tomorrow.getDate() + 1);

                document.getElementById('form-start-date').value = formatDateForInput(now);
                document.getElementById('form-end-date').value = formatDateForInput(tomorrow);

                // Clear reminders
                document.getElementById('reminders-container').innerHTML = '';

                // Show the form modal
                eventFormModal.style.display = "block";
            }

            // Edit Event button
            editBtn.onclick = function() {
                const eventId = editBtn.getAttribute('data-event-id');

                // Fetch event details
                fetch(`/api/v1/calendar/events/${eventId}`)
                    .then(response => response.json())
                    .then(event => {
                        // Populate form
                        document.getElementById('event-id').value = event.id;
                        document.getElementById('form-title').textContent = 'Edit Event';
                        document.getElementById('form-event-title').value = event.title;
                        document.getElementById('form-start-date').value = formatDateForInput(new Date(event.start_date));
                        document.getElementById('form-end-date').value = formatDateForInput(new Date(event.end_date));
                        document.getElementById('form-all-day').checked = event.all_day;
                        document.getElementById('form-location').value = event.location || '';
                        document.getElementById('form-event-type').value = event.event_type;
                        document.getElementById('form-priority').value = event.priority;
                        document.getElementById('form-status').value = event.status;
                        document.getElementById('form-description').value = event.description || '';

                        // Set jurisdiction and action if available in extendedProps
                        if (event.properties) {
                            document.getElementById('form-jurisdiction').value = event.properties.jurisdiction || '';
                            document.getElementById('form-action').value = event.properties.action || '';
                        }

                        // Populate reminders
                        const remindersContainer = document.getElementById('reminders-container');
                        remindersContainer.innerHTML = '';

                        if (event.reminders && event.reminders.length > 0) {
                            event.reminders.forEach(reminder => {
                                addReminderToForm(reminder);
                            });
                        }

                        // Hide event modal and show form modal
                        eventModal.style.display = "none";
                        eventFormModal.style.display = "block";
                    })
                    .catch(error => {
                        console.error('Error fetching event details:', error);
                        alert('Failed to load event details. Please try again.');
                    });
            }

            // Delete Event button
            deleteBtn.onclick = function() {
                const eventId = deleteBtn.getAttribute('data-event-id');

                if (confirm('Are you sure you want to delete this event?')) {
                    fetch(`/api/v1/calendar/events/${eventId}`, {
                        method: 'DELETE'
                    })
                    .then(response => {
                        if (response.ok) {
                            // Close modal and refresh calendar
                            eventModal.style.display = "none";
                            calendar.refetchEvents();
                        } else {
                            throw new Error('Failed to delete event');
                        }
                    })
                    .catch(error => {
                        console.error('Error deleting event:', error);
                        alert('Failed to delete event. Please try again.');
                    });
                }
            }

            // Save Event button
            saveEventBtn.onclick = function() {
                const form = document.getElementById('event-form');

                // Basic form validation
                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }

                // Gather form data
                const eventId = document.getElementById('event-id').value;
                const eventData = {
                    title: document.getElementById('form-event-title').value,
                    start_date: document.getElementById('form-start-date').value,
                    end_date: document.getElementById('form-end-date').value,
                    all_day: document.getElementById('form-all-day').checked,
                    location: document.getElementById('form-location').value || null,
                    event_type: document.getElementById('form-event-type').value,
                    priority: document.getElementById('form-priority').value,
                    status: document.getElementById('form-status').value,
                    description: document.getElementById('form-description').value || null,
                    properties: {
                        jurisdiction: document.getElementById('form-jurisdiction').value || null,
                        action: document.getElementById('form-action').value || null
                    }
                };

                // Gather reminders
                const reminderItems = document.querySelectorAll('.reminder-item');
                if (reminderItems.length > 0) {
                    eventData.reminders = [];

                    reminderItems.forEach(item => {
                        const reminderType = item.querySelector('.reminder-type').value;
                        const timeBefore = parseInt(item.querySelector('.time-before').value);
                        const timeUnit = item.querySelector('.time-unit').value;
                        const recipient = item.querySelector('.recipient').value || null;

                        eventData.reminders.push({
                            reminder_type: reminderType,
                            time_before_event: timeBefore,
                            time_unit: timeUnit,
                            recipient: recipient
                        });
                    });
                }

                // Determine if this is a create or update operation
                const method = eventId ? 'PUT' : 'POST';
                const url = eventId ? `/api/v1/calendar/events/${eventId}` : '/api/v1/calendar/events';

                // Send request to API
                fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(eventData)
                })
                .then(response => {
                    if (response.ok) {
                        // Close modal and refresh calendar
                        eventFormModal.style.display = "none";
                        calendar.refetchEvents();
                    } else {
                        throw new Error('Failed to save event');
                    }
                })
                .catch(error => {
                    console.error('Error saving event:', error);
                    alert('Failed to save event. Please try again.');
                });
            }

            // Add Reminder button
            addReminderBtn.onclick = function() {
                addReminderToForm();
            }

            // Export button and dropdown
            exportBtn.onclick = function(e) {
                e.stopPropagation();
                exportDropdown.style.display = exportDropdown.style.display === "block" ? "none" : "block";
            }

            // Export format selection
            const exportLinks = exportDropdown.querySelectorAll('a');
            exportLinks.forEach(link => {
                link.onclick = function(e) {
                    e.preventDefault();
                    const format = this.getAttribute('data-format');

                    // Get current calendar view dates
                    const view = calendar.view;
                    const start = view.activeStart.toISOString();
                    const end = view.activeEnd.toISOString();

                    // Create export URL with parameters
                    const exportUrl = `/api/v1/calendar/export?format=${format}&start_date=${start}&end_date=${end}`;

                    // Open in new tab or download
                    window.open(exportUrl, '_blank');

                    // Hide dropdown
                    exportDropdown.style.display = "none";
                }
            });

            // Helper function to add a reminder to the form
            function addReminderToForm(reminderData = null) {
                const template = document.getElementById('reminder-template');
                const container = document.getElementById('reminders-container');

                // Clone the template
                const reminderNode = document.importNode(template.content, true);
                const reminderItem = reminderNode.querySelector('.reminder-item');

                // If we have reminder data, populate the fields
                if (reminderData) {
                    reminderItem.querySelector('.reminder-type').value = reminderData.reminder_type;
                    reminderItem.querySelector('.time-before').value = reminderData.time_before_event;
                    reminderItem.querySelector('.time-unit').value = reminderData.time_unit;
                    if (reminderData.recipient) {
                        reminderItem.querySelector('.recipient').value = reminderData.recipient;
                    }
                }

                // Add remove button functionality
                const removeBtn = reminderItem.querySelector('.remove-reminder-btn');
                removeBtn.onclick = function() {
                    container.removeChild(reminderItem);
                }

                // Add to container
                container.appendChild(reminderItem);
            }

            // Helper function to format date for datetime-local input
            function formatDateForInput(date) {
                return date.toISOString().slice(0, 16);
            }

            // Close modals when clicking outside
            window.onclick = function(event) {
                if (event.target == eventModal) {
                    eventModal.style.display = "none";
                }
                if (event.target == eventFormModal) {
                    eventFormModal.style.display = "none";
                }
                if (!event.target.matches('#exportBtn') && !event.target.closest('#exportDropdown')) {
                    exportDropdown.style.display = "none";
                }
            }

            // Calendar view buttons
            const viewButtons = document.querySelectorAll('.view-btn');
            viewButtons.forEach(button => {
                button.addEventListener('click', () => {
                    viewButtons.forEach(btn => btn.classList.remove('active'));
                    button.classList.add('active');
                    const view = button.getAttribute('data-view');
                    calendar.changeView(view);
                });
            });

            // Filter buttons
            const filterButtons = document.querySelectorAll('.filter-btn');
            let activeFilters = ['all'];

            filterButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const category = button.getAttribute('data-category');

                    if (category === 'all') {
                        // If "All" is clicked, deactivate other filters
                        filterButtons.forEach(btn => btn.classList.remove('active'));
                        button.classList.add('active');
                        activeFilters = ['all'];
                    } else {
                        // If a specific category is clicked
                        const allButton = document.querySelector('.filter-btn[data-category="all"]');
                        allButton.classList.remove('active');

                        // Toggle this category
                        if (button.classList.contains('active')) {
                            button.classList.remove('active');
                            activeFilters = activeFilters.filter(filter => filter !== category);

                            // If no categories are selected, reactivate "All"
                            if (activeFilters.length === 0 || (activeFilters.length === 1 && activeFilters[0] === 'all')) {
                                allButton.classList.add('active');
                                activeFilters = ['all'];
                            }
                        } else {
                            button.classList.add('active');
                            // Remove 'all' if it exists
                            activeFilters = activeFilters.filter(filter => filter !== 'all');
                            activeFilters.push(category);
                        }
                    }

                    // Reapply filters
                    calendar.getEvents().forEach(event => {
                        if (activeFilters.includes('all') || activeFilters.includes(event.extendedProps.category)) {
                            event.setProp('display', 'auto');
                        } else {
                            event.setProp('display', 'none');
                        }
                    });
                });
            });

            // Initialize FullCalendar
            const calendarEl = document.getElementById('calendar');
            const calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: ''  // We handle views with separate buttons
                },
                events: function(info, successCallback, failureCallback) {
                    // Fetch events from API
                    fetch('/api/v1/calendar/events')
                        .then(response => response.json())
                        .then(data => {
                            successCallback(data);
                        })
                        .catch(error => {
                            console.error('Error fetching calendar events:', error);
                            // Provide demo data if API fails
                            const demoEvents = getDemoEvents();
                            successCallback(demoEvents);
                        });
                },
                eventClick: function(info) {
                    // Get event data
                    const event = info.event;
                    const extendedProps = event.extendedProps;

                    // For real API events
                    if (event.id) {
                        // Set event ID for edit and delete buttons
                        document.getElementById('edit-btn').setAttribute('data-event-id', event.id);
                        document.getElementById('delete-btn').setAttribute('data-event-id', event.id);

                        // Fetch full event details from API
                        fetch(`/api/v1/calendar/events/${event.id}`)
                            .then(response => response.json())
                            .then(eventData => {
                                populateEventModal(eventData);
                            })
                            .catch(error => {
                                console.error('Error fetching event details:', error);
                                // Fall back to basic event data
                                populateEventModalFromCalendarEvent(event);
                            });
                    } else {
                        // For demo events without real IDs
                        populateEventModalFromCalendarEvent(event);
                    }

                    // Show the modal
                    eventModal.style.display = "block";
                },

                // Helper function to populate event modal from API data
                function populateEventModal(eventData) {
                    document.getElementById('modal-title').textContent = eventData.title;

                    // Format dates
                    const startDate = new Date(eventData.start_date);
                    const endDate = new Date(eventData.end_date);
                    let dateText = startDate.toLocaleDateString();

                    if (!eventData.all_day) {
                        dateText += ' ' + startDate.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

                        if (startDate.toDateString() !== endDate.toDateString()) {
                            dateText += ' to ' + endDate.toLocaleDateString() + ' ' +
                                endDate.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                        } else {
                            dateText += ' - ' + endDate.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                        }
                    } else if (startDate.toDateString() !== endDate.toDateString()) {
                        dateText += ' to ' + endDate.toLocaleDateString();
                    }

                    document.getElementById('modal-date').textContent = dateText;

                    // Set category based on event_type
                    document.getElementById('modal-category').textContent = capitalizeFirstLetter(eventData.event_type);

                    // Set priority badge
                    const priorityBadge = document.getElementById('modal-priority');
                    priorityBadge.textContent = capitalizeFirstLetter(eventData.priority);
                    priorityBadge.className = 'priority-badge ' + eventData.priority;

                    // Set status badge
                    const statusBadge = document.getElementById('modal-status');
                    statusBadge.textContent = capitalizeFirstLetter(eventData.status.replace('_', ' '));
                    statusBadge.className = 'status-badge ' + eventData.status;

                    // Set description
                    document.getElementById('modal-description').textContent = eventData.description || 'No description provided';

                    // Set other fields from properties if available
                    if (eventData.properties) {
                        document.getElementById('modal-jurisdiction').textContent = eventData.properties.jurisdiction || 'Not specified';
                        document.getElementById('modal-deadline-type').textContent = eventData.properties.deadlineType || eventData.event_type;
                        document.getElementById('modal-action').textContent = eventData.properties.action || 'None specified';
                    } else {
                        document.getElementById('modal-jurisdiction').textContent = 'Not specified';
                        document.getElementById('modal-deadline-type').textContent = eventData.event_type;
                        document.getElementById('modal-action').textContent = 'None specified';
                    }

                    // Set regulation ID for details button if available
                    if (eventData.properties && eventData.properties.regulationId) {
                        document.getElementById('details-btn').setAttribute('data-regulation-id', eventData.properties.regulationId);
                        document.getElementById('details-btn').style.display = 'inline-block';
                    } else {
                        document.getElementById('details-btn').style.display = 'none';
                    }

                    // Populate reminders
                    const remindersList = document.getElementById('modal-reminders-list');
                    remindersList.innerHTML = '';

                    if (eventData.reminders && eventData.reminders.length > 0) {
                        document.getElementById('modal-reminders').style.display = 'block';

                        eventData.reminders.forEach(reminder => {
                            const li = document.createElement('li');
                            li.className = 'reminder-item';

                            const typeIcon = document.createElement('i');
                            if (reminder.reminder_type === 'email') {
                                typeIcon.className = 'fas fa-envelope';
                            } else if (reminder.reminder_type === 'sms') {
                                typeIcon.className = 'fas fa-sms';
                            } else if (reminder.reminder_type === 'in_app') {
                                typeIcon.className = 'fas fa-bell';
                            } else if (reminder.reminder_type === 'webhook') {
                                typeIcon.className = 'fas fa-link';
                            }

                            const timeText = document.createElement('span');
                            timeText.textContent = `${reminder.time_before_event} ${reminder.time_unit} before`;

                            const recipientText = document.createElement('span');
                            if (reminder.recipient) {
                                recipientText.textContent = ` to ${reminder.recipient}`;
                            }

                            li.appendChild(typeIcon);
                            li.appendChild(document.createTextNode(' '));
                            li.appendChild(timeText);
                            if (reminder.recipient) {
                                li.appendChild(recipientText);
                            }

                            remindersList.appendChild(li);
                        });
                    } else {
                        document.getElementById('modal-reminders').style.display = 'none';
                    }
                }

                // Helper function to populate event modal from calendar event (for demo events)
                function populateEventModalFromCalendarEvent(event) {
                    const extendedProps = event.extendedProps;

                    document.getElementById('modal-title').textContent = event.title;

                    // Format dates
                    let dateText = event.start.toLocaleDateString();
                    if (!event.allDay) {
                        dateText += ' ' + event.start.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

                        if (event.end) {
                            if (event.start.toDateString() !== event.end.toDateString()) {
                                dateText += ' to ' + event.end.toLocaleDateString() + ' ' +
                                    event.end.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                            } else {
                                dateText += ' - ' + event.end.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                            }
                        }
                    } else if (event.end && event.start.toDateString() !== event.end.toDateString()) {
                        dateText += ' to ' + event.end.toLocaleDateString();
                    }

                    document.getElementById('modal-date').textContent = dateText;

                    // Set category
                    document.getElementById('modal-category').textContent = capitalizeFirstLetter(extendedProps.category || 'general');

                    // Set priority badge (default to medium)
                    const priorityBadge = document.getElementById('modal-priority');
                    priorityBadge.textContent = capitalizeFirstLetter(extendedProps.priority || 'medium');
                    priorityBadge.className = 'priority-badge ' + (extendedProps.priority || 'medium');

                    // Set status badge (default to scheduled)
                    const statusBadge = document.getElementById('modal-status');
                    statusBadge.textContent = capitalizeFirstLetter(extendedProps.status || 'scheduled');
                    statusBadge.className = 'status-badge ' + (extendedProps.status || 'scheduled');

                    // Set other fields
                    document.getElementById('modal-jurisdiction').textContent = extendedProps.jurisdiction || 'Not specified';
                    document.getElementById('modal-description').textContent = extendedProps.description || 'No description provided';
                    document.getElementById('modal-deadline-type').textContent = extendedProps.deadlineType || 'Not specified';
                    document.getElementById('modal-action').textContent = extendedProps.action || 'None specified';

                    // Set regulation ID for details button if available
                    if (extendedProps.regulationId) {
                        document.getElementById('details-btn').setAttribute('data-regulation-id', extendedProps.regulationId);
                        document.getElementById('details-btn').style.display = 'inline-block';
                    } else {
                        document.getElementById('details-btn').style.display = 'none';
                    }

                    // Hide reminders section for demo events
                    document.getElementById('modal-reminders').style.display = 'none';

                    // Hide edit and delete buttons for demo events
                    document.getElementById('edit-btn').style.display = 'none';
                    document.getElementById('delete-btn').style.display = 'none';
                },
                eventClassNames: function(arg) {
                    // Add category-specific class
                    return ['event-' + arg.event.extendedProps.category];
                },
                eventDidMount: function(info) {
                    // Add category color indicator
                    const dotEl = document.createElement('span');
                    dotEl.classList.add('color-dot');
                    dotEl.classList.add(info.event.extendedProps.category + '-dot');

                    if (info.view.type.includes('dayGrid') || info.view.type.includes('timeGrid')) {
                        const titleEl = info.el.querySelector('.fc-event-title');
                        if (titleEl) {
                            titleEl.prepend(dotEl);
                        }
                    } else if (info.view.type.includes('list')) {
                        const dotContainer = document.createElement('span');
                        dotContainer.style.display = 'inline-block';
                        dotContainer.style.margin = '0 5px';
                        dotContainer.appendChild(dotEl);

                        const titleEl = info.el.querySelector('.fc-list-event-title a');
                        if (titleEl) {
                            titleEl.prepend(dotContainer);
                        }
                    }
                },
                dayMaxEvents: true,
                eventTimeFormat: {
                    hour: 'numeric',
                    minute: '2-digit',
                    meridiem: 'short'
                }
            });

            calendar.render();

            // Helper function for demo data
            function getDemoEvents() {
                return [
                    {
                        title: 'GDPR Annual Assessment',
                        start: '2024-03-15',
                        end: '2024-03-15',
                        backgroundColor: '#34A853',
                        borderColor: '#34A853',
                        extendedProps: {
                            category: 'data',
                            jurisdiction: 'European Union',
                            description: 'Annual data protection impact assessment required under GDPR Article 35',
                            deadlineType: 'Compliance Assessment',
                            action: 'Complete DPIA for all high-risk processing activities',
                            regulationId: 101
                        }
                    },
                    {
                        title: 'SEC Filing Deadline',
                        start: '2024-04-01',
                        end: '2024-04-01',
                        backgroundColor: '#4285F4',
                        borderColor: '#4285F4',
                        extendedProps: {
                            category: 'finance',
                            jurisdiction: 'United States',
                            description: 'Quarterly report filing deadline for Q1 2024',
                            deadlineType: 'Regulatory Filing',
                            action: 'Submit Form 10-Q to SEC',
                            regulationId: 202
                        }
                    },
                    {
                        title: 'HIPAA Security Review',
                        start: '2024-03-10',
                        end: '2024-03-12',
                        backgroundColor: '#EA4335',
                        borderColor: '#EA4335',
                        extendedProps: {
                            category: 'healthcare',
                            jurisdiction: 'United States',
                            description: 'Annual security risk assessment for HIPAA compliance',
                            deadlineType: 'Security Audit',
                            action: 'Complete security risk assessment and documentation',
                            regulationId: 303
                        }
                    },
                    {
                        title: 'Brazil LGPD Implementation',
                        start: '2024-03-20',
                        end: '2024-03-20',
                        backgroundColor: '#34A853',
                        borderColor: '#34A853',
                        extendedProps: {
                            category: 'data',
                            jurisdiction: 'Brazil',
                            description: 'Implementation deadline for LGPD data subject rights procedures',
                            deadlineType: 'Implementation Deadline',
                            action: 'Finalize data subject rights management processes',
                            regulationId: 404
                        }
                    },
                    {
                        title: 'AML Report Filing',
                        start: '2024-04-15',
                        end: '2024-04-15',
                        backgroundColor: '#4285F4',
                        borderColor: '#4285F4',
                        extendedProps: {
                            category: 'finance',
                            jurisdiction: 'United Kingdom',
                            description: 'Anti-Money Laundering compliance report due',
                            deadlineType: 'Regulatory Filing',
                            action: 'Submit AML compliance report to FCA',
                            regulationId: 505
                        }
                    },
                    {
                        title: 'ESG Disclosure Deadline',
                        start: '2024-03-31',
                        end: '2024-03-31',
                        backgroundColor: '#FBBC05',
                        borderColor: '#FBBC05',
                        extendedProps: {
                            category: 'general',
                            jurisdiction: 'EU Directive',
                            description: 'Environmental, Social, and Governance disclosure requirements',
                            deadlineType: 'Disclosure',
                            action: 'Publish ESG report with required metrics',
                            regulationId: 606
                        }
                    },
                    {
                        title: 'Data Breach Notification Test',
                        start: '2024-04-05',
                        end: '2024-04-05',
                        backgroundColor: '#34A853',
                        borderColor: '#34A853',
                        extendedProps: {
                            category: 'data',
                            jurisdiction: 'Global',
                            description: 'Quarterly test of data breach notification procedures',
                            deadlineType: 'Process Test',
                            action: 'Conduct simulation test of breach notification process',
                            regulationId: 707
                        }
                    },
                    {
                        title: 'PCI DSS Compliance Audit',
                        start: '2024-04-20',
                        end: '2024-04-22',
                        backgroundColor: '#4285F4',
                        borderColor: '#4285F4',
                        extendedProps: {
                            category: 'finance',
                            jurisdiction: 'Global Standard',
                            description: 'Annual Payment Card Industry Data Security Standard audit',
                            deadlineType: 'Compliance Audit',
                            action: 'Complete PCI DSS compliance assessment with QSA',
                            regulationId: 808
                        }
                    },
                    {
                        title: 'CCPA Consumer Request Report',
                        start: '2024-03-25',
                        end: '2024-03-25',
                        backgroundColor: '#34A853',
                        borderColor: '#34A853',
                        extendedProps: {
                            category: 'data',
                            jurisdiction: 'California, USA',
                            description: 'Quarterly report on CCPA consumer requests',
                            deadlineType: 'Internal Reporting',
                            action: 'Generate and review report on consumer data requests',
                            regulationId: 909
                        }
                    },
                    {
                        title: 'Corporate Governance Review',
                        start: '2024-04-10',
                        end: '2024-04-10',
                        backgroundColor: '#FBBC05',
                        borderColor: '#FBBC05',
                        extendedProps: {
                            category: 'general',
                            jurisdiction: 'Internal',
                            description: 'Quarterly review of corporate governance policies',
                            deadlineType: 'Internal Review',
                            action: 'Review and update governance documentation',
                            regulationId: 1010
                        }
                    }
                ];
            }

            // Helper function to capitalize first letter
            function capitalizeFirstLetter(string) {
                return string.charAt(0).toUpperCase() + string.slice(1);
            }
        });
    </script>
</body>
</html>
