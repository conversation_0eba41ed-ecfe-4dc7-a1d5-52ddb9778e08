
<!DOCTYPE html>
<html>
<head>
    <title>Regulation URLs</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .regulation-list {
            margin-top: 20px;
        }
        .regulation-item {
            margin-bottom: 15px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .regulation-title {
            font-weight: bold;
            font-size: 18px;
        }
        .regulation-url {
            margin-top: 5px;
            word-break: break-all;
        }
        .regulation-url a {
            color: #0066cc;
            text-decoration: none;
        }
        .regulation-url a:hover {
            text-decoration: underline;
        }
        .regulation-meta {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }
        .no-data {
            color: #666;
            font-style: italic;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Regulation URLs</h1>
    <p>Below is a list of all regulation URLs in the system:</p>

    <div class="regulation-list">
        {% if regulations %}
            {% for regulation in regulations %}
                <div class="regulation-item">
                    <div class="regulation-title">{{ regulation.title if regulation.title else "Untitled Regulation" }}</div>
                    <div class="regulation-url">
                        <a href="{{ regulation.url }}" target="_blank">{{ regulation.url }}</a>
                    </div>
                    <div class="regulation-meta">
                        Domain: {{ regulation.domain if regulation.domain else "Unknown" }} | 
                        Category: {{ regulation.category if regulation.category else "Uncategorized" }} | 
                        Confidence: {{ "%.2f"|format(regulation.confidence_level) if regulation.confidence_level else "N/A" }} | 
                        Regulator: {{ regulation.regulator.name if regulation.regulator else "Unknown" }} | 
                        ID: {{ regulation.id }}
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <p class="no-data">No regulation URLs found in the database.</p>
        {% endif %}
    </div>

    <div style="margin-top: 20px;">
        <a href="/">Back to Dashboard</a> | 
        <a href="/api/v1/regulators">View Regulators</a>
    </div>
</body>
</html>
