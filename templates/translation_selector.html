<div class="language-selector">
  <button id="language-button" aria-label="{{ _('Select language') }}">
    <span class="flag">{{ languages[current_lang]["flag"] }}</span>
    <span class="language-name">{{ languages[current_lang]["label"] }}</span>
    <span class="dropdown-arrow">▼</span>
  </button>
  <div class="language-dropdown">
    {% for lang_code, lang_info in languages.items() %}
      <a href="?lang={{ lang_code }}" class="language-option {% if current_lang == lang_code %}active{% endif %}">
        <span class="flag">{{ lang_info["flag"] }}</span>
        <span class="language-name">{{ lang_info["label"] }}</span>
      </a>
    {% endfor %}
  </div>
</div>

<style>
  .language-selector {
    position: relative;
    display: inline-block;
  }

  #language-button {
    background: none;
    border: none;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    color: var(--text-color);
    padding: 6px 10px;
    font-size: 14px;
    border-radius: 6px;
  }

  #language-button:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .language-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    min-width: 150px;
    display: none;
    flex-direction: column;
    padding: 8px 0;
  }

  .language-option {
    padding: 8px 16px;
    text-decoration: none;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .language-option:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .language-option.active {
    background-color: rgba(0, 0, 0, 0.07);
    font-weight: 500;
  }

  .dropdown-arrow {
    font-size: 8px;
    transition: transform 0.2s ease;
  }

  .language-selector.open .language-dropdown {
    display: flex;
  }

  .language-selector.open .dropdown-arrow {
    transform: rotate(180deg);
  }
</style>

<script>
  console.log('Language selector script loaded');
  document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing language selector');
    const languageButton = document.getElementById('language-button');
    const languageSelector = document.querySelector('.language-selector');

    if (!languageButton || !languageSelector) {
      console.error('Language selector elements not found');
      return;
    }
    console.log('Language selector elements found');

    // Toggle dropdown when button is clicked
    languageButton.addEventListener('click', function(e) {
      e.preventDefault();
      languageSelector.classList.toggle('open');
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
      if (languageSelector && !languageSelector.contains(e.target)) {
        languageSelector.classList.remove('open');
      }
    });

    // Log that the language selector is ready
    console.log('Language selector initialized');

    // Add transition for smoother animation
    const style = document.createElement('style');
    style.textContent = `
      .language-dropdown {
        transition: opacity 0.2s ease, transform 0.2s ease;
        opacity: 0;
        transform: translateY(10px);
      }
      .language-selector.open .language-dropdown {
        display: flex;
        opacity: 1;
        transform: translateY(0);
      }
    `;
    document.head.appendChild(style);
  });
</script>