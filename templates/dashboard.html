
<!DOCTYPE html>
<html lang="{{ current_lang if current_lang else 'en_US' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RegulationGuru Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/main.css">
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2ecc71;
            --success-color: #4CAF50;
            --danger-color: #F44336;
            --warning-color: #FFEB3B;
            --info-color: #00BCD4;
            --light-color: #F5F5F5;
            --dark-color: #212121;
            --body-bg-light: #F9F9F9;
            --body-bg-dark: #121212;
            --card-bg-light: #FFFFFF;
            --card-bg-dark: #1E1E1E;
            --text-light: #212121;
            --text-dark: #EEEEEE;
            --border-color: #e0e0e0;
            --border-color-dark: #333;
        }

        body {
            font-family: 'Inter', sans-serif;
            transition: background-color 0.3s, color 0.3s;
            background-color: var(--body-bg-light);
            color: var(--text-light);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            width: 100%;
        }

        body.dark-mode {
            background-color: var(--body-bg-dark);
            color: var(--text-dark);
        }

        .navbar {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            background-color: var(--card-bg-light);
            transition: background-color 0.3s;
            padding: 0.5rem 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        body.dark-mode .navbar {
            background-color: var(--card-bg-dark);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .navbar-left, .navbar-right {
            display: flex;
            align-items: center;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            color: var(--primary-color);
            text-decoration: none;
            font-size: 1.25rem;
        }

        .nav-links {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .nav-links li {
            margin-left: 1.5rem;
        }

        .nav-links a {
            display: flex;
            align-items: center;
            color: var(--text-light);
            text-decoration: none;
            transition: color 0.2s;
        }

        body.dark-mode .nav-links a {
            color: var(--text-dark);
        }

        .nav-links a:hover {
            color: var(--primary-color);
        }

        .nav-links .material-icons, .navbar-brand .material-icons {
            margin-right: 0.5rem;
        }

        .container {
            flex: 1;
            width: 100%;
            max-width: 100%;
            padding: 2rem;
            box-sizing: border-box;
        }

        .card {
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: box-shadow 0.3s ease, background-color 0.3s;
            background-color: var(--card-bg-light);
            border: 1px solid var(--border-color);
            margin-bottom: 1.5rem;
        }

        body.dark-mode .card {
            background-color: var(--card-bg-dark);
            border-color: var(--border-color-dark);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .card:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        body.dark-mode .card:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .card-body {
            padding: 1.5rem;
        }

        .btn {
            border-radius: 6px;
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: background-color 0.2s, transform 0.1s;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: #2980b9;
        }

        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }

        .btn-secondary:hover {
            background-color: #27ae60;
        }

        .btn-outline-primary {
            background-color: transparent;
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
        }

        body.dark-mode .btn-outline-primary {
            color: #64B5F6;
            border-color: #64B5F6;
        }

        .btn-outline-primary:hover {
            background-color: rgba(33, 150, 243, 0.1);
        }

        .welcome-banner {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        body.dark-mode .welcome-banner {
            background: linear-gradient(135deg, #1565C0, #0097A7);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .feature-icon {
            font-size: 48px;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        body.dark-mode .feature-icon {
            color: #64B5F6;
        }

        footer {
            margin-top: auto;
            padding: 2rem 0;
            background-color: var(--light-color);
            border-top: 1px solid var(--border-color);
            transition: background-color 0.3s;
            width: 100%;
        }

        body.dark-mode footer {
            background-color: #1A1A1A;
            border-top: 1px solid var(--border-color-dark);
        }

        .dark-mode-toggle {
            display: flex;
            align-items: center;
            margin-left: 1rem;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="navbar-left">
            <a class="navbar-brand" href="/">
                <i class="fas fa-shield-alt"></i>
                RegulationGuru
            </a>
            <ul class="nav-links">
                <li><a href="/"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li><a href="/api/v1/docs"><i class="fas fa-code"></i> API Docs</a></li>
                <li><a href="/ui/manage"><i class="fas fa-user-shield"></i> Admin</a></li>
            </ul>
        </div>
        <div class="navbar-right">
            <div class="language-nav-selector">
                <button id="nav-language-button" aria-label="{% if _ is defined %}{{ _('Select language') }}{% else %}Select language{% endif %}">
                    <span class="flag">{% if languages is defined and current_lang in languages %}{{ languages[current_lang]["flag"] }}{% else %}🌐{% endif %}</span>
                    <span class="language-name">{% if languages is defined and current_lang in languages %}{{ languages[current_lang]["label"] }}{% else %}Language{% endif %}</span>
                    <span class="dropdown-arrow">▼</span>
                </button>
                <div class="language-dropdown">
                    {% if languages is defined and languages %}
                        {% for lang_code, lang_info in languages.items() %}
                            <a href="?lang={{ lang_code }}" class="language-option {% if current_lang == lang_code %}active{% endif %}">
                                <span class="flag">{{ lang_info["flag"] }}</span>
                                <span class="language-name">{{ lang_info["label"] }}</span>
                            </a>
                        {% endfor %}
                    {% else %}
                        <a href="?lang=en_US" class="language-option {% if current_lang == 'en_US' %}active{% endif %}">
                            <span class="flag">🇺🇸</span>
                            <span class="language-name">English (US)</span>
                        </a>
                        <a href="?lang=en_GB" class="language-option {% if current_lang == 'en_GB' %}active{% endif %}">
                            <span class="flag">🇬🇧</span>
                            <span class="language-name">English (UK)</span>
                        </a>
                        <a href="?lang=es" class="language-option {% if current_lang == 'es' %}active{% endif %}">
                            <span class="flag">🇪🇸</span>
                            <span class="language-name">Español</span>
                        </a>
                        <a href="?lang=de" class="language-option {% if current_lang == 'de' %}active{% endif %}">
                            <span class="flag">🇩🇪</span>
                            <span class="language-name">Deutsch</span>
                        </a>
                        <a href="?lang=af" class="language-option {% if current_lang == 'af' %}active{% endif %}">
                            <span class="flag">🇿🇦</span>
                            <span class="language-name">Afrikaans</span>
                        </a>
                        <a href="?lang=zu" class="language-option {% if current_lang == 'zu' %}active{% endif %}">
                            <span class="flag">🇿🇦</span>
                            <span class="language-name">isiZulu</span>
                        </a>
                        <a href="?lang=gsw" class="language-option {% if current_lang == 'gsw' %}active{% endif %}">
                            <span class="flag">🇨🇭</span>
                            <span class="language-name">ZuriTüütsch</span>
                        </a>
                    {% endif %}
                </div>
            </div>
            <div class="dark-mode-toggle" id="themeToggle">
                <i class="fas fa-moon toggle-icon"></i>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="welcome-banner">
            <h1>{{ welcome_message if welcome_message else 'Welcome to RegulationGuru' }}</h1>
            <p class="lead">Comprehensive regulatory compliance management and tracking</p>
        </div>

        {% if show_api_docs %}
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h2 class="card-title"><i class="fas fa-code"></i> API Documentation</h2>
                        <p class="card-text">Explore our comprehensive API documentation to integrate with RegulationGuru.</p>
                        <div class="d-grid gap-2 d-md-flex justify-content-md-start">
                            <a href="/api/v1/docs" class="btn btn-primary">OpenAPI Docs</a>
                            <a href="/api/v1/redoc" class="btn btn-secondary">ReDoc</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="card-title">Compliance Tracking</h3>
                        <p class="card-text">Track regulatory compliance across multiple jurisdictions and requirements.</p>
                        <a href="/api/v1/compliance" class="btn btn-outline-primary">Explore</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h3 class="card-title">Regulatory Alerts</h3>
                        <p class="card-text">Stay updated with alerts on regulatory changes affecting your business.</p>
                        <a href="/api/v1/alerts" class="btn btn-outline-primary">Explore</a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="feature-icon">
                            <i class="fas fa-analytics"></i>
                        </div>
                        <h3 class="card-title">Impact Assessment</h3>
                        <p class="card-text">Assess the impact of regulatory changes on your organization.</p>
                        <a href="/api/v1/impact" class="btn btn-outline-primary">Explore</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h3 class="card-title"><i class="fas fa-globe"></i> Global Coverage</h3>
                        <p class="card-text">Access regulatory information from jurisdictions around the world.</p>
                        <a href="/api/v1/worldmap" class="btn btn-primary">View World Map</a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h3 class="card-title"><i class="fas fa-file-alt"></i> Document Analysis</h3>
                        <p class="card-text">Analyze regulatory documents using advanced AI techniques.</p>
                        <a href="/document-import" class="btn btn-primary">Document Import</a>
                    </div>
                </div>
            </div>
        </div>

        <h2 class="section-title"><i class="fas fa-plug"></i> API Endpoints</h2>
        <div class="api-endpoints">
            <div class="card api-card">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-globe"></i> Countries</h5>
                    <p class="card-text">Access country-specific regulatory information.</p>
                    <a href="/api/v1/countries" class="btn btn-sm btn-outline-primary">View Countries</a>
                </div>
            </div>
            <div class="card api-card">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-balance-scale"></i> Regulators</h5>
                    <p class="card-text">Information about regulatory authorities worldwide.</p>
                    <a href="/api/v1/regulators" class="btn btn-sm btn-outline-primary">View Regulators</a>
                </div>
            </div>
            <div class="card api-card">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-gavel"></i> Regulations</h5>
                    <p class="card-text">Detailed regulatory data and URLs.</p>
                    <a href="/api/v1/regulations" class="btn btn-sm btn-outline-primary">View Regulations</a>
                </div>
            </div>
            <div class="card api-card">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-chart-pie"></i> Trends</h5>
                    <p class="card-text">Analyze regulatory trends and patterns.</p>
                    <a href="/api/v1/trends" class="btn btn-sm btn-outline-primary">View Trends</a>
                </div>
            </div>
            <div class="card api-card">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-calendar-alt"></i> Digests</h5>
                    <p class="card-text">Scheduled regulatory digests and updates.</p>
                    <a href="/api/v1/digests" class="btn btn-sm btn-outline-primary">View Digests</a>
                </div>
            </div>
            <div class="card api-card">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-heartbeat"></i> Health Check</h5>
                    <p class="card-text">Check system health and status.</p>
                    <a href="/api/v1/health" class="btn btn-sm btn-outline-primary">Check Health</a>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>RegulationGuru</h5>
                    <p>Your global regulatory compliance platform</p>
                </div>
                <div class="col-md-3">
                    <h5>Resources</h5>
                    <ul class="list-unstyled">
                        <li><a href="/api/v1/docs">API Documentation</a></li>
                        <li><a href="/ui/manage">Admin Panel</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>Languages</h5>
                    <div class="language-options">
                        <a href="?lang=en_US" class="language-link">English (US)</a>
                        <a href="?lang=en_GB" class="language-link">English (UK)</a>
                        <a href="?lang=es" class="language-link">Español</a>
                    </div>
                </div>
            </div>
            <div class="row copyright">
                <div class="col-12 text-center">
                    <p>&copy; 2025 RegulationGuru. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="/static/js/main.js"></script>
</body>
</html>
