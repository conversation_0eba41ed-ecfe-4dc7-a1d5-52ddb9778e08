<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Regulatory Analytics Dashboard - RegulationGuru</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: rgba(44, 62, 80, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .analytics-card {
            background: rgba(255, 255, 255, 0.98);
            border: none;
            border-radius: 15px;
            box-shadow: 
                20px 20px 60px #bebebe,
                -20px -20px 60px #ffffff;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .analytics-card:hover {
            transform: translateY(-5px);
            box-shadow: 
                25px 25px 70px #bebebe,
                -25px -25px 70px #ffffff;
        }

        .metric-card {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 
                10px 10px 30px rgba(0, 0, 0, 0.1),
                -10px -10px 30px rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-3px);
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }

        .compliance-gauge {
            position: relative;
            width: 200px;
            height: 200px;
            margin: 0 auto;
        }

        .risk-indicator {
            padding: 10px;
            border-radius: 10px;
            margin: 5px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .risk-low { background: rgba(40, 167, 69, 0.1); border-left: 4px solid #28a745; }
        .risk-medium { background: rgba(255, 193, 7, 0.1); border-left: 4px solid #ffc107; }
        .risk-high { background: rgba(220, 53, 69, 0.1); border-left: 4px solid #dc3545; }

        .trend-arrow {
            font-size: 1.2rem;
            margin-left: 10px;
        }

        .trend-up { color: #28a745; }
        .trend-down { color: #dc3545; }
        .trend-stable { color: #6c757d; }

        .filter-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .insight-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin: 10px 0;
        }

        .insight-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-shield-alt me-2"></i>
                RegulationGuru
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Dashboard</a>
                <a class="nav-link" href="/regulatory-map">Regulatory Map</a>
                <a class="nav-link active" href="/regulatory-analytics">Analytics</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="text-white">
                        <i class="fas fa-chart-line me-2"></i>
                        Regulatory Analytics Dashboard
                    </h2>
                    <div class="btn-group">
                        <button class="btn btn-light btn-sm" id="refreshData">
                            <i class="fas fa-sync me-1"></i>
                            Refresh
                        </button>
                        <button class="btn btn-light btn-sm" id="exportReport">
                            <i class="fas fa-download me-1"></i>
                            Export Report
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="filter-panel">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">Time Period</label>
                            <select class="form-select" id="timePeriod">
                                <option value="30">Last 30 Days</option>
                                <option value="90" selected>Last 90 Days</option>
                                <option value="180">Last 6 Months</option>
                                <option value="365">Last Year</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Jurisdiction</label>
                            <select class="form-select" id="jurisdictionFilter">
                                <option value="">All Jurisdictions</option>
                                <option value="US">United States</option>
                                <option value="EU">European Union</option>
                                <option value="Global">Global</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Entity Type</label>
                            <select class="form-select" id="entityTypeFilter">
                                <option value="">All Types</option>
                                <option value="regulation">Regulations</option>
                                <option value="requirement">Requirements</option>
                                <option value="control">Controls</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button class="btn btn-primary w-100" id="applyFilters">
                                <i class="fas fa-filter me-1"></i>
                                Apply Filters
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Key Metrics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value text-primary" id="totalEntities">--</div>
                    <div class="metric-label">Total Entities</div>
                    <div class="trend-arrow trend-up" id="entitiesTrend">
                        <i class="fas fa-arrow-up"></i> 5.2%
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value text-success" id="complianceRate">--</div>
                    <div class="metric-label">Compliance Rate</div>
                    <div class="trend-arrow trend-up" id="complianceTrend">
                        <i class="fas fa-arrow-up"></i> 2.1%
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value text-warning" id="atRiskEntities">--</div>
                    <div class="metric-label">At Risk Entities</div>
                    <div class="trend-arrow trend-down" id="riskTrend">
                        <i class="fas fa-arrow-down"></i> 1.8%
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value text-info" id="avgComplianceScore">--</div>
                    <div class="metric-label">Avg Compliance Score</div>
                    <div class="trend-arrow trend-stable" id="scoreTrend">
                        <i class="fas fa-minus"></i> 0.3%
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row 1 -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="analytics-card p-4">
                    <h5 class="mb-3">
                        <i class="fas fa-chart-line me-2"></i>
                        Compliance Trends Over Time
                    </h5>
                    <div class="chart-container">
                        <canvas id="complianceTrendChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="analytics-card p-4">
                    <h5 class="mb-3">
                        <i class="fas fa-pie-chart me-2"></i>
                        Entity Distribution
                    </h5>
                    <div class="chart-container">
                        <canvas id="entityDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row 2 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="analytics-card p-4">
                    <h5 class="mb-3">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        Compliance Status Overview
                    </h5>
                    <div class="chart-container">
                        <canvas id="complianceStatusChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="analytics-card p-4">
                    <h5 class="mb-3">
                        <i class="fas fa-globe me-2"></i>
                        Jurisdiction Analysis
                    </h5>
                    <div class="chart-container">
                        <canvas id="jurisdictionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Risk Analysis -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="analytics-card p-4">
                    <h5 class="mb-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Risk Analysis
                    </h5>
                    <div id="riskAnalysis">
                        <!-- Risk indicators will be populated by JavaScript -->
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="analytics-card p-4">
                    <h5 class="mb-3">
                        <i class="fas fa-lightbulb me-2"></i>
                        AI Insights
                    </h5>
                    <div id="aiInsights">
                        <!-- AI insights will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="/static/js/regulatory-analytics.js"></script>
</body>
</html>
