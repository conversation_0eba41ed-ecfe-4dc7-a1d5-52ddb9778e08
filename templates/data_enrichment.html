
{% extends "base.html" %}

{% block title %}Data Enrichment Review{% endblock %}

{% block content %}
<div class="container my-4">
  <h1 class="mb-4">Data Enrichment Review</h1>
  
  <div class="alert alert-info">
    <i class="bi bi-info-circle me-2"></i>
    Review the suggested changes to existing records and approve or reject each modification.
  </div>

  <div id="enrichment-container">
    <div class="d-flex justify-content-between mb-3">
      <h2 class="mb-0" id="record-type">Loading...</h2>
      <div>
        <button id="approve-all-btn" class="btn btn-outline-success me-2">
          <i class="bi bi-check-all me-1"></i> Approve All
        </button>
        <button id="apply-changes-btn" class="btn btn-primary">
          <i class="bi bi-save me-1"></i> Apply Changes
        </button>
      </div>
    </div>

    <div id="record-cards-container">
      <!-- Record cards will be populated here -->
      <div class="text-center p-5">
        <div class="spinner-border" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-3">Loading enrichment suggestions...</p>
      </div>
    </div>
  </div>
</div>

<!-- Record Enrichment Card Template -->
<template id="record-card-template">
  <div class="card mb-4 enrichment-card">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h5 class="mb-0 record-name">Record Name</h5>
      <div class="btn-group">
        <button class="btn btn-sm btn-outline-secondary compare-btn">
          <i class="bi bi-arrows-collapse"></i> Expand Changes
        </button>
        <button class="btn btn-sm btn-success approve-btn">
          <i class="bi bi-check-lg"></i> Approve
        </button>
        <button class="btn btn-sm btn-danger reject-btn">
          <i class="bi bi-x-lg"></i> Reject
        </button>
      </div>
    </div>
    <div class="card-body">
      <div class="row mb-3">
        <div class="col-12">
          <h6>Overall Confidence: <span class="overall-confidence badge"></span></h6>
        </div>
      </div>
      
      <div class="changes-container collapse">
        <div class="row">
          <div class="col-md-6">
            <div class="card bg-light mb-3">
              <div class="card-header">Original Data</div>
              <div class="card-body original-data">
                <pre class="mb-0">Loading...</pre>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="card bg-light mb-3">
              <div class="card-header">Enriched Data</div>
              <div class="card-body enriched-data">
                <pre class="mb-0">Loading...</pre>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <table class="table table-bordered field-changes-table">
        <thead>
          <tr>
            <th style="width: 30%">Field</th>
            <th style="width: 30%">Current Value</th>
            <th style="width: 30%">Suggested Value</th>
            <th style="width: 10%">Confidence</th>
          </tr>
        </thead>
        <tbody>
          <!-- Field changes will be added here -->
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const jobId = new URLSearchParams(window.location.search).get('job_id');
    if (!jobId) {
      showError('No job ID provided');
      return;
    }
    
    loadEnrichmentSuggestions(jobId);
    
    // Setup buttons
    document.getElementById('approve-all-btn').addEventListener('click', approveAllChanges);
    document.getElementById('apply-changes-btn').addEventListener('click', () => applyChanges(jobId));
  });
  
  async function loadEnrichmentSuggestions(jobId) {
    try {
      const response = await fetch(`/api/v1/data-enrichment/suggestions/${jobId}`);
      
      if (!response.ok) {
        throw new Error(`Server error: ${response.status}`);
      }
      
      const data = await response.json();
      renderEnrichmentSuggestions(data);
      
    } catch (error) {
      showError(`Error loading enrichment suggestions: ${error.message}`);
    }
  }
  
  function renderEnrichmentSuggestions(data) {
    const container = document.getElementById('record-cards-container');
    const recordType = document.getElementById('record-type');
    
    container.innerHTML = '';
    recordType.textContent = formatRecordType(data.record_type);
    
    if (!data.suggestions || data.suggestions.length === 0) {
      container.innerHTML = `
        <div class="alert alert-warning">
          <i class="bi bi-exclamation-triangle me-2"></i>
          No enrichment suggestions available.
        </div>
      `;
      return;
    }
    
    data.suggestions.forEach(suggestion => {
      const card = createEnrichmentCard(suggestion);
      container.appendChild(card);
    });
  }
  
  function createEnrichmentCard(suggestion) {
    const template = document.getElementById('record-card-template');
    const card = template.content.cloneNode(true).querySelector('.enrichment-card');
    
    // Set record identifier
    card.setAttribute('data-record-id', suggestion.id);
    card.setAttribute('data-record-type', suggestion.record_type);
    
    // Set record name
    card.querySelector('.record-name').textContent = suggestion.name || `Record #${suggestion.id}`;
    
    // Set overall confidence
    const confidenceBadge = card.querySelector('.overall-confidence');
    const confidenceValue = Math.round(suggestion.overall_confidence * 100);
    confidenceBadge.textContent = `${confidenceValue}%`;
    confidenceBadge.classList.add(getConfidenceClass(confidenceValue));
    
    // Set original and enriched data
    card.querySelector('.original-data pre').textContent = JSON.stringify(suggestion.original_data, null, 2);
    card.querySelector('.enriched-data pre').textContent = JSON.stringify(suggestion.enriched_data, null, 2);
    
    // Populate field changes
    const tableBody = card.querySelector('.field-changes-table tbody');
    tableBody.innerHTML = '';
    
    suggestion.field_changes.forEach(change => {
      const row = document.createElement('tr');
      
      const fieldCell = document.createElement('td');
      fieldCell.textContent = formatFieldName(change.field);
      
      const currentCell = document.createElement('td');
      currentCell.textContent = formatValue(change.current_value);
      
      const suggestedCell = document.createElement('td');
      suggestedCell.textContent = formatValue(change.suggested_value);
      
      const confidenceCell = document.createElement('td');
      const confidencePercent = Math.round(change.confidence * 100);
      confidenceCell.innerHTML = `
        <div class="progress">
          <div class="progress-bar ${getConfidenceClass(confidencePercent, true)}" 
            style="width: ${confidencePercent}%" 
            role="progressbar">
            ${confidencePercent}%
          </div>
        </div>
      `;
      
      row.appendChild(fieldCell);
      row.appendChild(currentCell);
      row.appendChild(suggestedCell);
      row.appendChild(confidenceCell);
      
      // Highlight changed rows
      if (change.current_value !== change.suggested_value) {
        row.classList.add('table-warning');
      }
      
      tableBody.appendChild(row);
    });
    
    // Setup event listeners
    card.querySelector('.compare-btn').addEventListener('click', function() {
      const changesContainer = card.querySelector('.changes-container');
      changesContainer.classList.toggle('show');
      
      const icon = this.querySelector('i');
      if (changesContainer.classList.contains('show')) {
        this.innerHTML = '<i class="bi bi-arrows-expand"></i> Collapse Changes';
      } else {
        this.innerHTML = '<i class="bi bi-arrows-collapse"></i> Expand Changes';
      }
    });
    
    card.querySelector('.approve-btn').addEventListener('click', function() {
      card.setAttribute('data-approved', 'true');
      card.classList.add('border-success');
      this.disabled = true;
      card.querySelector('.reject-btn').disabled = false;
    });
    
    card.querySelector('.reject-btn').addEventListener('click', function() {
      card.setAttribute('data-approved', 'false');
      card.classList.remove('border-success');
      this.disabled = true;
      card.querySelector('.approve-btn').disabled = false;
    });
    
    return card;
  }
  
  function approveAllChanges() {
    document.querySelectorAll('.enrichment-card').forEach(card => {
      card.setAttribute('data-approved', 'true');
      card.classList.add('border-success');
      card.querySelector('.approve-btn').disabled = true;
      card.querySelector('.reject-btn').disabled = false;
    });
  }
  
  async function applyChanges(jobId) {
    const approvedChanges = [];
    
    document.querySelectorAll('.enrichment-card[data-approved="true"]').forEach(card => {
      approvedChanges.push({
        id: card.getAttribute('data-record-id'),
        record_type: card.getAttribute('data-record-type')
      });
    });
    
    if (approvedChanges.length === 0) {
      alert('No changes selected for application');
      return;
    }
    
    try {
      // Disable apply button and show loading state
      const applyBtn = document.getElementById('apply-changes-btn');
      applyBtn.disabled = true;
      applyBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Applying...';
      
      const response = await fetch(`/api/v1/data-enrichment/apply/${jobId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          approved_changes: approvedChanges
        })
      });
      
      if (!response.ok) {
        throw new Error(`Server error: ${response.status}`);
      }
      
      const result = await response.json();
      
      // Show success message
      const container = document.getElementById('enrichment-container');
      const successMessage = document.createElement('div');
      successMessage.className = 'alert alert-success mt-4';
      successMessage.innerHTML = `
        <h4 class="alert-heading">Changes Applied Successfully!</h4>
        <p>
          Applied ${result.applied_count} changes to ${result.record_type} records.
        </p>
        <hr>
        <p class="mb-0">
          <a href="/data-collection" class="btn btn-primary">
            <i class="bi bi-arrow-left me-1"></i> Return to Data Collection
          </a>
        </p>
      `;
      
      container.appendChild(successMessage);
      
    } catch (error) {
      console.error('Error applying changes:', error);
      
      // Reset apply button
      const applyBtn = document.getElementById('apply-changes-btn');
      applyBtn.disabled = false;
      applyBtn.innerHTML = '<i class="bi bi-save me-1"></i> Apply Changes';
      
      // Show error message
      alert(`Error applying changes: ${error.message}`);
    }
  }
  
  function showError(message) {
    const container = document.getElementById('record-cards-container');
    container.innerHTML = `
      <div class="alert alert-danger">
        <i class="bi bi-exclamation-circle me-2"></i>
        ${message}
      </div>
    `;
    
    document.getElementById('record-type').textContent = 'Error';
    document.getElementById('approve-all-btn').disabled = true;
    document.getElementById('apply-changes-btn').disabled = true;
  }
  
  // Helper functions
  function formatRecordType(type) {
    switch(type) {
      case 'country':
        return 'Country Records';
      case 'regulation':
        return 'Regulation Records';
      case 'regulator':
        return 'Regulator Records';
      case 'regulatory_source':
        return 'Regulatory Source Records';
      default:
        return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) + ' Records';
    }
  }
  
  function formatFieldName(field) {
    return field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }
  
  function formatValue(value) {
    if (value === null || value === undefined) {
      return '<em class="text-muted">Empty</em>';
    }
    
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    
    return String(value);
  }
  
  function getConfidenceClass(confidence, isProgressBar = false) {
    if (confidence >= 80) {
      return isProgressBar ? 'bg-success' : 'bg-success text-white';
    } else if (confidence >= 50) {
      return isProgressBar ? 'bg-warning' : 'bg-warning';
    } else {
      return isProgressBar ? 'bg-danger' : 'bg-danger text-white';
    }
  }
</script>

<style>
  .field-changes-table {
    font-size: 0.9rem;
  }
  
  .progress {
    height: 20px;
  }
  
  pre {
    font-size: 0.8rem;
    max-height: 300px;
    overflow-y: auto;
  }
  
  .enrichment-card {
    transition: border-color 0.3s;
  }
  
  .enrichment-card[data-approved="true"] {
    border-width: 2px;
  }
</style>
{% endblock %}
