{% extends "base.html" %}

{% block title %}Document Import & Mapping{% endblock %}

{% block content %}
<div class="container my-4">
  <h1 class="mb-4">Document Import & Database Mapping</h1>

  <div class="card mb-4">
    <div class="card-header">
      <h5 class="mb-0">Upload Document</h5>
    </div>
    <div class="card-body">
      <form id="document-upload-form" enctype="multipart/form-data">
        <div class="mb-3">
          <label for="document-file" class="form-label">Document File</label>
          <input type="file" class="form-control" id="document-file" name="document" accept=".pdf" required>
          <div class="form-text">Upload a PDF document containing regulatory information, country profiles, or regulator information.</div>
        </div>

        <button type="submit" class="btn btn-primary">Analyze Document</button>
      </form>
    </div>
  </div>

  <div id="status-container" class="d-none alert alert-info">
    <div class="d-flex align-items-center">
      <div class="spinner-border spinner-border-sm me-2" role="status"></div>
      <div>Analyzing document... <span id="status-message"></span></div>
    </div>
  </div>

  <div id="results-container" class="d-none">
    <div class="d-flex justify-content-between mb-3 align-items-center">
      <h2 class="mb-0">Document Analysis Results</h2>
      <button id="apply-mappings-btn" class="btn btn-success">Apply Selected Mappings</button>
    </div>

    <div class="card mb-4">
      <div class="card-header">
        <h5 class="mb-0">Document Classification</h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6 mb-3">
            <div class="card">
              <div class="card-body">
                <h6 class="card-title">Document Type</h6>
                <p id="document-type" class="mb-0 fs-5 fw-bold"></p>
              </div>
            </div>
          </div>
          <div class="col-md-6 mb-3">
            <div class="card">
              <div class="card-body">
                <h6 class="card-title">Confidence Score</h6>
                <div class="d-flex align-items-center">
                  <div class="progress flex-grow-1 me-3" style="height: 25px;">
                    <div id="confidence-bar" class="progress-bar" role="progressbar"></div>
                  </div>
                  <div id="confidence-score" class="fs-5 fw-bold"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <h6 class="mt-3">Content Sample</h6>
        <div class="border rounded p-3 bg-light">
          <p id="text-sample" class="mb-0 text-secondary"></p>
        </div>
      </div>
    </div>

    <div id="country-mappings-container" class="d-none mb-4">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Country Mappings</h5>
          <button class="btn btn-sm btn-outline-primary toggle-select-all" data-target="country">Select All</button>
        </div>
        <div class="card-body">
          <div id="existing-countries" class="mb-4">
            <h6>Existing Countries Found</h6>
            <div class="table-responsive">
              <table class="table table-striped table-hover">
                <thead>
                  <tr>
                    <th style="width: 50px;">Select</th>
                    <th>Country Name</th>
                    <th>Country Code</th>
                    <th>Match Confidence</th>
                  </tr>
                </thead>
                <tbody id="existing-countries-table">
                  <!-- Existing countries will be added here -->
                </tbody>
              </table>
            </div>
          </div>

          <div id="new-countries">
            <h6>New Countries Detected</h6>
            <div class="table-responsive">
              <table class="table table-striped table-hover">
                <thead>
                  <tr>
                    <th style="width: 50px;">Select</th>
                    <th>Country Name</th>
                    <th>Country Code</th>
                    <th>Region</th>
                    <th>Match Confidence</th>
                  </tr>
                </thead>
                <tbody id="new-countries-table">
                  <!-- New countries will be added here -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div id="regulation-mappings-container" class="d-none mb-4">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Regulation Mappings</h5>
          <button class="btn btn-sm btn-outline-primary toggle-select-all" data-target="regulation">Select All</button>
        </div>
        <div class="card-body">
          <div id="existing-regulations" class="mb-4">
            <h6>Existing Regulations Found</h6>
            <div class="table-responsive">
              <table class="table table-striped table-hover">
                <thead>
                  <tr>
                    <th style="width: 50px;">Select</th>
                    <th>Regulation Title</th>
                    <th>Match Confidence</th>
                  </tr>
                </thead>
                <tbody id="existing-regulations-table">
                  <!-- Existing regulations will be added here -->
                </tbody>
              </table>
            </div>
          </div>

          <div id="new-regulations">
            <h6>New Regulations Detected</h6>
            <div class="table-responsive">
              <table class="table table-striped table-hover">
                <thead>
                  <tr>
                    <th style="width: 50px;">Select</th>
                    <th>Regulation Title</th>
                    <th>Country</th>
                    <th>Date</th>
                    <th>Match Confidence</th>
                  </tr>
                </thead>
                <tbody id="new-regulations-table">
                  <!-- New regulations will be added here -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div id="regulator-mappings-container" class="d-none mb-4">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Regulator Mappings</h5>
          <button class="btn btn-sm btn-outline-primary toggle-select-all" data-target="regulator">Select All</button>
        </div>
        <div class="card-body">
          <div id="existing-regulators" class="mb-4">
            <h6>Existing Regulators Found</h6>
            <div class="table-responsive">
              <table class="table table-striped table-hover">
                <thead>
                  <tr>
                    <th style="width: 50px;">Select</th>
                    <th>Regulator Name</th>
                    <th>Match Confidence</th>
                  </tr>
                </thead>
                <tbody id="existing-regulators-table">
                  <!-- Existing regulators will be added here -->
                </tbody>
              </table>
            </div>
          </div>

          <div id="new-regulators">
            <h6>New Regulators Detected</h6>
            <div class="table-responsive">
              <table class="table table-striped table-hover">
                <thead>
                  <tr>
                    <th style="width: 50px;">Select</th>
                    <th>Regulator Name</th>
                    <th>Country</th>
                    <th>Website</th>
                    <th>Match Confidence</th>
                  </tr>
                </thead>
                <tbody id="new-regulators-table">
                  <!-- New regulators will be added here -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('document-upload-form');
    const statusContainer = document.getElementById('status-container');
    const statusMessage = document.getElementById('status-message');
    const resultsContainer = document.getElementById('results-container');

    let jobId = null;
    let statusCheckInterval = null;
    let analysisResult = null;

    form.addEventListener('submit', async function(e) {
      e.preventDefault();

      // Reset UI
      resetResults();

      // Show status
      statusContainer.classList.remove('d-none');
      statusMessage.textContent = 'Starting analysis...';

      // Submit form
      const formData = new FormData(form);

      try {
        const response = await fetch('/document-import/analyze', {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          throw new Error(`Server error: ${response.status}`);
        }

        const data = await response.json();
        jobId = data.job_id;

        // Start checking status
        statusMessage.textContent = 'Processing document...';
        statusCheckInterval = setInterval(checkStatus, 2000);

      } catch (error) {
        statusContainer.classList.add('alert-danger');
        statusContainer.classList.remove('alert-info');
        statusMessage.textContent = `Error: ${error.message}`;
      }
    });

    async function checkStatus() {
      if (!jobId) return;

      try {
        const response = await fetch(`/document-import/status/${jobId}`);
        if (!response.ok) {
          throw new Error(`Server error: ${response.status}`);
        }

        const data = await response.json();

        // Update status message
        statusMessage.textContent = data.status || 'Processing...';

        if (data.status === 'completed') {
          clearInterval(statusCheckInterval);
          statusCheckInterval = null;

          // Update UI to show completion
          statusContainer.classList.add('alert-success');
          statusContainer.classList.remove('alert-info');
          statusMessage.textContent = 'Analysis complete!';

          // Display results
          displayResults(data.result);
        } else if (data.status === 'failed') {
          clearInterval(statusCheckInterval);
          statusCheckInterval = null;

          // Update UI to show error
          statusContainer.classList.add('alert-danger');
          statusContainer.classList.remove('alert-info');
          statusMessage.textContent = `Analysis failed: ${data.error || 'Unknown error'}`;
        }
      } catch (error) {
        clearInterval(statusCheckInterval);
        statusCheckInterval = null;

        // Update UI to show error
        statusContainer.classList.add('alert-danger');
        statusContainer.classList.remove('alert-info');
        statusMessage.textContent = `Error checking status: ${error.message}`;
      }
    }

    function resetResults() {
      // Reset status container
      statusContainer.classList.remove('alert-success', 'alert-danger');
      statusContainer.classList.add('alert-info', 'd-none');
      statusMessage.textContent = '';

      // Clear results
      resultsContainer.innerHTML = '';
      resultsContainer.classList.add('d-none');

      // Clear any existing intervals
      if (statusCheckInterval) {
        clearInterval(statusCheckInterval);
        statusCheckInterval = null;
      }

      // Reset job ID
      jobId = null;
    }

    function displayResults(result) {
      if (!result) return;

      resultsContainer.classList.remove('d-none');
      resultsContainer.innerHTML = `
        <div class="card mt-4">
          <div class="card-body">
            <h5 class="card-title">Analysis Results</h5>
            <div class="results-content">
              ${formatResults(result)}
            </div>
          </div>
        </div>
      `;
    }

    function formatResults(result) {
      // Format the results based on what's returned from the API
      if (typeof result === 'string') {
        return `<p>${result}</p>`;
      }

      if (Array.isArray(result)) {
        return `<ul>${result.map(item => `<li>${item}</li>`).join('')}</ul>`;
      }

      if (typeof result === 'object') {
        return `<dl>${Object.entries(result).map(([key, value]) => 
          `<dt>${key}</dt><dd>${typeof value === 'object' ? JSON.stringify(value) : value}</dd>`
        ).join('')}</dl>`;
      }

      return JSON.stringify(result);
    }

    // Helper functions
    function formatDocumentType(type) {
      switch(type) {
        case 'country_profile':
          return 'Country Profile';
        case 'regulation':
          return 'Regulation Document';
        case 'regulator_profile':
          return 'Regulator Profile';
        case 'general_guidance':
          return 'General Guidance';
        default:
          return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
      }
    }

    function getConfidenceClass(confidence) {
      const percent = confidence * 100;
      if (percent >= 70) {
        return 'bg-success';
      } else if (percent >= 40) {
        return 'bg-warning';
      } else {
        return 'bg-danger';
      }
    }
  });
</script>
{% endblock %}