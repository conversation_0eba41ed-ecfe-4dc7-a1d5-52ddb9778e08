
<!DOCTYPE html>
<html>
<head>
    <title>RegulationGuru Analytics</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #3182ce;
            --primary-light: #ebf8ff;
            --success: #48bb78;
            --warning: #ed8936;
            --danger: #e53e3e;
            --bg-color: #f7fafc;
            --card-bg: #ffffff;
            --text-color: #2d3748;
            --text-secondary: #4a5568;
            --border-color: #e2e8f0;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            --transition: all 0.3s ease;
        }
        
        [data-theme="dark"] {
            --primary: #63b3ed;
            --primary-light: #2c3e50;
            --success: #68d391;
            --warning: #f6ad55;
            --danger: #fc8181;
            --bg-color: #1a202c;
            --card-bg: #2d3748;
            --text-color: #e2e8f0;
            --text-secondary: #cbd5e0;
            --border-color: #4a5568;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
        }
        
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--bg-color);
            color: var(--text-color);
            line-height: 1.6;
            transition: var(--transition);
        }
        
        .header {
            background-color: var(--card-bg);
            padding: 20px;
            box-shadow: var(--shadow);
            margin-bottom: 30px;
        }
        
        .container {
            max-width: 1300px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        h1 {
            color: var(--primary);
            margin: 0;
            font-size: 1.8rem;
        }
        
        .theme-toggle {
            background: none;
            border: none;
            color: var(--text-color);
            cursor: pointer;
            font-size: 1.2rem;
            padding: 5px;
        }
        
        .dashboard-container {
            margin-bottom: 30px;
        }
        
        .nav {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .nav-item {
            text-decoration: none;
            color: var(--text-secondary);
            padding: 10px 15px;
            border-radius: 6px;
            font-weight: 500;
            transition: var(--transition);
            display: flex;
            align-items: center;
        }
        
        .nav-item:hover {
            color: var(--primary);
            background-color: var(--primary-light);
        }
        
        .nav-icon {
            margin-right: 8px;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
            gap: 25px;
        }
        
        .card {
            background-color: var(--card-bg);
            border-radius: 10px;
            box-shadow: var(--shadow);
            padding: 25px;
            transition: var(--transition);
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }
        
        h2 {
            color: var(--primary);
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 10px;
        }
        
        .card-icon {
            margin-right: 10px;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .error {
            color: var(--danger);
            background-color: rgba(229, 62, 62, 0.1);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
            
            .nav {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="header-content">
                <h1>Regulatory Analytics Dashboard</h1>
                <button class="theme-toggle" id="themeToggle" aria-label="Toggle dark/light mode">
                    <i class="fas fa-moon toggle-icon"></i>
                </button>
            </div>
            <div class="nav">
                <a href="/dashboard" class="nav-item">
                    <i class="fas fa-home nav-icon"></i> Main Dashboard
                </a>
                <a href="/regulatory" class="nav-item">
                    <i class="fas fa-clipboard-check nav-icon"></i> Compliance
                </a>
                <a href="/settings" class="nav-item">
                    <i class="fas fa-cog nav-icon"></i> Settings
                </a>
            </div>
        </div>
    </div>
    
    <div class="container dashboard-container">
        <div class="grid">
            <div class="card">
                <h2><i class="fas fa-chart-pie card-icon"></i> Confidence Score Distribution</h2>
                <div class="chart-container">
                    <canvas id="confidenceChart"></canvas>
                </div>
            </div>
            
            <div class="card">
                <h2><i class="fas fa-flag card-icon"></i> Top Countries</h2>
                <div class="chart-container">
                    <canvas id="countriesChart"></canvas>
                </div>
            </div>
            
            <div class="card">
                <h2><i class="fas fa-building card-icon"></i> Top Regulators</h2>
                <div class="chart-container">
                    <canvas id="regulatorsChart"></canvas>
                </div>
            </div>
            
            <div class="card">
                <h2><i class="fas fa-tags card-icon"></i> URL Categories</h2>
                <div class="chart-container">
                    <canvas id="categoriesChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Theme switcher
        const themeToggleBtn = document.getElementById('themeToggle');
        const icon = themeToggleBtn.querySelector('.toggle-icon');
        
        // Check for saved theme preference or prefer-color-scheme
        const savedTheme = localStorage.getItem('theme');
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        
        if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
            document.documentElement.setAttribute('data-theme', 'dark');
            icon.classList.remove('fa-moon');
            icon.classList.add('fa-sun');
        }
        
        themeToggleBtn.addEventListener('click', () => {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            
            if (newTheme === 'dark') {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
            } else {
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
            }
            
            // Update chart colors for the theme
            updateChartTheme();
        });
        
        // Function to update chart colors based on theme
        function updateChartTheme() {
            Chart.defaults.color = getComputedStyle(document.documentElement).getPropertyValue('--text-color');
            // Redraw charts if they exist
            if (window.confidenceChart) window.confidenceChart.update();
            if (window.countriesChart) window.countriesChart.update();
            if (window.regulatorsChart) window.regulatorsChart.update();
            if (window.categoriesChart) window.categoriesChart.update();
        }
        
        // Configure Chart.js to use the theme colors
        Chart.defaults.color = getComputedStyle(document.documentElement).getPropertyValue('--text-color');
        
        // Fetch analytics data
        fetch('/api/v1/analytics/dashboard')
            .then(response => response.json())
            .then(data => {
                // Confidence Chart
                const confidenceCtx = document.getElementById('confidenceChart').getContext('2d');
                window.confidenceChart = new Chart(confidenceCtx, {
                    type: 'pie',
                    data: {
                        labels: ['High', 'Medium', 'Low'],
                        datasets: [{
                            data: [
                                data.confidence_distribution.high, 
                                data.confidence_distribution.medium, 
                                data.confidence_distribution.low
                            ],
                            backgroundColor: [
                                'rgba(72, 187, 120, 0.7)',
                                'rgba(237, 137, 54, 0.7)',
                                'rgba(229, 62, 62, 0.7)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
                
                // Countries Chart
                const countriesCtx = document.getElementById('countriesChart').getContext('2d');
                window.countriesChart = new Chart(countriesCtx, {
                    type: 'bar',
                    data: {
                        labels: data.top_countries.map(c => c.name),
                        datasets: [{
                            label: 'Number of Regulations',
                            data: data.top_countries.map(c => c.count),
                            backgroundColor: 'rgba(49, 130, 206, 0.7)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: getComputedStyle(document.documentElement).getPropertyValue('--border-color')
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
                
                // Regulators Chart
                const regulatorsCtx = document.getElementById('regulatorsChart').getContext('2d');
                window.regulatorsChart = new Chart(regulatorsCtx, {
                    type: 'bar',
                    data: {
                        labels: data.top_regulators.map(r => r.name),
                        datasets: [{
                            label: 'Number of Regulations',
                            data: data.top_regulators.map(r => r.count),
                            backgroundColor: 'rgba(102, 126, 234, 0.7)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: getComputedStyle(document.documentElement).getPropertyValue('--border-color')
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
                
                // Categories Chart if available
                if (data.categories) {
                    const categoriesCtx = document.getElementById('categoriesChart').getContext('2d');
                    window.categoriesChart = new Chart(categoriesCtx, {
                        type: 'doughnut',
                        data: {
                            labels: Object.keys(data.categories),
                            datasets: [{
                                data: Object.values(data.categories),
                                backgroundColor: [
                                    'rgba(229, 62, 62, 0.7)',
                                    'rgba(49, 130, 206, 0.7)',
                                    'rgba(237, 137, 54, 0.7)',
                                    'rgba(72, 187, 120, 0.7)',
                                    'rgba(102, 126, 234, 0.7)',
                                    'rgba(213, 63, 140, 0.7)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom'
                                }
                            }
                        }
                    });
                }
            })
            .catch(error => {
                console.error('Error fetching analytics data:', error);
                document.querySelectorAll('.chart-container').forEach(container => {
                    container.innerHTML = `<p class="error"><i class="fas fa-exclamation-circle"></i> Error loading chart data: ${error.message}</p>`;
                });
            });
    });
    </script>
</body>
</html>
