<!DOCTYPE html>
<html lang="{{ current_lang if current_lang else 'en_US' }}" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="RegulationGuru - Comprehensive regulatory compliance management platform">
    <title>RegulationGuru Dashboard - Enhanced UI</title>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" as="style">
    <link rel="preload" href="/static/css/enhanced-ui.css" as="style">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/enhanced-ui.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/static/images/favicon.svg">
    
    <style>
        /* Enhanced Dashboard Specific Styles */
        .hero-section {
            background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-700) 50%, var(--secondary-500) 100%);
            color: white;
            padding: var(--spacing-2xl) 0;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 1;
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
        }

        .hero-title {
            font-size: var(--font-size-4xl);
            font-weight: 700;
            margin-bottom: var(--spacing-lg);
            line-height: var(--leading-tight);
        }

        .hero-subtitle {
            font-size: var(--font-size-xl);
            opacity: 0.9;
            margin-bottom: var(--spacing-2xl);
            line-height: var(--leading-relaxed);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-lg);
            margin: var(--spacing-2xl) 0;
        }

        .stat-card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            text-align: center;
            transition: all var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        .stat-number {
            font-size: var(--font-size-3xl);
            font-weight: 700;
            color: var(--primary-500);
            margin-bottom: var(--spacing-sm);
        }

        .stat-label {
            color: var(--text-secondary);
            font-weight: 500;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
            margin: var(--spacing-2xl) 0;
        }

        .feature-card {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            transition: all var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: var(--font-size-2xl);
            margin-bottom: var(--spacing-lg);
        }

        .feature-title {
            font-size: var(--font-size-xl);
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
            color: var(--text-primary);
        }

        .feature-description {
            color: var(--text-secondary);
            line-height: var(--leading-relaxed);
            margin-bottom: var(--spacing-lg);
        }

        .quick-actions {
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            margin: var(--spacing-2xl) 0;
        }

        .quick-actions-title {
            font-size: var(--font-size-2xl);
            font-weight: 600;
            margin-bottom: var(--spacing-lg);
            text-align: center;
            color: var(--text-primary);
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
        }

        .action-button {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-md);
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            text-decoration: none;
            color: var(--text-primary);
            transition: all var(--transition-fast);
        }

        .action-button:hover {
            background: var(--primary-50);
            border-color: var(--primary-500);
            transform: translateY(-1px);
        }

        .animate-on-scroll {
            opacity: 0;
            transform: translateY(20px);
            transition: all var(--transition-slow);
        }

        .animate-on-scroll.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        /* Loading states */
        .loading-skeleton {
            background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .hero-title {
                font-size: var(--font-size-3xl);
            }
            
            .hero-subtitle {
                font-size: var(--font-size-lg);
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Enhanced Navigation -->
    <nav class="navbar navbar-enhanced" role="navigation" aria-label="Main navigation">
        <div class="container">
            <div class="navbar-left">
                <a class="navbar-brand" href="/" aria-label="RegulationGuru Home">
                    <i class="fas fa-shield-alt" aria-hidden="true"></i>
                    <span class="text-gradient">RegulationGuru</span>
                </a>
                
                <ul class="nav-links" role="menubar">
                    <li class="nav-item-enhanced" role="none">
                        <a href="/" role="menuitem" data-tooltip="Dashboard Overview">
                            <i class="fas fa-tachometer-alt" aria-hidden="true"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item-enhanced" role="none">
                        <a href="/compliance-calendar" role="menuitem" data-tooltip="Compliance Calendar">
                            <i class="fas fa-calendar-alt" aria-hidden="true"></i>
                            <span>Calendar</span>
                        </a>
                    </li>
                    <li class="nav-item-enhanced" role="none">
                        <a href="/compliance-analytics" role="menuitem" data-tooltip="Analytics Dashboard">
                            <i class="fas fa-chart-line" aria-hidden="true"></i>
                            <span>Analytics</span>
                        </a>
                    </li>
                    <li class="nav-item-enhanced" role="none">
                        <a href="/api/v1/docs" role="menuitem" data-tooltip="API Documentation">
                            <i class="fas fa-code" aria-hidden="true"></i>
                            <span>API</span>
                        </a>
                    </li>
                </ul>
            </div>
            
            <div class="navbar-right">
                <!-- Language Selector -->
                <div class="language-nav-selector">
                    <button id="nav-language-button" aria-label="Select language" aria-haspopup="true" aria-expanded="false">
                        <span class="flag" aria-hidden="true">🌐</span>
                        <span class="language-name">Language</span>
                        <span class="dropdown-arrow" aria-hidden="true">▼</span>
                    </button>
                    <div class="language-dropdown" role="menu" aria-label="Language options">
                        <a href="?lang=en_US" class="language-option" role="menuitem">
                            <span class="flag" aria-hidden="true">🇺🇸</span>
                            <span class="language-name">English (US)</span>
                        </a>
                        <a href="?lang=en_GB" class="language-option" role="menuitem">
                            <span class="flag" aria-hidden="true">🇬🇧</span>
                            <span class="language-name">English (UK)</span>
                        </a>
                        <a href="?lang=es" class="language-option" role="menuitem">
                            <span class="flag" aria-hidden="true">🇪🇸</span>
                            <span class="language-name">Español</span>
                        </a>
                    </div>
                </div>
                
                <!-- Theme Toggle -->
                <button id="themeToggle" class="dark-mode-toggle" aria-label="Toggle dark mode" data-tooltip="Switch theme">
                    <i class="fas fa-moon toggle-icon" aria-hidden="true"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content" role="main">
        <!-- Hero Section -->
        <section class="hero-section" aria-labelledby="hero-title">
            <div class="container">
                <div class="hero-content animate-on-scroll">
                    <h1 id="hero-title" class="hero-title">
                        Welcome to RegulationGuru
                    </h1>
                    <p class="hero-subtitle">
                        Your comprehensive platform for regulatory compliance management, 
                        risk assessment, and governance automation.
                    </p>
                    <div class="d-flex gap-3 justify-content-center">
                        <a href="/api/v1/docs" class="btn-enhanced btn-primary" data-tooltip="Explore our API">
                            <i class="fas fa-rocket" aria-hidden="true"></i>
                            Get Started
                        </a>
                        <a href="/compliance-calendar" class="btn-enhanced btn-outline" data-tooltip="View compliance calendar">
                            <i class="fas fa-calendar-alt" aria-hidden="true"></i>
                            View Calendar
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Statistics Section -->
        <section class="container" aria-labelledby="stats-title">
            <h2 id="stats-title" class="sr-only">Platform Statistics</h2>
            <div class="stats-grid animate-on-scroll">
                <div class="stat-card">
                    <div class="stat-number" id="total-regulations">1,247</div>
                    <div class="stat-label">Regulations Tracked</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="active-alerts">23</div>
                    <div class="stat-label">Active Alerts</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="compliance-score">94%</div>
                    <div class="stat-label">Compliance Score</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="jurisdictions">45</div>
                    <div class="stat-label">Jurisdictions</div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="container" aria-labelledby="features-title">
            <h2 id="features-title" class="section-title text-center">
                <i class="fas fa-star" aria-hidden="true"></i>
                Platform Features
            </h2>
            <div class="feature-grid animate-on-scroll">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line" aria-hidden="true"></i>
                    </div>
                    <h3 class="feature-title">Compliance Tracking</h3>
                    <p class="feature-description">
                        Monitor regulatory compliance across multiple jurisdictions with real-time updates and automated alerts.
                    </p>
                    <a href="/compliance-analytics" class="btn-enhanced btn-outline">
                        <i class="fas fa-arrow-right" aria-hidden="true"></i>
                        Explore Analytics
                    </a>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-bell" aria-hidden="true"></i>
                    </div>
                    <h3 class="feature-title">Smart Alerts</h3>
                    <p class="feature-description">
                        Stay informed with intelligent notifications about regulatory changes that impact your business.
                    </p>
                    <a href="/api/v1/alerts" class="btn-enhanced btn-outline">
                        <i class="fas fa-arrow-right" aria-hidden="true"></i>
                        Manage Alerts
                    </a>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-globe" aria-hidden="true"></i>
                    </div>
                    <h3 class="feature-title">Global Coverage</h3>
                    <p class="feature-description">
                        Access regulatory information from jurisdictions worldwide with comprehensive coverage and updates.
                    </p>
                    <a href="/api/v1/worldmap" class="btn-enhanced btn-outline">
                        <i class="fas fa-arrow-right" aria-hidden="true"></i>
                        View World Map
                    </a>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-robot" aria-hidden="true"></i>
                    </div>
                    <h3 class="feature-title">AI Analysis</h3>
                    <p class="feature-description">
                        Leverage artificial intelligence to analyze regulatory documents and assess compliance impact.
                    </p>
                    <a href="/document-summarizer" class="btn-enhanced btn-outline">
                        <i class="fas fa-arrow-right" aria-hidden="true"></i>
                        Try AI Tools
                    </a>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-calendar-check" aria-hidden="true"></i>
                    </div>
                    <h3 class="feature-title">Compliance Calendar</h3>
                    <p class="feature-description">
                        Track deadlines, schedule reviews, and manage compliance events with automated reminders.
                    </p>
                    <a href="/compliance-calendar" class="btn-enhanced btn-outline">
                        <i class="fas fa-arrow-right" aria-hidden="true"></i>
                        Open Calendar
                    </a>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt" aria-hidden="true"></i>
                    </div>
                    <h3 class="feature-title">Risk Assessment</h3>
                    <p class="feature-description">
                        Evaluate regulatory risks and their potential impact on your organization with advanced analytics.
                    </p>
                    <a href="/api/v1/impact" class="btn-enhanced btn-outline">
                        <i class="fas fa-arrow-right" aria-hidden="true"></i>
                        Assess Risk
                    </a>
                </div>
            </div>
        </section>

        <!-- Quick Actions Section -->
        <section class="container" aria-labelledby="actions-title">
            <div class="quick-actions animate-on-scroll">
                <h2 id="actions-title" class="quick-actions-title">Quick Actions</h2>
                <div class="actions-grid">
                    <a href="/api/v1/countries" class="action-button" data-tooltip="Browse countries">
                        <i class="fas fa-globe" aria-hidden="true"></i>
                        <span>View Countries</span>
                    </a>
                    <a href="/api/v1/regulators" class="action-button" data-tooltip="Browse regulators">
                        <i class="fas fa-balance-scale" aria-hidden="true"></i>
                        <span>View Regulators</span>
                    </a>
                    <a href="/api/v1/regulation-urls" class="action-button" data-tooltip="Browse regulations">
                        <i class="fas fa-gavel" aria-hidden="true"></i>
                        <span>View Regulations</span>
                    </a>
                    <a href="/api/v1/trends" class="action-button" data-tooltip="Analyze trends">
                        <i class="fas fa-chart-pie" aria-hidden="true"></i>
                        <span>View Trends</span>
                    </a>
                    <a href="/ui/manage" class="action-button" data-tooltip="Admin panel">
                        <i class="fas fa-user-shield" aria-hidden="true"></i>
                        <span>Admin Panel</span>
                    </a>
                    <a href="/api/v1/health" class="action-button" data-tooltip="System health">
                        <i class="fas fa-heartbeat" aria-hidden="true"></i>
                        <span>Health Check</span>
                    </a>
                </div>
            </div>
        </section>
    </main>

    <!-- Enhanced Footer -->
    <footer class="footer-enhanced" role="contentinfo">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h3 class="footer-title">
                        <i class="fas fa-shield-alt" aria-hidden="true"></i>
                        RegulationGuru
                    </h3>
                    <p class="footer-description">
                        Empowering organizations with comprehensive regulatory compliance management
                        and intelligent risk assessment tools.
                    </p>
                </div>
                <div class="col-md-3">
                    <h4 class="footer-section-title">Resources</h4>
                    <ul class="footer-links">
                        <li><a href="/api/v1/docs">API Documentation</a></li>
                        <li><a href="/ui/manage">Admin Panel</a></li>
                        <li><a href="/compliance-calendar">Compliance Calendar</a></li>
                        <li><a href="/document-summarizer">AI Tools</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h4 class="footer-section-title">Support</h4>
                    <ul class="footer-links">
                        <li><a href="/api/v1/health">System Status</a></li>
                        <li><a href="#" data-modal-target="contact-modal">Contact Us</a></li>
                        <li><a href="#">Help Center</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 RegulationGuru. All rights reserved.</p>
                <p>Built with ❤️ for regulatory compliance professionals worldwide.</p>
            </div>
        </div>
    </footer>

    <!-- Contact Modal -->
    <div id="contact-modal" class="modal-enhanced" role="dialog" aria-labelledby="contact-title" aria-hidden="true">
        <div class="modal-content-enhanced">
            <div class="modal-header">
                <h2 id="contact-title">Contact Us</h2>
                <button type="button" data-modal-close aria-label="Close modal">
                    <i class="fas fa-times" aria-hidden="true"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="contact-form">
                    <div class="form-group-enhanced">
                        <label for="contact-name" class="form-label-enhanced">Name</label>
                        <input type="text" id="contact-name" class="form-input-enhanced" required>
                    </div>
                    <div class="form-group-enhanced">
                        <label for="contact-email" class="form-label-enhanced">Email</label>
                        <input type="email" id="contact-email" class="form-input-enhanced" required>
                    </div>
                    <div class="form-group-enhanced">
                        <label for="contact-message" class="form-label-enhanced">Message</label>
                        <textarea id="contact-message" class="form-input-enhanced" rows="4" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" data-modal-close class="btn-enhanced btn-outline">Cancel</button>
                <button type="submit" form="contact-form" class="btn-enhanced btn-primary">Send Message</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/static/js/main.js"></script>
    <script src="/static/js/enhanced-ui.js"></script>

    <!-- Analytics and Performance -->
    <script>
        // Performance monitoring
        window.addEventListener('load', () => {
            if ('performance' in window) {
                const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
                console.log(`Page load time: ${loadTime}ms`);
            }
        });

        // Error tracking
        window.addEventListener('error', (e) => {
            console.error('JavaScript error:', e.error);
        });
    </script>
</body>
</html>
