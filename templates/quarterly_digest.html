
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        :root {
            --primary-color: #3a5a81;
            --secondary-color: #2c3e50;
            --accent-color: #e67e22;
            --bg-color: #f4f7f9;
            --text-color: #333;
            --light-gray: #ecf0f1;
            --border-color: #d1d8e0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--bg-color);
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background-color: var(--primary-color);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        h1, h2, h3, h4 {
            color: var(--secondary-color);
            margin-top: 0;
        }
        
        .period-info {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stat-card h3 {
            margin-top: 0;
            color: var(--primary-color);
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 10px;
        }
        
        .change-item {
            border-bottom: 1px solid var(--border-color);
            padding: 15px 0;
        }
        
        .change-item:last-child {
            border-bottom: none;
        }
        
        .change-title {
            font-weight: bold;
            color: var(--secondary-color);
            margin-bottom: 5px;
        }
        
        .change-meta {
            font-size: 0.9em;
            color: #777;
            display: flex;
            gap: 15px;
            margin-bottom: 10px;
        }
        
        .importance-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .importance-high {
            background-color: #f39c12;
            color: white;
        }
        
        .importance-critical {
            background-color: #e74c3c;
            color: white;
        }
        
        .importance-medium {
            background-color: #3498db;
            color: white;
        }
        
        .importance-low {
            background-color: #2ecc71;
            color: white;
        }
        
        .country-flag {
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .change-description {
            margin-bottom: 10px;
        }
        
        .change-link {
            font-size: 0.9em;
        }
        
        .change-link a {
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .change-link a:hover {
            text-decoration: underline;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid var(--border-color);
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            margin-bottom: -2px;
        }
        
        .tab.active {
            border-bottom: 2px solid var(--accent-color);
            font-weight: bold;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .chart {
            height: 300px;
            margin-bottom: 20px;
        }
        
        .footer {
            text-align: center;
            margin-top: 50px;
            padding: 20px;
            border-top: 1px solid var(--border-color);
            color: #777;
        }
        
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .header-content {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <h1>{{ title }}</h1>
                <div class="period-selector">
                    <span>{{ digest.period.label }}</span>
                </div>
            </div>
        </div>
    </header>
    
    <div class="container">
        <div class="period-info">
            <h2>Quarterly Regulatory Digest: {{ digest.period.label }}</h2>
            <p>This digest covers regulatory changes from {{ digest.period.start_date.strftime('%B %d, %Y') }} to {{ digest.period.end_date.strftime('%B %d, %Y') }}.</p>
            <p><strong>Total regulatory changes:</strong> {{ digest.summary.total_changes }}</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <h3>By Importance</h3>
                <div class="stat-content">
                    {% for level, count in digest.summary.by_importance.items() %}
                        <div class="stat-item">
                            <span>{{ level }}:</span>
                            <strong>{{ count }}</strong>
                        </div>
                    {% endfor %}
                </div>
            </div>
            
            <div class="stat-card">
                <h3>Top Categories</h3>
                <div class="stat-content">
                    {% for category, count in digest.summary.by_category.items() %}
                        {% if loop.index <= 5 %}
                            <div class="stat-item">
                                <span>{{ category }}:</span>
                                <strong>{{ count }}</strong>
                            </div>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>
            
            <div class="stat-card">
                <h3>By Region</h3>
                <div class="stat-content">
                    {% for country, count in digest.summary.by_country.items() %}
                        {% if loop.index <= 5 %}
                            <div class="stat-item">
                                <span>{{ country }}:</span>
                                <strong>{{ count }}</strong>
                            </div>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>
            
            <div class="stat-card">
                <h3>Top Regulators</h3>
                <div class="stat-content">
                    {% for regulator in digest.summary.top_regulators %}
                        <div class="stat-item">
                            <span>{{ regulator.name }}:</span>
                            <strong>{{ regulator.count }}</strong>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        
        <div class="tabs">
            <div class="tab active" onclick="switchTab(event, 'highlights')">Highlights</div>
            <div class="tab" onclick="switchTab(event, 'all-changes')">All Changes</div>
        </div>
        
        <div id="highlights" class="tab-content card active">
            <h2>Quarterly Highlights</h2>
            <p>The most significant regulatory changes from this quarter:</p>
            
            {% for item in digest.highlights %}
                <div class="change-item">
                    <div class="change-title">{{ item.title }}</div>
                    <div class="change-meta">
                        <span class="country-flag">{{ item.country_code }}</span>
                        <span class="date">{{ item.change_date.strftime('%b %d, %Y') }}</span>
                        <span class="importance-badge importance-{% if item.importance >= 9 %}critical{% elif item.importance >= 7 %}high{% elif item.importance >= 4 %}medium{% else %}low{% endif %}">
                            Importance: {{ item.importance }}/10
                        </span>
                    </div>
                    <div class="change-description">
                        {{ item.description }}
                    </div>
                    {% if item.url %}
                        <div class="change-link">
                            <a href="{{ item.url }}" target="_blank">Read more</a>
                        </div>
                    {% endif %}
                </div>
            {% endfor %}
        </div>
        
        <div id="all-changes" class="tab-content card">
            <h2>All Regulatory Changes</h2>
            <p>Complete list of regulatory changes from this quarter:</p>
            
            {% for item in digest.changes %}
                <div class="change-item">
                    <div class="change-title">{{ item.title }}</div>
                    <div class="change-meta">
                        <span class="country-flag">{{ item.country_code }}</span>
                        <span class="date">{{ item.change_date.strftime('%b %d, %Y') }}</span>
                        <span class="importance-badge importance-{% if item.importance >= 9 %}critical{% elif item.importance >= 7 %}high{% elif item.importance >= 4 %}medium{% else %}low{% endif %}">
                            Importance: {{ item.importance }}/10
                        </span>
                    </div>
                    <div class="change-description">
                        {{ item.description }}
                    </div>
                    {% if item.url %}
                        <div class="change-link">
                            <a href="{{ item.url }}" target="_blank">Read more</a>
                        </div>
                    {% endif %}
                </div>
            {% endfor %}
        </div>
    </div>
    
    <div class="footer">
        <p>This digest was generated on {{ digest.period.end_date.strftime('%B %d, %Y') }}.</p>
        <p>© RegulationGuru - Your global regulatory compliance platform</p>
    </div>
    
    <script>
        function switchTab(event, tabId) {
            // Hide all tab contents
            const tabContents = document.getElementsByClassName('tab-content');
            for (let i = 0; i < tabContents.length; i++) {
                tabContents[i].classList.remove('active');
            }
            
            // Remove active class from all tabs
            const tabs = document.getElementsByClassName('tab');
            for (let i = 0; i < tabs.length; i++) {
                tabs[i].classList.remove('active');
            }
            
            // Show the selected tab content and mark tab as active
            document.getElementById(tabId).classList.add('active');
            event.currentTarget.classList.add('active');
        }
    </script>
</body>
</html>
