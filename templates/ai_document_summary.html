
{% extends "base.html" %}

{% block title %}AI Regulatory Document Summarization{% endblock %}

{% block content %}
<div class="container-fluid p-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <h2><i class="bi bi-file-earmark-text-fill me-2"></i>AI Regulatory Document Summarization</h2>
            <p class="lead">
                Upload regulatory documents to generate AI-powered summaries, extracting key requirements, deadlines, and penalties.
            </p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-5">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="m-0"><i class="bi bi-upload me-2"></i>Upload Document</h5>
                </div>
                <div class="card-body">
                    <form id="uploadForm" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="documentFile" class="form-label">Select a PDF document</label>
                            <input class="form-control" type="file" id="documentFile" accept="application/pdf" required>
                            <div class="form-text">Only PDF files are supported.</div>
                        </div>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-magic me-2"></i>Generate AI Summary
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card shadow-sm mt-4">
                <div class="card-header bg-info text-white">
                    <h5 class="m-0"><i class="bi bi-clock-history me-2"></i>Recent Summaries</h5>
                </div>
                <div class="card-body p-0">
                    <div id="summariesList" class="list-group list-group-flush">
                        <div class="list-group-item text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading summaries...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-7">
            <div class="card shadow-sm" id="summaryCard">
                <div class="card-header bg-success text-white">
                    <h5 class="m-0"><i class="bi bi-file-earmark-text me-2"></i><span id="summaryTitle">Document Summary</span></h5>
                </div>
                <div class="card-body">
                    <div id="loadingSpinner" class="text-center py-5 d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-3">Generating AI summary... This may take a minute.</p>
                    </div>
                    
                    <div id="summaryContent" class="d-none">
                        <h5 class="border-bottom pb-2">Executive Summary</h5>
                        <div id="summaryText" class="mb-4"></div>
                        
                        <h5 class="border-bottom pb-2">Key Requirements</h5>
                        <ul id="requirementsList" class="mb-4"></ul>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="border-bottom pb-2">Important Deadlines</h5>
                                <ul id="deadlinesList" class="mb-4"></ul>
                            </div>
                            <div class="col-md-6">
                                <h5 class="border-bottom pb-2">Penalties</h5>
                                <ul id="penaltiesList" class="mb-4"></ul>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <button class="btn btn-outline-primary" id="downloadBtn">
                                <i class="bi bi-download me-2"></i>Download Summary
                            </button>
                            <button class="btn btn-outline-secondary" id="shareBtn">
                                <i class="bi bi-share me-2"></i>Share
                            </button>
                        </div>
                    </div>
                    
                    <div id="placeholderContent">
                        <div class="text-center py-5">
                            <i class="bi bi-file-earmark-text display-1 text-muted"></i>
                            <p class="mt-3">Upload a regulatory document to generate an AI-powered summary</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load recent summaries
    loadRecentSummaries();
    
    // Handle form submission
    const uploadForm = document.getElementById('uploadForm');
    uploadForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const fileInput = document.getElementById('documentFile');
        const file = fileInput.files[0];
        
        if (!file) {
            alert('Please select a PDF file');
            return;
        }
        
        // Show loading spinner
        document.getElementById('loadingSpinner').classList.remove('d-none');
        document.getElementById('placeholderContent').classList.add('d-none');
        document.getElementById('summaryContent').classList.add('d-none');
        
        // Prepare form data
        const formData = new FormData();
        formData.append('file', file);
        
        try {
            // Call the API
            const response = await fetch('/api/v1/document_analysis/summarize', {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || 'Error generating summary');
            }
            
            const summaryData = await response.json();
            displaySummary(summaryData);
            
            // Refresh the summaries list
            loadRecentSummaries();
        } catch (error) {
            alert('Error: ' + error.message);
        } finally {
            // Hide loading spinner
            document.getElementById('loadingSpinner').classList.add('d-none');
        }
    });
    
    // Download button handler
    document.getElementById('downloadBtn').addEventListener('click', function() {
        // Get the current summary data
        const summaryTitle = document.getElementById('summaryTitle').textContent;
        const summaryText = document.getElementById('summaryText').textContent;
        const requirementsList = Array.from(document.getElementById('requirementsList').children)
            .map(li => li.textContent);
        const deadlinesList = Array.from(document.getElementById('deadlinesList').children)
            .map(li => li.textContent);
        const penaltiesList = Array.from(document.getElementById('penaltiesList').children)
            .map(li => li.textContent);
        
        // Create summary text
        let summaryContent = `# ${summaryTitle}\n\n`;
        summaryContent += `## Executive Summary\n\n${summaryText}\n\n`;
        summaryContent += `## Key Requirements\n\n`;
        requirementsList.forEach((req, index) => {
            summaryContent += `${index + 1}. ${req}\n`;
        });
        summaryContent += `\n## Important Deadlines\n\n`;
        deadlinesList.forEach((deadline, index) => {
            summaryContent += `${index + 1}. ${deadline}\n`;
        });
        summaryContent += `\n## Penalties\n\n`;
        penaltiesList.forEach((penalty, index) => {
            summaryContent += `${index + 1}. ${penalty}\n`;
        });
        
        // Create a download link
        const blob = new Blob([summaryContent], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${summaryTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_summary.md`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    });
});

async function loadRecentSummaries() {
    try {
        const response = await fetch('/api/v1/document_analysis/summaries');
        if (!response.ok) {
            throw new Error('Failed to load summaries');
        }
        
        const summaries = await response.json();
        const summariesList = document.getElementById('summariesList');
        
        if (summaries.length === 0) {
            summariesList.innerHTML = `
                <div class="list-group-item text-center py-4">
                    <i class="bi bi-info-circle text-muted"></i>
                    <p class="mt-2">No summaries found</p>
                </div>
            `;
            return;
        }
        
        summariesList.innerHTML = '';
        
        summaries.forEach(summary => {
            const date = new Date(summary.created_at).toLocaleDateString();
            const item = document.createElement('a');
            item.href = '#';
            item.className = 'list-group-item list-group-item-action';
            item.dataset.id = summary.id;
            item.innerHTML = `
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1 text-truncate" style="max-width: 200px;">${summary.filename}</h6>
                    <small>${date}</small>
                </div>
                <p class="mb-1 text-truncate">${summary.summary.substring(0, 60)}...</p>
            `;
            
            item.addEventListener('click', async function(e) {
                e.preventDefault();
                const summaryId = this.dataset.id;
                await loadSummaryDetails(summaryId);
            });
            
            summariesList.appendChild(item);
        });
    } catch (error) {
        console.error('Error loading summaries:', error);
        document.getElementById('summariesList').innerHTML = `
            <div class="list-group-item text-center py-4">
                <i class="bi bi-exclamation-triangle text-danger"></i>
                <p class="mt-2">Error loading summaries</p>
            </div>
        `;
    }
}

async function loadSummaryDetails(summaryId) {
    try {
        // Show loading spinner
        document.getElementById('loadingSpinner').classList.remove('d-none');
        document.getElementById('placeholderContent').classList.add('d-none');
        document.getElementById('summaryContent').classList.add('d-none');
        
        const response = await fetch(`/api/v1/document_analysis/summary/${summaryId}`);
        if (!response.ok) {
            throw new Error('Failed to load summary details');
        }
        
        const summaryData = await response.json();
        displaySummary(summaryData);
        
    } catch (error) {
        console.error('Error loading summary details:', error);
        alert('Error loading summary details');
    } finally {
        // Hide loading spinner
        document.getElementById('loadingSpinner').classList.add('d-none');
    }
}

function displaySummary(summaryData) {
    // Set title
    document.getElementById('summaryTitle').textContent = summaryData.filename || 'Document Summary';
    
    // Set summary text
    document.getElementById('summaryText').textContent = summaryData.summary;
    
    // Set requirements
    const requirementsList = document.getElementById('requirementsList');
    requirementsList.innerHTML = '';
    if (summaryData.key_requirements && summaryData.key_requirements.length > 0) {
        summaryData.key_requirements.forEach(req => {
            const li = document.createElement('li');
            li.textContent = req;
            requirementsList.appendChild(li);
        });
    } else {
        const li = document.createElement('li');
        li.textContent = 'No specific requirements identified';
        requirementsList.appendChild(li);
    }
    
    // Set deadlines
    const deadlinesList = document.getElementById('deadlinesList');
    deadlinesList.innerHTML = '';
    if (summaryData.deadlines && summaryData.deadlines.length > 0) {
        summaryData.deadlines.forEach(deadline => {
            const li = document.createElement('li');
            li.textContent = deadline;
            deadlinesList.appendChild(li);
        });
    } else {
        const li = document.createElement('li');
        li.textContent = 'No specific deadlines identified';
        deadlinesList.appendChild(li);
    }
    
    // Set penalties
    const penaltiesList = document.getElementById('penaltiesList');
    penaltiesList.innerHTML = '';
    if (summaryData.penalties && summaryData.penalties.length > 0) {
        summaryData.penalties.forEach(penalty => {
            const li = document.createElement('li');
            li.textContent = penalty;
            penaltiesList.appendChild(li);
        });
    } else {
        const li = document.createElement('li');
        li.textContent = 'No specific penalties identified';
        penaltiesList.appendChild(li);
    }
    
    // Show summary content
    document.getElementById('summaryContent').classList.remove('d-none');
    document.getElementById('placeholderContent').classList.add('d-none');
}
</script>
{% endblock %}
