<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Regulatory Map - RegulationGuru</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- D3.js for visualization -->
    <script src="https://d3js.org/d3.v7.min.js"></script>
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: rgba(44, 62, 80, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .regulatory-map-container {
            height: 85vh;
            border: none;
            border-radius: 15px;
            position: relative;
            overflow: hidden;
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            box-shadow:
                20px 20px 60px #bebebe,
                -20px -20px 60px #ffffff;
        }
        
        .map-controls {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.98);
            padding: 20px;
            border-radius: 15px;
            box-shadow:
                10px 10px 30px rgba(0, 0, 0, 0.1),
                -10px -10px 30px rgba(255, 255, 255, 0.8);
            max-width: 320px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .map-controls:hover {
            transform: translateY(-2px);
            box-shadow:
                15px 15px 40px rgba(0, 0, 0, 0.15),
                -15px -15px 40px rgba(255, 255, 255, 0.9);
        }

        .controls-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid var(--light-color);
        }

        .controls-toggle {
            background: none;
            border: none;
            font-size: 1.2rem;
            color: var(--secondary-color);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .controls-toggle:hover {
            transform: rotate(180deg);
        }

        .controls-content {
            transition: all 0.3s ease;
        }

        .controls-content.collapsed {
            display: none;
        }
        
        .entity-node {
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.2));
        }

        .entity-node:hover {
            stroke-width: 4px;
            filter: drop-shadow(4px 4px 8px rgba(0, 0, 0, 0.3)) brightness(1.1);
            transform: scale(1.1);
        }

        .entity-node.selected {
            stroke-width: 5px;
            stroke: #ff6b6b;
            filter: drop-shadow(0 0 15px rgba(255, 107, 107, 0.6));
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .entity-node.highlighted {
            stroke: #4ecdc4;
            stroke-width: 3px;
            filter: drop-shadow(0 0 10px rgba(78, 205, 196, 0.8));
        }
        
        .relationship-link {
            stroke: #999;
            stroke-opacity: 0.6;
            stroke-width: 2px;
            fill: none;
            marker-end: url(#arrowhead);
            transition: all 0.3s ease;
        }

        .relationship-link:hover {
            stroke-opacity: 1;
            stroke-width: 4px;
            stroke: #4ecdc4;
            filter: drop-shadow(0 0 5px rgba(78, 205, 196, 0.6));
        }

        .relationship-link.highlighted {
            stroke: #ff6b6b;
            stroke-opacity: 0.9;
            stroke-width: 3px;
            animation: flow 2s linear infinite;
        }

        @keyframes flow {
            0% { stroke-dasharray: 5, 5; stroke-dashoffset: 0; }
            100% { stroke-dasharray: 5, 5; stroke-dashoffset: 10; }
        }
        
        .entity-label {
            font-size: 12px;
            font-weight: 500;
            text-anchor: middle;
            pointer-events: none;
        }
        
        .mini-map {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.98);
            padding: 15px;
            border-radius: 15px;
            box-shadow:
                10px 10px 30px rgba(0, 0, 0, 0.1),
                -10px -10px 30px rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 999;
        }

        .mini-map svg {
            border: 1px solid #dee2e6;
            border-radius: 5px;
            background: #f8f9fa;
        }

        .legend {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.98);
            padding: 20px;
            border-radius: 15px;
            box-shadow:
                10px 10px 30px rgba(0, 0, 0, 0.1),
                -10px -10px 30px rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 250px;
            transition: all 0.3s ease;
        }

        .legend:hover {
            transform: translateY(-2px);
            box-shadow:
                15px 15px 40px rgba(0, 0, 0, 0.15),
                -15px -15px 40px rgba(255, 255, 255, 0.9);
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .entity-details {
            position: absolute;
            top: 50%;
            right: 20px;
            width: 380px;
            background: rgba(255, 255, 255, 0.98);
            border: none;
            border-radius: 20px;
            padding: 25px;
            box-shadow:
                20px 20px 60px rgba(0, 0, 0, 0.1),
                -20px -20px 60px rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: none;
            transform: translateY(-50%);
            z-index: 1001;
            animation: slideInRight 0.3s ease-out;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%) translateY(-50%);
            }
            to {
                opacity: 1;
                transform: translateX(0) translateY(-50%);
            }
        }

        .entity-details.show {
            display: block;
        }
        
        .compliance-badge {
            font-size: 0.8em;
            padding: 4px 8px;
            border-radius: 12px;
        }
        
        .compliance-compliant {
            background-color: #d4edda;
            color: #155724;
        }
        
        .compliance-at-risk {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .compliance-non-compliant {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .compliance-unknown {
            background-color: #e2e3e5;
            color: #383d41;
        }
        
        .filter-section {
            margin-bottom: 15px;
        }
        
        .filter-section label {
            font-weight: 600;
            margin-bottom: 5px;
            display: block;
        }
        
        .btn-sm {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }

        /* Export Modal */
        .export-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            backdrop-filter: blur(5px);
        }

        .export-modal-content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            min-width: 300px;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Tooltip */
        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1500;
            max-width: 250px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        /* Loading Spinner */
        .loading-spinner {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1000;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid var(--secondary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Enhanced Buttons */
        .btn-group .btn {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-group .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-group .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-group .btn:hover::before {
            left: 100%;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .map-controls {
                position: relative;
                top: 0;
                left: 0;
                margin-bottom: 20px;
                max-width: 100%;
            }

            .legend {
                position: relative;
                bottom: auto;
                right: auto;
                margin-top: 20px;
                max-width: 100%;
            }

            .mini-map {
                display: none;
            }

            .entity-details {
                position: fixed;
                top: 0;
                right: 0;
                width: 100%;
                height: 100%;
                transform: none;
                border-radius: 0;
                z-index: 2000;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-shield-alt me-2"></i>
                RegulationGuru
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Dashboard</a>
                <a class="nav-link active" href="/regulatory-map">Regulatory Map</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>
                        <i class="fas fa-project-diagram me-2"></i>
                        Enhanced Regulatory Map
                    </h2>
                    <div class="btn-group">
                        <button class="btn btn-outline-primary btn-sm" id="resetZoom">
                            <i class="fas fa-search-minus me-1"></i>
                            Reset Zoom
                        </button>
                        <button class="btn btn-outline-primary btn-sm" id="saveView">
                            <i class="fas fa-save me-1"></i>
                            Save View
                        </button>
                        <button class="btn btn-outline-primary btn-sm" id="exportMap">
                            <i class="fas fa-download me-1"></i>
                            Export
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="regulatory-map-container" id="mapContainer">
                    <!-- Map Controls -->
                    <div class="map-controls">
                        <div class="controls-header">
                            <h6 class="mb-0">
                                <i class="fas fa-sliders-h me-2"></i>
                                Controls
                            </h6>
                            <button class="controls-toggle" id="toggleControls">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                        </div>

                        <div class="controls-content" id="controlsContent">
                        
                        <div class="filter-section">
                            <label for="entityTypeFilter">Entity Types:</label>
                            <select class="form-select form-select-sm" id="entityTypeFilter" multiple>
                                <option value="regulation">Regulations</option>
                                <option value="requirement">Requirements</option>
                                <option value="control">Controls</option>
                                <option value="business_process">Business Processes</option>
                                <option value="system">Systems</option>
                                <option value="policy">Policies</option>
                                <option value="procedure">Procedures</option>
                            </select>
                        </div>
                        
                        <div class="filter-section">
                            <label for="jurisdictionFilter">Jurisdiction:</label>
                            <select class="form-select form-select-sm" id="jurisdictionFilter">
                                <option value="">All Jurisdictions</option>
                                <option value="US">United States</option>
                                <option value="EU">European Union</option>
                                <option value="UK">United Kingdom</option>
                                <option value="CA">Canada</option>
                                <option value="AU">Australia</option>
                            </select>
                        </div>
                        
                        <div class="filter-section">
                            <label for="complianceFilter">Compliance Status:</label>
                            <select class="form-select form-select-sm" id="complianceFilter">
                                <option value="">All Statuses</option>
                                <option value="compliant">Compliant</option>
                                <option value="at_risk">At Risk</option>
                                <option value="non_compliant">Non-Compliant</option>
                                <option value="unknown">Unknown</option>
                            </select>
                        </div>
                        
                        <div class="filter-section">
                            <label for="searchFilter">Search:</label>
                            <input type="text" class="form-control form-control-sm" id="searchFilter" 
                                   placeholder="Search entities...">
                        </div>
                        
                        <div class="filter-section">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showRelationships" checked>
                                <label class="form-check-label" for="showRelationships">
                                    Show Relationships
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showLabels" checked>
                                <label class="form-check-label" for="showLabels">
                                    Show Labels
                                </label>
                            </div>
                        </div>
                        
                        <div class="filter-section">
                            <label>Layout Algorithm:</label>
                            <select class="form-select form-select-sm" id="layoutAlgorithm">
                                <option value="force">Force-Directed</option>
                                <option value="hierarchical">Hierarchical</option>
                                <option value="circular">Circular</option>
                                <option value="grid">Grid</option>
                            </select>
                        </div>

                        <div class="filter-section">
                            <label>Animation Speed:</label>
                            <input type="range" class="form-range" id="animationSpeed"
                                   min="0.1" max="2" step="0.1" value="1">
                            <div class="d-flex justify-content-between">
                                <small>Slow</small>
                                <small>Fast</small>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button class="btn btn-primary btn-sm" id="applyFilters">
                                <i class="fas fa-sync me-1"></i>
                                Apply Filters
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" id="resetFilters">
                                <i class="fas fa-undo me-1"></i>
                                Reset All
                            </button>
                        </div>

                        </div> <!-- End controls-content -->
                    </div>
                    
                    <!-- Mini Map -->
                    <div class="mini-map" id="miniMap">
                        <h6 class="mb-2">
                            <i class="fas fa-map me-2"></i>
                            Overview
                        </h6>
                        <svg id="miniMapSvg" width="150" height="100"></svg>
                    </div>

                    <!-- Enhanced Legend -->
                    <div class="legend">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">
                                <i class="fas fa-palette me-2"></i>
                                Legend
                            </h6>
                            <button class="btn btn-sm btn-outline-secondary" id="toggleLegend">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>

                        <div class="legend-content" id="legendContent">
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #007bff;"></div>
                            <span>Regulation</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #28a745;"></div>
                            <span>Requirement</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #ffc107;"></div>
                            <span>Control</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #17a2b8;"></div>
                            <span>Business Process</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #6f42c1;"></div>
                            <span>System</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #fd7e14;"></div>
                            <span>Policy</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #e83e8c;"></div>
                            <span>Procedure</span>
                        </div>

                        <hr class="my-3">

                        <h6 class="mb-2">
                            <i class="fas fa-heartbeat me-2"></i>
                            Compliance Status
                        </h6>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #28a745; border: 3px solid #28a745;"></div>
                            <span>Compliant</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #ffc107; border: 3px solid #ffc107;"></div>
                            <span>At Risk</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #dc3545; border: 3px solid #dc3545;"></div>
                            <span>Non-Compliant</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #6c757d; border: 3px solid #6c757d;"></div>
                            <span>Unknown</span>
                        </div>

                        </div> <!-- End legend-content -->
                    </div>
                    
                    <!-- Entity Details Panel -->
                    <div class="entity-details" id="entityDetails">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <h6 class="mb-0">Entity Details</h6>
                            <button class="btn-close" id="closeDetails"></button>
                        </div>
                        <div id="entityDetailsContent">
                            <!-- Content will be populated by JavaScript -->
                        </div>
                    </div>
                    
                    <!-- SVG Container for the map -->
                    <svg id="regulatoryMapSvg" width="100%" height="100%">
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                                    refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#999" />
                            </marker>
                        </defs>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="/static/js/regulatory-map.js"></script>
</body>
</html>
