
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}RegulationGuru Admin{% endblock %}</title>
    <!-- Material Design Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- <PERSON><PERSON> Font -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Starlette Admin CSS -->
    {{ admin_templates.css() | safe }}
    <style>
        :root {
            --bs-body-font-family: 'Roboto', sans-serif;
        }
        body {
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        /* Dark mode styles */
        body.dark-mode {
            background-color: #121212;
            color: #e0e0e0;
        }
        body.dark-mode .card,
        body.dark-mode .list-group-item,
        body.dark-mode .modal-content,
        body.dark-mode .offcanvas {
            background-color: #1e1e1e;
            color: #e0e0e0;
            border-color: #333;
        }
        body.dark-mode .navbar,
        body.dark-mode .footer {
            background-color: #1e1e1e !important;
            color: #e0e0e0;
            border-color: #333;
        }
        body.dark-mode .form-control,
        body.dark-mode .form-select {
            background-color: #333;
            color: #e0e0e0;
            border-color: #444;
        }
        body.dark-mode .input-group-text {
            background-color: #444;
            color: #e0e0e0;
            border-color: #555;
        }
        body.dark-mode .dropdown-menu {
            background-color: #1e1e1e;
            color: #e0e0e0;
            border-color: #333;
        }
        body.dark-mode .dropdown-item,
        body.dark-mode .nav-link {
            color: #e0e0e0;
        }
        body.dark-mode .dropdown-item:hover,
        body.dark-mode .nav-link:hover {
            background-color: #333;
        }
        body.dark-mode .table {
            color: #e0e0e0;
            border-color: #333;
        }
        body.dark-mode .table thead th {
            border-color: #333;
            background-color: #2d2d2d;
        }
        body.dark-mode .table-striped > tbody > tr:nth-of-type(odd) > * {
            background-color: rgba(255, 255, 255, 0.05);
            color: #e0e0e0;
        }
        body.dark-mode .btn-outline-secondary {
            color: #e0e0e0;
            border-color: #666;
        }
        body.dark-mode .pagination .page-link {
            background-color: #1e1e1e;
            color: #e0e0e0;
            border-color: #333;
        }
        body.dark-mode .pagination .page-item.active .page-link {
            background-color: #0d6efd;
            color: white;
        }
        /* Custom styles */
        .theme-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        .theme-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #2196F3;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
    {% block extra_head %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <div class="container-fluid">
            <a class="navbar-brand" href="/ui/manage">
                <i class="material-icons">admin_panel_settings</i> {{ admin.title }}
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarSupportedContent">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link" href="/"><i class="material-icons">dashboard</i> Dashboard</a>
                    </li>
                    {% for view in admin.views %}
                    <li class="nav-item">
                        <a class="nav-link {% if view.identity == request.url.path %}active{% endif %}" href="{{ view.identity }}">
                            <i class="material-icons">{{ view.icon|default('table_view') }}</i> {{ view.label }}
                        </a>
                    </li>
                    {% endfor %}
                </ul>
                
                <div class="d-flex align-items-center">
                    <span id="mode-text" class="me-2">Light Mode</span>
                    <label class="theme-switch">
                        <input type="checkbox" id="theme-toggle">
                        <span class="slider"></span>
                    </label>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% block content %}{% endblock %}
    </div>

    <footer class="footer bg-light p-3 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; 2025 RegulationGuru Admin</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="/" class="btn btn-sm btn-outline-secondary">Back to Main Dashboard</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Starlette Admin JS -->
    {{ admin_templates.js() | safe }}
    
    <script>
        // Theme toggling
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggle = document.getElementById('theme-toggle');
            const modeText = document.getElementById('mode-text');
            
            // Always default to dark mode
            document.body.classList.add('dark-mode');
            themeToggle.checked = true;
            modeText.textContent = 'Dark Mode';
            localStorage.setItem('theme', 'dark');
            
            // Toggle theme when the switch is clicked
            themeToggle.addEventListener('change', function() {
                if (this.checked) {
                    document.body.classList.add('dark-mode');
                    localStorage.setItem('theme', 'dark');
                    modeText.textContent = 'Dark Mode';
                } else {
                    document.body.classList.remove('dark-mode');
                    localStorage.setItem('theme', 'light');
                    modeText.textContent = 'Light Mode';
                }
            });
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
