
<!DOCTYPE html>
<html>
<head>
    <title>Regulators</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .regulator-list {
            margin-top: 20px;
        }
        .regulator-item {
            margin-bottom: 15px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .regulator-name {
            font-weight: bold;
            font-size: 18px;
        }
        .regulator-meta {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }
        .regulator-description {
            margin-top: 8px;
        }
        .no-data {
            color: #666;
            font-style: italic;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Regulators</h1>
    <p>Below is a list of all regulators in the system:</p>

    <div class="regulator-list">
        {% if regulators %}
            {% for regulator in regulators %}
                <div class="regulator-item">
                    <div class="regulator-name">{{ regulator.name }}</div>
                    <div class="regulator-description">{{ regulator.description if regulator.description else "No description available" }}</div>
                    <div class="regulator-meta">
                        Type: {{ regulator.type if regulator.type else "Unspecified" }} | 
                        Country: {{ regulator.country.name if regulator.country else "Global" }} | 
                        ID: {{ regulator.id }}
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <p class="no-data">No regulators found in the database.</p>
        {% endif %}
    </div>

    <div style="margin-top: 20px;">
        <a href="/">Back to Dashboard</a> | 
        <a href="/api/v1/regulation-urls">View Regulation URLs</a>
    </div>
</body>
</html>
