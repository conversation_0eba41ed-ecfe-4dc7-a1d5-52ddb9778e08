
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Regulatory Compliance Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #3182ce;
            --primary-dark: #2c5282;
            --success: #48bb78;
            --warning: #ed8936;
            --danger: #e53e3e;
            --bg-color: #f7fafc;
            --card-bg: #ffffff;
            --text-color: #2d3748;
            --text-secondary: #4a5568;
            --border-color: #e2e8f0;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            --transition: all 0.3s ease;
        }
        
        [data-theme="dark"] {
            --primary: #63b3ed;
            --primary-dark: #90cdf4;
            --success: #68d391;
            --warning: #f6ad55;
            --danger: #fc8181;
            --bg-color: #1a202c;
            --card-bg: #2d3748;
            --text-color: #e2e8f0;
            --text-secondary: #cbd5e0;
            --border-color: #4a5568;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
        }
        
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--bg-color);
            color: var(--text-color);
            line-height: 1.6;
            transition: var(--transition);
        }
        
        header {
            background-color: var(--card-bg);
            padding: 15px 20px;
            box-shadow: var(--shadow);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        header h1 {
            margin: 0;
            font-size: 1.8rem;
            color: var(--primary);
        }
        
        nav ul {
            display: flex;
            list-style: none;
            padding: 0;
            margin-top: 15px;
            gap: 20px;
        }
        
        nav ul li a {
            text-decoration: none;
            color: var(--text-secondary);
            font-weight: 500;
            transition: var(--transition);
            padding: 5px 0;
            border-bottom: 2px solid transparent;
        }
        
        nav ul li a:hover {
            color: var(--primary);
            border-bottom: 2px solid var(--primary);
        }
        
        .dashboard-container {
            max-width: 1300px;
            margin: 30px auto;
            padding: 0 20px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
        }
        
        .widget {
            background-color: var(--card-bg);
            border-radius: 10px;
            box-shadow: var(--shadow);
            padding: 20px;
            transition: var(--transition);
            display: flex;
            flex-direction: column;
        }
        
        .widget:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }
        
        .widget h2 {
            font-size: 1.4rem;
            margin-top: 0;
            margin-bottom: 20px;
            color: var(--primary);
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 10px;
        }
        
        .compliance-score {
            grid-column: span 1;
            text-align: center;
        }
        
        .compliance-by-region {
            grid-column: span 2;
        }
        
        .critical-gaps, 
        .recommendations, 
        .recent-regulatory-changes {
            grid-column: span 1;
        }
        
        .score-circle {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: conic-gradient(var(--primary) 0%, var(--primary) 0%, #e2e8f0 0%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            position: relative;
        }
        
        .score-circle::before {
            content: "";
            position: absolute;
            width: 130px;
            height: 130px;
            border-radius: 50%;
            background-color: var(--card-bg);
        }
        
        .score-circle span {
            position: relative;
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary);
        }
        
        ul {
            padding-left: 20px;
            margin: 0;
        }
        
        li {
            margin-bottom: 12px;
        }
        
        .severity-high {
            background-color: var(--danger);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .severity-medium {
            background-color: var(--warning);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .severity-low {
            background-color: var(--success);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .dashboard-container {
                grid-template-columns: 1fr;
            }
            
            .compliance-by-region {
                grid-column: span 1;
            }
        }
        
        /* Theme toggle button */
        .theme-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            background: none;
            border: none;
            color: var(--text-color);
            cursor: pointer;
            font-size: 1.2rem;
            padding: 5px;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <h1>Regulatory Compliance Dashboard</h1>
            <button class="theme-toggle" id="themeToggle" aria-label="Toggle dark/light mode">
                <i class="fas fa-moon toggle-icon"></i>
            </button>
        </div>
        <nav>
            <ul>
                <li><a href="/dashboard"><i class="fas fa-home"></i> Main Dashboard</a></li>
                <li><a href="/analytics"><i class="fas fa-chart-bar"></i> Analytics</a></li>
                <li><a href="/settings"><i class="fas fa-cog"></i> Settings</a></li>
            </ul>
        </nav>
    </header>
    
    <div class="dashboard-container">
        <div class="widget compliance-score">
            <h2><i class="fas fa-tachometer-alt"></i> Overall Compliance Score</h2>
            <div class="score-circle">
                <span id="complianceScore">0%</span>
            </div>
        </div>
        
        <div class="widget compliance-by-region">
            <h2><i class="fas fa-globe"></i> Compliance by Region</h2>
            <canvas id="regionChart"></canvas>
        </div>
        
        <div class="widget critical-gaps">
            <h2><i class="fas fa-exclamation-triangle"></i> Critical Compliance Gaps</h2>
            <div id="gapsContainer">Loading gaps...</div>
        </div>
        
        <div class="widget recommendations">
            <h2><i class="fas fa-lightbulb"></i> Recommendations</h2>
            <div id="recommendationsContainer">Loading recommendations...</div>
        </div>
        
        <div class="widget recent-regulatory-changes">
            <h2><i class="fas fa-history"></i> Recent Regulatory Changes</h2>
            <div id="changesContainer">Loading changes...</div>
        </div>
    </div>
    
    <script>
        // Theme switcher
        const themeToggleBtn = document.getElementById('themeToggle');
        const icon = themeToggleBtn.querySelector('.toggle-icon');
        
        // Check for saved theme preference or prefer-color-scheme
        const savedTheme = localStorage.getItem('theme');
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        
        if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
            document.documentElement.setAttribute('data-theme', 'dark');
            icon.classList.remove('fa-moon');
            icon.classList.add('fa-sun');
        }
        
        themeToggleBtn.addEventListener('click', () => {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            
            if (newTheme === 'dark') {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
            } else {
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
            }
        });
        
        // Fetch compliance data from API
        async function fetchComplianceData() {
            try {
                const response = await fetch('/api/v1/compliance/summary');
                const data = await response.json();
                
                // Update compliance score and animate the circle
                const score = Math.round(data.overall_score);
                document.getElementById('complianceScore').textContent = `${score}%`;
                
                // Animate the circle
                const scoreCircle = document.querySelector('.score-circle');
                scoreCircle.style.background = `conic-gradient(var(--primary) 0% ${score}%, #e2e8f0 ${score}% 100%)`;
                
                // Render regional chart
                renderRegionalChart(data.regional_scores);
                
                // Populate gaps
                populateGaps(data.critical_gaps);
                
                // Populate recommendations
                populateRecommendations(data.recommendations);
                
                // Populate regulatory changes
                populateChanges(data.recent_changes);
            } catch (error) {
                console.error('Error fetching compliance data:', error);
            }
        }
        
        function renderRegionalChart(regionalData) {
            const ctx = document.getElementById('regionChart').getContext('2d');
            
            // Configure Chart.js to use the theme colors
            Chart.defaults.color = getComputedStyle(document.documentElement).getPropertyValue('--text-color');
            
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: regionalData.map(r => r.region),
                    datasets: [{
                        label: 'Compliance Score (%)',
                        data: regionalData.map(r => r.score),
                        backgroundColor: [
                            'rgba(49, 130, 206, 0.7)',
                            'rgba(72, 187, 120, 0.7)',
                            'rgba(213, 63, 140, 0.7)',
                            'rgba(237, 137, 54, 0.7)',
                            'rgba(102, 126, 234, 0.7)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            grid: {
                                color: getComputedStyle(document.documentElement).getPropertyValue('--border-color')
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }
        
        function populateGaps(gaps) {
            const container = document.getElementById('gapsContainer');
            if (!gaps || gaps.length === 0) {
                container.innerHTML = '<p>No critical gaps detected.</p>';
                return;
            }
            
            const gapsList = document.createElement('ul');
            gaps.forEach(gap => {
                const item = document.createElement('li');
                item.innerHTML = `<strong>${gap.country}:</strong> ${gap.description} <span class="severity-${gap.severity.toLowerCase()}">${gap.severity}</span>`;
                gapsList.appendChild(item);
            });
            
            container.innerHTML = '';
            container.appendChild(gapsList);
        }
        
        function populateRecommendations(recommendations) {
            const container = document.getElementById('recommendationsContainer');
            if (!recommendations || recommendations.length === 0) {
                container.innerHTML = '<p>No recommendations available.</p>';
                return;
            }
            
            const recList = document.createElement('ul');
            recommendations.forEach(rec => {
                const item = document.createElement('li');
                item.innerHTML = `<strong>${rec.title}</strong>: ${rec.description}`;
                recList.appendChild(item);
            });
            
            container.innerHTML = '';
            container.appendChild(recList);
        }
        
        function populateChanges(changes) {
            const container = document.getElementById('changesContainer');
            if (!changes || changes.length === 0) {
                container.innerHTML = '<p>No recent regulatory changes.</p>';
                return;
            }
            
            const changesList = document.createElement('ul');
            changes.forEach(change => {
                const item = document.createElement('li');
                const date = new Date(change.date).toLocaleDateString();
                item.innerHTML = `<strong>${date} - ${change.country}</strong>: ${change.description}`;
                changesList.appendChild(item);
            });
            
            container.innerHTML = '';
            container.appendChild(changesList);
        }
        
        // Initialize the dashboard
        document.addEventListener('DOMContentLoaded', fetchComplianceData);
    </script>
</body>
</html>
