<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Regulatory Data Collection</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .source-card {
            margin-bottom: 20px;
            transition: all 0.3s;
        }
        .source-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .status-up-to-date {
            color: #28a745;
        }
        .status-due {
            color: #ffc107;
        }
        .status-error {
            color: #dc3545;
        }
        .status-never-run {
            color: #6c757d;
        }
        .action-buttons {
            display: flex;
            gap: 10px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">Regulatory Data Collection</h1>

        <div class="d-flex justify-content-between mb-4">
            <h1>Data Collection</h1>
            <div>
              <a href="/document-import" class="btn btn-primary me-2">
                <i class="bi bi-file-earmark-plus"></i> Import Documents
              </a>
              <div class="dropdown d-inline-block me-2">
                <button class="btn btn-outline-success dropdown-toggle" type="button" id="enrichDataBtn" data-bs-toggle="dropdown" aria-expanded="false">
                  <i class="bi bi-database-up"></i> Enrich Data
                </button>
                <ul class="dropdown-menu" aria-labelledby="enrichDataBtn">
                  <li><a class="dropdown-item enrich-data-btn" href="#" data-type="country">Enrich Country Data</a></li>
                  <li><a class="dropdown-item enrich-data-btn" href="#" data-type="regulator">Enrich Regulator Data</a></li>
                  <li><a class="dropdown-item enrich-data-btn" href="#" data-type="regulation">Enrich Regulation Data</a></li>
                  <li><a class="dropdown-item enrich-data-btn" href="#" data-type="regulatory_source">Enrich Source Data</a></li>
                </ul>
              </div>
              <button class="btn btn-outline-primary" id="refreshSourcesBtn">
                <i class="bi bi-arrow-repeat"></i> Refresh Sources
              </button>
            </div>
          </div>

        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">Add New Regulatory Source</div>
                    <div class="card-body">
                        <form id="addSourceForm">
                            <div class="mb-3">
                                <label for="name" class="form-label">Source Name</label>
                                <input type="text" class="form-control" id="name" required>
                            </div>
                            <div class="mb-3">
                                <label for="url" class="form-label">Source URL</label>
                                <input type="url" class="form-control" id="url" required>
                            </div>
                            <div class="mb-3">
                                <label for="countryCode" class="form-label">Country Code</label>
                                <input type="text" class="form-control" id="countryCode" required>
                            </div>
                            <div class="mb-3">
                                <label for="regulatorName" class="form-label">Regulator Name (Optional)</label>
                                <input type="text" class="form-control" id="regulatorName">
                            </div>
                            <div class="mb-3">
                                <label for="description" class="form-label">Description (Optional)</label>
                                <textarea class="form-control" id="description" rows="2"></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="sourceType" class="form-label">Source Type</label>
                                <select class="form-select" id="sourceType" required>
                                    <option value="website">Website</option>
                                    <option value="rss">RSS Feed</option>
                                    <option value="api">API</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="frequency" class="form-label">Collection Frequency (hours)</label>
                                <input type="number" class="form-control" id="frequency" value="24" min="1" required>
                            </div>
                            <button type="submit" class="btn btn-primary">Add Source</button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">Collection Stats</div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h5 class="card-title">Total Sources</h5>
                                        <h2 id="totalSources">0</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h5 class="card-title">Sources Due</h5>
                                        <h2 id="sourcesDue">0</h2>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-6 mb-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h5 class="card-title">Total URLs</h5>
                                        <h2 id="totalUrls">0</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h5 class="card-title">Sources with Errors</h5>
                                        <h2 id="sourcesWithErrors">0</h2>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="d-grid mt-3">
                            <button id="collectAllBtn" class="btn btn-success">Collect All Due Sources</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>Regulatory Sources</span>
                <div class="input-group" style="width: 300px;">
                    <input type="text" id="sourceSearch" class="form-control" placeholder="Search sources...">
                    <button class="btn btn-outline-secondary" type="button" id="refreshBtn">
                        <i class="bi bi-arrow-clockwise"></i> Refresh
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="sourcesList" class="row">
                    <!-- Sources will be loaded here -->
                    <div class="text-center py-5">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading sources...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadSources();

            // Add source form submission
            document.getElementById('addSourceForm').addEventListener('submit', function(e) {
                e.preventDefault();
                addSource();
            });

            // Collect all button
            document.getElementById('collectAllBtn').addEventListener('click', function() {
                collectAllSources();
            });

            // Refresh button
            document.getElementById('refreshBtn').addEventListener('click', function() {
                loadSources();
            });

            // Search functionality
            document.getElementById('sourceSearch').addEventListener('input', function() {
                filterSources(this.value);
            });

            // Enrich data buttons
            document.querySelectorAll('.enrich-data-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const recordType = this.getAttribute('data-type');
                    startDataEnrichment(recordType);
                });
            });
        });

        async function startDataEnrichment(recordType) {
            try {
                // Show loading toast (Implementation needed)
                showToast('Starting data enrichment process...', 'info');

                const response = await fetch(`/api/v1/data-enrichment/generate?record_type=${recordType}`);

                if (!response.ok) {
                    throw new Error(`Server error: ${response.status}`);
                }

                const data = await response.json();

                if (data.job_id) {
                    showToast('Data enrichment process started', 'success');

                    // Redirect to enrichment review page
                    window.location.href = `/data-enrichment?job_id=${data.job_id}`;
                } else {
                    throw new Error('No job ID returned from server');
                }

            } catch (error) {
                console.error('Error starting data enrichment:', error);
                showToast(`Error: ${error.message}`, 'danger');
            }
        }


        function loadSources() {
            fetch('/api/v1/collect/sources')
                .then(response => response.json())
                .then(sources => {
                    displaySources(sources);
                    updateStats();
                })
                .catch(error => {
                    console.error('Error loading sources:', error);
                    document.getElementById('sourcesList').innerHTML = `
                        <div class="col-12 text-center py-5">
                            <div class="alert alert-danger">Error loading sources: ${error.message}</div>
                        </div>
                    `;
                });
        }

        function displaySources(sources) {
            const sourcesList = document.getElementById('sourcesList');

            if (sources.length === 0) {
                sourcesList.innerHTML = `
                    <div class="col-12 text-center py-5">
                        <p>No regulatory sources found. Add a new source to get started.</p>
                    </div>
                `;
                return;
            }

            let html = '';

            sources.forEach(source => {
                const lastCollection = source.last_collection ? new Date(source.last_collection).toLocaleString() : 'Never';

                html += `
                    <div class="col-md-6 col-lg-4 source-card-container">
                        <div class="card source-card" data-source-id="${source.id}" data-source-name="${source.name}">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <span>${source.name}</span>
                                <span class="badge bg-secondary">${source.source_type}</span>
                            </div>
                            <div class="card-body">
                                <p class="card-text"><strong>URL:</strong> <a href="${source.url}" target="_blank">${source.url}</a></p>
                                <p class="card-text"><strong>Country ID:</strong> ${source.country_id}</p>
                                <p class="card-text"><strong>Last Collection:</strong> ${lastCollection}</p>
                                <p class="card-text"><strong>URLs Collected:</strong> ${source.url_count}</p>
                                <div class="action-buttons">
                                    <button class="btn btn-primary btn-collect" data-source-id="${source.id}">Collect Now</button>
                                    <button class="btn btn-info btn-view" data-source-id="${source.id}">View Data</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            sourcesList.innerHTML = html;

            // Add event listeners to buttons
            document.querySelectorAll('.btn-collect').forEach(button => {
                button.addEventListener('click', function() {
                    const sourceId = this.getAttribute('data-source-id');
                    collectFromSource(sourceId);
                });
            });

            document.querySelectorAll('.btn-view').forEach(button => {
                button.addEventListener('click', function() {
                    const sourceId = this.getAttribute('data-source-id');
                    viewSourceData(sourceId);
                });
            });
        }

        function addSource() {
            const sourceData = {
                name: document.getElementById('name').value,
                url: document.getElementById('url').value,
                country_code: document.getElementById('countryCode').value,
                regulator_name: document.getElementById('regulatorName').value || null,
                description: document.getElementById('description').value || null,
                source_type: document.getElementById('sourceType').value,
                collection_frequency: parseInt(document.getElementById('frequency').value)
            };

            fetch('/api/v1/collect/sources', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(sourceData)
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => {
                        throw new Error(err.detail || 'Error adding source');
                    });
                }
                return response.json();
            })
            .then(data => {
                alert(`Source added successfully! ID: ${data.id}`);
                document.getElementById('addSourceForm').reset();
                loadSources();
            })
            .catch(error => {
                alert(`Error: ${error.message}`);
                console.error('Error adding source:', error);
            });
        }

        function collectFromSource(sourceId) {
            const button = document.querySelector(`.btn-collect[data-source-id="${sourceId}"]`);
            const sourceName = button.closest('.source-card').getAttribute('data-source-name');

            button.disabled = true;
            button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Collecting...';

            fetch(`/api/v1/collect/collect/${sourceId}?force=true`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                alert(`Data collection from "${sourceName}" has been started.`);
                setTimeout(() => {
                    loadSources();
                }, 2000);
            })
            .catch(error => {
                alert(`Error starting collection: ${error.message}`);
                console.error('Error collecting data:', error);
                button.disabled = false;
                button.textContent = 'Collect Now';
            });
        }

        function collectAllSources() {
            const button = document.getElementById('collectAllBtn');
            button.disabled = true;
            button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Collecting...';

            fetch('/api/v1/collect/collect-all', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                alert(`Data collection started for ${data.sources} sources.`);
                setTimeout(() => {
                    loadSources();
                }, 2000);
                button.disabled = false;
                button.textContent = 'Collect All Due Sources';
            })
            .catch(error => {
                alert(`Error starting collection: ${error.message}`);
                console.error('Error collecting data:', error);
                button.disabled = false;
                button.textContent = 'Collect All Due Sources';
            });
        }

        function viewSourceData(sourceId) {
            // Redirect to a page showing regulations from this source
            window.location.href = `/api/v1/regulations?source_id=${sourceId}`;
        }

        function updateStats() {
            fetch('/api/v1/collect/status')
                .then(response => response.json())
                .then(statuses => {
                    const totalSources = statuses.length;
                    const sourcesDue = statuses.filter(s => s.status === 'due').length;
                    const sourcesWithErrors = statuses.filter(s => s.status === 'error').length;
                    let totalUrls = 0;

                    statuses.forEach(s => {
                        totalUrls += s.url_count;
                    });

                    document.getElementById('totalSources').textContent = totalSources;
                    document.getElementById('sourcesDue').textContent = sourcesDue;
                    document.getElementById('totalUrls').textContent = totalUrls;
                    document.getElementById('sourcesWithErrors').textContent = sourcesWithErrors;
                })
                .catch(error => {
                    console.error('Error updating stats:', error);
                });
        }

        function filterSources(searchTerm) {
            searchTerm = searchTerm.toLowerCase();
            document.querySelectorAll('.source-card-container').forEach(card => {
                const sourceName = card.querySelector('.source-card').getAttribute('data-source-name').toLowerCase();
                if (sourceName.includes(searchTerm)) {
                    card.style.display = '';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        // Placeholder for showToast function - needs implementation
        function showToast(message, type) {
          console.log(`Toast: ${message} (${type})`); // Replace with actual toast implementation
        }
    </script>

                <!-- Toast container for notifications -->
                <div id="toastContainer" class="toast-container position-fixed bottom-0 end-0 p-3"></div>
</body>
</html>