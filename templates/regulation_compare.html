
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Regulation Comparison Tool</title>
    <link rel="stylesheet" href="/static/css/styles.css">
    <style>
        .comparison-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .comparison-selector {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .selector {
            flex: 1;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .comparison-table th, .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .comparison-table th {
            background-color: #f2f2f2;
            position: sticky;
            top: 0;
        }
        
        .comparison-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .highlight {
            background-color: #fff3cd;
        }
        
        .similarity-high {
            background-color: #d4edda;
        }
        
        .similarity-medium {
            background-color: #fff3cd;
        }
        
        .similarity-low {
            background-color: #f8d7da;
        }
    </style>
</head>
<body>
    <header>
        <h1>Regulation Comparison Tool</h1>
        <nav>
            <ul>
                <li><a href="/dashboard">Dashboard</a></li>
                <li><a href="/regulatory-dashboard">Compliance Dashboard</a></li>
                <li><a href="/settings">Settings</a></li>
            </ul>
        </nav>
    </header>
    
    <div class="comparison-container">
        <h2>Compare Regulatory Requirements</h2>
        <p>Select two regulations to compare their requirements side-by-side.</p>
        
        <div class="comparison-selector">
            <div class="selector">
                <label for="regulation1">First Regulation:</label>
                <select id="regulation1" class="form-control">
                    <option value="">Select a regulation</option>
                    <option value="gdpr">EU GDPR</option>
                    <option value="ccpa">California CCPA</option>
                    <option value="lgpd">Brazil LGPD</option>
                    <option value="pdpa">Singapore PDPA</option>
                    <option value="pipeda">Canada PIPEDA</option>
                </select>
            </div>
            
            <div class="selector">
                <label for="regulation2">Second Regulation:</label>
                <select id="regulation2" class="form-control">
                    <option value="">Select a regulation</option>
                    <option value="gdpr">EU GDPR</option>
                    <option value="ccpa">California CCPA</option>
                    <option value="lgpd">Brazil LGPD</option>
                    <option value="pdpa">Singapore PDPA</option>
                    <option value="pipeda">Canada PIPEDA</option>
                </select>
            </div>
        </div>
        
        <button id="compareButton" class="btn btn-primary">Compare Regulations</button>
        
        <div id="comparisonResults">
            <p>Select two regulations and click 'Compare Regulations' to see a detailed comparison.</p>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const regulation1Select = document.getElementById('regulation1');
            const regulation2Select = document.getElementById('regulation2');
            const compareButton = document.getElementById('compareButton');
            const resultsContainer = document.getElementById('comparisonResults');
            
            compareButton.addEventListener('click', async () => {
                const reg1 = regulation1Select.value;
                const reg2 = regulation2Select.value;
                
                if (!reg1 || !reg2) {
                    resultsContainer.innerHTML = '<p class="error">Please select two regulations to compare.</p>';
                    return;
                }
                
                if (reg1 === reg2) {
                    resultsContainer.innerHTML = '<p class="error">Please select two different regulations to compare.</p>';
                    return;
                }
                
                resultsContainer.innerHTML = '<p>Loading comparison...</p>';
                
                try {
                    // In a real application, fetch from API
                    // const response = await fetch(`/api/v1/regulations/compare?reg1=${reg1}&reg2=${reg2}`);
                    // const data = await response.json();
                    
                    // Mock data for demonstration
                    const mockComparison = getMockComparisonData(reg1, reg2);
                    
                    // Render comparison table
                    renderComparisonTable(mockComparison, reg1, reg2);
                } catch (error) {
                    resultsContainer.innerHTML = `<p class="error">Error comparing regulations: ${error.message}</p>`;
                }
            });
            
            function renderComparisonTable(data, reg1Code, reg2Code) {
                const reg1Name = getRegulationName(reg1Code);
                const reg2Name = getRegulationName(reg2Code);
                
                let tableHtml = `
                    <h3>Comparison Results: ${reg1Name} vs. ${reg2Name}</h3>
                    <div class="similarity-meter">
                        <p>Overall Similarity: <strong>${data.similarity_score}%</strong></p>
                        <div class="progress">
                            <div class="progress-bar" style="width: ${data.similarity_score}%"></div>
                        </div>
                    </div>
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th>Requirement Category</th>
                                <th>${reg1Name}</th>
                                <th>${reg2Name}</th>
                                <th>Similarity</th>
                            </tr>
                        </thead>
                        <tbody>`;
                
                data.categories.forEach(category => {
                    const similarityClass = getSimilarityClass(category.similarity);
                    
                    tableHtml += `
                        <tr>
                            <td><strong>${category.name}</strong></td>
                            <td>${category.reg1_requirement}</td>
                            <td>${category.reg2_requirement}</td>
                            <td class="${similarityClass}">${category.similarity}%</td>
                        </tr>`;
                });
                
                tableHtml += `
                        </tbody>
                    </table>
                    
                    <h3>Key Differences</h3>
                    <ul>`;
                
                data.key_differences.forEach(diff => {
                    tableHtml += `<li><strong>${diff.category}:</strong> ${diff.description}</li>`;
                });
                
                tableHtml += `
                    </ul>
                    
                    <h3>Compliance Recommendations</h3>
                    <p>${data.recommendations}</p>`;
                
                resultsContainer.innerHTML = tableHtml;
            }
            
            function getSimilarityClass(score) {
                if (score >= 80) return 'similarity-high';
                if (score >= 50) return 'similarity-medium';
                return 'similarity-low';
            }
            
            function getRegulationName(code) {
                const names = {
                    'gdpr': 'EU GDPR',
                    'ccpa': 'California CCPA',
                    'lgpd': 'Brazil LGPD',
                    'pdpa': 'Singapore PDPA',
                    'pipeda': 'Canada PIPEDA'
                };
                return names[code] || code;
            }
            
            function getMockComparisonData(reg1, reg2) {
                // This would come from an API in a real application
                if (reg1 === 'gdpr' && reg2 === 'ccpa') {
                    return {
                        similarity_score: 72,
                        categories: [
                            {
                                name: "Scope",
                                reg1_requirement: "Applies to all organizations processing EU residents' data, regardless of location",
                                reg2_requirement: "Applies to businesses collecting data from California residents that meet certain thresholds",
                                similarity: 65
                            },
                            {
                                name: "Legal Basis",
                                reg1_requirement: "Requires explicit legal basis for processing, like consent, contract, legitimate interest",
                                reg2_requirement: "Focuses on notice and opt-out rights rather than legal basis for collection",
                                similarity: 45
                            },
                            {
                                name: "Right to Access",
                                reg1_requirement: "Right to access personal data, confirmation of processing, and purpose",
                                reg2_requirement: "Right to know what personal information is collected and disclosed",
                                similarity: 85
                            },
                            {
                                name: "Right to Deletion",
                                reg1_requirement: "Right to erasure (right to be forgotten) with certain exceptions",
                                reg2_requirement: "Right to deletion with exceptions",
                                similarity: 90
                            },
                            {
                                name: "Data Breach Notification",
                                reg1_requirement: "Notify supervisory authority within 72 hours, notify affected individuals if high risk",
                                reg2_requirement: "No specific CCPA breach notification (covered by separate California law)",
                                similarity: 30
                            }
                        ],
                        key_differences: [
                            {
                                category: "Legal Basis",
                                description: "GDPR requires specific legal basis for processing, while CCPA focuses on notice and opt-out rights"
                            },
                            {
                                category: "Right to Object",
                                description: "GDPR provides right to object to processing for direct marketing, research, and legitimate interests; CCPA offers opt-out for sale of data"
                            },
                            {
                                category: "Data Protection Officer",
                                description: "GDPR requires DPO appointment in certain cases; CCPA has no equivalent requirement"
                            }
                        ],
                        recommendations: "Organizations complying with GDPR will likely meet many CCPA requirements, but should focus on adapting to CCPA's specific requirements for selling data, consumer requests, and California-specific privacy notices."
                    };
                }
                
                // Default mock data for other regulation pairs
                return {
                    similarity_score: 65,
                    categories: [
                        {
                            name: "Scope",
                            reg1_requirement: "Varies based on regulation",
                            reg2_requirement: "Varies based on regulation",
                            similarity: 60
                        },
                        {
                            name: "Data Subject Rights",
                            reg1_requirement: "Various rights provided",
                            reg2_requirement: "Various rights provided",
                            similarity: 70
                        },
                        {
                            name: "Security Requirements",
                            reg1_requirement: "Appropriate technical and organizational measures",
                            reg2_requirement: "Reasonable security procedures and practices",
                            similarity: 75
                        }
                    ],
                    key_differences: [
                        {
                            category: "Approach",
                            description: "Regulations take different approaches to data protection"
                        },
                        {
                            category: "Enforcement",
                            description: "Enforcement mechanisms and penalties differ significantly"
                        }
                    ],
                    recommendations: "Review both regulations in detail and implement a compliance strategy that satisfies the more stringent requirements of each category."
                };
            }
        });
    </script>
</body>
</html>
