
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Regulatory Relationship Mapping</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <style>
        .document-card {
            margin-bottom: 15px;
            border-left: 4px solid #007bff;
        }
        .relationship-card {
            margin-bottom: 10px;
            border-left: 4px solid #28a745;
        }
        .cross-reference-card {
            margin-bottom: 10px;
            border-left: 4px solid #dc3545;
        }
        .mapping-card {
            margin-bottom: 10px;
            border-left: 4px solid #ffc107;
        }
        .drag-area {
            border: 2px dashed #ccc;
            border-radius: 5px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s;
        }
        .drag-area.active {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .file-item {
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 8px 15px;
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .file-item .btn-remove {
            color: #dc3545;
            cursor: pointer;
        }
        #visualization-container {
            width: 100%;
            height: 500px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-top: 20px;
        }
        .nav-pills .nav-link.active {
            background-color: #007bff;
        }
        .framework-badge {
            margin-right: 5px;
        }
        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px;
        }
        .loading-spinner {
            width: 3rem;
            height: 3rem;
        }
    </style>
</head>
<body>
    <div class="container mt-4 mb-5">
        <h1 class="display-5 mb-4">Regulatory Relationship Mapping</h1>
        
        <div class="row">
            <div class="col-lg-8">
                <!-- Document Upload Section -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Upload Regulatory Documents</h5>
                    </div>
                    <div class="card-body">
                        <div class="drag-area" id="dropArea">
                            <h5>Drag & Drop Files Here</h5>
                            <p>or</p>
                            <button class="btn btn-primary" id="browseBtn">Browse Files</button>
                            <input type="file" id="fileInput" multiple style="display: none;">
                            <p class="mt-2 text-muted">Supported formats: PDF, TXT</p>
                        </div>
                        
                        <div id="fileList" class="mb-3"></div>
                        
                        <div class="d-grid gap-2">
                            <button class="btn btn-success" id="analyzeBtn" disabled>Analyze Documents & Map Relationships</button>
                        </div>
                    </div>
                </div>
                
                <!-- Analysis Results Section -->
                <div id="resultsContainer" style="display: none;">
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">Analysis Results</h5>
                        </div>
                        <div class="card-body">
                            <ul class="nav nav-pills mb-3" id="resultsTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="documents-tab" data-bs-toggle="pill" data-bs-target="#documents" type="button" role="tab">Documents</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="relationships-tab" data-bs-toggle="pill" data-bs-target="#relationships" type="button" role="tab">Relationships</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="mappings-tab" data-bs-toggle="pill" data-bs-target="#mappings" type="button" role="tab">Control Mappings</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="visualization-tab" data-bs-toggle="pill" data-bs-target="#visualization" type="button" role="tab">Visualization</button>
                                </li>
                            </ul>
                            
                            <div class="tab-content" id="resultsTabContent">
                                <!-- Documents Tab -->
                                <div class="tab-pane fade show active" id="documents" role="tabpanel">
                                    <div id="documentsContainer"></div>
                                </div>
                                
                                <!-- Relationships Tab -->
                                <div class="tab-pane fade" id="relationships" role="tabpanel">
                                    <div class="mb-4">
                                        <h5>Hierarchical Relationships</h5>
                                        <div id="hierarchicalContainer">
                                            <p class="text-muted">No hierarchical relationships found between documents.</p>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <h5>Cross-References</h5>
                                        <div id="crossReferencesContainer">
                                            <p class="text-muted">No cross-references found between documents.</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Control Mappings Tab -->
                                <div class="tab-pane fade" id="mappings" role="tabpanel">
                                    <div class="mb-3">
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-outline-primary active" data-framework="NIST">NIST</button>
                                            <button type="button" class="btn btn-outline-primary" data-framework="ISO">ISO</button>
                                            <button type="button" class="btn btn-outline-primary" data-framework="ISF">ISF</button>
                                        </div>
                                    </div>
                                    
                                    <div id="frameworkMappingsContainer">
                                        <div id="NIST-mappings"></div>
                                        <div id="ISO-mappings" style="display: none;"></div>
                                        <div id="ISF-mappings" style="display: none;"></div>
                                    </div>
                                </div>
                                
                                <!-- Visualization Tab -->
                                <div class="tab-pane fade" id="visualization" role="tabpanel">
                                    <div id="visualization-container"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Loading Indicator -->
                <div id="loadingContainer" class="card mb-4" style="display: none;">
                    <div class="card-body loading-container">
                        <div class="spinner-border loading-spinner text-primary mb-3" role="status"></div>
                        <h5 id="loadingText">Analyzing documents...</h5>
                        <p class="text-muted" id="loadingSubtext">This may take a few moments</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <!-- Information Panel -->
                <div class="card mb-4">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">About Relationship Mapping</h5>
                    </div>
                    <div class="card-body">
                        <h6>Types of Relationships Detected:</h6>
                        <ul>
                            <li><strong>Parent/Child Relationships</strong>: Hierarchical connections between regulations</li>
                            <li><strong>Cross-References</strong>: Requirements that reference other regulatory sections</li>
                            <li><strong>Control Mappings</strong>: Connections to common frameworks (NIST, ISO, ISF)</li>
                        </ul>
                        
                        <h6>Analysis Capabilities:</h6>
                        <ul>
                            <li>Extract structured content from regulatory documents</li>
                            <li>Identify key requirements, deadlines, and penalties</li>
                            <li>Map relationships between related regulations</li>
                            <li>Connect requirements to control frameworks</li>
                        </ul>
                        
                        <div class="alert alert-info mt-3">
                            <i class="bi bi-info-circle"></i> For best results, upload related regulatory documents together to enable comprehensive relationship mapping.
                        </div>
                    </div>
                </div>
                
                <!-- Tips Panel -->
                <div class="card">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0">Tips & Features</h5>
                    </div>
                    <div class="card-body">
                        <ul>
                            <li>Upload multiple documents to map relationships between them</li>
                            <li>View hierarchical relationships to understand regulatory dependencies</li>
                            <li>See cross-references between specific requirements</li>
                            <li>Map requirements to common control frameworks</li>
                            <li>Integrate with external compliance systems via SuperGlu</li>
                            <li>Visualize the relationships with an interactive graph</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vis-network@9.1.2/dist/vis-network.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Elements
            const dropArea = document.getElementById('dropArea');
            const fileInput = document.getElementById('fileInput');
            const browseBtn = document.getElementById('browseBtn');
            const fileList = document.getElementById('fileList');
            const analyzeBtn = document.getElementById('analyzeBtn');
            const resultsContainer = document.getElementById('resultsContainer');
            const loadingContainer = document.getElementById('loadingContainer');
            
            const files = [];
            
            // Event Listeners
            browseBtn.addEventListener('click', () => fileInput.click());
            
            fileInput.addEventListener('change', handleFiles);
            
            dropArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropArea.classList.add('active');
            });
            
            dropArea.addEventListener('dragleave', () => {
                dropArea.classList.remove('active');
            });
            
            dropArea.addEventListener('drop', (e) => {
                e.preventDefault();
                dropArea.classList.remove('active');
                handleFiles({ target: { files: e.dataTransfer.files } });
            });
            
            analyzeBtn.addEventListener('click', analyzeDocuments);
            
            // Framework tab switching
            document.querySelectorAll('[data-framework]').forEach(button => {
                button.addEventListener('click', function() {
                    // Update active button
                    document.querySelectorAll('[data-framework]').forEach(btn => {
                        btn.classList.remove('active');
                    });
                    this.classList.add('active');
                    
                    // Show corresponding mappings
                    const framework = this.getAttribute('data-framework');
                    document.querySelectorAll('[id$="-mappings"]').forEach(div => {
                        div.style.display = 'none';
                    });
                    document.getElementById(`${framework}-mappings`).style.display = 'block';
                });
            });
            
            // Functions
            function handleFiles(e) {
                const selectedFiles = Array.from(e.target.files);
                
                selectedFiles.forEach(file => {
                    // Check if file is already in the list
                    if (!files.some(f => f.name === file.name && f.size === file.size)) {
                        files.push(file);
                        
                        // Add file to UI
                        const fileItem = document.createElement('div');
                        fileItem.className = 'file-item';
                        fileItem.innerHTML = `
                            <span>${file.name} (${formatFileSize(file.size)})</span>
                            <span class="btn-remove" data-name="${file.name}" data-size="${file.size}">×</span>
                        `;
                        fileList.appendChild(fileItem);
                    }
                });
                
                // Update analyze button state
                analyzeBtn.disabled = files.length === 0;
                
                // Clear file input
                fileInput.value = null;
                
                // Add remove event listeners
                document.querySelectorAll('.btn-remove').forEach(btn => {
                    btn.addEventListener('click', removeFile);
                });
            }
            
            function removeFile(e) {
                const name = e.target.getAttribute('data-name');
                const size = parseInt(e.target.getAttribute('data-size'));
                
                // Remove from files array
                const index = files.findIndex(f => f.name === name && f.size === size);
                if (index !== -1) {
                    files.splice(index, 1);
                }
                
                // Remove from UI
                e.target.parentElement.remove();
                
                // Update analyze button state
                analyzeBtn.disabled = files.length === 0;
            }
            
            function formatFileSize(bytes) {
                if (bytes < 1024) return bytes + ' bytes';
                if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
                return (bytes / 1048576).toFixed(1) + ' MB';
            }
            
            async function analyzeDocuments() {
                // Show loading indicator
                resultsContainer.style.display = 'none';
                loadingContainer.style.display = 'block';
                document.getElementById('loadingText').textContent = 'Analyzing documents...';
                document.getElementById('loadingSubtext').textContent = 'This may take a few moments';
                
                try {
                    // Create form data
                    const formData = new FormData();
                    files.forEach(file => {
                        formData.append('files', file);
                    });
                    
                    // Send request
                    const response = await fetch('/api/documents/batch-analyze', {
                        method: 'POST',
                        body: formData
                    });
                    
                    if (!response.ok) {
                        throw new Error(`HTTP error ${response.status}`);
                    }
                    
                    const data = await response.json();
                    
                    // Display results
                    displayResults(data);
                    
                    // Hide loading indicator and show results
                    loadingContainer.style.display = 'none';
                    resultsContainer.style.display = 'block';
                } catch (error) {
                    console.error('Error analyzing documents:', error);
                    
                    // Show error message
                    document.getElementById('loadingText').textContent = 'Error analyzing documents';
                    document.getElementById('loadingSubtext').textContent = error.message;
                    document.getElementById('loadingSubtext').classList.add('text-danger');
                    
                    // Add retry button
                    const retryBtn = document.createElement('button');
                    retryBtn.className = 'btn btn-primary mt-3';
                    retryBtn.textContent = 'Try Again';
                    retryBtn.addEventListener('click', () => {
                        loadingContainer.style.display = 'none';
                        document.getElementById('loadingSubtext').classList.remove('text-danger');
                        
                        // Remove retry button
                        if (document.querySelector('#loadingContainer .btn')) {
                            document.querySelector('#loadingContainer .btn').remove();
                        }
                    });
                    
                    if (!document.querySelector('#loadingContainer .btn')) {
                        document.querySelector('#loadingContainer').appendChild(retryBtn);
                    }
                }
            }
            
            function displayResults(data) {
                // Documents Tab
                const documentsContainer = document.getElementById('documentsContainer');
                documentsContainer.innerHTML = '';
                
                data.documents.forEach(doc => {
                    const docCard = document.createElement('div');
                    docCard.className = 'card document-card mb-3';
                    docCard.innerHTML = `
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">${doc.file_name}</h6>
                            <span class="badge bg-${getConfidenceBadgeColor(doc.confidence_score)}">
                                ${(doc.confidence_score * 100).toFixed(0)}% Confidence
                            </span>
                        </div>
                        <div class="card-body">
                            <p>${doc.summary || 'No summary available'}</p>
                            <div class="d-flex flex-wrap">
                                <div class="me-4 mb-2">
                                    <strong>Requirements:</strong> ${doc.requirements ? doc.requirements.length : 0}
                                </div>
                                <div class="me-4 mb-2">
                                    <strong>Deadlines:</strong> ${doc.deadlines ? doc.deadlines.length : 0}
                                </div>
                                <div class="mb-2">
                                    <strong>Penalties:</strong> ${doc.penalties ? doc.penalties.length : 0}
                                </div>
                            </div>
                            <div class="mt-2">
                                <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#details-${doc.file_name.replace(/\s+/g, '-').replace(/\./g, '_')}">
                                    Show Details
                                </button>
                            </div>
                            <div class="collapse mt-3" id="details-${doc.file_name.replace(/\s+/g, '-').replace(/\./g, '_')}">
                                <div class="card card-body bg-light">
                                    <h6>Entities</h6>
                                    <div class="mb-2">
                                        <small class="text-muted">Organizations:</small> 
                                        ${doc.entities && doc.entities.organizations ? doc.entities.organizations.join(', ') : 'None'}
                                    </div>
                                    <div class="mb-2">
                                        <small class="text-muted">Regulatory Terms:</small>
                                        ${doc.entities && doc.entities.regulatory_terms ? doc.entities.regulatory_terms.join(', ') : 'None'}
                                    </div>
                                    
                                    <h6 class="mt-3">Sections</h6>
                                    <div class="mb-2">
                                        <small class="text-muted">Articles:</small> 
                                        ${doc.sections && doc.sections.articles ? doc.sections.articles.length : 0}
                                    </div>>
                                    <div class="mb-2">
                                        <small class="text-muted">Sections:</small>
                                        ${doc.sections && doc.sections.sections ? doc.sections.sections.length : 0}
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    documentsContainer.appendChild(docCard);
                });
                
                // Relationships Tab
                const relationships = data.relationships;
                
                // Hierarchical Relationships
                const hierarchicalContainer = document.getElementById('hierarchicalContainer');
                hierarchicalContainer.innerHTML = '';
                
                if (relationships && relationships.hierarchical_relationships && relationships.hierarchical_relationships.length > 0) {
                    relationships.hierarchical_relationships.forEach(rel => {
                        const relCard = document.createElement('div');
                        relCard.className = 'card relationship-card mb-2';
                        relCard.innerHTML = `
                            <div class="card-body py-3">
                                <div class="d-flex align-items-center">
                                    <div>${rel.source_title}</div>
                                    <div class="mx-3">
                                        <span class="badge bg-info">${formatRelationshipType(rel.relationship_type)}</span>
                                    </div>
                                    <div>${rel.target_title}</div>
                                </div>
                            </div>
                        `;
                        hierarchicalContainer.appendChild(relCard);
                    });
                } else {
                    hierarchicalContainer.innerHTML = '<p class="text-muted">No hierarchical relationships found between documents.</p>';
                }
                
                // Cross-References
                const crossReferencesContainer = document.getElementById('crossReferencesContainer');
                crossReferencesContainer.innerHTML = '';
                
                if (relationships && relationships.cross_references && relationships.cross_references.length > 0) {
                    relationships.cross_references.forEach(ref => {
                        const refCard = document.createElement('div');
                        refCard.className = 'card cross-reference-card mb-2';
                        refCard.innerHTML = `
                            <div class="card-body py-3">
                                <div class="d-flex align-items-center flex-wrap">
                                    <div class="me-auto">${ref.source_text || ref.source_title}</div>
                                    <div>
                                        <span class="badge bg-danger">References</span>
                                    </div>
                                    <div class="ms-2">${ref.target_text || ref.target_title || `${ref.target_type} ${ref.target_number}`}</div>
                                </div>
                            </div>
                        `;
                        crossReferencesContainer.appendChild(refCard);
                    });
                } else {
                    crossReferencesContainer.innerHTML = '<p class="text-muted">No cross-references found between documents.</p>';
                }
                
                // Control Mappings Tab
                updateFrameworkMappings('NIST', relationships);
                updateFrameworkMappings('ISO', relationships);
                updateFrameworkMappings('ISF', relationships);
                
                // Visualization Tab
                if (relationships && relationships.documents && 
                    (relationships.hierarchical_relationships || relationships.cross_references)) {
                    createNetworkVisualization(relationships);
                } else {
                    document.getElementById('visualization-container').innerHTML = 
                        '<div class="d-flex justify-content-center align-items-center h-100">' +
                        '<p class="text-muted">Not enough data to generate visualization.</p>' +
                        '</div>';
                }
            }
            
            function updateFrameworkMappings(framework, relationships) {
                const container = document.getElementById(`${framework}-mappings`);
                container.innerHTML = '';
                
                if (relationships && relationships.framework_mappings && 
                    relationships.framework_mappings[framework] && 
                    relationships.framework_mappings[framework].length > 0) {
                    
                    // Group mappings by control ID
                    const groupedMappings = {};
                    relationships.framework_mappings[framework].forEach(mapping => {
                        if (!groupedMappings[mapping.control_id]) {
                            groupedMappings[mapping.control_id] = [];
                        }
                        groupedMappings[mapping.control_id].push(mapping);
                    });
                    
                    // Create cards for each control
                    Object.keys(groupedMappings).sort().forEach(controlId => {
                        const mappings = groupedMappings[controlId];
                        const mappingCard = document.createElement('div');
                        mappingCard.className = 'card mapping-card mb-3';
                        
                        let mappingContent = `
                            <div class="card-header bg-light">
                                <h6 class="mb-0">${framework} ${controlId}</h6>
                            </div>
                            <div class="card-body">
                                <div class="list-group">
                        `;
                        
                        mappings.forEach(mapping => {
                            mappingContent += `
                                <div class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <small class="text-muted">From document</small>
                                    </div>
                                    <p class="mb-1">${mapping.requirement_text}</p>
                                </div>
                            `;
                        });
                        
                        mappingContent += `
                                </div>
                            </div>
                        `;
                        
                        mappingCard.innerHTML = mappingContent;
                        container.appendChild(mappingCard);
                    });
                    
                } else {
                    container.innerHTML = `<p class="text-muted">No ${framework} control mappings found.</p>`;
                }
            }
            
            function createNetworkVisualization(relationships) {
                const container = document.getElementById('visualization-container');
                
                // Create nodes
                const nodes = new vis.DataSet();
                const edges = new vis.DataSet();
                
                // Add document nodes
                relationships.documents.forEach(doc => {
                    nodes.add({
                        id: doc.id,
                        label: doc.title,
                        group: 'document',
                        shape: 'box',
                        title: `Confidence: ${(doc.confidence * 100).toFixed(0)}%`
                    });
                });
                
                // Add hierarchical relationship edges
                if (relationships.hierarchical_relationships) {
                    relationships.hierarchical_relationships.forEach(rel => {
                        edges.add({
                            from: rel.source_id,
                            to: rel.target_id,
                            label: formatRelationshipType(rel.relationship_type),
                            arrows: 'to',
                            color: {color: '#28a745'}
                        });
                    });
                }
                
                // Add cross-reference edges
                if (relationships.cross_references) {
                    relationships.cross_references.forEach((ref, index) => {
                        if (ref.reference_type === 'document') {
                            edges.add({
                                from: ref.source_id,
                                to: ref.target_id,
                                label: 'references',
                                arrows: 'to',
                                dashes: true,
                                color: {color: '#dc3545'}
                            });
                        }
                    });
                }
                
                // Create network
                const data = {
                    nodes: nodes,
                    edges: edges
                };
                
                const options = {
                    nodes: {
                        font: {
                            size: 12
                        }
                    },
                    edges: {
                        font: {
                            size: 10,
                            align: 'middle'
                        },
                        smooth: {
                            type: 'continuous'
                        }
                    },
                    physics: {
                        enabled: true,
                        solver: 'forceAtlas2Based',
                        forceAtlas2Based: {
                            gravitationalConstant: -50,
                            centralGravity: 0.01,
                            springLength: 100,
                            springConstant: 0.08
                        },
                        stabilization: {
                            iterations: 100
                        }
                    },
                    groups: {
                        document: {
                            color: {
                                background: '#007bff',
                                border: '#0056b3',
                                highlight: {
                                    background: '#0069d9',
                                    border: '#0056b3'
                                }
                            },
                            font: {
                                color: 'white'
                            }
                        }
                    }
                };
                
                new vis.Network(container, data, options);
            }
            
            function getConfidenceBadgeColor(confidence) {
                if (confidence >= 0.8) return 'success';
                if (confidence >= 0.5) return 'warning';
                return 'danger';
            }
            
            function formatRelationshipType(type) {
                switch (type) {
                    case 'amended_by': return 'Amended by';
                    case 'newer_version': return 'Newer version';
                    default: return type.replace('_', ' ');
                }
            }
        });
    </script>
</body>
</html>
