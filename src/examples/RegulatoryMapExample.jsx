import React, { useState, useEffect } from 'react';
import { RegulatoryMapContainer } from '../components/regulatory-map';
import { NodeTypes, EdgeTypes, ComplianceStatus } from '../constants/regulatoryMap';

/**
 * Example component demonstrating the use of the RegulatoryMapContainer
 */
const RegulatoryMapExample = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Fetch sample data
  useEffect(() => {
    // In a real application, this would be an API call
    // For this example, we'll use a timeout to simulate an API call
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Sample data
        const sampleData = generateSampleData();
        
        setData(sampleData);
        setError(null);
      } catch (err) {
        setError('Failed to load regulatory map data');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, []);
  
  // Generate sample data
  const generateSampleData = () => {
    // Create nodes
    const nodes = [
      // Regulations
      {
        id: 'reg1',
        type: NodeTypes.REGULATION,
        name: 'General Data Protection Regulation (GDPR)',
        description: 'Regulation on data protection and privacy in the EU and the European Economic Area.',
        status: ComplianceStatus.PARTIALLY_COMPLIANT,
        metadata: {
          regulatoryAuthority: 'European Union',
          jurisdiction: 'EU',
          effectiveDate: '2018-05-25',
          category: ['Privacy', 'Data Protection']
        }
      },
      {
        id: 'reg2',
        type: NodeTypes.REGULATION,
        name: 'California Consumer Privacy Act (CCPA)',
        description: 'State statute intended to enhance privacy rights and consumer protection for residents of California.',
        status: ComplianceStatus.COMPLIANT,
        metadata: {
          regulatoryAuthority: 'California State Legislature',
          jurisdiction: 'California, USA',
          effectiveDate: '2020-01-01',
          category: ['Privacy', 'Data Protection']
        }
      },
      {
        id: 'reg3',
        type: NodeTypes.REGULATION,
        name: 'Health Insurance Portability and Accountability Act (HIPAA)',
        description: 'US legislation that provides data privacy and security provisions for safeguarding medical information.',
        status: ComplianceStatus.NON_COMPLIANT,
        metadata: {
          regulatoryAuthority: 'US Department of Health and Human Services',
          jurisdiction: 'USA',
          effectiveDate: '1996-08-21',
          category: ['Healthcare', 'Privacy']
        }
      },
      
      // Requirements
      {
        id: 'req1',
        type: NodeTypes.REQUIREMENT,
        name: 'Data Subject Access Rights',
        description: 'Individuals have the right to access their personal data.',
        status: ComplianceStatus.PARTIALLY_COMPLIANT,
        metadata: {
          section: 'Article 15',
          priority: 'High'
        }
      },
      {
        id: 'req2',
        type: NodeTypes.REQUIREMENT,
        name: 'Data Protection Impact Assessment',
        description: 'Conduct impact assessments for high-risk processing activities.',
        status: ComplianceStatus.NON_COMPLIANT,
        metadata: {
          section: 'Article 35',
          priority: 'Medium',
          deadline: '2023-12-31'
        }
      },
      {
        id: 'req3',
        type: NodeTypes.REQUIREMENT,
        name: 'Right to Deletion',
        description: 'Consumers have the right to request deletion of personal information.',
        status: ComplianceStatus.COMPLIANT,
        metadata: {
          section: 'Section 1798.105',
          priority: 'High'
        }
      },
      {
        id: 'req4',
        type: NodeTypes.REQUIREMENT,
        name: 'Security Rule',
        description: 'Ensure the confidentiality, integrity, and availability of all e-PHI.',
        status: ComplianceStatus.NON_COMPLIANT,
        metadata: {
          section: '45 CFR Part 160',
          priority: 'Critical'
        }
      },
      
      // Controls
      {
        id: 'ctrl1',
        type: NodeTypes.CONTROL,
        name: 'Data Subject Request Process',
        description: 'Process for handling data subject access requests.',
        status: ComplianceStatus.PARTIALLY_COMPLIANT,
        metadata: {
          controlType: 'Process',
          implementationStatus: 'In Progress',
          owner: 'Privacy Team',
          lastTestedDate: '2023-06-15'
        }
      },
      {
        id: 'ctrl2',
        type: NodeTypes.CONTROL,
        name: 'Data Inventory',
        description: 'Comprehensive inventory of all personal data processing activities.',
        status: ComplianceStatus.NON_COMPLIANT,
        metadata: {
          controlType: 'Documentation',
          implementationStatus: 'Planned',
          owner: 'Data Governance Team'
        }
      },
      {
        id: 'ctrl3',
        type: NodeTypes.CONTROL,
        name: 'Data Deletion Workflow',
        description: 'Automated workflow for processing data deletion requests.',
        status: ComplianceStatus.COMPLIANT,
        metadata: {
          controlType: 'Technical',
          implementationStatus: 'Implemented',
          owner: 'IT Department',
          lastTestedDate: '2023-08-10'
        }
      },
      {
        id: 'ctrl4',
        type: NodeTypes.CONTROL,
        name: 'Access Control System',
        description: 'System for controlling access to PHI based on role.',
        status: ComplianceStatus.PARTIALLY_COMPLIANT,
        metadata: {
          controlType: 'Technical',
          implementationStatus: 'In Progress',
          owner: 'Security Team'
        }
      },
      
      // Business Processes
      {
        id: 'proc1',
        type: NodeTypes.BUSINESS_PROCESS,
        name: 'Customer Onboarding',
        description: 'Process for registering and onboarding new customers.',
        status: ComplianceStatus.PARTIALLY_COMPLIANT,
        metadata: {
          processOwner: 'Customer Success',
          department: 'Sales',
          criticality: 'High'
        }
      },
      {
        id: 'proc2',
        type: NodeTypes.BUSINESS_PROCESS,
        name: 'Marketing Campaigns',
        description: 'Process for planning and executing marketing campaigns.',
        status: ComplianceStatus.COMPLIANT,
        metadata: {
          processOwner: 'Marketing Director',
          department: 'Marketing',
          criticality: 'Medium'
        }
      },
      {
        id: 'proc3',
        type: NodeTypes.BUSINESS_PROCESS,
        name: 'Patient Records Management',
        description: 'Process for managing patient health records.',
        status: ComplianceStatus.NON_COMPLIANT,
        metadata: {
          processOwner: 'Medical Records Department',
          department: 'Healthcare Operations',
          criticality: 'Critical'
        }
      },
      
      // Systems
      {
        id: 'sys1',
        type: NodeTypes.SYSTEM,
        name: 'Customer Relationship Management',
        description: 'System for managing customer interactions and data.',
        status: ComplianceStatus.PARTIALLY_COMPLIANT,
        metadata: {
          systemOwner: 'IT Department',
          vendor: 'Salesforce',
          version: '2023.1',
          criticality: 'High'
        }
      },
      {
        id: 'sys2',
        type: NodeTypes.SYSTEM,
        name: 'Marketing Automation Platform',
        description: 'System for automating marketing activities and campaigns.',
        status: ComplianceStatus.COMPLIANT,
        metadata: {
          systemOwner: 'Marketing Operations',
          vendor: 'HubSpot',
          version: '4.2',
          criticality: 'Medium'
        }
      },
      {
        id: 'sys3',
        type: NodeTypes.SYSTEM,
        name: 'Electronic Health Record System',
        description: 'System for managing patient health records electronically.',
        status: ComplianceStatus.NON_COMPLIANT,
        metadata: {
          systemOwner: 'IT Healthcare Systems',
          vendor: 'Epic',
          version: '2022.3',
          criticality: 'Critical'
        }
      }
    ];
    
    // Create edges
    const edges = [
      // Regulation to Requirements
      {
        id: 'edge1',
        source: 'reg1',
        target: 'req1',
        type: EdgeTypes.CONTAINS
      },
      {
        id: 'edge2',
        source: 'reg1',
        target: 'req2',
        type: EdgeTypes.CONTAINS
      },
      {
        id: 'edge3',
        source: 'reg2',
        target: 'req3',
        type: EdgeTypes.CONTAINS
      },
      {
        id: 'edge4',
        source: 'reg3',
        target: 'req4',
        type: EdgeTypes.CONTAINS
      },
      
      // Requirements to Controls
      {
        id: 'edge5',
        source: 'req1',
        target: 'ctrl1',
        type: EdgeTypes.IMPLEMENTS
      },
      {
        id: 'edge6',
        source: 'req2',
        target: 'ctrl2',
        type: EdgeTypes.IMPLEMENTS
      },
      {
        id: 'edge7',
        source: 'req3',
        target: 'ctrl3',
        type: EdgeTypes.IMPLEMENTS
      },
      {
        id: 'edge8',
        source: 'req4',
        target: 'ctrl4',
        type: EdgeTypes.IMPLEMENTS
      },
      
      // Requirements to Business Processes
      {
        id: 'edge9',
        source: 'req1',
        target: 'proc1',
        type: EdgeTypes.IMPACTS
      },
      {
        id: 'edge10',
        source: 'req3',
        target: 'proc2',
        type: EdgeTypes.IMPACTS
      },
      {
        id: 'edge11',
        source: 'req4',
        target: 'proc3',
        type: EdgeTypes.IMPACTS
      },
      
      // Business Processes to Systems
      {
        id: 'edge12',
        source: 'proc1',
        target: 'sys1',
        type: EdgeTypes.USES
      },
      {
        id: 'edge13',
        source: 'proc2',
        target: 'sys2',
        type: EdgeTypes.USES
      },
      {
        id: 'edge14',
        source: 'proc3',
        target: 'sys3',
        type: EdgeTypes.USES
      },
      
      // Cross-references
      {
        id: 'edge15',
        source: 'req1',
        target: 'req3',
        type: EdgeTypes.REFERENCES
      },
      {
        id: 'edge16',
        source: 'ctrl1',
        target: 'ctrl3',
        type: EdgeTypes.SUPPORTS
      },
      {
        id: 'edge17',
        source: 'reg1',
        target: 'reg2',
        type: EdgeTypes.REFERENCES
      }
    ];
    
    return { nodes, edges };
  };
  
  // Handle save view
  const handleSaveView = (viewState) => {
    console.log('Saving view:', viewState);
    // In a real application, this would save to a server
    alert('View saved successfully!');
  };
  
  // Handle load view
  const handleLoadView = async (viewId) => {
    console.log('Loading view:', viewId);
    // In a real application, this would load from a server
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Return a sample view state
    return {
      config: {
        showLabels: true,
        labelSize: 12,
        layout: {
          type: 'force',
          linkDistance: 150,
          chargeStrength: -500
        }
      },
      filters: [],
      viewport: { center: { x: 0, y: 0 }, zoom: 1 },
      viewMode: 'graph',
      selectedEntities: { nodeIds: [], edgeIds: [] }
    };
  };
  
  // Handle export image
  const handleExportImage = (format) => {
    console.log('Exporting image as:', format);
    // In a real application, this would generate and download an image
    alert(`Exporting as ${format.toUpperCase()}...`);
  };
  
  // Handle generate report
  const handleGenerateReport = (reportType, reportData) => {
    console.log('Generating report:', reportType, reportData);
    // In a real application, this would generate and download a report
    alert(`Generating ${reportType} report...`);
  };
  
  if (loading) {
    return <div className="loading">Loading regulatory map data...</div>;
  }
  
  if (error) {
    return <div className="error">{error}</div>;
  }
  
  return (
    <div style={{ width: '100%', height: '800px' }}>
      <RegulatoryMapContainer
        data={data}
        initialConfig={{
          showLabels: true,
          labelSize: 12,
          layout: {
            type: 'force',
            linkDistance: 120,
            chargeStrength: -400
          },
          title: 'Regulatory Compliance Map'
        }}
        onSaveView={handleSaveView}
        onLoadView={handleLoadView}
        onExportImage={handleExportImage}
        onGenerateReport={handleGenerateReport}
      />
    </div>
  );
};

export default RegulatoryMapExample;
