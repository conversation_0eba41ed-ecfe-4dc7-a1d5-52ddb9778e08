import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import RegulatoryMapVisualization from './RegulatoryMapVisualization';
import RegulatoryMapControlPanel from './RegulatoryMapControlPanel';
import RegulatoryMapDetailPanel from './RegulatoryMapDetailPanel';
import { ViewModes, ColorSchemes } from '../../constants/regulatoryMap';
import './RegulatoryMapContainer.css';

/**
 * RegulatoryMapContainer Component
 * 
 * A container component that integrates the visualization with control and detail panels
 */
const RegulatoryMapContainer = ({
  data,
  initialConfig,
  onSaveView,
  onLoadView,
  onExportImage,
  onGenerateReport
}) => {
  // State
  const [config, setConfig] = useState({
    ...initialConfig,
    colorScheme: initialConfig.colorScheme || ColorSchemes.STANDARD
  });
  const [selectedEntities, setSelectedEntities] = useState({ nodeIds: [], edgeIds: [] });
  const [viewMode, setViewMode] = useState(initialConfig.viewMode || ViewModes.GRAPH);
  const [filters, setFilters] = useState(initialConfig.filters || []);
  const [isLoading, setIsLoading] = useState(false);
  const [viewport, setViewport] = useState(initialConfig.viewport || { center: { x: 0, y: 0 }, zoom: 1 });
  const [detailPanelOpen, setDetailPanelOpen] = useState(true);
  const [controlPanelOpen, setControlPanelOpen] = useState(true);
  
  // Apply filters to data
  const filteredData = useCallback(() => {
    if (!data || filters.length === 0) return data;
    
    // Clone data to avoid mutating the original
    const clonedData = {
      nodes: [...data.nodes],
      edges: [...data.edges]
    };
    
    // Apply node filters
    const filteredNodes = clonedData.nodes.filter(node => {
      return filters.every(filter => {
        if (!filter.enabled) return true;
        
        // Skip if filter doesn't apply to nodes
        if (filter.entityType && filter.entityType !== 'node') return true;
        
        // Handle different filter types
        switch (filter.type) {
          case 'type':
            return filter.values.includes(node.type);
          case 'status':
            return filter.values.includes(node.status);
          case 'jurisdiction':
            return filter.values.includes(node.metadata?.jurisdiction);
          case 'category':
            return filter.values.includes(node.metadata?.category);
          case 'search':
            return node.name.toLowerCase().includes(filter.value.toLowerCase()) ||
                   (node.description && node.description.toLowerCase().includes(filter.value.toLowerCase()));
          default:
            return true;
        }
      });
    });
    
    // Get filtered node IDs for edge filtering
    const filteredNodeIds = filteredNodes.map(node => node.id);
    
    // Apply edge filters
    const filteredEdges = clonedData.edges.filter(edge => {
      // First check if both source and target nodes are visible
      if (!filteredNodeIds.includes(edge.source) || !filteredNodeIds.includes(edge.target)) {
        return false;
      }
      
      return filters.every(filter => {
        if (!filter.enabled) return true;
        
        // Skip if filter doesn't apply to edges
        if (filter.entityType && filter.entityType !== 'edge') return true;
        
        // Handle different filter types
        switch (filter.type) {
          case 'edgeType':
            return filter.values.includes(edge.type);
          default:
            return true;
        }
      });
    });
    
    return {
      nodes: filteredNodes,
      edges: filteredEdges
    };
  }, [data, filters]);
  
  // Handle selection change
  const handleSelectionChange = useCallback((selection) => {
    setSelectedEntities(selection);
  }, []);
  
  // Handle viewport change
  const handleViewportChange = useCallback((newViewport) => {
    setViewport(newViewport);
  }, []);
  
  // Handle filter change
  const handleFilterChange = useCallback((newFilters) => {
    setFilters(newFilters);
  }, []);
  
  // Handle config change
  const handleConfigChange = useCallback((configChanges) => {
    setConfig(prevConfig => ({
      ...prevConfig,
      ...configChanges
    }));
  }, []);
  
  // Handle view mode change
  const handleViewModeChange = useCallback((mode) => {
    setViewMode(mode);
    
    // Update config based on view mode
    switch (mode) {
      case ViewModes.HIERARCHICAL:
        setConfig(prevConfig => ({
          ...prevConfig,
          layout: {
            ...prevConfig.layout,
            type: 'hierarchical',
            direction: 'TB' // Top to bottom
          }
        }));
        break;
      case ViewModes.JURISDICTIONAL:
        // This would typically switch to a geographic visualization
        break;
      case ViewModes.IMPACT:
        // This would typically switch to an impact heat map
        break;
      case ViewModes.TIMELINE:
        // This would typically switch to a timeline view
        break;
      default: // ViewModes.GRAPH
        setConfig(prevConfig => ({
          ...prevConfig,
          layout: {
            ...prevConfig.layout,
            type: 'force'
          }
        }));
        break;
    }
  }, []);
  
  // Handle save view
  const handleSaveView = useCallback(() => {
    if (onSaveView) {
      const viewState = {
        config,
        filters,
        viewport,
        viewMode,
        selectedEntities
      };
      
      onSaveView(viewState);
    }
  }, [config, filters, viewport, viewMode, selectedEntities, onSaveView]);
  
  // Handle load view
  const handleLoadView = useCallback((viewId) => {
    setIsLoading(true);
    
    if (onLoadView) {
      onLoadView(viewId).then(viewState => {
        if (viewState) {
          setConfig(viewState.config || config);
          setFilters(viewState.filters || filters);
          setViewport(viewState.viewport || viewport);
          setViewMode(viewState.viewMode || viewMode);
          setSelectedEntities(viewState.selectedEntities || { nodeIds: [], edgeIds: [] });
        }
        
        setIsLoading(false);
      }).catch(() => {
        setIsLoading(false);
      });
    } else {
      setIsLoading(false);
    }
  }, [config, filters, viewport, viewMode, onLoadView]);
  
  // Handle export image
  const handleExportImage = useCallback((format) => {
    if (onExportImage) {
      onExportImage(format);
    }
  }, [onExportImage]);
  
  // Handle generate report
  const handleGenerateReport = useCallback((reportType) => {
    if (onGenerateReport) {
      onGenerateReport(reportType, {
        data: filteredData(),
        config,
        selectedEntities,
        viewMode,
        filters
      });
    }
  }, [filteredData, config, selectedEntities, viewMode, filters, onGenerateReport]);
  
  // Handle toggle panels
  const toggleDetailPanel = useCallback(() => {
    setDetailPanelOpen(prev => !prev);
  }, []);
  
  const toggleControlPanel = useCallback(() => {
    setControlPanelOpen(prev => !prev);
  }, []);
  
  // Get selected entities data
  const getSelectedEntitiesData = useCallback(() => {
    if (!data) return { nodes: [], edges: [] };
    
    const selectedNodes = data.nodes.filter(node => selectedEntities.nodeIds.includes(node.id));
    const selectedEdges = data.edges.filter(edge => selectedEntities.edgeIds.includes(edge.id));
    
    return { nodes: selectedNodes, edges: selectedEdges };
  }, [data, selectedEntities]);
  
  return (
    <div className="regulatory-map-container">
      {/* Toolbar */}
      <div className="regulatory-map-toolbar">
        <div className="toolbar-left">
          <button 
            className="toolbar-button"
            onClick={toggleControlPanel}
            title={controlPanelOpen ? "Hide Control Panel" : "Show Control Panel"}
          >
            {controlPanelOpen ? "◀" : "▶"}
          </button>
          
          <div className="view-mode-selector">
            <select 
              value={viewMode} 
              onChange={(e) => handleViewModeChange(e.target.value)}
              title="Select View Mode"
            >
              <option value={ViewModes.GRAPH}>Graph View</option>
              <option value={ViewModes.HIERARCHICAL}>Hierarchical View</option>
              <option value={ViewModes.JURISDICTIONAL}>Jurisdictional View</option>
              <option value={ViewModes.IMPACT}>Impact View</option>
              <option value={ViewModes.TIMELINE}>Timeline View</option>
            </select>
          </div>
          
          <div className="search-box">
            <input 
              type="text" 
              placeholder="Search..." 
              onChange={(e) => {
                const searchValue = e.target.value.trim();
                if (searchValue) {
                  // Add or update search filter
                  const searchFilter = {
                    id: 'search',
                    type: 'search',
                    value: searchValue,
                    enabled: true
                  };
                  
                  setFilters(prev => {
                    const filtered = prev.filter(f => f.id !== 'search');
                    return [...filtered, searchFilter];
                  });
                } else {
                  // Remove search filter if search box is empty
                  setFilters(prev => prev.filter(f => f.id !== 'search'));
                }
              }}
            />
          </div>
        </div>
        
        <div className="toolbar-center">
          <h2 className="map-title">{config.title || "Regulatory Map"}</h2>
        </div>
        
        <div className="toolbar-right">
          <button 
            className="toolbar-button"
            onClick={handleSaveView}
            title="Save Current View"
          >
            Save View
          </button>
          
          <div className="toolbar-dropdown">
            <button className="toolbar-button">Export</button>
            <div className="dropdown-content">
              <button onClick={() => handleExportImage('png')}>Export as PNG</button>
              <button onClick={() => handleExportImage('svg')}>Export as SVG</button>
              <button onClick={() => handleGenerateReport('compliance')}>Compliance Report</button>
              <button onClick={() => handleGenerateReport('impact')}>Impact Report</button>
            </div>
          </div>
          
          <button 
            className="toolbar-button"
            onClick={toggleDetailPanel}
            title={detailPanelOpen ? "Hide Detail Panel" : "Show Detail Panel"}
          >
            {detailPanelOpen ? "▶" : "◀"}
          </button>
        </div>
      </div>
      
      <div className="regulatory-map-content">
        {/* Control Panel */}
        {controlPanelOpen && (
          <RegulatoryMapControlPanel
            filters={filters}
            onFilterChange={handleFilterChange}
            config={config}
            onConfigChange={handleConfigChange}
            onLoadView={handleLoadView}
            data={data}
          />
        )}
        
        {/* Main Visualization */}
        <div className="regulatory-map-visualization-container">
          {isLoading && (
            <div className="regulatory-map-loading">
              <div className="regulatory-map-loading-spinner"></div>
            </div>
          )}
          
          <RegulatoryMapVisualization
            data={filteredData()}
            config={{
              ...config,
              viewMode,
              initialViewport: viewport
            }}
            onSelectionChange={handleSelectionChange}
            onViewportChange={handleViewportChange}
          />
        </div>
        
        {/* Detail Panel */}
        {detailPanelOpen && (
          <RegulatoryMapDetailPanel
            selectedEntities={getSelectedEntitiesData()}
            onClose={toggleDetailPanel}
          />
        )}
      </div>
      
      {/* Status Bar */}
      <div className="regulatory-map-status-bar">
        <div className="status-left">
          {data && `${filteredData().nodes.length} nodes, ${filteredData().edges.length} edges`}
        </div>
        <div className="status-center">
          {selectedEntities.nodeIds.length > 0 && 
            `${selectedEntities.nodeIds.length} node(s) selected`}
          {selectedEntities.edgeIds.length > 0 && 
            `${selectedEntities.nodeIds.length > 0 ? ', ' : ''}${selectedEntities.edgeIds.length} edge(s) selected`}
        </div>
        <div className="status-right">
          {`Zoom: ${Math.round(viewport.zoom * 100)}%`}
        </div>
      </div>
    </div>
  );
};

RegulatoryMapContainer.propTypes = {
  /**
   * The graph data to visualize
   */
  data: PropTypes.shape({
    nodes: PropTypes.array.isRequired,
    edges: PropTypes.array.isRequired
  }).isRequired,
  
  /**
   * Initial configuration for the visualization
   */
  initialConfig: PropTypes.object,
  
  /**
   * Callback when a view is saved
   */
  onSaveView: PropTypes.func,
  
  /**
   * Callback to load a saved view
   */
  onLoadView: PropTypes.func,
  
  /**
   * Callback to export the visualization as an image
   */
  onExportImage: PropTypes.func,
  
  /**
   * Callback to generate a report
   */
  onGenerateReport: PropTypes.func
};

RegulatoryMapContainer.defaultProps = {
  initialConfig: {
    showLabels: true,
    labelSize: 10,
    labelColor: '#000',
    layout: {
      type: 'force',
      linkDistance: 100,
      chargeStrength: -300,
      gravity: 0.1
    },
    zoomRange: [0.1, 4],
    colorScheme: ColorSchemes.STANDARD,
    viewMode: ViewModes.GRAPH
  }
};

export default RegulatoryMapContainer;
