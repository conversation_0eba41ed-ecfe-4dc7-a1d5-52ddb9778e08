import { NodeTypes, EdgeTypes, ComplianceStatus } from '../../constants/regulatoryMap';

/**
 * Get the color for a node based on its type and status
 * @param {Object} node - The node object
 * @param {Object} config - The visualization configuration
 * @returns {string} - The color as a hex or RGB string
 */
export const getNodeColor = (node, config) => {
  // If node has a custom color in its style, use that
  if (node.style?.color) {
    return node.style.color;
  }
  
  // If config has custom node styles, check those
  if (config?.nodeStyles?.[node.type]?.color) {
    return config.nodeStyles[node.type].color;
  }
  
  // Default color scheme based on compliance status
  const statusColors = config?.colorScheme?.status || {
    [ComplianceStatus.COMPLIANT]: '#4CAF50',        // Green
    [ComplianceStatus.PARTIALLY_COMPLIANT]: '#FFC107', // Yellow
    [ComplianceStatus.NON_COMPLIANT]: '#F44336',    // Red
    [ComplianceStatus.NOT_APPLICABLE]: '#9E9E9E',   // Gray
    [ComplianceStatus.UNKNOWN]: '#78909C'           // Blue-gray
  };
  
  // If node has a status, use the status color
  if (node.status && statusColors[node.status]) {
    return statusColors[node.status];
  }
  
  // Default colors by node type
  const typeColors = config?.colorScheme?.types || {
    [NodeTypes.REGULATION]: '#1976D2',      // Blue
    [NodeTypes.REQUIREMENT]: '#42A5F5',     // Light blue
    [NodeTypes.CONTROL]: '#7E57C2',         // Purple
    [NodeTypes.BUSINESS_PROCESS]: '#26A69A', // Teal
    [NodeTypes.SYSTEM]: '#FF7043',          // Deep orange
    [NodeTypes.DOCUMENT]: '#78909C'         // Blue-gray
  };
  
  return typeColors[node.type] || '#9E9E9E'; // Default to gray if no match
};

/**
 * Get the size for a node based on its type and importance
 * @param {Object} node - The node object
 * @param {Object} config - The visualization configuration
 * @returns {number} - The radius of the node
 */
export const getNodeSize = (node, config) => {
  // If node has a custom size in its style, use that
  if (node.style?.size) {
    return node.style.size;
  }
  
  // If config has custom node styles, check those
  if (config?.nodeStyles?.[node.type]?.size) {
    return config.nodeStyles[node.type].size;
  }
  
  // Default sizes by node type
  const typeSizes = {
    [NodeTypes.REGULATION]: 20,
    [NodeTypes.REQUIREMENT]: 12,
    [NodeTypes.CONTROL]: 10,
    [NodeTypes.BUSINESS_PROCESS]: 15,
    [NodeTypes.SYSTEM]: 15,
    [NodeTypes.DOCUMENT]: 8
  };
  
  // Base size from type
  let size = typeSizes[node.type] || 10;
  
  // Adjust size based on importance if available
  if (node.metadata?.importance) {
    const importanceFactor = parseFloat(node.metadata.importance);
    if (!isNaN(importanceFactor) && importanceFactor >= 0 && importanceFactor <= 1) {
      size = size * (0.7 + (importanceFactor * 0.6)); // Scale between 70% and 130% of base size
    }
  }
  
  return size;
};

/**
 * Get the shape for a node based on its type
 * @param {Object} node - The node object
 * @param {Object} config - The visualization configuration
 * @returns {string} - The shape name ('circle', 'square', 'diamond', 'hexagon')
 */
export const getNodeShape = (node, config) => {
  // If node has a custom shape in its style, use that
  if (node.style?.shape) {
    return node.style.shape;
  }
  
  // If config has custom node styles, check those
  if (config?.nodeStyles?.[node.type]?.shape) {
    return config.nodeStyles[node.type].shape;
  }
  
  // Default shapes by node type
  const typeShapes = {
    [NodeTypes.REGULATION]: 'circle',
    [NodeTypes.REQUIREMENT]: 'circle',
    [NodeTypes.CONTROL]: 'square',
    [NodeTypes.BUSINESS_PROCESS]: 'diamond',
    [NodeTypes.SYSTEM]: 'hexagon',
    [NodeTypes.DOCUMENT]: 'square'
  };
  
  return typeShapes[node.type] || 'circle';
};

/**
 * Get the style for an edge based on its type
 * @param {Object} edge - The edge object
 * @param {Object} config - The visualization configuration
 * @returns {Object} - The edge style object with color, width, and pattern
 */
export const getEdgeStyle = (edge, config) => {
  // If edge has a custom style, use that
  if (edge.style) {
    return {
      color: edge.style.color || '#666',
      width: edge.style.width || 1,
      pattern: edge.style.pattern || null
    };
  }
  
  // If config has custom edge styles, check those
  if (config?.edgeStyles?.[edge.type]) {
    return {
      color: config.edgeStyles[edge.type].color || '#666',
      width: config.edgeStyles[edge.type].width || 1,
      pattern: config.edgeStyles[edge.type].pattern || null
    };
  }
  
  // Default styles by edge type
  const typeStyles = {
    [EdgeTypes.CONTAINS]: {
      color: '#666',
      width: 1.5,
      pattern: null
    },
    [EdgeTypes.REFERENCES]: {
      color: '#999',
      width: 1,
      pattern: '5,5'
    },
    [EdgeTypes.IMPLEMENTS]: {
      color: '#7E57C2',
      width: 1.5,
      pattern: null
    },
    [EdgeTypes.IMPACTS]: {
      color: '#1976D2',
      width: 1.5,
      pattern: null
    },
    [EdgeTypes.CONFLICTS]: {
      color: '#F44336',
      width: 1.5,
      pattern: '2,2'
    },
    [EdgeTypes.SUPERSEDES]: {
      color: '#FF9800',
      width: 1.5,
      pattern: null
    }
  };
  
  return typeStyles[edge.type] || { color: '#666', width: 1, pattern: null };
};

/**
 * Calculate the path for an edge with a curved line
 * @param {Object} source - The source node
 * @param {Object} target - The target node
 * @returns {string} - The SVG path string
 */
export const calculateEdgePath = (source, target) => {
  // Calculate the midpoint
  const midX = (source.x + target.x) / 2;
  const midY = (source.y + target.y) / 2;
  
  // Calculate the normal vector
  const dx = target.x - source.x;
  const dy = target.y - source.y;
  const length = Math.sqrt(dx * dx + dy * dy);
  
  // Default to straight line for very short edges
  if (length < 30) {
    return `M${source.x},${source.y} L${target.x},${target.y}`;
  }
  
  // Calculate control point offset (perpendicular to the line)
  const offset = length / 5; // Adjust this value to control the curve
  const nx = -dy / length;
  const ny = dx / length;
  
  const controlX = midX + offset * nx;
  const controlY = midY + offset * ny;
  
  return `M${source.x},${source.y} Q${controlX},${controlY} ${target.x},${target.y}`;
};

/**
 * Calculate the position for an edge label
 * @param {Object} source - The source node
 * @param {Object} target - The target node
 * @returns {Object} - The position {x, y} for the label
 */
export const calculateEdgeLabelPosition = (source, target) => {
  // Calculate the midpoint
  const midX = (source.x + target.x) / 2;
  const midY = (source.y + target.y) / 2;
  
  // Calculate the normal vector
  const dx = target.x - source.x;
  const dy = target.y - source.y;
  const length = Math.sqrt(dx * dx + dy * dy);
  
  // Calculate label offset (perpendicular to the line)
  const offset = 10; // Adjust this value to control the label distance from the line
  const nx = -dy / length;
  const ny = dx / length;
  
  return {
    x: midX + offset * nx,
    y: midY + offset * ny
  };
};
