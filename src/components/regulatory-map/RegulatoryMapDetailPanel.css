.regulatory-map-detail-panel {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.detail-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.detail-panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.close-button {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.close-button:hover {
  background-color: #f0f0f0;
  color: #333;
}

.detail-panel-tabs {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
}

.tab-button {
  flex: 1;
  padding: 12px 8px;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab-button:hover {
  background-color: #f5f5f5;
  color: #333;
}

.tab-button.active {
  color: #1a73e8;
  border-bottom-color: #1a73e8;
}

.detail-panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.detail-panel-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 12px 16px;
  border-top: 1px solid #e0e0e0;
}

.action-button {
  padding: 8px 16px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover {
  background-color: #e0e0e0;
}

.action-button.primary {
  background-color: #1a73e8;
  color: white;
  border-color: #1a73e8;
}

.action-button.primary:hover {
  background-color: #1669d9;
  border-color: #1669d9;
}

/* Empty selection */
.empty-selection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
  color: #666;
  font-style: italic;
  text-align: center;
}

/* Entity details */
.entity-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.entity-title {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  color: #333;
  word-break: break-word;
}

.entity-type {
  font-size: 14px;
  color: #666;
  margin-top: -12px;
}

.entity-status {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.status-compliant {
  background-color: #4CAF50;
}

.status-partially-compliant {
  background-color: #FFC107;
  color: #333;
}

.status-non-compliant {
  background-color: #F44336;
}

.status-not-applicable {
  background-color: #9E9E9E;
}

.status-unknown {
  background-color: #78909C;
}

.entity-description {
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

.entity-description p {
  margin: 0;
}

.entity-properties {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.property-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.property-label {
  font-size: 12px;
  font-weight: 500;
  color: #666;
}

.property-value {
  font-size: 14px;
  color: #333;
  word-break: break-word;
}

/* Related entities */
.related-entities {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.related-entities h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.related-entities-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.related-entity-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid #1a73e8;
}

.related-entity-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.related-entity-type {
  font-size: 12px;
  color: #666;
}

.related-entity-relationship {
  font-size: 12px;
  color: #1a73e8;
  font-weight: 500;
  margin-top: 4px;
}

/* Annotations */
.annotations-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.annotations-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.annotations-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.add-annotation-button {
  padding: 6px 12px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-annotation-button:hover {
  background-color: #e0e0e0;
}

.annotations-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.empty-annotations {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 16px;
  color: #666;
  text-align: center;
}

.empty-annotations p {
  margin: 4px 0;
}

/* Impact assessment */
.impact-assessment {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.impact-assessment h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.impact-assessment h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.impact-metrics {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.impact-metric {
  flex: 1;
  min-width: 100px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  text-align: center;
}

.metric-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.impact-areas {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.impact-area-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.impact-area-name {
  font-size: 14px;
  color: #333;
}

.impact-area-level {
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 4px;
  color: white;
}

.impact-area-level.high {
  background-color: #F44336;
}

.impact-area-level.medium {
  background-color: #FFC107;
  color: #333;
}

.impact-area-level.low {
  background-color: #4CAF50;
}

.view-full-assessment {
  align-self: center;
  padding: 8px 16px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 8px;
}

.view-full-assessment:hover {
  background-color: #e0e0e0;
}

/* Dark theme */
.dark-theme .detail-panel-header,
.dark-theme .detail-panel-tabs,
.dark-theme .detail-panel-footer {
  border-color: #444;
}

.dark-theme .detail-panel-header h3,
.dark-theme .entity-title,
.dark-theme .entity-description,
.dark-theme .property-value,
.dark-theme .related-entities h3,
.dark-theme .related-entity-name,
.dark-theme .annotations-header h3,
.dark-theme .impact-assessment h3,
.dark-theme .impact-assessment h4,
.dark-theme .metric-value,
.dark-theme .impact-area-name {
  color: #e0e0e0;
}

.dark-theme .entity-type,
.dark-theme .property-label,
.dark-theme .related-entity-type,
.dark-theme .metric-label,
.dark-theme .empty-selection,
.dark-theme .empty-annotations {
  color: #aaa;
}

.dark-theme .close-button {
  color: #aaa;
}

.dark-theme .close-button:hover {
  background-color: #444;
  color: #e0e0e0;
}

.dark-theme .tab-button {
  color: #aaa;
}

.dark-theme .tab-button:hover {
  background-color: #444;
  color: #e0e0e0;
}

.dark-theme .tab-button.active {
  color: #64B5F6;
  border-bottom-color: #64B5F6;
}

.dark-theme .action-button,
.dark-theme .add-annotation-button,
.dark-theme .view-full-assessment {
  background-color: #444;
  border-color: #555;
  color: #e0e0e0;
}

.dark-theme .action-button:hover,
.dark-theme .add-annotation-button:hover,
.dark-theme .view-full-assessment:hover {
  background-color: #555;
}

.dark-theme .action-button.primary {
  background-color: #1976D2;
  border-color: #1976D2;
  color: white;
}

.dark-theme .action-button.primary:hover {
  background-color: #1565C0;
  border-color: #1565C0;
}

.dark-theme .related-entity-item,
.dark-theme .impact-metric,
.dark-theme .impact-area-item {
  background-color: #333;
}

.dark-theme .related-entity-relationship {
  color: #64B5F6;
}
