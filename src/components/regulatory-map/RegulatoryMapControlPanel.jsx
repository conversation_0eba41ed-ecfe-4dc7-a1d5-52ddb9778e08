import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { NodeTypes, EdgeTypes, ComplianceStatus, ColorSchemes } from '../../constants/regulatoryMap';
import './RegulatoryMapControlPanel.css';

/**
 * RegulatoryMapControlPanel Component
 * 
 * Control panel for filtering and configuring the regulatory map visualization
 */
const RegulatoryMapControlPanel = ({
  filters,
  onFilterChange,
  config,
  onConfigChange,
  onLoadView,
  data
}) => {
  // State for accordion sections
  const [expandedSections, setExpandedSections] = useState({
    entityTypes: true,
    complianceStatus: true,
    jurisdictions: false,
    categories: false,
    visualization: false,
    savedViews: false
  });
  
  // Toggle accordion section
  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };
  
  // Extract unique values from data
  const getUniqueValues = (field) => {
    if (!data || !data.nodes) return [];
    
    const values = new Set();
    
    data.nodes.forEach(node => {
      if (field === 'jurisdiction' || field === 'category') {
        const value = node.metadata?.[field];
        if (value) {
          if (Array.isArray(value)) {
            value.forEach(v => values.add(v));
          } else {
            values.add(value);
          }
        }
      }
    });
    
    return Array.from(values).sort();
  };
  
  // Handle filter changes
  const handleFilterChange = (filterId, values, enabled = true) => {
    // Find existing filter
    const existingFilterIndex = filters.findIndex(f => f.id === filterId);
    
    if (existingFilterIndex >= 0) {
      // Update existing filter
      const updatedFilters = [...filters];
      updatedFilters[existingFilterIndex] = {
        ...updatedFilters[existingFilterIndex],
        values,
        enabled
      };
      
      onFilterChange(updatedFilters);
    } else {
      // Create new filter
      const newFilter = {
        id: filterId,
        type: filterId,
        values,
        enabled
      };
      
      onFilterChange([...filters, newFilter]);
    }
  };
  
  // Get current filter values
  const getFilterValues = (filterId) => {
    const filter = filters.find(f => f.id === filterId);
    return filter ? filter.values : [];
  };
  
  // Check if filter is enabled
  const isFilterEnabled = (filterId) => {
    const filter = filters.find(f => f.id === filterId);
    return filter ? filter.enabled : true;
  };
  
  // Handle config changes
  const handleConfigChange = (key, value) => {
    onConfigChange({ [key]: value });
  };
  
  // Get unique jurisdictions and categories
  const jurisdictions = getUniqueValues('jurisdiction');
  const categories = getUniqueValues('category');
  
  return (
    <div className="regulatory-map-control-panel">
      <div className="control-panel-header">
        <h3>Filters & Settings</h3>
      </div>
      
      <div className="control-panel-content">
        {/* Entity Types Section */}
        <div className="control-panel-section">
          <div 
            className="section-header" 
            onClick={() => toggleSection('entityTypes')}
          >
            <h4>Entity Types</h4>
            <span className={`expand-icon ${expandedSections.entityTypes ? 'expanded' : ''}`}>
              ▼
            </span>
          </div>
          
          {expandedSections.entityTypes && (
            <div className="section-content">
              <div className="filter-group">
                {Object.values(NodeTypes).map(type => (
                  <div className="filter-item" key={type}>
                    <label>
                      <input 
                        type="checkbox" 
                        checked={getFilterValues('type').includes(type) || getFilterValues('type').length === 0}
                        onChange={(e) => {
                          const currentValues = getFilterValues('type');
                          let newValues;
                          
                          if (e.target.checked) {
                            // Add type to filter
                            newValues = currentValues.length === 0 
                              ? Object.values(NodeTypes) // If none selected, select all
                              : [...currentValues, type];
                          } else {
                            // Remove type from filter
                            newValues = currentValues.filter(t => t !== type);
                            
                            // If all are unchecked, disable the filter
                            if (newValues.length === 0) {
                              handleFilterChange('type', [], false);
                              return;
                            }
                          }
                          
                          handleFilterChange('type', newValues);
                        }}
                      />
                      {type.replace('_', ' ')}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
        
        {/* Compliance Status Section */}
        <div className="control-panel-section">
          <div 
            className="section-header" 
            onClick={() => toggleSection('complianceStatus')}
          >
            <h4>Compliance Status</h4>
            <span className={`expand-icon ${expandedSections.complianceStatus ? 'expanded' : ''}`}>
              ▼
            </span>
          </div>
          
          {expandedSections.complianceStatus && (
            <div className="section-content">
              <div className="filter-group">
                {Object.values(ComplianceStatus).map(status => (
                  <div className="filter-item" key={status}>
                    <label>
                      <input 
                        type="checkbox" 
                        checked={getFilterValues('status').includes(status) || getFilterValues('status').length === 0}
                        onChange={(e) => {
                          const currentValues = getFilterValues('status');
                          let newValues;
                          
                          if (e.target.checked) {
                            // Add status to filter
                            newValues = currentValues.length === 0 
                              ? Object.values(ComplianceStatus) // If none selected, select all
                              : [...currentValues, status];
                          } else {
                            // Remove status from filter
                            newValues = currentValues.filter(s => s !== status);
                            
                            // If all are unchecked, disable the filter
                            if (newValues.length === 0) {
                              handleFilterChange('status', [], false);
                              return;
                            }
                          }
                          
                          handleFilterChange('status', newValues);
                        }}
                      />
                      <span className={`status-indicator status-${status.toLowerCase()}`}></span>
                      {status.replace('_', ' ')}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
        
        {/* Jurisdictions Section */}
        <div className="control-panel-section">
          <div 
            className="section-header" 
            onClick={() => toggleSection('jurisdictions')}
          >
            <h4>Jurisdictions</h4>
            <span className={`expand-icon ${expandedSections.jurisdictions ? 'expanded' : ''}`}>
              ▼
            </span>
          </div>
          
          {expandedSections.jurisdictions && (
            <div className="section-content">
              {jurisdictions.length > 0 ? (
                <div className="filter-group">
                  {jurisdictions.map(jurisdiction => (
                    <div className="filter-item" key={jurisdiction}>
                      <label>
                        <input 
                          type="checkbox" 
                          checked={getFilterValues('jurisdiction').includes(jurisdiction) || getFilterValues('jurisdiction').length === 0}
                          onChange={(e) => {
                            const currentValues = getFilterValues('jurisdiction');
                            let newValues;
                            
                            if (e.target.checked) {
                              // Add jurisdiction to filter
                              newValues = currentValues.length === 0 
                                ? jurisdictions // If none selected, select all
                                : [...currentValues, jurisdiction];
                            } else {
                              // Remove jurisdiction from filter
                              newValues = currentValues.filter(j => j !== jurisdiction);
                              
                              // If all are unchecked, disable the filter
                              if (newValues.length === 0) {
                                handleFilterChange('jurisdiction', [], false);
                                return;
                              }
                            }
                            
                            handleFilterChange('jurisdiction', newValues);
                          }}
                        />
                        {jurisdiction}
                      </label>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="empty-message">No jurisdiction data available</div>
              )}
            </div>
          )}
        </div>
        
        {/* Categories Section */}
        <div className="control-panel-section">
          <div 
            className="section-header" 
            onClick={() => toggleSection('categories')}
          >
            <h4>Categories</h4>
            <span className={`expand-icon ${expandedSections.categories ? 'expanded' : ''}`}>
              ▼
            </span>
          </div>
          
          {expandedSections.categories && (
            <div className="section-content">
              {categories.length > 0 ? (
                <div className="filter-group">
                  {categories.map(category => (
                    <div className="filter-item" key={category}>
                      <label>
                        <input 
                          type="checkbox" 
                          checked={getFilterValues('category').includes(category) || getFilterValues('category').length === 0}
                          onChange={(e) => {
                            const currentValues = getFilterValues('category');
                            let newValues;
                            
                            if (e.target.checked) {
                              // Add category to filter
                              newValues = currentValues.length === 0 
                                ? categories // If none selected, select all
                                : [...currentValues, category];
                            } else {
                              // Remove category from filter
                              newValues = currentValues.filter(c => c !== category);
                              
                              // If all are unchecked, disable the filter
                              if (newValues.length === 0) {
                                handleFilterChange('category', [], false);
                                return;
                              }
                            }
                            
                            handleFilterChange('category', newValues);
                          }}
                        />
                        {category}
                      </label>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="empty-message">No category data available</div>
              )}
            </div>
          )}
        </div>
        
        {/* Visualization Settings Section */}
        <div className="control-panel-section">
          <div 
            className="section-header" 
            onClick={() => toggleSection('visualization')}
          >
            <h4>Visualization Settings</h4>
            <span className={`expand-icon ${expandedSections.visualization ? 'expanded' : ''}`}>
              ▼
            </span>
          </div>
          
          {expandedSections.visualization && (
            <div className="section-content">
              <div className="settings-group">
                <div className="settings-item">
                  <label>Show Labels</label>
                  <div className="toggle-switch">
                    <input 
                      type="checkbox" 
                      id="show-labels" 
                      checked={config.showLabels}
                      onChange={(e) => handleConfigChange('showLabels', e.target.checked)}
                    />
                    <label htmlFor="show-labels"></label>
                  </div>
                </div>
                
                <div className="settings-item">
                  <label>Label Size</label>
                  <input 
                    type="range" 
                    min="8" 
                    max="16" 
                    value={config.labelSize || 10}
                    onChange={(e) => handleConfigChange('labelSize', parseInt(e.target.value))}
                    disabled={!config.showLabels}
                  />
                  <span className="range-value">{config.labelSize || 10}px</span>
                </div>
                
                <div className="settings-item">
                  <label>Link Distance</label>
                  <input 
                    type="range" 
                    min="50" 
                    max="300" 
                    value={config.layout?.linkDistance || 100}
                    onChange={(e) => handleConfigChange('layout', {
                      ...config.layout,
                      linkDistance: parseInt(e.target.value)
                    })}
                  />
                  <span className="range-value">{config.layout?.linkDistance || 100}</span>
                </div>
                
                <div className="settings-item">
                  <label>Charge Strength</label>
                  <input 
                    type="range" 
                    min="-1000" 
                    max="-100" 
                    value={config.layout?.chargeStrength || -300}
                    onChange={(e) => handleConfigChange('layout', {
                      ...config.layout,
                      chargeStrength: parseInt(e.target.value)
                    })}
                  />
                  <span className="range-value">{config.layout?.chargeStrength || -300}</span>
                </div>
                
                <div className="settings-item">
                  <label>Color Scheme</label>
                  <select 
                    value={Object.keys(ColorSchemes).find(
                      key => ColorSchemes[key] === config.colorScheme
                    ) || 'STANDARD'}
                    onChange={(e) => handleConfigChange('colorScheme', ColorSchemes[e.target.value])}
                  >
                    <option value="STANDARD">Standard</option>
                    <option value="COLOR_BLIND_SAFE">Color Blind Safe</option>
                    <option value="HIGH_CONTRAST">High Contrast</option>
                  </select>
                </div>
                
                <div className="settings-item">
                  <label>Theme</label>
                  <select 
                    value={config.theme || 'light'}
                    onChange={(e) => handleConfigChange('theme', e.target.value)}
                  >
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                    <option value="auto">Auto (System)</option>
                  </select>
                </div>
              </div>
            </div>
          )}
        </div>
        
        {/* Saved Views Section */}
        <div className="control-panel-section">
          <div 
            className="section-header" 
            onClick={() => toggleSection('savedViews')}
          >
            <h4>Saved Views</h4>
            <span className={`expand-icon ${expandedSections.savedViews ? 'expanded' : ''}`}>
              ▼
            </span>
          </div>
          
          {expandedSections.savedViews && (
            <div className="section-content">
              {/* This would typically be populated from the server */}
              <div className="saved-views-list">
                <div className="saved-view-item" onClick={() => onLoadView('default')}>
                  <span className="view-name">Default View</span>
                  <span className="view-date">System</span>
                </div>
                <div className="saved-view-item" onClick={() => onLoadView('compliance-overview')}>
                  <span className="view-name">Compliance Overview</span>
                  <span className="view-date">System</span>
                </div>
                <div className="saved-view-item" onClick={() => onLoadView('impact-analysis')}>
                  <span className="view-name">Impact Analysis</span>
                  <span className="view-date">System</span>
                </div>
                {/* User saved views would be listed here */}
              </div>
            </div>
          )}
        </div>
      </div>
      
      <div className="control-panel-footer">
        <button 
          className="reset-button"
          onClick={() => {
            // Reset all filters
            onFilterChange([]);
            
            // Reset config to defaults
            onConfigChange({
              showLabels: true,
              labelSize: 10,
              layout: {
                type: 'force',
                linkDistance: 100,
                chargeStrength: -300,
                gravity: 0.1
              },
              colorScheme: ColorSchemes.STANDARD
            });
          }}
        >
          Reset All
        </button>
      </div>
    </div>
  );
};

RegulatoryMapControlPanel.propTypes = {
  /**
   * Current filters
   */
  filters: PropTypes.array,
  
  /**
   * Callback when filters change
   */
  onFilterChange: PropTypes.func.isRequired,
  
  /**
   * Current configuration
   */
  config: PropTypes.object,
  
  /**
   * Callback when configuration changes
   */
  onConfigChange: PropTypes.func.isRequired,
  
  /**
   * Callback to load a saved view
   */
  onLoadView: PropTypes.func,
  
  /**
   * The graph data
   */
  data: PropTypes.object
};

RegulatoryMapControlPanel.defaultProps = {
  filters: [],
  config: {}
};

export default RegulatoryMapControlPanel;
