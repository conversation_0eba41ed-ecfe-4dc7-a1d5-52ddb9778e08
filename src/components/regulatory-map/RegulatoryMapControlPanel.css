.regulatory-map-control-panel {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.control-panel-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.control-panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.control-panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.control-panel-footer {
  padding: 12px 16px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
}

.reset-button {
  padding: 6px 12px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reset-button:hover {
  background-color: #e0e0e0;
}

/* Section styling */
.control-panel-section {
  margin-bottom: 8px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  transition: background-color 0.2s ease;
}

.section-header:hover {
  background-color: #f1f3f4;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.expand-icon {
  font-size: 10px;
  transition: transform 0.2s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.section-content {
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
}

/* Filter group styling */
.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-item label {
  display: flex;
  align-items: center;
  font-size: 14px;
  cursor: pointer;
}

.filter-item input[type="checkbox"] {
  margin-right: 8px;
}

.status-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-compliant {
  background-color: #4CAF50;
}

.status-partially_compliant {
  background-color: #FFC107;
}

.status-non_compliant {
  background-color: #F44336;
}

.status-not_applicable {
  background-color: #9E9E9E;
}

.status-unknown {
  background-color: #78909C;
}

/* Settings group styling */
.settings-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.settings-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.settings-item label {
  font-size: 14px;
  font-weight: 500;
}

.settings-item input[type="range"] {
  width: 100%;
}

.range-value {
  font-size: 12px;
  color: #666;
  align-self: flex-end;
}

.settings-item select {
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  font-size: 14px;
}

/* Toggle switch styling */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-switch label {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;
}

.toggle-switch label:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

.toggle-switch input:checked + label {
  background-color: #1a73e8;
}

.toggle-switch input:checked + label:before {
  transform: translateX(20px);
}

/* Saved views styling */
.saved-views-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.saved-view-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.saved-view-item:hover {
  background-color: #e0e0e0;
}

.view-name {
  font-size: 14px;
  font-weight: 500;
}

.view-date {
  font-size: 12px;
  color: #666;
}

.empty-message {
  font-size: 14px;
  color: #666;
  font-style: italic;
  text-align: center;
  padding: 12px 0;
}

/* Dark theme */
.dark-theme .control-panel-header,
.dark-theme .control-panel-footer,
.dark-theme .section-header,
.dark-theme .section-content {
  border-color: #444;
}

.dark-theme .control-panel-header h3,
.dark-theme .section-header h4 {
  color: #e0e0e0;
}

.dark-theme .section-header {
  background-color: #333;
}

.dark-theme .section-header:hover {
  background-color: #444;
}

.dark-theme .reset-button {
  background-color: #444;
  border-color: #555;
  color: #e0e0e0;
}

.dark-theme .reset-button:hover {
  background-color: #555;
}

.dark-theme .filter-item label,
.dark-theme .settings-item label {
  color: #e0e0e0;
}

.dark-theme .range-value,
.dark-theme .view-date {
  color: #aaa;
}

.dark-theme .settings-item select {
  background-color: #444;
  border-color: #555;
  color: #e0e0e0;
}

.dark-theme .toggle-switch label {
  background-color: #555;
}

.dark-theme .saved-view-item {
  background-color: #333;
}

.dark-theme .saved-view-item:hover {
  background-color: #444;
}

.dark-theme .empty-message {
  color: #aaa;
}
