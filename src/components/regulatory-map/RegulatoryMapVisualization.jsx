import React, { useEffect, useRef, useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import * as d3 from 'd3';
import { useResizeObserver } from '../../hooks/useResizeObserver';
import { NodeTypes, EdgeTypes, ComplianceStatus } from '../../constants/regulatoryMap';
import { getNodeColor, getNodeSize, getNodeShape, getEdgeStyle } from './visualEncodings';
import './RegulatoryMapVisualization.css';

/**
 * RegulatoryMapVisualization Component
 * 
 * A React component that renders an interactive visualization of the regulatory map
 * using D3.js for the graph visualization.
 */
const RegulatoryMapVisualization = ({
  data,
  config,
  onNodeClick,
  onEdgeClick,
  onSelectionChange,
  onViewportChange
}) => {
  // Refs
  const containerRef = useRef(null);
  const svgRef = useRef(null);
  const dimensions = useResizeObserver(containerRef);
  
  // State
  const [nodes, setNodes] = useState([]);
  const [edges, setEdges] = useState([]);
  const [selectedNodes, setSelectedNodes] = useState([]);
  const [selectedEdges, setSelectedEdges] = useState([]);
  const [transform, setTransform] = useState({ x: 0, y: 0, k: 1 });
  const [simulation, setSimulation] = useState(null);
  
  // Process data when it changes
  useEffect(() => {
    if (!data) return;
    
    // Process nodes and edges from data
    const processedNodes = data.nodes.map(node => ({
      ...node,
      x: node.position?.x || Math.random() * 900,
      y: node.position?.y || Math.random() * 600,
      radius: getNodeSize(node, config),
      color: getNodeColor(node, config),
      shape: getNodeShape(node, config),
      selected: false
    }));
    
    const processedEdges = data.edges.map(edge => ({
      ...edge,
      style: getEdgeStyle(edge, config)
    }));
    
    setNodes(processedNodes);
    setEdges(processedEdges);
    
    // Initialize or update simulation
    if (dimensions) {
      initializeSimulation(processedNodes, processedEdges, dimensions);
    }
  }, [data, config, dimensions]);
  
  // Initialize force simulation
  const initializeSimulation = useCallback((nodes, edges, dimensions) => {
    if (!dimensions) return;
    
    // Create force simulation
    const sim = d3.forceSimulation(nodes)
      .force('link', d3.forceLink(edges)
        .id(d => d.id)
        .distance(config.layout?.linkDistance || 100)
      )
      .force('charge', d3.forceManyBody()
        .strength(config.layout?.chargeStrength || -300)
      )
      .force('center', d3.forceCenter(
        dimensions.width / 2,
        dimensions.height / 2
      ))
      .force('collision', d3.forceCollide()
        .radius(d => d.radius + 10)
      )
      .on('tick', () => {
        setNodes([...nodes]);
        setEdges([...edges]);
      });
    
    setSimulation(sim);
    
    return () => {
      sim.stop();
    };
  }, [config]);
  
  // Handle zoom behavior
  useEffect(() => {
    if (!svgRef.current || !dimensions) return;
    
    const svg = d3.select(svgRef.current);
    
    const zoomed = (event) => {
      const newTransform = event.transform;
      setTransform(newTransform);
      
      if (onViewportChange) {
        onViewportChange({
          center: {
            x: -newTransform.x / newTransform.k + dimensions.width / (2 * newTransform.k),
            y: -newTransform.y / newTransform.k + dimensions.height / (2 * newTransform.k)
          },
          zoom: newTransform.k
        });
      }
    };
    
    const zoom = d3.zoom()
      .scaleExtent([config.zoomRange?.[0] || 0.1, config.zoomRange?.[1] || 4])
      .on('zoom', zoomed);
    
    svg.call(zoom);
    
    // Set initial transform if provided in config
    if (config.initialViewport) {
      const { center, zoom } = config.initialViewport;
      const initialTransform = d3.zoomIdentity
        .translate(
          -center.x * zoom + dimensions.width / 2,
          -center.y * zoom + dimensions.height / 2
        )
        .scale(zoom);
      
      svg.call(zoom.transform, initialTransform);
    }
    
    return () => {
      svg.on('.zoom', null);
    };
  }, [dimensions, config, onViewportChange]);
  
  // Handle node selection
  const handleNodeClick = useCallback((node, event) => {
    event.stopPropagation();
    
    let newSelectedNodes;
    
    if (event.shiftKey) {
      // Multi-select with shift key
      newSelectedNodes = selectedNodes.includes(node.id)
        ? selectedNodes.filter(id => id !== node.id)
        : [...selectedNodes, node.id];
    } else {
      // Single select
      newSelectedNodes = [node.id];
    }
    
    setSelectedNodes(newSelectedNodes);
    
    if (onNodeClick) {
      onNodeClick(node, event);
    }
    
    if (onSelectionChange) {
      onSelectionChange({
        nodeIds: newSelectedNodes,
        edgeIds: selectedEdges
      });
    }
  }, [selectedNodes, selectedEdges, onNodeClick, onSelectionChange]);
  
  // Handle edge selection
  const handleEdgeClick = useCallback((edge, event) => {
    event.stopPropagation();
    
    let newSelectedEdges;
    
    if (event.shiftKey) {
      // Multi-select with shift key
      newSelectedEdges = selectedEdges.includes(edge.id)
        ? selectedEdges.filter(id => id !== edge.id)
        : [...selectedEdges, edge.id];
    } else {
      // Single select
      newSelectedEdges = [edge.id];
    }
    
    setSelectedEdges(newSelectedEdges);
    
    if (onEdgeClick) {
      onEdgeClick(edge, event);
    }
    
    if (onSelectionChange) {
      onSelectionChange({
        nodeIds: selectedNodes,
        edgeIds: newSelectedEdges
      });
    }
  }, [selectedNodes, selectedEdges, onEdgeClick, onSelectionChange]);
  
  // Handle background click to clear selection
  const handleBackgroundClick = useCallback(() => {
    setSelectedNodes([]);
    setSelectedEdges([]);
    
    if (onSelectionChange) {
      onSelectionChange({
        nodeIds: [],
        edgeIds: []
      });
    }
  }, [onSelectionChange]);
  
  // Render the visualization
  return (
    <div 
      ref={containerRef} 
      className="regulatory-map-container"
      data-testid="regulatory-map-container"
    >
      {dimensions && (
        <svg
          ref={svgRef}
          width={dimensions.width}
          height={dimensions.height}
          className="regulatory-map-svg"
          onClick={handleBackgroundClick}
        >
          <g transform={`translate(${transform.x},${transform.y}) scale(${transform.k})`}>
            {/* Render edges */}
            <g className="edges">
              {edges.map(edge => (
                <line
                  key={edge.id}
                  x1={nodes.find(n => n.id === edge.source)?.x || 0}
                  y1={nodes.find(n => n.id === edge.source)?.y || 0}
                  x2={nodes.find(n => n.id === edge.target)?.x || 0}
                  y2={nodes.find(n => n.id === edge.target)?.y || 0}
                  stroke={edge.style.color}
                  strokeWidth={edge.style.width}
                  strokeDasharray={edge.style.pattern}
                  opacity={selectedEdges.length > 0 && !selectedEdges.includes(edge.id) ? 0.3 : 1}
                  className={`edge ${selectedEdges.includes(edge.id) ? 'selected' : ''}`}
                  onClick={(e) => handleEdgeClick(edge, e)}
                />
              ))}
            </g>
            
            {/* Render nodes */}
            <g className="nodes">
              {nodes.map(node => {
                // Determine if node is selected
                const isSelected = selectedNodes.includes(node.id);
                
                // Determine opacity based on selection state
                const opacity = selectedNodes.length > 0 && !isSelected ? 0.3 : 1;
                
                return (
                  <g
                    key={node.id}
                    transform={`translate(${node.x},${node.y})`}
                    onClick={(e) => handleNodeClick(node, e)}
                    className={`node ${isSelected ? 'selected' : ''} node-type-${node.type}`}
                  >
                    {/* Render appropriate shape based on node type */}
                    {node.shape === 'circle' && (
                      <circle
                        r={node.radius}
                        fill={node.color}
                        stroke={isSelected ? '#000' : '#666'}
                        strokeWidth={isSelected ? 2 : 1}
                        opacity={opacity}
                      />
                    )}
                    
                    {node.shape === 'square' && (
                      <rect
                        x={-node.radius}
                        y={-node.radius}
                        width={node.radius * 2}
                        height={node.radius * 2}
                        fill={node.color}
                        stroke={isSelected ? '#000' : '#666'}
                        strokeWidth={isSelected ? 2 : 1}
                        opacity={opacity}
                      />
                    )}
                    
                    {node.shape === 'diamond' && (
                      <polygon
                        points={`0,${-node.radius} ${node.radius},0 0,${node.radius} ${-node.radius},0`}
                        fill={node.color}
                        stroke={isSelected ? '#000' : '#666'}
                        strokeWidth={isSelected ? 2 : 1}
                        opacity={opacity}
                      />
                    )}
                    
                    {node.shape === 'hexagon' && (
                      <polygon
                        points={`${node.radius * 0.866},${node.radius / 2} ${node.radius * 0.866},${-node.radius / 2} 0,${-node.radius} ${-node.radius * 0.866},${-node.radius / 2} ${-node.radius * 0.866},${node.radius / 2} 0,${node.radius}`}
                        fill={node.color}
                        stroke={isSelected ? '#000' : '#666'}
                        strokeWidth={isSelected ? 2 : 1}
                        opacity={opacity}
                      />
                    )}
                    
                    {/* Render label if configured to show */}
                    {config.showLabels && (
                      <text
                        dy=".35em"
                        textAnchor="middle"
                        fontSize={config.labelSize || 10}
                        fill={config.labelColor || '#000'}
                        opacity={opacity}
                        className="node-label"
                      >
                        {node.name.length > 20 ? node.name.substring(0, 17) + '...' : node.name}
                      </text>
                    )}
                  </g>
                );
              })}
            </g>
          </g>
        </svg>
      )}
    </div>
  );
};

RegulatoryMapVisualization.propTypes = {
  /**
   * The graph data to visualize
   */
  data: PropTypes.shape({
    nodes: PropTypes.arrayOf(PropTypes.shape({
      id: PropTypes.string.isRequired,
      type: PropTypes.oneOf(Object.values(NodeTypes)).isRequired,
      name: PropTypes.string.isRequired,
      description: PropTypes.string,
      status: PropTypes.oneOf(Object.values(ComplianceStatus)),
      metadata: PropTypes.object,
      position: PropTypes.shape({
        x: PropTypes.number,
        y: PropTypes.number,
        fixed: PropTypes.bool
      }),
      style: PropTypes.shape({
        color: PropTypes.string,
        size: PropTypes.number,
        icon: PropTypes.string,
        border: PropTypes.string
      }),
      visibility: PropTypes.shape({
        visible: PropTypes.bool,
        filtered: PropTypes.bool,
        faded: PropTypes.bool
      })
    })).isRequired,
    edges: PropTypes.arrayOf(PropTypes.shape({
      id: PropTypes.string.isRequired,
      source: PropTypes.string.isRequired,
      target: PropTypes.string.isRequired,
      type: PropTypes.oneOf(Object.values(EdgeTypes)).isRequired,
      label: PropTypes.string,
      metadata: PropTypes.object,
      style: PropTypes.shape({
        color: PropTypes.string,
        width: PropTypes.number,
        pattern: PropTypes.string
      }),
      visibility: PropTypes.shape({
        visible: PropTypes.bool,
        filtered: PropTypes.bool,
        faded: PropTypes.bool
      })
    })).isRequired
  }).isRequired,
  
  /**
   * Configuration options for the visualization
   */
  config: PropTypes.shape({
    // Rendering options
    showLabels: PropTypes.bool,
    labelSize: PropTypes.number,
    labelColor: PropTypes.string,
    
    // Layout options
    layout: PropTypes.shape({
      type: PropTypes.string,
      linkDistance: PropTypes.number,
      chargeStrength: PropTypes.number,
      gravity: PropTypes.number
    }),
    
    // Zoom options
    zoomRange: PropTypes.arrayOf(PropTypes.number),
    initialViewport: PropTypes.shape({
      center: PropTypes.shape({
        x: PropTypes.number,
        y: PropTypes.number
      }),
      zoom: PropTypes.number
    }),
    
    // Visual encoding options
    nodeStyles: PropTypes.object,
    edgeStyles: PropTypes.object,
    colorScheme: PropTypes.object
  }),
  
  /**
   * Callback when a node is clicked
   */
  onNodeClick: PropTypes.func,
  
  /**
   * Callback when an edge is clicked
   */
  onEdgeClick: PropTypes.func,
  
  /**
   * Callback when the selection changes
   */
  onSelectionChange: PropTypes.func,
  
  /**
   * Callback when the viewport changes (pan/zoom)
   */
  onViewportChange: PropTypes.func
};

RegulatoryMapVisualization.defaultProps = {
  config: {
    showLabels: true,
    labelSize: 10,
    labelColor: '#000',
    layout: {
      type: 'force',
      linkDistance: 100,
      chargeStrength: -300,
      gravity: 0.1
    },
    zoomRange: [0.1, 4]
  }
};

export default RegulatoryMapVisualization;
