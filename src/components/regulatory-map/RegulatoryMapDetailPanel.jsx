import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { NodeTypes, EdgeTypes, ComplianceStatus } from '../../constants/regulatoryMap';
import './RegulatoryMapDetailPanel.css';

/**
 * RegulatoryMapDetailPanel Component
 * 
 * Detail panel for displaying information about selected entities
 */
const RegulatoryMapDetailPanel = ({ selectedEntities, onClose }) => {
  // State for active tab
  const [activeTab, setActiveTab] = useState('details');
  
  // Get selected node and edge
  const selectedNode = selectedEntities.nodes?.[0] || null;
  const selectedEdge = selectedEntities.edges?.[0] || null;
  
  // Handle tab change
  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };
  
  // Format date string
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString(undefined, {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (e) {
      return dateString;
    }
  };
  
  // Get status label and class
  const getStatusInfo = (status) => {
    const statusMap = {
      [ComplianceStatus.COMPLIANT]: { label: 'Compliant', className: 'status-compliant' },
      [ComplianceStatus.PARTIALLY_COMPLIANT]: { label: 'Partially Compliant', className: 'status-partially-compliant' },
      [ComplianceStatus.NON_COMPLIANT]: { label: 'Non-Compliant', className: 'status-non-compliant' },
      [ComplianceStatus.NOT_APPLICABLE]: { label: 'Not Applicable', className: 'status-not-applicable' },
      [ComplianceStatus.UNKNOWN]: { label: 'Unknown', className: 'status-unknown' }
    };
    
    return statusMap[status] || { label: 'Unknown', className: 'status-unknown' };
  };
  
  // Get type label
  const getTypeLabel = (type) => {
    return type.replace('_', ' ');
  };
  
  // Render node details
  const renderNodeDetails = () => {
    if (!selectedNode) {
      return (
        <div className="empty-selection">
          <p>Select a node to view details</p>
        </div>
      );
    }
    
    const statusInfo = getStatusInfo(selectedNode.status);
    
    return (
      <div className="entity-details">
        <h3 className="entity-title">{selectedNode.name}</h3>
        
        <div className="entity-type">
          {getTypeLabel(selectedNode.type)}
        </div>
        
        {selectedNode.status && (
          <div className={`entity-status ${statusInfo.className}`}>
            {statusInfo.label}
          </div>
        )}
        
        {selectedNode.description && (
          <div className="entity-description">
            <p>{selectedNode.description}</p>
          </div>
        )}
        
        <div className="entity-properties">
          {selectedNode.type === NodeTypes.REGULATION && (
            <>
              {selectedNode.metadata?.regulatoryAuthority && (
                <div className="property-item">
                  <div className="property-label">Authority</div>
                  <div className="property-value">{selectedNode.metadata.regulatoryAuthority}</div>
                </div>
              )}
              
              {selectedNode.metadata?.jurisdiction && (
                <div className="property-item">
                  <div className="property-label">Jurisdiction</div>
                  <div className="property-value">{selectedNode.metadata.jurisdiction}</div>
                </div>
              )}
              
              {selectedNode.metadata?.effectiveDate && (
                <div className="property-item">
                  <div className="property-label">Effective Date</div>
                  <div className="property-value">{formatDate(selectedNode.metadata.effectiveDate)}</div>
                </div>
              )}
              
              {selectedNode.metadata?.category && (
                <div className="property-item">
                  <div className="property-label">Category</div>
                  <div className="property-value">
                    {Array.isArray(selectedNode.metadata.category) 
                      ? selectedNode.metadata.category.join(', ')
                      : selectedNode.metadata.category}
                  </div>
                </div>
              )}
            </>
          )}
          
          {selectedNode.type === NodeTypes.REQUIREMENT && (
            <>
              {selectedNode.metadata?.section && (
                <div className="property-item">
                  <div className="property-label">Section</div>
                  <div className="property-value">{selectedNode.metadata.section}</div>
                </div>
              )}
              
              {selectedNode.metadata?.deadline && (
                <div className="property-item">
                  <div className="property-label">Deadline</div>
                  <div className="property-value">{formatDate(selectedNode.metadata.deadline)}</div>
                </div>
              )}
              
              {selectedNode.metadata?.priority && (
                <div className="property-item">
                  <div className="property-label">Priority</div>
                  <div className="property-value">{selectedNode.metadata.priority}</div>
                </div>
              )}
            </>
          )}
          
          {selectedNode.type === NodeTypes.CONTROL && (
            <>
              {selectedNode.metadata?.controlType && (
                <div className="property-item">
                  <div className="property-label">Control Type</div>
                  <div className="property-value">{selectedNode.metadata.controlType}</div>
                </div>
              )}
              
              {selectedNode.metadata?.implementationStatus && (
                <div className="property-item">
                  <div className="property-label">Implementation Status</div>
                  <div className="property-value">{selectedNode.metadata.implementationStatus}</div>
                </div>
              )}
              
              {selectedNode.metadata?.owner && (
                <div className="property-item">
                  <div className="property-label">Owner</div>
                  <div className="property-value">{selectedNode.metadata.owner}</div>
                </div>
              )}
              
              {selectedNode.metadata?.lastTestedDate && (
                <div className="property-item">
                  <div className="property-label">Last Tested</div>
                  <div className="property-value">{formatDate(selectedNode.metadata.lastTestedDate)}</div>
                </div>
              )}
            </>
          )}
          
          {selectedNode.type === NodeTypes.BUSINESS_PROCESS && (
            <>
              {selectedNode.metadata?.processOwner && (
                <div className="property-item">
                  <div className="property-label">Process Owner</div>
                  <div className="property-value">{selectedNode.metadata.processOwner}</div>
                </div>
              )}
              
              {selectedNode.metadata?.department && (
                <div className="property-item">
                  <div className="property-label">Department</div>
                  <div className="property-value">{selectedNode.metadata.department}</div>
                </div>
              )}
              
              {selectedNode.metadata?.criticality && (
                <div className="property-item">
                  <div className="property-label">Criticality</div>
                  <div className="property-value">{selectedNode.metadata.criticality}</div>
                </div>
              )}
            </>
          )}
          
          {selectedNode.type === NodeTypes.SYSTEM && (
            <>
              {selectedNode.metadata?.systemOwner && (
                <div className="property-item">
                  <div className="property-label">System Owner</div>
                  <div className="property-value">{selectedNode.metadata.systemOwner}</div>
                </div>
              )}
              
              {selectedNode.metadata?.department && (
                <div className="property-item">
                  <div className="property-label">Department</div>
                  <div className="property-value">{selectedNode.metadata.department}</div>
                </div>
              )}
              
              {selectedNode.metadata?.vendor && (
                <div className="property-item">
                  <div className="property-label">Vendor</div>
                  <div className="property-value">{selectedNode.metadata.vendor}</div>
                </div>
              )}
              
              {selectedNode.metadata?.version && (
                <div className="property-item">
                  <div className="property-label">Version</div>
                  <div className="property-value">{selectedNode.metadata.version}</div>
                </div>
              )}
            </>
          )}
          
          {/* Display any other metadata */}
          {selectedNode.metadata && Object.entries(selectedNode.metadata)
            .filter(([key]) => !['regulatoryAuthority', 'jurisdiction', 'effectiveDate', 'category',
                              'section', 'deadline', 'priority', 'controlType', 'implementationStatus',
                              'owner', 'lastTestedDate', 'processOwner', 'department', 'criticality',
                              'systemOwner', 'vendor', 'version'].includes(key))
            .map(([key, value]) => (
              <div className="property-item" key={key}>
                <div className="property-label">{key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}</div>
                <div className="property-value">
                  {typeof value === 'object' ? JSON.stringify(value) : value.toString()}
                </div>
              </div>
            ))
          }
        </div>
      </div>
    );
  };
  
  // Render edge details
  const renderEdgeDetails = () => {
    if (!selectedEdge) {
      return (
        <div className="empty-selection">
          <p>Select an edge to view details</p>
        </div>
      );
    }
    
    return (
      <div className="entity-details">
        <h3 className="entity-title">Relationship</h3>
        
        <div className="entity-type">
          {selectedEdge.type.replace('_', ' ')}
        </div>
        
        {selectedEdge.label && (
          <div className="entity-description">
            <p>{selectedEdge.label}</p>
          </div>
        )}
        
        <div className="entity-properties">
          <div className="property-item">
            <div className="property-label">Source</div>
            <div className="property-value">{selectedEntities.nodes.find(n => n.id === selectedEdge.source)?.name || selectedEdge.source}</div>
          </div>
          
          <div className="property-item">
            <div className="property-label">Target</div>
            <div className="property-value">{selectedEntities.nodes.find(n => n.id === selectedEdge.target)?.name || selectedEdge.target}</div>
          </div>
          
          {/* Display any metadata */}
          {selectedEdge.metadata && Object.entries(selectedEdge.metadata).map(([key, value]) => (
            <div className="property-item" key={key}>
              <div className="property-label">{key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}</div>
              <div className="property-value">
                {typeof value === 'object' ? JSON.stringify(value) : value.toString()}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };
  
  // Render related entities
  const renderRelatedEntities = () => {
    if (!selectedNode && !selectedEdge) {
      return (
        <div className="empty-selection">
          <p>Select a node or edge to view related entities</p>
        </div>
      );
    }
    
    // If node is selected, show related nodes
    if (selectedNode) {
      const relatedEdges = selectedEntities.edges.filter(
        edge => edge.source === selectedNode.id || edge.target === selectedNode.id
      );
      
      const relatedNodes = selectedEntities.nodes.filter(
        node => node.id !== selectedNode.id && relatedEdges.some(
          edge => edge.source === node.id || edge.target === node.id
        )
      );
      
      if (relatedNodes.length === 0) {
        return (
          <div className="empty-selection">
            <p>No related entities found</p>
          </div>
        );
      }
      
      return (
        <div className="related-entities">
          <h3>Related Entities</h3>
          
          <div className="related-entities-list">
            {relatedNodes.map(node => {
              const edge = relatedEdges.find(
                e => e.source === node.id || e.target === node.id
              );
              
              const relationshipType = edge ? edge.type.replace('_', ' ') : '';
              const direction = edge && edge.source === selectedNode.id ? 'to' : 'from';
              
              return (
                <div className="related-entity-item" key={node.id}>
                  <div className="related-entity-name">{node.name}</div>
                  <div className="related-entity-type">{getTypeLabel(node.type)}</div>
                  <div className="related-entity-relationship">
                    {direction === 'to' ? `${relationshipType} →` : `← ${relationshipType}`}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      );
    }
    
    // If edge is selected, show connected nodes
    if (selectedEdge) {
      const sourceNode = selectedEntities.nodes.find(n => n.id === selectedEdge.source);
      const targetNode = selectedEntities.nodes.find(n => n.id === selectedEdge.target);
      
      if (!sourceNode && !targetNode) {
        return (
          <div className="empty-selection">
            <p>Connected nodes not found in selection</p>
          </div>
        );
      }
      
      return (
        <div className="related-entities">
          <h3>Connected Entities</h3>
          
          <div className="related-entities-list">
            {sourceNode && (
              <div className="related-entity-item">
                <div className="related-entity-name">{sourceNode.name}</div>
                <div className="related-entity-type">{getTypeLabel(sourceNode.type)}</div>
                <div className="related-entity-relationship">Source</div>
              </div>
            )}
            
            {targetNode && (
              <div className="related-entity-item">
                <div className="related-entity-name">{targetNode.name}</div>
                <div className="related-entity-type">{getTypeLabel(targetNode.type)}</div>
                <div className="related-entity-relationship">Target</div>
              </div>
            )}
          </div>
        </div>
      );
    }
  };
  
  // Render annotations
  const renderAnnotations = () => {
    // This would typically fetch annotations from the server
    // For now, we'll show a placeholder
    
    return (
      <div className="annotations-panel">
        <div className="annotations-header">
          <h3>Annotations</h3>
          <button className="add-annotation-button">Add Annotation</button>
        </div>
        
        {(!selectedNode && !selectedEdge) ? (
          <div className="empty-selection">
            <p>Select a node or edge to view annotations</p>
          </div>
        ) : (
          <div className="annotations-list">
            <div className="empty-annotations">
              <p>No annotations found</p>
              <p>Click "Add Annotation" to create one</p>
            </div>
          </div>
        )}
      </div>
    );
  };
  
  // Render impact assessment
  const renderImpactAssessment = () => {
    if (!selectedNode) {
      return (
        <div className="empty-selection">
          <p>Select a node to view impact assessment</p>
        </div>
      );
    }
    
    // This would typically fetch impact assessment data from the server
    // For now, we'll show a placeholder
    
    return (
      <div className="impact-assessment">
        <h3>Impact Assessment</h3>
        
        <div className="impact-metrics">
          <div className="impact-metric">
            <div className="metric-label">Overall Impact</div>
            <div className="metric-value">Medium</div>
          </div>
          
          <div className="impact-metric">
            <div className="metric-label">Implementation Effort</div>
            <div className="metric-value">High</div>
          </div>
          
          <div className="impact-metric">
            <div className="metric-label">Risk Level</div>
            <div className="metric-value">Medium</div>
          </div>
        </div>
        
        <div className="impact-areas">
          <h4>Impacted Areas</h4>
          
          <div className="impact-area-item">
            <div className="impact-area-name">IT Systems</div>
            <div className="impact-area-level high">High</div>
          </div>
          
          <div className="impact-area-item">
            <div className="impact-area-name">Business Processes</div>
            <div className="impact-area-level medium">Medium</div>
          </div>
          
          <div className="impact-area-item">
            <div className="impact-area-name">Training</div>
            <div className="impact-area-level low">Low</div>
          </div>
        </div>
        
        <button className="view-full-assessment">View Full Assessment</button>
      </div>
    );
  };
  
  return (
    <div className="regulatory-map-detail-panel">
      <div className="detail-panel-header">
        <h3>Details</h3>
        <button className="close-button" onClick={onClose}>×</button>
      </div>
      
      <div className="detail-panel-tabs">
        <button 
          className={`tab-button ${activeTab === 'details' ? 'active' : ''}`}
          onClick={() => handleTabChange('details')}
        >
          Details
        </button>
        <button 
          className={`tab-button ${activeTab === 'related' ? 'active' : ''}`}
          onClick={() => handleTabChange('related')}
        >
          Related
        </button>
        <button 
          className={`tab-button ${activeTab === 'annotations' ? 'active' : ''}`}
          onClick={() => handleTabChange('annotations')}
        >
          Annotations
        </button>
        <button 
          className={`tab-button ${activeTab === 'impact' ? 'active' : ''}`}
          onClick={() => handleTabChange('impact')}
        >
          Impact
        </button>
      </div>
      
      <div className="detail-panel-content">
        {activeTab === 'details' && (
          selectedNode ? renderNodeDetails() : renderEdgeDetails()
        )}
        
        {activeTab === 'related' && renderRelatedEntities()}
        
        {activeTab === 'annotations' && renderAnnotations()}
        
        {activeTab === 'impact' && renderImpactAssessment()}
      </div>
      
      {(selectedNode || selectedEdge) && (
        <div className="detail-panel-footer">
          <button className="action-button">View Documentation</button>
          <button className="action-button primary">Manage Compliance</button>
        </div>
      )}
    </div>
  );
};

RegulatoryMapDetailPanel.propTypes = {
  /**
   * Selected entities (nodes and edges)
   */
  selectedEntities: PropTypes.shape({
    nodes: PropTypes.array,
    edges: PropTypes.array
  }),
  
  /**
   * Callback when the panel is closed
   */
  onClose: PropTypes.func
};

RegulatoryMapDetailPanel.defaultProps = {
  selectedEntities: {
    nodes: [],
    edges: []
  }
};

export default RegulatoryMapDetailPanel;
