.regulatory-map-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: #f8f9fa;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* Toolbar */
.regulatory-map-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
  height: 56px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.toolbar-left,
.toolbar-center,
.toolbar-right {
  display: flex;
  align-items: center;
}

.toolbar-left {
  flex: 1;
}

.toolbar-center {
  flex: 2;
  justify-content: center;
}

.toolbar-right {
  flex: 1;
  justify-content: flex-end;
}

.map-title {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.toolbar-button {
  padding: 6px 12px;
  margin: 0 4px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toolbar-button:hover {
  background-color: #f5f5f5;
  border-color: #ccc;
}

.toolbar-button:active {
  background-color: #e0e0e0;
}

.view-mode-selector select {
  padding: 6px 12px;
  margin: 0 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  font-size: 14px;
  cursor: pointer;
}

.search-box {
  margin-left: 16px;
  position: relative;
}

.search-box input {
  padding: 6px 12px;
  padding-left: 30px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  width: 200px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: 8px center;
  background-size: 14px;
}

.toolbar-dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-content {
  display: none;
  position: absolute;
  right: 0;
  background-color: #fff;
  min-width: 160px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  z-index: 1;
  border-radius: 4px;
  overflow: hidden;
}

.dropdown-content button {
  width: 100%;
  padding: 8px 16px;
  text-align: left;
  background: none;
  border: none;
  font-size: 14px;
  cursor: pointer;
}

.dropdown-content button:hover {
  background-color: #f5f5f5;
}

.toolbar-dropdown:hover .dropdown-content {
  display: block;
}

/* Main Content */
.regulatory-map-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* Control Panel */
.regulatory-map-control-panel {
  width: 280px;
  background-color: #fff;
  border-right: 1px solid #e0e0e0;
  overflow-y: auto;
  z-index: 5;
  transition: width 0.3s ease;
}

/* Visualization Container */
.regulatory-map-visualization-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

/* Detail Panel */
.regulatory-map-detail-panel {
  width: 320px;
  background-color: #fff;
  border-left: 1px solid #e0e0e0;
  overflow-y: auto;
  z-index: 5;
  transition: width 0.3s ease;
}

/* Status Bar */
.regulatory-map-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 16px;
  background-color: #f5f5f5;
  border-top: 1px solid #e0e0e0;
  height: 28px;
  font-size: 12px;
  color: #666;
}

.status-left,
.status-center,
.status-right {
  flex: 1;
}

.status-center {
  text-align: center;
}

.status-right {
  text-align: right;
}

/* Loading Overlay */
.regulatory-map-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(248, 249, 250, 0.7);
  z-index: 100;
}

.regulatory-map-loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #1a73e8;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
  .regulatory-map-control-panel {
    width: 240px;
  }
  
  .regulatory-map-detail-panel {
    width: 280px;
  }
}

@media (max-width: 992px) {
  .regulatory-map-control-panel {
    width: 200px;
  }
  
  .regulatory-map-detail-panel {
    width: 240px;
  }
  
  .search-box input {
    width: 150px;
  }
}

@media (max-width: 768px) {
  .regulatory-map-toolbar {
    flex-wrap: wrap;
    height: auto;
  }
  
  .toolbar-center {
    order: -1;
    width: 100%;
    margin-bottom: 8px;
  }
  
  .toolbar-left,
  .toolbar-right {
    flex: 1;
  }
  
  .regulatory-map-control-panel,
  .regulatory-map-detail-panel {
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: 20;
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  }
  
  .regulatory-map-control-panel {
    left: 0;
    transform: translateX(-100%);
  }
  
  .regulatory-map-control-panel.open {
    transform: translateX(0);
  }
  
  .regulatory-map-detail-panel {
    right: 0;
    transform: translateX(100%);
  }
  
  .regulatory-map-detail-panel.open {
    transform: translateX(0);
  }
}

/* Dark Theme */
.dark-theme.regulatory-map-container {
  background-color: #1e1e1e;
  color: #e0e0e0;
}

.dark-theme .regulatory-map-toolbar,
.dark-theme .regulatory-map-control-panel,
.dark-theme .regulatory-map-detail-panel {
  background-color: #2d2d2d;
  border-color: #444;
}

.dark-theme .regulatory-map-status-bar {
  background-color: #252525;
  border-color: #444;
  color: #ccc;
}

.dark-theme .toolbar-button,
.dark-theme .view-mode-selector select,
.dark-theme .search-box input {
  background-color: #3d3d3d;
  border-color: #555;
  color: #e0e0e0;
}

.dark-theme .toolbar-button:hover {
  background-color: #4d4d4d;
}

.dark-theme .dropdown-content {
  background-color: #3d3d3d;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.dark-theme .dropdown-content button {
  color: #e0e0e0;
}

.dark-theme .dropdown-content button:hover {
  background-color: #4d4d4d;
}

.dark-theme .map-title {
  color: #e0e0e0;
}

.dark-theme .regulatory-map-loading {
  background-color: rgba(30, 30, 30, 0.7);
}

.dark-theme .regulatory-map-loading-spinner {
  border-color: rgba(255, 255, 255, 0.1);
  border-top-color: #1a73e8;
}
