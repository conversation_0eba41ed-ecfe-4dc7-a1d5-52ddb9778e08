.regulatory-map-container {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #f8f9fa;
  overflow: hidden;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.regulatory-map-svg {
  display: block;
  width: 100%;
  height: 100%;
}

/* Node styling */
.node {
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.node.selected circle,
.node.selected rect,
.node.selected polygon {
  stroke: #000;
  stroke-width: 2px;
  filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.3));
}

.node:hover circle,
.node:hover rect,
.node:hover polygon {
  filter: brightness(1.1);
}

.node-label {
  pointer-events: none;
  user-select: none;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  font-size: 10px;
}

/* Edge styling */
.edge {
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.edge.selected {
  stroke-width: 2px;
  filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.3));
}

.edge:hover {
  filter: brightness(1.2);
}

/* Node type specific styling */
.node-type-REGULATION circle {
  stroke-width: 2px;
}

.node-type-REQUIREMENT circle {
  stroke-width: 1px;
}

.node-type-CONTROL rect {
  stroke-dasharray: 2, 2;
}

.node-type-BUSINESS_PROCESS polygon {
  stroke-width: 1.5px;
}

.node-type-SYSTEM polygon {
  stroke-width: 1.5px;
}

/* Tooltip styling */
.regulatory-map-tooltip {
  position: absolute;
  padding: 8px 12px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  pointer-events: none;
  z-index: 1000;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  font-size: 12px;
  max-width: 250px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.regulatory-map-tooltip.visible {
  opacity: 1;
}

.regulatory-map-tooltip h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
}

.regulatory-map-tooltip p {
  margin: 0;
  color: #666;
}

/* Loading state */
.regulatory-map-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(248, 249, 250, 0.7);
  z-index: 10;
}

.regulatory-map-loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #1a73e8;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .node-label {
    font-size: 8px;
  }
}

/* High contrast mode */
.high-contrast-mode .node circle,
.high-contrast-mode .node rect,
.high-contrast-mode .node polygon {
  stroke: #000;
  stroke-width: 2px;
}

.high-contrast-mode .edge {
  stroke: #000;
  stroke-width: 1.5px;
}

.high-contrast-mode .node-label {
  fill: #000;
  font-weight: bold;
}

/* Dark theme */
.dark-theme.regulatory-map-container {
  background-color: #1e1e1e;
}

.dark-theme .node-label {
  fill: #fff;
}

.dark-theme .regulatory-map-tooltip {
  background-color: #2d2d2d;
  color: #fff;
}

.dark-theme .regulatory-map-tooltip p {
  color: #ccc;
}

.dark-theme .regulatory-map-loading {
  background-color: rgba(30, 30, 30, 0.7);
}

.dark-theme .regulatory-map-loading-spinner {
  border-color: rgba(255, 255, 255, 0.1);
  border-top-color: #1a73e8;
}
