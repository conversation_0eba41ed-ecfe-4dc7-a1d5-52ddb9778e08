import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import RegulatoryMapContainer from '../RegulatoryMapContainer';
import { NodeTypes, EdgeTypes, ComplianceStatus, ViewModes } from '../../../constants/regulatoryMap';

// Mock the child components with more interactive mocks
jest.mock('../RegulatoryMapVisualization', () => {
  return function MockVisualization(props) {
    return (
      <div data-testid="mock-visualization">
        <button
          data-testid="trigger-selection-change"
          onClick={() => props.onSelectionChange && props.onSelectionChange({
            nodeIds: ['node1'],
            edgeIds: []
          })}
        >
          Select Node
        </button>
        <button
          data-testid="trigger-viewport-change"
          onClick={() => props.onViewportChange && props.onViewportChange({
            center: { x: 100, y: 100 },
            zoom: 2
          })}
        >
          Change Viewport
        </button>
        <div>
          <span>Config: {JSON.stringify({
            showLabels: props.config.showLabels,
            viewMode: props.config.viewMode
          })}</span>
        </div>
      </div>
    );
  };
});

jest.mock('../RegulatoryMapControlPanel', () => {
  return function MockControlPanel(props) {
    return (
      <div data-testid="mock-control-panel">
        <button
          data-testid="trigger-filter-change"
          onClick={() => props.onFilterChange && props.onFilterChange([
            { id: 'type', type: 'type', values: [NodeTypes.REGULATION], enabled: true }
          ])}
        >
          Apply Filter
        </button>
        <button
          data-testid="trigger-config-change"
          onClick={() => props.onConfigChange && props.onConfigChange({
            showLabels: false
          })}
        >
          Change Config
        </button>
      </div>
    );
  };
});

jest.mock('../RegulatoryMapDetailPanel', () => {
  return function MockDetailPanel(props) {
    return (
      <div data-testid="mock-detail-panel">
        <span data-testid="selected-entities-count">
          Selected Entities: {props.selectedEntities.nodes.length}
        </span>
        <button
          data-testid="close-detail-panel"
          onClick={props.onClose}
        >
          Close
        </button>
      </div>
    );
  };
});

describe('RegulatoryMapContainer Integration Tests', () => {
  const mockData = {
    nodes: [
      {
        id: 'node1',
        type: NodeTypes.REGULATION,
        name: 'Test Regulation',
        status: ComplianceStatus.COMPLIANT
      },
      {
        id: 'node2',
        type: NodeTypes.REQUIREMENT,
        name: 'Test Requirement',
        status: ComplianceStatus.PARTIALLY_COMPLIANT
      }
    ],
    edges: [
      {
        id: 'edge1',
        source: 'node1',
        target: 'node2',
        type: EdgeTypes.CONTAINS
      }
    ]
  };

  const mockConfig = {
    showLabels: true,
    labelSize: 12,
    layout: {
      type: 'force',
      linkDistance: 120,
      chargeStrength: -400
    },
    title: 'Regulatory Compliance Map'
  };

  test('renders all components correctly', () => {
    render(
      <RegulatoryMapContainer
        data={mockData}
        initialConfig={mockConfig}
      />
    );

    // Check if all components are rendered
    expect(screen.getByTestId('mock-visualization')).toBeInTheDocument();
    expect(screen.getByTestId('mock-control-panel')).toBeInTheDocument();
    expect(screen.getByTestId('mock-detail-panel')).toBeInTheDocument();

    // Check if the title is rendered
    expect(screen.getByText('Regulatory Compliance Map')).toBeInTheDocument();
  });

  test('toggles control panel visibility', () => {
    render(
      <RegulatoryMapContainer
        data={mockData}
        initialConfig={mockConfig}
      />
    );

    // Find the control panel toggle button
    const toggleButton = screen.getByTitle('Hide Control Panel');

    // Click to hide the control panel
    fireEvent.click(toggleButton);

    // The control panel should be hidden
    expect(screen.queryByTestId('mock-control-panel')).not.toBeInTheDocument();

    // Button should now show "Show Control Panel"
    expect(screen.getByTitle('Show Control Panel')).toBeInTheDocument();

    // Click again to show the control panel
    fireEvent.click(screen.getByTitle('Show Control Panel'));

    // The control panel should be visible again
    expect(screen.getByTestId('mock-control-panel')).toBeInTheDocument();
  });

  test('toggles detail panel visibility', () => {
    render(
      <RegulatoryMapContainer
        data={mockData}
        initialConfig={mockConfig}
      />
    );

    // Find the detail panel toggle button
    const toggleButton = screen.getByTitle('Hide Detail Panel');

    // Click to hide the detail panel
    fireEvent.click(toggleButton);

    // The detail panel should be hidden
    expect(screen.queryByTestId('mock-detail-panel')).not.toBeInTheDocument();

    // Button should now show "Show Detail Panel"
    expect(screen.getByTitle('Show Detail Panel')).toBeInTheDocument();

    // Click again to show the detail panel
    fireEvent.click(screen.getByTitle('Show Detail Panel'));

    // The detail panel should be visible again
    expect(screen.getByTestId('mock-detail-panel')).toBeInTheDocument();
  });

  test('changes view mode', () => {
    render(
      <RegulatoryMapContainer
        data={mockData}
        initialConfig={mockConfig}
      />
    );

    // Find the view mode selector
    const viewModeSelector = screen.getByTitle('Select View Mode');

    // Change to hierarchical view
    fireEvent.change(viewModeSelector, { target: { value: ViewModes.HIERARCHICAL } });

    // The view mode should be updated (we can't directly test the state, but we can check if the option is selected)
    expect(viewModeSelector.value).toBe(ViewModes.HIERARCHICAL);
  });

  test('calls onSaveView when save button is clicked', () => {
    const handleSaveView = jest.fn();

    render(
      <RegulatoryMapContainer
        data={mockData}
        initialConfig={mockConfig}
        onSaveView={handleSaveView}
      />
    );

    // Find the save button
    const saveButton = screen.getByTitle('Save Current View');

    // Click the save button
    fireEvent.click(saveButton);

    // The onSaveView callback should be called
    expect(handleSaveView).toHaveBeenCalled();
  });

  test('calls onExportImage when export option is clicked', async () => {
    const handleExportImage = jest.fn();

    render(
      <RegulatoryMapContainer
        data={mockData}
        initialConfig={mockConfig}
        onExportImage={handleExportImage}
      />
    );

    // Find the export dropdown button
    const exportButton = screen.getByText('Export');

    // Click the export button to open the dropdown
    fireEvent.click(exportButton);

    // Find and click the "Export as PNG" option
    const exportPngOption = screen.getByText('Export as PNG');
    fireEvent.click(exportPngOption);

    // The onExportImage callback should be called with 'png'
    expect(handleExportImage).toHaveBeenCalledWith('png');
  });

  test('calls onGenerateReport when report option is clicked', async () => {
    const handleGenerateReport = jest.fn();

    render(
      <RegulatoryMapContainer
        data={mockData}
        initialConfig={mockConfig}
        onGenerateReport={handleGenerateReport}
      />
    );

    // Find the export dropdown button
    const exportButton = screen.getByText('Export');

    // Click the export button to open the dropdown
    fireEvent.click(exportButton);

    // Find and click the "Compliance Report" option
    const complianceReportOption = screen.getByText('Compliance Report');
    fireEvent.click(complianceReportOption);

    // The onGenerateReport callback should be called with 'compliance'
    expect(handleGenerateReport).toHaveBeenCalledWith('compliance', expect.anything());
  });

  test('filters data when search is performed', async () => {
    render(
      <RegulatoryMapContainer
        data={mockData}
        initialConfig={mockConfig}
      />
    );

    // Find the search input
    const searchInput = screen.getByPlaceholderText('Search...');

    // Enter a search term
    fireEvent.change(searchInput, { target: { value: 'Test' } });

    // Wait for the search to be applied
    await waitFor(() => {
      // We can't directly test the filtered data, but we can check if the search filter is applied
      // by checking the status bar text (which shows the number of nodes and edges)
      expect(screen.getByText('2 nodes, 1 edges')).toBeInTheDocument();
    });

    // Clear the search
    fireEvent.change(searchInput, { target: { value: '' } });

    // Wait for the search to be cleared
    await waitFor(() => {
      // The filter should be removed, showing all nodes and edges
      expect(screen.getByText('2 nodes, 1 edges')).toBeInTheDocument();
    });
  });

  test('handles selection changes from visualization', async () => {
    render(
      <RegulatoryMapContainer
        data={mockData}
        initialConfig={mockConfig}
      />
    );

    // Trigger selection change from visualization
    fireEvent.click(screen.getByTestId('trigger-selection-change'));

    // Wait for the selection to be updated
    await waitFor(() => {
      // The detail panel should show the selected entity
      expect(screen.getByTestId('selected-entities-count')).toHaveTextContent('Selected Entities: 1');
    });
  });

  test('handles viewport changes from visualization', async () => {
    render(
      <RegulatoryMapContainer
        data={mockData}
        initialConfig={mockConfig}
      />
    );

    // Trigger viewport change from visualization
    fireEvent.click(screen.getByTestId('trigger-viewport-change'));

    // Wait for the viewport to be updated
    await waitFor(() => {
      // The status bar should show the zoom level
      expect(screen.getByText('Zoom: 200%')).toBeInTheDocument();
    });
  });

  test('handles filter changes from control panel', async () => {
    render(
      <RegulatoryMapContainer
        data={mockData}
        initialConfig={mockConfig}
      />
    );

    // Trigger filter change from control panel
    fireEvent.click(screen.getByTestId('trigger-filter-change'));

    // Wait for the filters to be applied
    await waitFor(() => {
      // The component should still be rendered
      expect(screen.getByTestId('mock-visualization')).toBeInTheDocument();

      // In a real application, this would filter the data
      // For this test, we're just checking that the component doesn't crash
    });
  });

  test('handles config changes from control panel', async () => {
    render(
      <RegulatoryMapContainer
        data={mockData}
        initialConfig={mockConfig}
      />
    );

    // Trigger config change from control panel
    fireEvent.click(screen.getByTestId('trigger-config-change'));

    // Wait for the config to be updated
    await waitFor(() => {
      // The component should still be rendered
      expect(screen.getByTestId('mock-visualization')).toBeInTheDocument();

      // In a real application, this would update the visualization
      // For this test, we're just checking that the component doesn't crash
    });
  });

  test('closes detail panel using the close button', () => {
    render(
      <RegulatoryMapContainer
        data={mockData}
        initialConfig={mockConfig}
      />
    );

    // Check if detail panel is initially visible
    expect(screen.getByTestId('mock-detail-panel')).toBeInTheDocument();

    // Click the close button in the detail panel
    fireEvent.click(screen.getByTestId('close-detail-panel'));

    // The detail panel should be hidden
    expect(screen.queryByTestId('mock-detail-panel')).not.toBeInTheDocument();
  });
});
