import { getNodeColor, getNodeSize, getNodeShape, getEdgeStyle } from '../visualEncodings';
import { NodeTypes, EdgeTypes, ComplianceStatus } from '../../../constants/regulatoryMap';

describe('visualEncodings', () => {
  describe('getNodeColor', () => {
    it('should return custom color from node style if available', () => {
      const node = {
        type: NodeTypes.REGULATION,
        style: {
          color: '#FF0000'
        }
      };
      
      expect(getNodeColor(node, {})).toBe('#FF0000');
    });
    
    it('should return color from config nodeStyles if available', () => {
      const node = {
        type: NodeTypes.REGULATION
      };
      
      const config = {
        nodeStyles: {
          [NodeTypes.REGULATION]: {
            color: '#00FF00'
          }
        }
      };
      
      expect(getNodeColor(node, config)).toBe('#00FF00');
    });
    
    it('should return color based on status if available', () => {
      const node = {
        type: NodeTypes.REGULATION,
        status: ComplianceStatus.COMPLIANT
      };
      
      const config = {
        colorScheme: {
          status: {
            [ComplianceStatus.COMPLIANT]: '#4CAF50'
          }
        }
      };
      
      expect(getNodeColor(node, config)).toBe('#4CAF50');
    });
    
    it('should return color based on node type as fallback', () => {
      const node = {
        type: NodeTypes.REGULATION
      };
      
      const config = {
        colorScheme: {
          types: {
            [NodeTypes.REGULATION]: '#1976D2'
          }
        }
      };
      
      expect(getNodeColor(node, config)).toBe('#1976D2');
    });
    
    it('should return default gray if no match is found', () => {
      const node = {
        type: 'UNKNOWN_TYPE'
      };
      
      expect(getNodeColor(node, {})).toBe('#9E9E9E');
    });
  });
  
  describe('getNodeSize', () => {
    it('should return custom size from node style if available', () => {
      const node = {
        type: NodeTypes.REGULATION,
        style: {
          size: 30
        }
      };
      
      expect(getNodeSize(node, {})).toBe(30);
    });
    
    it('should return size from config nodeStyles if available', () => {
      const node = {
        type: NodeTypes.REGULATION
      };
      
      const config = {
        nodeStyles: {
          [NodeTypes.REGULATION]: {
            size: 25
          }
        }
      };
      
      expect(getNodeSize(node, config)).toBe(25);
    });
    
    it('should return default size based on node type', () => {
      const node = {
        type: NodeTypes.REGULATION
      };
      
      expect(getNodeSize(node, {})).toBe(20);
    });
    
    it('should adjust size based on importance if available', () => {
      const node = {
        type: NodeTypes.REGULATION,
        metadata: {
          importance: 1.0 // Maximum importance
        }
      };
      
      // Base size is 20, with importance 1.0 it should scale to 130% of base size
      const expectedSize = 20 * (0.7 + (1.0 * 0.6));
      expect(getNodeSize(node, {})).toBe(expectedSize);
    });
  });
  
  describe('getNodeShape', () => {
    it('should return custom shape from node style if available', () => {
      const node = {
        type: NodeTypes.REGULATION,
        style: {
          shape: 'hexagon'
        }
      };
      
      expect(getNodeShape(node, {})).toBe('hexagon');
    });
    
    it('should return shape from config nodeStyles if available', () => {
      const node = {
        type: NodeTypes.REGULATION
      };
      
      const config = {
        nodeStyles: {
          [NodeTypes.REGULATION]: {
            shape: 'diamond'
          }
        }
      };
      
      expect(getNodeShape(node, config)).toBe('diamond');
    });
    
    it('should return default shape based on node type', () => {
      const node = {
        type: NodeTypes.REGULATION
      };
      
      expect(getNodeShape(node, {})).toBe('circle');
      
      const controlNode = {
        type: NodeTypes.CONTROL
      };
      
      expect(getNodeShape(controlNode, {})).toBe('square');
      
      const processNode = {
        type: NodeTypes.BUSINESS_PROCESS
      };
      
      expect(getNodeShape(processNode, {})).toBe('diamond');
    });
    
    it('should return circle as fallback for unknown types', () => {
      const node = {
        type: 'UNKNOWN_TYPE'
      };
      
      expect(getNodeShape(node, {})).toBe('circle');
    });
  });
  
  describe('getEdgeStyle', () => {
    it('should return custom style from edge if available', () => {
      const edge = {
        type: EdgeTypes.CONTAINS,
        style: {
          color: '#FF0000',
          width: 2,
          pattern: '5,5'
        }
      };
      
      expect(getEdgeStyle(edge, {})).toEqual({
        color: '#FF0000',
        width: 2,
        pattern: '5,5'
      });
    });
    
    it('should use default values for missing style properties', () => {
      const edge = {
        type: EdgeTypes.CONTAINS,
        style: {
          color: '#FF0000'
          // width and pattern missing
        }
      };
      
      expect(getEdgeStyle(edge, {})).toEqual({
        color: '#FF0000',
        width: 1,
        pattern: null
      });
    });
    
    it('should return style from config edgeStyles if available', () => {
      const edge = {
        type: EdgeTypes.CONTAINS
      };
      
      const config = {
        edgeStyles: {
          [EdgeTypes.CONTAINS]: {
            color: '#00FF00',
            width: 3,
            pattern: '2,2'
          }
        }
      };
      
      expect(getEdgeStyle(edge, config)).toEqual({
        color: '#00FF00',
        width: 3,
        pattern: '2,2'
      });
    });
    
    it('should return default style based on edge type', () => {
      const edge = {
        type: EdgeTypes.CONTAINS
      };
      
      expect(getEdgeStyle(edge, {})).toEqual({
        color: '#666',
        width: 1.5,
        pattern: null
      });
      
      const referencesEdge = {
        type: EdgeTypes.REFERENCES
      };
      
      expect(getEdgeStyle(referencesEdge, {})).toEqual({
        color: '#999',
        width: 1,
        pattern: '5,5'
      });
    });
    
    it('should return default style for unknown edge types', () => {
      const edge = {
        type: 'UNKNOWN_TYPE'
      };
      
      expect(getEdgeStyle(edge, {})).toEqual({
        color: '#666',
        width: 1,
        pattern: null
      });
    });
  });
});
