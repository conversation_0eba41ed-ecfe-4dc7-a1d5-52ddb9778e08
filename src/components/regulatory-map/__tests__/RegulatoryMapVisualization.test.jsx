import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import RegulatoryMapVisualization from '../RegulatoryMapVisualization';
import { NodeTypes, EdgeTypes, ComplianceStatus } from '../../../constants/regulatoryMap';

// Create a real ResizeObserver for integration testing
global.ResizeObserver = class ResizeObserver {
  constructor(callback) {
    this.callback = callback;
  }
  observe(element) {
    // Simulate a resize
    this.callback([
      {
        target: element,
        contentRect: { width: 800, height: 600 }
      }
    ]);
  }
  unobserve() {}
  disconnect() {}
};

describe('RegulatoryMapVisualization Integration Tests', () => {
  // Test data with more realistic content
  const testData = {
    nodes: [
      {
        id: 'reg1',
        type: NodeTypes.REGULATION,
        name: 'General Data Protection Regulation (GDPR)',
        description: 'Regulation on data protection and privacy in the EU and the European Economic Area.',
        status: ComplianceStatus.PARTIALLY_COMPLIANT,
        metadata: {
          regulatoryAuthority: 'European Union',
          jurisdiction: 'EU',
          effectiveDate: '2018-05-25',
          category: ['Privacy', 'Data Protection']
        },
        position: { x: 100, y: 100 }
      },
      {
        id: 'req1',
        type: NodeTypes.REQUIREMENT,
        name: 'Data Subject Access Rights',
        description: 'Individuals have the right to access their personal data.',
        status: ComplianceStatus.PARTIALLY_COMPLIANT,
        metadata: {
          section: 'Article 15',
          priority: 'High'
        },
        position: { x: 250, y: 150 }
      },
      {
        id: 'ctrl1',
        type: NodeTypes.CONTROL,
        name: 'Data Subject Request Process',
        description: 'Process for handling data subject access requests.',
        status: ComplianceStatus.PARTIALLY_COMPLIANT,
        metadata: {
          controlType: 'Process',
          implementationStatus: 'In Progress',
          owner: 'Privacy Team',
          lastTestedDate: '2023-06-15'
        },
        position: { x: 400, y: 200 }
      }
    ],
    edges: [
      {
        id: 'edge1',
        source: 'reg1',
        target: 'req1',
        type: EdgeTypes.CONTAINS
      },
      {
        id: 'edge2',
        source: 'req1',
        target: 'ctrl1',
        type: EdgeTypes.IMPLEMENTS
      }
    ]
  };

  // Test configuration
  const testConfig = {
    showLabels: true,
    labelSize: 12,
    labelColor: '#000',
    layout: {
      type: 'force',
      linkDistance: 120,
      chargeStrength: -400,
      gravity: 0.1
    },
    zoomRange: [0.1, 4]
  };

  test('renders the visualization with all components', () => {
    const { container } = render(
      <RegulatoryMapVisualization
        data={testData}
        config={testConfig}
      />
    );

    // Check if the container is rendered
    const mapContainer = screen.getByTestId('regulatory-map-container');
    expect(mapContainer).toBeInTheDocument();

    // Check if SVG is rendered
    const svg = container.querySelector('svg');
    expect(svg).toBeInTheDocument();

    // Check if nodes container is rendered
    const nodesContainer = container.querySelector('.nodes');
    expect(nodesContainer).toBeInTheDocument();

    // Check if edges container is rendered
    const edgesContainer = container.querySelector('.edges');
    expect(edgesContainer).toBeInTheDocument();
  });

  test('renders nodes with correct visual encodings', () => {
    const { container } = render(
      <RegulatoryMapVisualization
        data={testData}
        config={testConfig}
      />
    );

    // Wait for nodes to be rendered
    setTimeout(() => {
      // Check if all nodes are rendered
      const nodeElements = container.querySelectorAll('.node');
      expect(nodeElements.length).toBe(testData.nodes.length);

      // Check for specific node types
      const regulationNode = container.querySelector('.node-type-REGULATION');
      expect(regulationNode).toBeInTheDocument();

      const requirementNode = container.querySelector('.node-type-REQUIREMENT');
      expect(requirementNode).toBeInTheDocument();

      const controlNode = container.querySelector('.node-type-CONTROL');
      expect(controlNode).toBeInTheDocument();

      // Check for node shapes
      const circles = container.querySelectorAll('circle');
      const rects = container.querySelectorAll('rect');

      // Regulation and Requirement should be circles, Control should be a square
      expect(circles.length).toBeGreaterThanOrEqual(2);
      expect(rects.length).toBeGreaterThanOrEqual(1);
    }, 100);
  });

  test('renders edges between nodes', () => {
    const { container } = render(
      <RegulatoryMapVisualization
        data={testData}
        config={testConfig}
      />
    );

    // Wait for edges to be rendered
    setTimeout(() => {
      // Check if all edges are rendered
      const edgeElements = container.querySelectorAll('.edge');
      expect(edgeElements.length).toBe(testData.edges.length);
    }, 100);
  });

  test('handles node selection', () => {
    const handleNodeClick = jest.fn();
    const handleSelectionChange = jest.fn();

    const { container } = render(
      <RegulatoryMapVisualization
        data={testData}
        config={testConfig}
        onNodeClick={handleNodeClick}
        onSelectionChange={handleSelectionChange}
      />
    );

    // Wait for nodes to be rendered
    setTimeout(() => {
      // Find and click a node
      const nodeElements = container.querySelectorAll('.node');
      if (nodeElements.length > 0) {
        fireEvent.click(nodeElements[0]);

        // Check if callbacks are called
        expect(handleNodeClick).toHaveBeenCalled();
        expect(handleSelectionChange).toHaveBeenCalledWith({
          nodeIds: expect.arrayContaining([testData.nodes[0].id]),
          edgeIds: expect.any(Array)
        });

        // Check if the node is visually selected
        expect(nodeElements[0].classList.contains('selected')).toBe(true);
      }
    }, 100);
  });

  test('handles multi-selection with shift key', () => {
    const handleSelectionChange = jest.fn();

    const { container } = render(
      <RegulatoryMapVisualization
        data={testData}
        config={testConfig}
        onSelectionChange={handleSelectionChange}
      />
    );

    // Wait for nodes to be rendered
    setTimeout(() => {
      // Find nodes
      const nodeElements = container.querySelectorAll('.node');
      if (nodeElements.length >= 2) {
        // Click first node
        fireEvent.click(nodeElements[0]);

        // Click second node with shift key
        fireEvent.click(nodeElements[1], { shiftKey: true });

        // Check if both nodes are selected
        expect(handleSelectionChange).toHaveBeenLastCalledWith({
          nodeIds: expect.arrayContaining([testData.nodes[0].id, testData.nodes[1].id]),
          edgeIds: expect.any(Array)
        });
      }
    }, 100);
  });

  test('clears selection when background is clicked', () => {
    const handleSelectionChange = jest.fn();

    const { container } = render(
      <RegulatoryMapVisualization
        data={testData}
        config={testConfig}
        onSelectionChange={handleSelectionChange}
      />
    );

    // Wait for visualization to be rendered
    setTimeout(() => {
      // Find and click a node first
      const nodeElements = container.querySelectorAll('.node');
      if (nodeElements.length > 0) {
        fireEvent.click(nodeElements[0]);

        // Reset mock to focus on the background click
        handleSelectionChange.mockClear();

        // Click the SVG background
        const svg = container.querySelector('svg');
        if (svg) {
          fireEvent.click(svg);

          // Check if selection is cleared
          expect(handleSelectionChange).toHaveBeenCalledWith({
            nodeIds: [],
            edgeIds: []
          });
        }
      }
    }, 100);
  });

  test('shows or hides labels based on configuration', () => {
    // Render with labels shown
    const { container, rerender } = render(
      <RegulatoryMapVisualization
        data={testData}
        config={{ ...testConfig, showLabels: true }}
      />
    );

    // Wait for nodes to be rendered
    setTimeout(() => {
      // Check if labels are rendered
      const labels = container.querySelectorAll('.node-label');
      expect(labels.length).toBeGreaterThan(0);

      // Re-render with labels hidden
      rerender(
        <RegulatoryMapVisualization
          data={testData}
          config={{ ...testConfig, showLabels: false }}
        />
      );

      // Wait for update
      setTimeout(() => {
        // Check if labels are not rendered
        const updatedLabels = container.querySelectorAll('.node-label');
        expect(updatedLabels.length).toBe(0);
      }, 100);
    }, 100);
  });

  test('applies different node sizes based on node type', () => {
    const { container } = render(
      <RegulatoryMapVisualization
        data={testData}
        config={testConfig}
      />
    );

    // Wait for nodes to be rendered
    setTimeout(() => {
      // Find regulation node (should be larger)
      const regulationNode = container.querySelector('.node-type-REGULATION circle');

      // Find requirement node (should be smaller)
      const requirementNode = container.querySelector('.node-type-REQUIREMENT circle');

      if (regulationNode && requirementNode) {
        const regulationRadius = parseFloat(regulationNode.getAttribute('r'));
        const requirementRadius = parseFloat(requirementNode.getAttribute('r'));

        // Regulation nodes should be larger than requirement nodes
        expect(regulationRadius).toBeGreaterThan(requirementRadius);
      }
    }, 100);
  });
});
