/**
 * Node types for the regulatory map
 */
export const NodeTypes = {
  REGULATION: 'REGULATION',
  REQUIREMENT: 'REQUIREMENT',
  CONTROL: 'CONTROL',
  BUSINESS_PROCESS: 'BUSINESS_PROCESS',
  SYSTEM: 'SYSTEM',
  DOCUMENT: 'DOCUMENT'
};

/**
 * Edge types for the regulatory map
 */
export const EdgeTypes = {
  CONTAINS: 'CONTAINS',
  REFERENCES: 'REFERENCES',
  IMPLEMENTS: 'IMPLEMENTS',
  IMPACTS: 'IMPACTS',
  USES: 'USES',
  SUPPORTS: 'SUPPORTS',
  CONFLICTS: 'CONFLICTS',
  SUPERSEDES: 'SUPERSEDES',
  DERIVES_FROM: 'DERIVES_FROM'
};

/**
 * Compliance status values
 */
export const ComplianceStatus = {
  COMPLIANT: 'COMPLIANT',
  PARTIALLY_COMPLIANT: 'PARTIALLY_COMPLIANT',
  NON_COMPLIANT: 'NON_COMPLIANT',
  NOT_APPLICABLE: 'NOT_APPLICABLE',
  UNKNOWN: 'UNKNOWN'
};

/**
 * Layout types for the regulatory map
 */
export const LayoutTypes = {
  FORCE: 'force',
  HIERARCHICAL: 'hierarchical',
  RADIAL: 'radial',
  GRID: 'grid',
  GEOGRAPHIC: 'geographic'
};

/**
 * View modes for the regulatory map
 */
export const ViewModes = {
  GRAPH: 'graph',
  HIERARCHICAL: 'hierarchical',
  JURISDICTIONAL: 'jurisdictional',
  IMPACT: 'impact',
  TIMELINE: 'timeline'
};

/**
 * Filter operators
 */
export const FilterOperators = {
  EQUALS: 'equals',
  NOT_EQUALS: 'not_equals',
  CONTAINS: 'contains',
  NOT_CONTAINS: 'not_contains',
  STARTS_WITH: 'starts_with',
  ENDS_WITH: 'ends_with',
  GREATER_THAN: 'greater_than',
  LESS_THAN: 'less_than',
  IN: 'in',
  NOT_IN: 'not_in',
  EXISTS: 'exists',
  NOT_EXISTS: 'not_exists'
};

/**
 * Default color schemes
 */
export const ColorSchemes = {
  STANDARD: {
    types: {
      [NodeTypes.REGULATION]: '#1976D2',      // Blue
      [NodeTypes.REQUIREMENT]: '#42A5F5',     // Light blue
      [NodeTypes.CONTROL]: '#7E57C2',         // Purple
      [NodeTypes.BUSINESS_PROCESS]: '#26A69A', // Teal
      [NodeTypes.SYSTEM]: '#FF7043',          // Deep orange
      [NodeTypes.DOCUMENT]: '#78909C'         // Blue-gray
    },
    status: {
      [ComplianceStatus.COMPLIANT]: '#4CAF50',        // Green
      [ComplianceStatus.PARTIALLY_COMPLIANT]: '#FFC107', // Yellow
      [ComplianceStatus.NON_COMPLIANT]: '#F44336',    // Red
      [ComplianceStatus.NOT_APPLICABLE]: '#9E9E9E',   // Gray
      [ComplianceStatus.UNKNOWN]: '#78909C'           // Blue-gray
    },
    edges: {
      [EdgeTypes.CONTAINS]: '#666666',
      [EdgeTypes.REFERENCES]: '#999999',
      [EdgeTypes.IMPLEMENTS]: '#7E57C2',
      [EdgeTypes.IMPACTS]: '#1976D2',
      [EdgeTypes.USES]: '#26A69A',
      [EdgeTypes.SUPPORTS]: '#66BB6A',
      [EdgeTypes.CONFLICTS]: '#F44336',
      [EdgeTypes.SUPERSEDES]: '#FF9800',
      [EdgeTypes.DERIVES_FROM]: '#8D6E63'
    }
  },
  
  COLOR_BLIND_SAFE: {
    types: {
      [NodeTypes.REGULATION]: '#0072B2',      // Blue
      [NodeTypes.REQUIREMENT]: '#56B4E9',     // Light blue
      [NodeTypes.CONTROL]: '#CC79A7',         // Pink
      [NodeTypes.BUSINESS_PROCESS]: '#009E73', // Green
      [NodeTypes.SYSTEM]: '#E69F00',          // Orange
      [NodeTypes.DOCUMENT]: '#999999'         // Gray
    },
    status: {
      [ComplianceStatus.COMPLIANT]: '#009E73',        // Green
      [ComplianceStatus.PARTIALLY_COMPLIANT]: '#E69F00', // Orange
      [ComplianceStatus.NON_COMPLIANT]: '#D55E00',    // Red
      [ComplianceStatus.NOT_APPLICABLE]: '#999999',   // Gray
      [ComplianceStatus.UNKNOWN]: '#56B4E9'           // Light blue
    }
  },
  
  HIGH_CONTRAST: {
    types: {
      [NodeTypes.REGULATION]: '#000000',      // Black
      [NodeTypes.REQUIREMENT]: '#0000FF',     // Blue
      [NodeTypes.CONTROL]: '#FF0000',         // Red
      [NodeTypes.BUSINESS_PROCESS]: '#008000', // Green
      [NodeTypes.SYSTEM]: '#800080',          // Purple
      [NodeTypes.DOCUMENT]: '#808080'         // Gray
    },
    status: {
      [ComplianceStatus.COMPLIANT]: '#008000',        // Green
      [ComplianceStatus.PARTIALLY_COMPLIANT]: '#FFA500', // Orange
      [ComplianceStatus.NON_COMPLIANT]: '#FF0000',    // Red
      [ComplianceStatus.NOT_APPLICABLE]: '#000000',   // Black
      [ComplianceStatus.UNKNOWN]: '#0000FF'           // Blue
    }
  }
};
