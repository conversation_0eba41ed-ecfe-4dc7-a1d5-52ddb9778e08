import { useState, useEffect, useRef } from 'react';

/**
 * Custom hook that observes and returns the dimensions of a DOM element
 * @param {React.RefObject} elementRef - React ref object for the element to observe
 * @returns {Object|null} - The dimensions of the element (width, height) or null if not available
 */
export const useResizeObserver = (elementRef) => {
  const [dimensions, setDimensions] = useState(null);
  const observerRef = useRef(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // Create new ResizeObserver instance
    observerRef.current = new ResizeObserver((entries) => {
      if (entries.length === 0) return;
      
      const entry = entries[0];
      
      // Use contentRect for older browsers
      if (entry.contentRect) {
        const { width, height } = entry.contentRect;
        setDimensions({ width, height });
      } 
      // Use borderBoxSize for newer browsers (more accurate)
      else if (entry.borderBoxSize) {
        // Handle both array form and single object form
        const borderBoxSize = Array.isArray(entry.borderBoxSize) 
          ? entry.borderBoxSize[0] 
          : entry.borderBoxSize;
        
        setDimensions({
          width: borderBoxSize.inlineSize,
          height: borderBoxSize.blockSize
        });
      }
    });

    // Start observing the element
    observerRef.current.observe(element);

    // Set initial dimensions
    setDimensions({
      width: element.offsetWidth,
      height: element.offsetHeight
    });

    // Cleanup: stop observing when component unmounts
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [elementRef]);

  return dimensions;
};

export default useResizeObserver;
