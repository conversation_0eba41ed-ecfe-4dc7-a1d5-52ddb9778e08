import { renderHook, act } from '@testing-library/react-hooks';
import { useRegulatoryMap } from '../useRegulatoryMap';
import regulatoryMapService from '../../services/regulatoryMapService';

// Mock the regulatory map service
jest.mock('../../services/regulatoryMapService', () => ({
  getMapData: jest.fn(),
  getEntity: jest.fn(),
  getRelatedEntities: jest.fn(),
  saveView: jest.fn(),
  getView: jest.fn(),
  getUserViews: jest.fn(),
  addAnnotation: jest.fn(),
  getAnnotations: jest.fn(),
  getImpactAssessment: jest.fn(),
  generateReport: jest.fn(),
  exportImage: jest.fn()
}));

// Mock URL.createObjectURL
global.URL.createObjectURL = jest.fn(() => 'mock-url');

// Mock document methods for download
document.createElement = jest.fn(() => ({
  href: '',
  download: '',
  click: jest.fn()
}));
document.body.appendChild = jest.fn();
document.body.removeChild = jest.fn();

describe('useRegulatoryMap', () => {
  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  test('fetches map data on initial render', async () => {
    // Mock data
    const mockData = {
      nodes: [{ id: 'node1', name: 'Test Node' }],
      edges: [{ id: 'edge1', source: 'node1', target: 'node2' }]
    };
    
    // Mock service response
    regulatoryMapService.getMapData.mockResolvedValueOnce(mockData);
    regulatoryMapService.getUserViews.mockResolvedValueOnce([]);
    
    // Render the hook
    const { result, waitForNextUpdate } = renderHook(() => useRegulatoryMap());
    
    // Initially loading should be true
    expect(result.current.loading).toBe(true);
    
    // Wait for the data to load
    await waitForNextUpdate();
    
    // Check if the service was called
    expect(regulatoryMapService.getMapData).toHaveBeenCalled();
    
    // Check if the data was set correctly
    expect(result.current.data).toEqual(mockData);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBeNull();
  });
  
  test('handles errors when fetching map data', async () => {
    // Mock error
    const error = new Error('Failed to fetch data');
    
    // Mock service response
    regulatoryMapService.getMapData.mockRejectedValueOnce(error);
    regulatoryMapService.getUserViews.mockResolvedValueOnce([]);
    
    // Render the hook
    const { result, waitForNextUpdate } = renderHook(() => useRegulatoryMap());
    
    // Wait for the error to be set
    await waitForNextUpdate();
    
    // Check if the error was set correctly
    expect(result.current.error).toBe('Failed to fetch data');
    expect(result.current.loading).toBe(false);
    expect(result.current.data).toBeNull();
  });
  
  test('applies filters and refetches data', async () => {
    // Mock data
    const mockData1 = { nodes: [{ id: 'node1' }], edges: [] };
    const mockData2 = { nodes: [{ id: 'node2' }], edges: [] };
    
    // Mock service responses
    regulatoryMapService.getMapData.mockResolvedValueOnce(mockData1);
    regulatoryMapService.getUserViews.mockResolvedValueOnce([]);
    
    // Render the hook
    const { result, waitForNextUpdate } = renderHook(() => useRegulatoryMap());
    
    // Wait for the initial data to load
    await waitForNextUpdate();
    
    // Mock the second service call
    regulatoryMapService.getMapData.mockResolvedValueOnce(mockData2);
    
    // Apply filters
    const newFilters = { nodeTypes: ['REGULATION'] };
    act(() => {
      result.current.applyFilters(newFilters);
    });
    
    // Wait for the data to reload
    await waitForNextUpdate();
    
    // Check if the service was called with the filters
    expect(regulatoryMapService.getMapData).toHaveBeenCalledWith(newFilters);
    
    // Check if the data was updated
    expect(result.current.data).toEqual(mockData2);
    expect(result.current.filters).toEqual(newFilters);
  });
  
  test('handles selection changes', () => {
    // Render the hook
    const { result } = renderHook(() => useRegulatoryMap());
    
    // Initial selection should be empty
    expect(result.current.selectedEntities).toEqual({ nodeIds: [], edgeIds: [] });
    
    // Update selection
    const newSelection = { nodeIds: ['node1'], edgeIds: [] };
    act(() => {
      result.current.handleSelectionChange(newSelection);
    });
    
    // Check if the selection was updated
    expect(result.current.selectedEntities).toEqual(newSelection);
  });
  
  test('saves and loads views', async () => {
    // Mock data
    const mockView = {
      id: 'view1',
      name: 'Test View',
      filters: { nodeTypes: ['REGULATION'] }
    };
    
    // Mock service responses
    regulatoryMapService.getMapData.mockResolvedValue({ nodes: [], edges: [] });
    regulatoryMapService.getUserViews.mockResolvedValue([]);
    regulatoryMapService.saveView.mockResolvedValueOnce(mockView);
    regulatoryMapService.getView.mockResolvedValueOnce(mockView);
    
    // Render the hook
    const { result, waitForNextUpdate } = renderHook(() => useRegulatoryMap());
    
    // Wait for the initial data to load
    await waitForNextUpdate();
    
    // Save a view
    let savedView;
    await act(async () => {
      savedView = await result.current.saveView({ name: 'Test View' });
    });
    
    // Check if the service was called correctly
    expect(regulatoryMapService.saveView).toHaveBeenCalledWith({ name: 'Test View' });
    
    // Check if the view was saved correctly
    expect(savedView).toEqual(mockView);
    
    // Load a view
    let loadedView;
    await act(async () => {
      loadedView = await result.current.loadView('view1');
    });
    
    // Check if the service was called correctly
    expect(regulatoryMapService.getView).toHaveBeenCalledWith('view1');
    
    // Check if the view was loaded correctly
    expect(loadedView).toEqual(mockView);
    
    // Check if the filters were updated
    expect(result.current.filters).toEqual(mockView.filters);
  });
  
  test('exports an image', async () => {
    // Mock data
    const mockBlob = new Blob(['mock image data'], { type: 'image/png' });
    
    // Mock service responses
    regulatoryMapService.getMapData.mockResolvedValue({ nodes: [], edges: [] });
    regulatoryMapService.getUserViews.mockResolvedValue([]);
    regulatoryMapService.exportImage.mockResolvedValueOnce(mockBlob);
    
    // Render the hook
    const { result, waitForNextUpdate } = renderHook(() => useRegulatoryMap());
    
    // Wait for the initial data to load
    await waitForNextUpdate();
    
    // Export an image
    let success;
    await act(async () => {
      success = await result.current.exportImage('png', { width: 1200, height: 800 });
    });
    
    // Check if the service was called correctly
    expect(regulatoryMapService.exportImage).toHaveBeenCalledWith('png', { width: 1200, height: 800 });
    
    // Check if the export was successful
    expect(success).toBe(true);
    
    // Check if the download link was created and clicked
    expect(URL.createObjectURL).toHaveBeenCalledWith(mockBlob);
    expect(document.createElement).toHaveBeenCalledWith('a');
    expect(document.body.appendChild).toHaveBeenCalled();
    expect(document.body.removeChild).toHaveBeenCalled();
  });
  
  test('generates a report', async () => {
    // Mock data
    const mockReport = {
      success: true,
      reportUrl: 'https://example.com/report.pdf'
    };
    
    // Mock service responses
    regulatoryMapService.getMapData.mockResolvedValue({ nodes: [], edges: [] });
    regulatoryMapService.getUserViews.mockResolvedValue([]);
    regulatoryMapService.generateReport.mockResolvedValueOnce(mockReport);
    
    // Render the hook
    const { result, waitForNextUpdate } = renderHook(() => useRegulatoryMap());
    
    // Wait for the initial data to load
    await waitForNextUpdate();
    
    // Generate a report
    let reportResult;
    await act(async () => {
      reportResult = await result.current.generateReport('compliance', { format: 'pdf' });
    });
    
    // Check if the service was called correctly
    expect(regulatoryMapService.generateReport).toHaveBeenCalledWith('compliance', { format: 'pdf' });
    
    // Check if the report was generated correctly
    expect(reportResult).toEqual(mockReport);
  });
});
