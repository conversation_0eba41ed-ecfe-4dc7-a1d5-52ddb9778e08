import { useState, useEffect, useCallback } from 'react';
import regulatoryMapService from '../services/regulatoryMapService';

/**
 * Custom hook for working with the regulatory map
 * @param {Object} initialFilters - Initial filters to apply
 * @returns {Object} - Regulatory map state and functions
 */
export const useRegulatoryMap = (initialFilters = {}) => {
  // State
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState(initialFilters);
  const [selectedEntities, setSelectedEntities] = useState({ nodeIds: [], edgeIds: [] });
  const [views, setViews] = useState([]);
  
  // Fetch map data
  const fetchMapData = useCallback(async (filterOptions = filters) => {
    try {
      setLoading(true);
      setError(null);
      
      const mapData = await regulatoryMapService.getMapData(filterOptions);
      setData(mapData);
      
      return mapData;
    } catch (err) {
      setError(err.message || 'Failed to fetch regulatory map data');
      return null;
    } finally {
      setLoading(false);
    }
  }, [filters]);
  
  // Fetch entity details
  const fetchEntityDetails = useCallback(async (entityId) => {
    try {
      setLoading(true);
      setError(null);
      
      const entityData = await regulatoryMapService.getEntity(entityId);
      return entityData;
    } catch (err) {
      setError(err.message || `Failed to fetch entity ${entityId}`);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);
  
  // Fetch related entities
  const fetchRelatedEntities = useCallback(async (entityId, options) => {
    try {
      setLoading(true);
      setError(null);
      
      const relatedData = await regulatoryMapService.getRelatedEntities(entityId, options);
      return relatedData;
    } catch (err) {
      setError(err.message || `Failed to fetch related entities for ${entityId}`);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);
  
  // Save view
  const saveView = useCallback(async (viewData) => {
    try {
      setLoading(true);
      setError(null);
      
      const savedView = await regulatoryMapService.saveView(viewData);
      
      // Update views list
      setViews(prevViews => {
        const existingIndex = prevViews.findIndex(v => v.id === savedView.id);
        if (existingIndex >= 0) {
          // Update existing view
          return [
            ...prevViews.slice(0, existingIndex),
            savedView,
            ...prevViews.slice(existingIndex + 1)
          ];
        } else {
          // Add new view
          return [...prevViews, savedView];
        }
      });
      
      return savedView;
    } catch (err) {
      setError(err.message || 'Failed to save view');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);
  
  // Load view
  const loadView = useCallback(async (viewId) => {
    try {
      setLoading(true);
      setError(null);
      
      const viewData = await regulatoryMapService.getView(viewId);
      
      // Apply view settings
      if (viewData.filters) {
        setFilters(viewData.filters);
      }
      
      return viewData;
    } catch (err) {
      setError(err.message || `Failed to load view ${viewId}`);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);
  
  // Fetch user views
  const fetchUserViews = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const userViews = await regulatoryMapService.getUserViews();
      setViews(userViews);
      
      return userViews;
    } catch (err) {
      setError(err.message || 'Failed to fetch user views');
      return [];
    } finally {
      setLoading(false);
    }
  }, []);
  
  // Add annotation
  const addAnnotation = useCallback(async (entityId, annotationData) => {
    try {
      setLoading(true);
      setError(null);
      
      const savedAnnotation = await regulatoryMapService.addAnnotation(entityId, annotationData);
      return savedAnnotation;
    } catch (err) {
      setError(err.message || `Failed to add annotation to entity ${entityId}`);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);
  
  // Fetch annotations
  const fetchAnnotations = useCallback(async (entityId) => {
    try {
      setLoading(true);
      setError(null);
      
      const annotations = await regulatoryMapService.getAnnotations(entityId);
      return annotations;
    } catch (err) {
      setError(err.message || `Failed to fetch annotations for entity ${entityId}`);
      return [];
    } finally {
      setLoading(false);
    }
  }, []);
  
  // Fetch impact assessment
  const fetchImpactAssessment = useCallback(async (entityId) => {
    try {
      setLoading(true);
      setError(null);
      
      const impactData = await regulatoryMapService.getImpactAssessment(entityId);
      return impactData;
    } catch (err) {
      setError(err.message || `Failed to fetch impact assessment for entity ${entityId}`);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);
  
  // Generate report
  const generateReport = useCallback(async (reportType, reportData) => {
    try {
      setLoading(true);
      setError(null);
      
      const reportResult = await regulatoryMapService.generateReport(reportType, reportData);
      return reportResult;
    } catch (err) {
      setError(err.message || `Failed to generate ${reportType} report`);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);
  
  // Export image
  const exportImage = useCallback(async (format, options) => {
    try {
      setLoading(true);
      setError(null);
      
      const imageBlob = await regulatoryMapService.exportImage(format, options);
      
      // Create a download link
      const url = URL.createObjectURL(imageBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `regulatory-map.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      return true;
    } catch (err) {
      setError(err.message || `Failed to export image as ${format}`);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);
  
  // Apply filters
  const applyFilters = useCallback((newFilters) => {
    setFilters(newFilters);
  }, []);
  
  // Handle selection change
  const handleSelectionChange = useCallback((selection) => {
    setSelectedEntities(selection);
  }, []);
  
  // Load initial data
  useEffect(() => {
    fetchMapData();
    fetchUserViews();
  }, [fetchMapData, fetchUserViews]);
  
  // Refetch when filters change
  useEffect(() => {
    fetchMapData(filters);
  }, [filters, fetchMapData]);
  
  return {
    // State
    data,
    loading,
    error,
    filters,
    selectedEntities,
    views,
    
    // Actions
    fetchMapData,
    fetchEntityDetails,
    fetchRelatedEntities,
    saveView,
    loadView,
    fetchUserViews,
    addAnnotation,
    fetchAnnotations,
    fetchImpactAssessment,
    generateReport,
    exportImage,
    applyFilters,
    handleSelectionChange
  };
};

export default useRegulatoryMap;
