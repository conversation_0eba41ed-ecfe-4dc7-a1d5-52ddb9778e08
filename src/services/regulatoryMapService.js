import axios from 'axios';

// Base API URL - would typically come from environment variables
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:3001/api';

/**
 * Service for interacting with the Regulatory Map API
 */
class RegulatoryMapService {
  /**
   * Get the regulatory map data
   * @param {Object} options - Query options
   * @param {string[]} options.jurisdictions - Filter by jurisdictions
   * @param {string[]} options.categories - Filter by categories
   * @param {string[]} options.nodeTypes - Filter by node types
   * @param {string[]} options.statuses - Filter by compliance statuses
   * @returns {Promise<Object>} - The regulatory map data
   */
  async getMapData(options = {}) {
    try {
      const response = await axios.get(`${API_BASE_URL}/regulatory-map`, {
        params: options
      });
      
      return response.data;
    } catch (error) {
      console.error('Error fetching regulatory map data:', error);
      throw error;
    }
  }
  
  /**
   * Get a specific entity by ID
   * @param {string} entityId - The entity ID
   * @returns {Promise<Object>} - The entity data
   */
  async getEntity(entityId) {
    try {
      const response = await axios.get(`${API_BASE_URL}/entities/${entityId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching entity ${entityId}:`, error);
      throw error;
    }
  }
  
  /**
   * Get related entities for a specific entity
   * @param {string} entityId - The entity ID
   * @param {Object} options - Query options
   * @param {number} options.depth - Relationship depth (default: 1)
   * @param {string[]} options.relationshipTypes - Filter by relationship types
   * @returns {Promise<Object>} - The related entities data
   */
  async getRelatedEntities(entityId, options = {}) {
    try {
      const response = await axios.get(`${API_BASE_URL}/entities/${entityId}/related`, {
        params: options
      });
      
      return response.data;
    } catch (error) {
      console.error(`Error fetching related entities for ${entityId}:`, error);
      throw error;
    }
  }
  
  /**
   * Save a view
   * @param {Object} viewData - The view data to save
   * @returns {Promise<Object>} - The saved view data
   */
  async saveView(viewData) {
    try {
      const response = await axios.post(`${API_BASE_URL}/views`, viewData);
      return response.data;
    } catch (error) {
      console.error('Error saving view:', error);
      throw error;
    }
  }
  
  /**
   * Get a saved view by ID
   * @param {string} viewId - The view ID
   * @returns {Promise<Object>} - The view data
   */
  async getView(viewId) {
    try {
      const response = await axios.get(`${API_BASE_URL}/views/${viewId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching view ${viewId}:`, error);
      throw error;
    }
  }
  
  /**
   * Get all saved views for the current user
   * @returns {Promise<Array>} - The list of saved views
   */
  async getUserViews() {
    try {
      const response = await axios.get(`${API_BASE_URL}/views`);
      return response.data;
    } catch (error) {
      console.error('Error fetching user views:', error);
      throw error;
    }
  }
  
  /**
   * Add an annotation to an entity
   * @param {string} entityId - The entity ID
   * @param {Object} annotationData - The annotation data
   * @returns {Promise<Object>} - The saved annotation data
   */
  async addAnnotation(entityId, annotationData) {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/entities/${entityId}/annotations`,
        annotationData
      );
      
      return response.data;
    } catch (error) {
      console.error(`Error adding annotation to entity ${entityId}:`, error);
      throw error;
    }
  }
  
  /**
   * Get annotations for an entity
   * @param {string} entityId - The entity ID
   * @returns {Promise<Array>} - The list of annotations
   */
  async getAnnotations(entityId) {
    try {
      const response = await axios.get(`${API_BASE_URL}/entities/${entityId}/annotations`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching annotations for entity ${entityId}:`, error);
      throw error;
    }
  }
  
  /**
   * Get impact assessment for an entity
   * @param {string} entityId - The entity ID
   * @returns {Promise<Object>} - The impact assessment data
   */
  async getImpactAssessment(entityId) {
    try {
      const response = await axios.get(`${API_BASE_URL}/entities/${entityId}/impact`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching impact assessment for entity ${entityId}:`, error);
      throw error;
    }
  }
  
  /**
   * Generate a report
   * @param {string} reportType - The report type
   * @param {Object} reportData - The report data
   * @returns {Promise<Object>} - The report generation result
   */
  async generateReport(reportType, reportData) {
    try {
      const response = await axios.post(`${API_BASE_URL}/reports/${reportType}`, reportData);
      return response.data;
    } catch (error) {
      console.error(`Error generating ${reportType} report:`, error);
      throw error;
    }
  }
  
  /**
   * Export the map as an image
   * @param {string} format - The image format (png, svg)
   * @param {Object} options - Export options
   * @returns {Promise<Blob>} - The exported image blob
   */
  async exportImage(format, options = {}) {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/export/image/${format}`,
        options,
        { responseType: 'blob' }
      );
      
      return response.data;
    } catch (error) {
      console.error(`Error exporting image as ${format}:`, error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const regulatoryMapService = new RegulatoryMapService();
export default regulatoryMapService;
