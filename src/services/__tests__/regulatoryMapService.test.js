import axios from 'axios';
import regulatoryMapService from '../regulatoryMapService';
import { NodeTypes, ComplianceStatus } from '../../constants/regulatoryMap';

// Mock axios
jest.mock('axios');

describe('RegulatoryMapService', () => {
  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  describe('getMapData', () => {
    test('fetches regulatory map data successfully', async () => {
      // Mock data
      const mockData = {
        nodes: [
          { id: 'node1', type: NodeTypes.REGULATION, name: 'Test Regulation' }
        ],
        edges: [
          { id: 'edge1', source: 'node1', target: 'node2', type: 'CONTAINS' }
        ]
      };
      
      // Mock axios response
      axios.get.mockResolvedValueOnce({ data: mockData });
      
      // Call the service
      const result = await regulatoryMapService.getMapData();
      
      // Check if axios was called correctly
      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining('/regulatory-map'),
        expect.any(Object)
      );
      
      // Check if the result is correct
      expect(result).toEqual(mockData);
    });
    
    test('handles errors when fetching map data', async () => {
      // Mock axios error
      const error = new Error('Network error');
      axios.get.mockRejectedValueOnce(error);
      
      // Call the service and expect it to throw
      await expect(regulatoryMapService.getMapData()).rejects.toThrow();
      
      // Check if axios was called
      expect(axios.get).toHaveBeenCalled();
    });
    
    test('applies filters correctly', async () => {
      // Mock data
      const mockData = { nodes: [], edges: [] };
      
      // Mock axios response
      axios.get.mockResolvedValueOnce({ data: mockData });
      
      // Call the service with filters
      const filters = {
        jurisdictions: ['EU', 'US'],
        categories: ['Privacy'],
        nodeTypes: [NodeTypes.REGULATION],
        statuses: [ComplianceStatus.COMPLIANT]
      };
      
      await regulatoryMapService.getMapData(filters);
      
      // Check if axios was called with the correct parameters
      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining('/regulatory-map'),
        {
          params: filters
        }
      );
    });
  });
  
  describe('getEntity', () => {
    test('fetches entity details successfully', async () => {
      // Mock data
      const mockEntity = {
        id: 'entity1',
        type: NodeTypes.REGULATION,
        name: 'Test Entity'
      };
      
      // Mock axios response
      axios.get.mockResolvedValueOnce({ data: mockEntity });
      
      // Call the service
      const result = await regulatoryMapService.getEntity('entity1');
      
      // Check if axios was called correctly
      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining('/entities/entity1')
      );
      
      // Check if the result is correct
      expect(result).toEqual(mockEntity);
    });
    
    test('handles errors when fetching entity details', async () => {
      // Mock axios error
      const error = new Error('Entity not found');
      axios.get.mockRejectedValueOnce(error);
      
      // Call the service and expect it to throw
      await expect(regulatoryMapService.getEntity('invalid-id')).rejects.toThrow();
      
      // Check if axios was called
      expect(axios.get).toHaveBeenCalled();
    });
  });
  
  describe('saveView', () => {
    test('saves a view successfully', async () => {
      // Mock data
      const viewData = {
        name: 'Test View',
        config: { showLabels: true },
        filters: []
      };
      
      const savedView = {
        ...viewData,
        id: 'view1',
        createdAt: '2023-01-01T00:00:00Z'
      };
      
      // Mock axios response
      axios.post.mockResolvedValueOnce({ data: savedView });
      
      // Call the service
      const result = await regulatoryMapService.saveView(viewData);
      
      // Check if axios was called correctly
      expect(axios.post).toHaveBeenCalledWith(
        expect.stringContaining('/views'),
        viewData
      );
      
      // Check if the result is correct
      expect(result).toEqual(savedView);
    });
    
    test('handles errors when saving a view', async () => {
      // Mock axios error
      const error = new Error('Failed to save view');
      axios.post.mockRejectedValueOnce(error);
      
      // Call the service and expect it to throw
      await expect(regulatoryMapService.saveView({})).rejects.toThrow();
      
      // Check if axios was called
      expect(axios.post).toHaveBeenCalled();
    });
  });
  
  describe('getView', () => {
    test('fetches a view successfully', async () => {
      // Mock data
      const mockView = {
        id: 'view1',
        name: 'Test View',
        config: { showLabels: true },
        filters: []
      };
      
      // Mock axios response
      axios.get.mockResolvedValueOnce({ data: mockView });
      
      // Call the service
      const result = await regulatoryMapService.getView('view1');
      
      // Check if axios was called correctly
      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining('/views/view1')
      );
      
      // Check if the result is correct
      expect(result).toEqual(mockView);
    });
    
    test('handles errors when fetching a view', async () => {
      // Mock axios error
      const error = new Error('View not found');
      axios.get.mockRejectedValueOnce(error);
      
      // Call the service and expect it to throw
      await expect(regulatoryMapService.getView('invalid-id')).rejects.toThrow();
      
      // Check if axios was called
      expect(axios.get).toHaveBeenCalled();
    });
  });
  
  describe('generateReport', () => {
    test('generates a report successfully', async () => {
      // Mock data
      const reportData = {
        filters: [],
        format: 'pdf'
      };
      
      const reportResult = {
        success: true,
        reportUrl: 'https://example.com/report.pdf'
      };
      
      // Mock axios response
      axios.post.mockResolvedValueOnce({ data: reportResult });
      
      // Call the service
      const result = await regulatoryMapService.generateReport('compliance', reportData);
      
      // Check if axios was called correctly
      expect(axios.post).toHaveBeenCalledWith(
        expect.stringContaining('/reports/compliance'),
        reportData
      );
      
      // Check if the result is correct
      expect(result).toEqual(reportResult);
    });
    
    test('handles errors when generating a report', async () => {
      // Mock axios error
      const error = new Error('Failed to generate report');
      axios.post.mockRejectedValueOnce(error);
      
      // Call the service and expect it to throw
      await expect(regulatoryMapService.generateReport('compliance', {})).rejects.toThrow();
      
      // Check if axios was called
      expect(axios.post).toHaveBeenCalled();
    });
  });
  
  describe('exportImage', () => {
    test('exports an image successfully', async () => {
      // Mock data
      const options = {
        width: 1200,
        height: 800
      };
      
      const mockBlob = new Blob(['mock image data'], { type: 'image/png' });
      
      // Mock axios response
      axios.post.mockResolvedValueOnce({ data: mockBlob });
      
      // Call the service
      const result = await regulatoryMapService.exportImage('png', options);
      
      // Check if axios was called correctly
      expect(axios.post).toHaveBeenCalledWith(
        expect.stringContaining('/export/image/png'),
        options,
        { responseType: 'blob' }
      );
      
      // Check if the result is correct
      expect(result).toEqual(mockBlob);
    });
    
    test('handles errors when exporting an image', async () => {
      // Mock axios error
      const error = new Error('Failed to export image');
      axios.post.mockRejectedValueOnce(error);
      
      // Call the service and expect it to throw
      await expect(regulatoryMapService.exportImage('png', {})).rejects.toThrow();
      
      // Check if axios was called
      expect(axios.post).toHaveBeenCalled();
    });
  });
});
