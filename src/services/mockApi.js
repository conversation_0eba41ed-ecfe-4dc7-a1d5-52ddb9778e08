import { createServer, Model, Response } from 'miragejs';
import { NodeTypes, EdgeTypes, ComplianceStatus } from '../constants/regulatoryMap';

/**
 * Create a mock API server for development and testing
 * @param {Object} options - Configuration options
 * @returns {Object} - The mock server instance
 */
export function createMockServer(options = {}) {
  return createServer({
    models: {
      entity: Model,
      view: Model,
      annotation: Model,
      impactAssessment: Model
    },
    
    seeds(server) {
      // Seed with sample data
      const { nodes, edges } = generateSampleData();
      
      // Add entities
      nodes.forEach(node => {
        server.create('entity', {
          ...node,
          relationships: edges
            .filter(edge => edge.source === node.id || edge.target === node.id)
            .map(edge => ({
              id: edge.id,
              type: edge.type,
              sourceId: edge.source,
              targetId: edge.target
            }))
        });
      });
      
      // Add sample views
      server.create('view', {
        id: 'default',
        name: 'Default View',
        description: 'Default system view',
        userId: 'system',
        isPublic: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        viewState: {
          filters: [],
          viewport: { center: { x: 0, y: 0 }, zoom: 1 },
          layout: 'force',
          selectedEntities: [],
          visibleTypes: Object.values(NodeTypes),
          visibleRelationships: Object.values(EdgeTypes)
        }
      });
      
      server.create('view', {
        id: 'compliance-overview',
        name: 'Compliance Overview',
        description: 'Overview of compliance status',
        userId: 'system',
        isPublic: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        viewState: {
          filters: [
            {
              id: 'status',
              type: 'status',
              values: [ComplianceStatus.NON_COMPLIANT, ComplianceStatus.PARTIALLY_COMPLIANT],
              enabled: true
            }
          ],
          viewport: { center: { x: 0, y: 0 }, zoom: 1 },
          layout: 'force',
          selectedEntities: [],
          visibleTypes: Object.values(NodeTypes),
          visibleRelationships: Object.values(EdgeTypes)
        }
      });
      
      // Add sample impact assessments
      server.create('impactAssessment', {
        id: 'impact1',
        entityId: 'reg1',
        overallImpact: 'Medium',
        impactAreas: [
          {
            area: 'IT Systems',
            score: 0.8,
            description: 'High impact on IT systems',
            affectedEntities: [
              { entityId: 'sys1', entityType: NodeTypes.SYSTEM, impactLevel: 'High' }
            ]
          },
          {
            area: 'Business Processes',
            score: 0.6,
            description: 'Medium impact on business processes',
            affectedEntities: [
              { entityId: 'proc1', entityType: NodeTypes.BUSINESS_PROCESS, impactLevel: 'Medium' }
            ]
          },
          {
            area: 'Training',
            score: 0.3,
            description: 'Low impact on training requirements',
            affectedEntities: []
          }
        ],
        implementationEffort: 'High',
        implementationCost: {
          amount: 150000,
          currency: 'USD',
          breakdown: [
            { category: 'IT Development', amount: 80000 },
            { category: 'Process Changes', amount: 40000 },
            { category: 'Training', amount: 30000 }
          ]
        },
        timeline: {
          startDate: '2023-01-01',
          endDate: '2023-06-30',
          milestones: [
            { name: 'Requirements Analysis', date: '2023-01-31' },
            { name: 'Implementation', date: '2023-04-30' },
            { name: 'Testing', date: '2023-05-31' },
            { name: 'Deployment', date: '2023-06-30' }
          ]
        },
        risks: [
          {
            description: 'Tight timeline',
            likelihood: 'Medium',
            impact: 'High',
            mitigationStrategy: 'Prioritize critical requirements'
          },
          {
            description: 'Resource constraints',
            likelihood: 'High',
            impact: 'Medium',
            mitigationStrategy: 'Secure additional budget for contractors'
          }
        ]
      });
    },
    
    routes() {
      this.namespace = 'api';
      
      // Get regulatory map data
      this.get('/regulatory-map', (schema, request) => {
        const { jurisdictions, categories, nodeTypes, statuses } = request.queryParams;
        
        let nodes = schema.entities.all().models;
        
        // Apply filters if provided
        if (jurisdictions) {
          const jurisdictionList = jurisdictions.split(',');
          nodes = nodes.filter(node => 
            node.metadata?.jurisdiction && jurisdictionList.includes(node.metadata.jurisdiction)
          );
        }
        
        if (categories) {
          const categoryList = categories.split(',');
          nodes = nodes.filter(node => {
            if (!node.metadata?.category) return false;
            
            if (Array.isArray(node.metadata.category)) {
              return node.metadata.category.some(cat => categoryList.includes(cat));
            }
            
            return categoryList.includes(node.metadata.category);
          });
        }
        
        if (nodeTypes) {
          const typeList = nodeTypes.split(',');
          nodes = nodes.filter(node => typeList.includes(node.type));
        }
        
        if (statuses) {
          const statusList = statuses.split(',');
          nodes = nodes.filter(node => statusList.includes(node.status));
        }
        
        // Extract node IDs for edge filtering
        const nodeIds = nodes.map(node => node.id);
        
        // Get all relationships from the filtered nodes
        const edges = [];
        nodes.forEach(node => {
          if (node.relationships) {
            node.relationships.forEach(rel => {
              // Only include relationships where both source and target are in the filtered nodes
              if (nodeIds.includes(rel.sourceId) && nodeIds.includes(rel.targetId)) {
                // Avoid duplicates
                if (!edges.some(e => e.id === rel.id)) {
                  edges.push({
                    id: rel.id,
                    source: rel.sourceId,
                    target: rel.targetId,
                    type: rel.type
                  });
                }
              }
            });
          }
        });
        
        // Return the filtered map data
        return {
          nodes: nodes.map(node => ({
            id: node.id,
            type: node.type,
            name: node.name,
            description: node.description,
            status: node.status,
            metadata: node.metadata,
            position: node.position,
            style: node.style,
            visibility: node.visibility
          })),
          edges
        };
      });
      
      // Get entity by ID
      this.get('/entities/:id', (schema, request) => {
        const id = request.params.id;
        const entity = schema.entities.findBy({ id });
        
        if (!entity) {
          return new Response(404, {}, { error: 'Entity not found' });
        }
        
        return entity;
      });
      
      // Get related entities
      this.get('/entities/:id/related', (schema, request) => {
        const id = request.params.id;
        const { depth = 1, relationshipTypes } = request.queryParams;
        
        const entity = schema.entities.findBy({ id });
        
        if (!entity) {
          return new Response(404, {}, { error: 'Entity not found' });
        }
        
        // Get direct relationships
        let relationships = entity.relationships || [];
        
        // Filter by relationship types if provided
        if (relationshipTypes) {
          const typeList = relationshipTypes.split(',');
          relationships = relationships.filter(rel => typeList.includes(rel.type));
        }
        
        // Get related entity IDs
        const relatedIds = relationships.map(rel => 
          rel.sourceId === id ? rel.targetId : rel.sourceId
        );
        
        // Get related entities
        const relatedEntities = schema.entities.where(entity => relatedIds.includes(entity.id));
        
        // Return the related entities and relationships
        return {
          entities: relatedEntities.models.map(entity => ({
            id: entity.id,
            type: entity.type,
            name: entity.name,
            description: entity.description,
            status: entity.status,
            metadata: entity.metadata
          })),
          relationships: relationships.map(rel => ({
            id: rel.id,
            source: rel.sourceId,
            target: rel.targetId,
            type: rel.type
          }))
        };
      });
      
      // Save view
      this.post('/views', (schema, request) => {
        const attrs = JSON.parse(request.requestBody);
        
        // Generate ID if not provided
        if (!attrs.id) {
          attrs.id = Math.random().toString(36).substring(2, 15);
        }
        
        // Set timestamps
        attrs.createdAt = new Date().toISOString();
        attrs.updatedAt = new Date().toISOString();
        
        // Create or update the view
        const existingView = schema.views.findBy({ id: attrs.id });
        
        if (existingView) {
          return existingView.update(attrs);
        }
        
        return schema.views.create(attrs);
      });
      
      // Get view by ID
      this.get('/views/:id', (schema, request) => {
        const id = request.params.id;
        const view = schema.views.findBy({ id });
        
        if (!view) {
          return new Response(404, {}, { error: 'View not found' });
        }
        
        return view;
      });
      
      // Get all views for the current user
      this.get('/views', (schema) => {
        // In a real API, this would filter by the current user
        return schema.views.all();
      });
      
      // Add annotation
      this.post('/entities/:id/annotations', (schema, request) => {
        const entityId = request.params.id;
        const attrs = JSON.parse(request.requestBody);
        
        // Check if entity exists
        const entity = schema.entities.findBy({ id: entityId });
        
        if (!entity) {
          return new Response(404, {}, { error: 'Entity not found' });
        }
        
        // Generate ID if not provided
        if (!attrs.id) {
          attrs.id = Math.random().toString(36).substring(2, 15);
        }
        
        // Set entity ID and timestamps
        attrs.entityId = entityId;
        attrs.createdAt = new Date().toISOString();
        attrs.updatedAt = new Date().toISOString();
        
        return schema.annotations.create(attrs);
      });
      
      // Get annotations for an entity
      this.get('/entities/:id/annotations', (schema, request) => {
        const entityId = request.params.id;
        
        // Check if entity exists
        const entity = schema.entities.findBy({ id: entityId });
        
        if (!entity) {
          return new Response(404, {}, { error: 'Entity not found' });
        }
        
        return schema.annotations.where({ entityId });
      });
      
      // Get impact assessment for an entity
      this.get('/entities/:id/impact', (schema, request) => {
        const entityId = request.params.id;
        
        // Check if entity exists
        const entity = schema.entities.findBy({ id: entityId });
        
        if (!entity) {
          return new Response(404, {}, { error: 'Entity not found' });
        }
        
        // Get impact assessment
        const impactAssessment = schema.impactAssessments.findBy({ entityId });
        
        if (!impactAssessment) {
          return new Response(404, {}, { error: 'Impact assessment not found' });
        }
        
        return impactAssessment;
      });
      
      // Generate report
      this.post('/reports/:type', (schema, request) => {
        const reportType = request.params.type;
        const reportData = JSON.parse(request.requestBody);
        
        // In a real API, this would generate a report
        return {
          success: true,
          reportType,
          reportUrl: `https://example.com/reports/${reportType}_${Date.now()}.pdf`,
          generatedAt: new Date().toISOString()
        };
      });
      
      // Export image
      this.post('/export/image/:format', (schema, request) => {
        const format = request.params.format;
        
        // In a real API, this would generate an image
        // For the mock, we'll just return a success message
        return new Response(200, {
          'Content-Type': format === 'png' ? 'image/png' : 'image/svg+xml',
          'Content-Disposition': `attachment; filename="regulatory-map.${format}"`
        }, '');
      });
    }
  });
}

/**
 * Generate sample data for the mock API
 * @returns {Object} - The sample data
 */
function generateSampleData() {
  // Create nodes
  const nodes = [
    // Regulations
    {
      id: 'reg1',
      type: NodeTypes.REGULATION,
      name: 'General Data Protection Regulation (GDPR)',
      description: 'Regulation on data protection and privacy in the EU and the European Economic Area.',
      status: ComplianceStatus.PARTIALLY_COMPLIANT,
      metadata: {
        regulatoryAuthority: 'European Union',
        jurisdiction: 'EU',
        effectiveDate: '2018-05-25',
        category: ['Privacy', 'Data Protection']
      }
    },
    {
      id: 'reg2',
      type: NodeTypes.REGULATION,
      name: 'California Consumer Privacy Act (CCPA)',
      description: 'State statute intended to enhance privacy rights and consumer protection for residents of California.',
      status: ComplianceStatus.COMPLIANT,
      metadata: {
        regulatoryAuthority: 'California State Legislature',
        jurisdiction: 'California, USA',
        effectiveDate: '2020-01-01',
        category: ['Privacy', 'Data Protection']
      }
    },
    {
      id: 'reg3',
      type: NodeTypes.REGULATION,
      name: 'Health Insurance Portability and Accountability Act (HIPAA)',
      description: 'US legislation that provides data privacy and security provisions for safeguarding medical information.',
      status: ComplianceStatus.NON_COMPLIANT,
      metadata: {
        regulatoryAuthority: 'US Department of Health and Human Services',
        jurisdiction: 'USA',
        effectiveDate: '1996-08-21',
        category: ['Healthcare', 'Privacy']
      }
    },
    
    // Requirements
    {
      id: 'req1',
      type: NodeTypes.REQUIREMENT,
      name: 'Data Subject Access Rights',
      description: 'Individuals have the right to access their personal data.',
      status: ComplianceStatus.PARTIALLY_COMPLIANT,
      metadata: {
        section: 'Article 15',
        priority: 'High'
      }
    },
    {
      id: 'req2',
      type: NodeTypes.REQUIREMENT,
      name: 'Data Protection Impact Assessment',
      description: 'Conduct impact assessments for high-risk processing activities.',
      status: ComplianceStatus.NON_COMPLIANT,
      metadata: {
        section: 'Article 35',
        priority: 'Medium',
        deadline: '2023-12-31'
      }
    },
    {
      id: 'req3',
      type: NodeTypes.REQUIREMENT,
      name: 'Right to Deletion',
      description: 'Consumers have the right to request deletion of personal information.',
      status: ComplianceStatus.COMPLIANT,
      metadata: {
        section: 'Section 1798.105',
        priority: 'High'
      }
    },
    {
      id: 'req4',
      type: NodeTypes.REQUIREMENT,
      name: 'Security Rule',
      description: 'Ensure the confidentiality, integrity, and availability of all e-PHI.',
      status: ComplianceStatus.NON_COMPLIANT,
      metadata: {
        section: '45 CFR Part 160',
        priority: 'Critical'
      }
    },
    
    // Controls
    {
      id: 'ctrl1',
      type: NodeTypes.CONTROL,
      name: 'Data Subject Request Process',
      description: 'Process for handling data subject access requests.',
      status: ComplianceStatus.PARTIALLY_COMPLIANT,
      metadata: {
        controlType: 'Process',
        implementationStatus: 'In Progress',
        owner: 'Privacy Team',
        lastTestedDate: '2023-06-15'
      }
    },
    {
      id: 'ctrl2',
      type: NodeTypes.CONTROL,
      name: 'Data Inventory',
      description: 'Comprehensive inventory of all personal data processing activities.',
      status: ComplianceStatus.NON_COMPLIANT,
      metadata: {
        controlType: 'Documentation',
        implementationStatus: 'Planned',
        owner: 'Data Governance Team'
      }
    },
    {
      id: 'ctrl3',
      type: NodeTypes.CONTROL,
      name: 'Data Deletion Workflow',
      description: 'Automated workflow for processing data deletion requests.',
      status: ComplianceStatus.COMPLIANT,
      metadata: {
        controlType: 'Technical',
        implementationStatus: 'Implemented',
        owner: 'IT Department',
        lastTestedDate: '2023-08-10'
      }
    },
    {
      id: 'ctrl4',
      type: NodeTypes.CONTROL,
      name: 'Access Control System',
      description: 'System for controlling access to PHI based on role.',
      status: ComplianceStatus.PARTIALLY_COMPLIANT,
      metadata: {
        controlType: 'Technical',
        implementationStatus: 'In Progress',
        owner: 'Security Team'
      }
    },
    
    // Business Processes
    {
      id: 'proc1',
      type: NodeTypes.BUSINESS_PROCESS,
      name: 'Customer Onboarding',
      description: 'Process for registering and onboarding new customers.',
      status: ComplianceStatus.PARTIALLY_COMPLIANT,
      metadata: {
        processOwner: 'Customer Success',
        department: 'Sales',
        criticality: 'High'
      }
    },
    {
      id: 'proc2',
      type: NodeTypes.BUSINESS_PROCESS,
      name: 'Marketing Campaigns',
      description: 'Process for planning and executing marketing campaigns.',
      status: ComplianceStatus.COMPLIANT,
      metadata: {
        processOwner: 'Marketing Director',
        department: 'Marketing',
        criticality: 'Medium'
      }
    },
    {
      id: 'proc3',
      type: NodeTypes.BUSINESS_PROCESS,
      name: 'Patient Records Management',
      description: 'Process for managing patient health records.',
      status: ComplianceStatus.NON_COMPLIANT,
      metadata: {
        processOwner: 'Medical Records Department',
        department: 'Healthcare Operations',
        criticality: 'Critical'
      }
    },
    
    // Systems
    {
      id: 'sys1',
      type: NodeTypes.SYSTEM,
      name: 'Customer Relationship Management',
      description: 'System for managing customer interactions and data.',
      status: ComplianceStatus.PARTIALLY_COMPLIANT,
      metadata: {
        systemOwner: 'IT Department',
        vendor: 'Salesforce',
        version: '2023.1',
        criticality: 'High'
      }
    },
    {
      id: 'sys2',
      type: NodeTypes.SYSTEM,
      name: 'Marketing Automation Platform',
      description: 'System for automating marketing activities and campaigns.',
      status: ComplianceStatus.COMPLIANT,
      metadata: {
        systemOwner: 'Marketing Operations',
        vendor: 'HubSpot',
        version: '4.2',
        criticality: 'Medium'
      }
    },
    {
      id: 'sys3',
      type: NodeTypes.SYSTEM,
      name: 'Electronic Health Record System',
      description: 'System for managing patient health records electronically.',
      status: ComplianceStatus.NON_COMPLIANT,
      metadata: {
        systemOwner: 'IT Healthcare Systems',
        vendor: 'Epic',
        version: '2022.3',
        criticality: 'Critical'
      }
    }
  ];
  
  // Create edges
  const edges = [
    // Regulation to Requirements
    {
      id: 'edge1',
      source: 'reg1',
      target: 'req1',
      type: EdgeTypes.CONTAINS
    },
    {
      id: 'edge2',
      source: 'reg1',
      target: 'req2',
      type: EdgeTypes.CONTAINS
    },
    {
      id: 'edge3',
      source: 'reg2',
      target: 'req3',
      type: EdgeTypes.CONTAINS
    },
    {
      id: 'edge4',
      source: 'reg3',
      target: 'req4',
      type: EdgeTypes.CONTAINS
    },
    
    // Requirements to Controls
    {
      id: 'edge5',
      source: 'req1',
      target: 'ctrl1',
      type: EdgeTypes.IMPLEMENTS
    },
    {
      id: 'edge6',
      source: 'req2',
      target: 'ctrl2',
      type: EdgeTypes.IMPLEMENTS
    },
    {
      id: 'edge7',
      source: 'req3',
      target: 'ctrl3',
      type: EdgeTypes.IMPLEMENTS
    },
    {
      id: 'edge8',
      source: 'req4',
      target: 'ctrl4',
      type: EdgeTypes.IMPLEMENTS
    },
    
    // Requirements to Business Processes
    {
      id: 'edge9',
      source: 'req1',
      target: 'proc1',
      type: EdgeTypes.IMPACTS
    },
    {
      id: 'edge10',
      source: 'req3',
      target: 'proc2',
      type: EdgeTypes.IMPACTS
    },
    {
      id: 'edge11',
      source: 'req4',
      target: 'proc3',
      type: EdgeTypes.IMPACTS
    },
    
    // Business Processes to Systems
    {
      id: 'edge12',
      source: 'proc1',
      target: 'sys1',
      type: EdgeTypes.USES
    },
    {
      id: 'edge13',
      source: 'proc2',
      target: 'sys2',
      type: EdgeTypes.USES
    },
    {
      id: 'edge14',
      source: 'proc3',
      target: 'sys3',
      type: EdgeTypes.USES
    },
    
    // Cross-references
    {
      id: 'edge15',
      source: 'req1',
      target: 'req3',
      type: EdgeTypes.REFERENCES
    },
    {
      id: 'edge16',
      source: 'ctrl1',
      target: 'ctrl3',
      type: EdgeTypes.SUPPORTS
    },
    {
      id: 'edge17',
      source: 'reg1',
      target: 'reg2',
      type: EdgeTypes.REFERENCES
    }
  ];
  
  return { nodes, edges };
}
