"""
FastAPI server entry point for root level application
"""
import os
import uvicorn
import logging
import sys
from fastapi import FastAP<PERSON>, Request, HTTPException
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse, RedirectResponse
from pydantic import BaseModel
from typing import List, Dict, Any

# Configure logging with more detailed format
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

app = FastAPI(title="Regulatory Analysis System")
templates = Jinja2Templates(directory="templates")

# Create and mount static directory if it doesn't exist
if not os.path.exists("statics"):
    os.makedirs("statics/css", exist_ok=True)
    os.makedirs("statics/js", exist_ok=True)

# Mount static files directory
app.mount("/static", StaticFiles(directory="statics"), name="static")

class Regulator(BaseModel):
    id: int
    name: str
    country: str

class RegulatorWithCountry(BaseModel):
    regulator: Regulator
    country_name: str

# Example data (replace with your actual data fetching mechanism)
regulators = {
    1: {"name": "Regulator A", "country": "USA"},
    2: {"name": "Regulator B", "country": "Canada"},
}
countries = {
    "USA": "United States of America",
    "Canada": "Canada",
}

@app.get("/api/v1/compliance/summary")
async def get_compliance_summary():
    """API endpoint for compliance summary data"""
    try:
        # Mock data for demonstration
        return {
            "overall_score": 78.5,
            "by_region": {
                "North America": 82.3,
                "Europe": 76.8,
                "Asia Pacific": 70.2,
                "Latin America": 65.9
            },
            "critical_gaps": [
                {"region": "Asia Pacific", "requirement": "Data Localization", "score": 45.2},
                {"region": "Latin America", "requirement": "Consent Management", "score": 52.1}
            ],
            "recommendations": [
                "Implement enhanced data localization controls in Asia Pacific region",
                "Update consent management process for Latin American operations",
                "Review European data transfer mechanisms for GDPR compliance"
            ],
            "recent_changes": [
                {"date": "2025-02-15", "region": "Europe", "description": "Updated GDPR enforcement guidelines"},
                {"date": "2025-02-10", "region": "North America", "description": "New state privacy law in California"}
            ]
        }
    except Exception as e:
        logger.error(f"Error generating compliance summary: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/regulators/{regulator_id}", response_model=RegulatorWithCountry)
async def get_regulator(regulator_id: int):
    regulator_data = regulators.get(regulator_id)
    if regulator_data:
        country_name = countries.get(regulator_data["country"])
        if country_name:
            return {"regulator": Regulator(**regulator_data), "country_name": country_name}
        else:
            return {"regulator": Regulator(**regulator_data), "country_name": "Unknown"}
    else:
        return {"detail": "Regulator not found"}

@app.get("/")
async def root(request: Request):
    """Render the main dashboard."""
    from app.i18n import get_translations_context
    context = {"request": request, **get_translations_context(request)}
    return templates.TemplateResponse("dashboard.html", context)

@app.get("/regulatory_dashboard")
async def regulatory_dashboard(request: Request):
    from app.i18n import get_translations_context
    context = {"request": request, **get_translations_context(request)}
    return templates.TemplateResponse("regulatory_dashboard.html", context)

@app.get("/relationship_mapping")
async def relationship_mapping(request: Request):
    from app.i18n import get_translations_context
    context = {"request": request, **get_translations_context(request)}
    return templates.TemplateResponse("relationship_mapping.html", context)

@app.get("/api/v1/docs", include_in_schema=False)
async def redirect_old_docs():
    """Redirect from old docs path to new docs path."""
    return RedirectResponse(url="/docs")

@app.get("/api/v1", response_class=RedirectResponse, include_in_schema=False)
async def api_root():
    """
    API v1 root endpoint, redirects to API documentation.
    """
    return RedirectResponse(url="/docs")

def run_server():
    """Run the FastAPI server with improved error handling"""
    try:
        # ALWAYS bind to 0.0.0.0 for Replit
        host = "0.0.0.0"
        port = 8080  # Use a different port than the app/main.py

        logger.info(f"Starting FastAPI server on {host}:{port}")
        logger.info(f"API will be available at: http://{host}:{port}")

        # Configure uvicorn with explicit settings
        config = uvicorn.Config(
            app=app,
            host=host,
            port=port,
            log_level="info",
            access_log=True,
            timeout_keep_alive=30,
            workers=1,
            reload=False  # Disable reload to prevent duplicate processes
        )

        server = uvicorn.Server(config)
        server.run()

    except Exception as e:
        logger.error(f"Critical error starting server: {str(e)}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    logger.info("Starting FastAPI server process")
    run_server()