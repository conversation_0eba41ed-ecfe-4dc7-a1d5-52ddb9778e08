run = ["uvicorn", "main:app", "--host", "0.0.0.0", "--reload"]
modules = ["python-3.11"]

hidden = [".pythonlibs"]

[nix]
channel = "stable-24_05"

[deployment]
run = ["sh", "-c", "python main.py"]
deploymentTarget = "cloudrun"

[[ports]]
localPort = 8000
externalPort = 80

[[ports]]
localPort = 8080
externalPort = 8080

[workflows]

[[workflows.workflow]]
name = "Alembic: Create Migration"
author = 5374441

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "python scripts/migrate.py create 'New migration'"

[[workflows.workflow]]
name = "Alembic: Upgrade to Latest"
author = 5374441

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "python scripts/migrate.py upgrade"

[[workflows.workflow]]
name = "Alembic: Show Current"
author = 5374441

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "python scripts/migrate.py current"

[[workflows.workflow]]
name = "Run Test Coverage"
author = 5374441

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "python generate_coverage.py"

[[workflows.workflow]]
name = "Process URLs"
author = 5374441

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "python url_processor.py test_url_input.txt"

[[workflows.workflow]]
name = "Analyze Confidence Levels"
author = 5374441

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "python analyze_confidence.py"

[[workflows.workflow]]
name = "Analyze Test Coverage"
author = 5374441

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "python analyze_test_coverage.py"

[[workflows.workflow]]
name = "Take Screenshots"
author = 5374441

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "python scripts/take_screenshot.py"

[[workflows.workflow]]
name = "Kill All Processes"
author = 5374441

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "python scripts/kill_processes.py"

[[workflows.workflow]]
name = "Run Tests with Coverage"
author = 5374441

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "python run_tests_with_coverage.py"

[[workflows.workflow]]
name = "Run App"
author = 5374441

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "python main.py"

[[workflows.workflow]]
name = "Run"
author = 5374441

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "python run.py"

[[workflows.workflow]]
name = "Run Regulatory Tests"
author = 5374441

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "python run_regulatory_tests.py -v"

[[workflows.workflow]]
name = "Run API Server"
author = 5374441

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "python run.py"

[[workflows.workflow]]
name = "Run Application"
author = 5374441

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "python run.py"

[[workflows.workflow]]
name = "Start Server"
author = 5374441

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "python main.py"

[[workflows.workflow]]
name = "Babel: Compile Translations"
author = 5374441

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "python scripts/translate.py compile"
