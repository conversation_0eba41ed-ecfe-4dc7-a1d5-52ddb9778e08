# Admin Interface Dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy admin requirements
COPY requirements.admin.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.admin.txt

# Copy application code
COPY app/ ./app/
COPY templates/ ./templates/
COPY statics/ ./statics/

# Create admin-specific entry point
COPY admin_main.py .

# Expose admin port
EXPOSE 8001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# Run admin interface
CMD ["python", "admin_main.py"]
