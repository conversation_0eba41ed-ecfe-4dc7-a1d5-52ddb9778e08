#!/usr/bin/env python
"""
Run integration tests with the correct environment setup.
"""
import os
import subprocess
import sys
import time

def run_integration_tests():
    """Run integration tests with the correct environment setup."""
    # Set environment variables for testing
    os.environ["DATABASE_URL"] = "sqlite:///:memory:"
    os.environ["TESTING"] = "True"

    print("Running integration tests...")

    # Define the test command
    command = [
        "python", "-m", "pytest",
        "-k", "integration",
        "-v",
        "--no-header",
        "--cov=app",
        "--cov-report=term"
    ]

    # Run the tests
    print(f"Running command: {' '.join(command)}")
    start_time = time.time()
    result = subprocess.run(command)
    end_time = time.time()

    # Print summary
    print(f"\nTests completed in {end_time - start_time:.2f} seconds")
    print(f"Exit code: {result.returncode}")

    return result.returncode

if __name__ == "__main__":
    sys.exit(run_integration_tests())
