
:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --success-color: #4CAF50;
    --danger-color: #F44336;
    --warning-color: #FFEB3B;
    --info-color: #00BCD4;
    --light-color: #F5F5F5;
    --dark-color: #212121;
    --body-bg-light: #F9F9F9;
    --body-bg-dark: #121212;
    --card-bg-light: #FFFFFF;
    --card-bg-dark: #1E1E1E;
    --text-light: #212121;
    --text-dark: #EEEEEE;
    --border-color: #e0e0e0;
    --border-color-dark: #333;
}

body {
    font-family: 'Inter', sans-serif;
    transition: background-color 0.3s, color 0.3s;
    background-color: var(--body-bg-light);
    color: var(--text-light);
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    width: 100%;
}

body.dark-mode {
    background-color: var(--body-bg-dark);
    color: var(--text-dark);
}

/* Navbar Styles */
.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1.5rem;
    background-color: var(--card-bg-light);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    width: 100%;
    box-sizing: border-box;
}

body.dark-mode .navbar {
    background-color: var(--card-bg-dark);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.navbar-left, .navbar-right {
    display: flex;
    align-items: center;
}

.navbar-brand {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: var(--primary-color);
    text-decoration: none;
    font-size: 1.25rem;
    margin-right: 2rem;
}

.navbar-brand i {
    margin-right: 0.5rem;
}

.nav-links {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-links li {
    margin-right: 1.5rem;
}

.nav-links a {
    display: flex;
    align-items: center;
    color: var(--text-light);
    text-decoration: none;
    transition: color 0.2s;
}

.nav-links i {
    margin-right: 0.5rem;
}

body.dark-mode .nav-links a {
    color: var(--text-dark);
}

.nav-links a:hover {
    color: var(--primary-color);
}

/* Container & Layout */
.container {
    width: 100%;
    max-width: 100%;
    padding: 2rem;
    box-sizing: border-box;
    flex: 1;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -1rem 2rem;
}

.col-md-4 {
    flex: 0 0 33.333333%;
    padding: 0 1rem;
    box-sizing: border-box;
}

.col-md-6 {
    flex: 0 0 50%;
    padding: 0 1rem;
    box-sizing: border-box;
}

.col-12 {
    flex: 0 0 100%;
    padding: 0 1rem;
    box-sizing: border-box;
}

/* Cards */
.card {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    background-color: var(--card-bg-light);
    border: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
    overflow: hidden;
    height: 100%;
}

body.dark-mode .card {
    background-color: var(--card-bg-dark);
    border-color: var(--border-color-dark);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

body.dark-mode .card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    margin-top: 0;
    margin-bottom: 1rem;
    font-weight: 600;
}

.card-text {
    margin-bottom: 1.5rem;
}

/* Welcome Banner */
.welcome-banner {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    color: white;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

body.dark-mode .welcome-banner {
    background: linear-gradient(135deg, #1565C0, #0097A7);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Buttons */
.btn {
    border-radius: 6px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: background-color 0.2s, transform 0.1s;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background-color: #27ae60;
}

.btn-outline-primary {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

body.dark-mode .btn-outline-primary {
    color: #64B5F6;
    border-color: #64B5F6;
}

.btn-outline-primary:hover {
    background-color: rgba(33, 150, 243, 0.1);
}

/* API Endpoints Grid */
.api-endpoints {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.api-card {
    height: 100%;
}

/* Footer */
footer {
    padding: 2rem 0;
    background-color: var(--light-color);
    border-top: 1px solid var(--border-color);
    margin-top: auto;
    width: 100%;
}

body.dark-mode footer {
    background-color: #1A1A1A;
    border-top: 1px solid var(--border-color-dark);
}

footer .row {
    margin-bottom: 1rem;
}

.list-unstyled {
    list-style: none;
    padding-left: 0;
    margin: 0;
}

.list-unstyled li {
    margin-bottom: 0.5rem;
}

.language-options {
    display: flex;
    flex-direction: column;
}

.language-link {
    color: var(--text-light);
    text-decoration: none;
    margin-bottom: 0.5rem;
}

body.dark-mode .language-link {
    color: var(--text-dark);
}

.copyright {
    margin-top: 1.5rem;
}

/* Theme Toggle */
.dark-mode-toggle {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 0.5rem;
    color: var(--text-light);
    transition: color 0.2s;
}

body.dark-mode .dark-mode-toggle {
    color: var(--text-dark);
}

.dark-mode-toggle:hover {
    color: var(--primary-color);
}

/* Language Selector */
.language-nav-selector {
    position: relative;
    margin-right: 1rem;
}

#nav-language-button {
    background: none;
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    color: var(--text-light);
    padding: 6px 10px;
    font-size: 14px;
    border-radius: 6px;
}

body.dark-mode #nav-language-button {
    border-color: var(--border-color-dark);
    color: var(--text-dark);
}

#nav-language-button:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

body.dark-mode #nav-language-button:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.dropdown-arrow {
    font-size: 8px;
    transition: transform 0.2s ease;
}

.language-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--card-bg-light);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    min-width: 150px;
    display: none;
    flex-direction: column;
    padding: 8px 0;
    margin-top: 5px;
    max-height: 300px;
    overflow-y: auto;
}

body.dark-mode .language-dropdown {
    background-color: var(--card-bg-dark);
    border-color: var(--border-color-dark);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.language-dropdown.show {
    display: flex;
}

.language-option {
    padding: 8px 16px;
    text-decoration: none;
    color: var(--text-light);
    display: flex;
    align-items: center;
    gap: 8px;
}

body.dark-mode .language-option {
    color: var(--text-dark);
}

.language-option:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

body.dark-mode .language-option:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.language-option.active {
    background-color: rgba(33, 150, 243, 0.1);
    font-weight: 500;
}

body.dark-mode .language-option.active {
    background-color: rgba(100, 181, 246, 0.1);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .navbar {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .navbar-right {
        margin-top: 1rem;
        width: 100%;
        justify-content: space-between;
    }
    
    .nav-links {
        margin-top: 1rem;
        flex-direction: column;
    }
    
    .nav-links li {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
    
    .col-md-4, .col-md-6 {
        flex: 0 0 100%;
    }
    
    .language-dropdown {
        right: 0;
        width: 100%;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .col-md-4 {
        flex: 0 0 50%;
    }
}

/* Feature Icons */
.feature-icon {
    font-size: 48px;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

body.dark-mode .feature-icon {
    color: #64B5F6;
}

/* Section Titles */
.section-title {
    margin: 2rem 0 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}
