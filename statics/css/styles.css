
/* RegulationGuru Styles */
:root {
    --primary-color: #2196F3;
    --secondary-color: #FF9800;
    --success-color: #4CAF50;
    --danger-color: #F44336;
    --warning-color: #FFEB3B;
    --info-color: #00BCD4;
    --light-color: #F5F5F5;
    --dark-color: #212121;
    --body-bg-light: #F9F9F9;
    --body-bg-dark: #121212;
    --card-bg-light: #FFFFFF;
    --card-bg-dark: #1E1E1E;
    --text-light: #212121;
    --text-dark: #FFFFFF;
    
    /* Enhanced dark mode text colors */
    --text-dark-secondary: #E0E0E0;
    --text-dark-disabled: #BDBDBD;
    --text-dark-hint: #9E9E9E;
}

body {
    font-family: 'Roboto', sans-serif;
    transition: background-color 0.3s, color 0.3s;
    background-color: var(--body-bg-light);
    color: var(--text-light);
    margin: 0;
    padding: 0;
}

body.dark-mode {
    background-color: var(--body-bg-dark);
    color: var(--text-dark);
    font-family: 'Roboto', sans-serif;
    font-weight: 400;
    letter-spacing: 0.015em;
}

/* Ensure high contrast text in dark mode */
body.dark-mode h1, 
body.dark-mode h2, 
body.dark-mode h3, 
body.dark-mode h4, 
body.dark-mode h5, 
body.dark-mode h6 {
    color: #ffffff;
    font-weight: 500;
}

body.dark-mode p, 
body.dark-mode li, 
body.dark-mode div,
body.dark-mode span:not(.material-icons) {
    color: #e0e0e0;
}

/* Navbar styling for light/dark modes */
#main-navbar {
    background-color: #ffffff;
    transition: background-color 0.3s ease;
}

body.dark-mode #main-navbar {
    background-color: #252525;
}

body.dark-mode .navbar-brand,
body.dark-mode .nav-link,
body.dark-mode .navbar .material-icons {
    color: #e0e0e0;
}

body.dark-mode .navbar-toggler-icon {
    filter: invert(1);
}

.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark-mode .navbar {
    background-color: #1A1A1A !important;
    border-bottom: 1px solid #333;
}

.dark-mode .navbar-light .navbar-brand,
.dark-mode .navbar-light .navbar-nav .nav-link {
    color: var(--text-dark);
}

.card {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.3s ease;
    background-color: var(--card-bg-light);
}

.dark-mode .card {
    background-color: var(--card-bg-dark);
    border-color: #333;
    color: #ffffff;
}

.dark-mode .card-title {
    color: #ffffff;
    font-weight: 500;
}

.dark-mode .card-text,
.dark-mode .list-group-item {
    color: #e0e0e0;
}

.dark-mode .loading-text {
    color: #bdbdbd;
}

.card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.theme-switch {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
}

.material-icons {
    vertical-align: middle;
    margin-right: 5px;
}

.welcome-banner {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    color: white;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark-mode .welcome-banner {
    background: linear-gradient(135deg, #1565C0, #0097A7);
}

.dark-mode .table {
    color: #e0e0e0;
}

.dark-mode .table td, 
.dark-mode .table th {
    border-color: #333;
}

.dark-mode .table thead th {
    background-color: #2d2d2d;
}

.dark-mode .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.05);
}

footer {
    margin-top: 3rem;
    padding: 2rem 0;
    background-color: var(--light-color);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.dark-mode footer {
    background-color: #1A1A1A;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Dashboard specific styles */
.compliance-score {
    font-size: 48px;
    font-weight: bold;
    text-align: center;
}

.region-chart {
    height: 300px;
    margin-bottom: 2rem;
}

.gap-card, .recommendation-card, .change-card {
    margin-bottom: 1rem;
    transition: transform 0.2s;
}

.gap-card:hover, .recommendation-card:hover, .change-card:hover {
    transform: translateY(-5px);
}

.loading-text {
    color: #888;
    font-style: italic;
}

.dark-mode .loading-text {
    color: #aaa;
}
