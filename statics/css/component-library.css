/* Advanced Component Library for RegulationGuru */

/* Data Visualization Components */
.chart-container {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-all);
}

.chart-container:hover {
    box-shadow: var(--shadow-lg);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-6);
}

.chart-title {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.chart-controls {
    display: flex;
    gap: var(--space-2);
}

/* Progress Components */
.progress-ring {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto;
}

.progress-ring svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.progress-ring circle {
    fill: none;
    stroke-width: 8;
    stroke-linecap: round;
}

.progress-ring .background {
    stroke: var(--gray-200);
}

.progress-ring .progress {
    stroke: var(--brand-primary);
    stroke-dasharray: 0 100;
    transition: stroke-dasharray 1s ease-in-out;
}

.progress-ring .progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--text-primary);
}

/* Advanced Table Component */
.table-advanced {
    width: 100%;
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table-advanced thead {
    background: var(--bg-tertiary);
}

.table-advanced th {
    padding: var(--space-4) var(--space-6);
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-primary);
    font-size: var(--text-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.table-advanced td {
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid var(--border-primary);
    color: var(--text-secondary);
    vertical-align: middle;
}

.table-advanced tbody tr {
    transition: var(--transition-colors);
}

.table-advanced tbody tr:hover {
    background: var(--bg-tertiary);
}

.table-advanced tbody tr:last-child td {
    border-bottom: none;
}

/* Status Badge Component */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-badge.success {
    background: rgba(72, 187, 120, 0.1);
    color: var(--success-dark);
    border: 1px solid rgba(72, 187, 120, 0.2);
}

.status-badge.warning {
    background: rgba(237, 137, 54, 0.1);
    color: var(--warning-dark);
    border: 1px solid rgba(237, 137, 54, 0.2);
}

.status-badge.error {
    background: rgba(229, 62, 62, 0.1);
    color: var(--error-dark);
    border: 1px solid rgba(229, 62, 62, 0.2);
}

.status-badge.info {
    background: rgba(49, 130, 206, 0.1);
    color: var(--info-dark);
    border: 1px solid rgba(49, 130, 206, 0.2);
}

.status-badge .status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
}

/* Timeline Component */
.timeline {
    position: relative;
    padding-left: var(--space-8);
}

.timeline::before {
    content: '';
    position: absolute;
    left: var(--space-4);
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--border-primary);
}

.timeline-item {
    position: relative;
    margin-bottom: var(--space-8);
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -var(--space-6);
    top: var(--space-2);
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--brand-primary);
    border: 3px solid var(--bg-secondary);
    box-shadow: 0 0 0 3px var(--border-primary);
}

.timeline-content {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    box-shadow: var(--shadow-sm);
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-2);
}

.timeline-title {
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.timeline-date {
    font-size: var(--text-sm);
    color: var(--text-tertiary);
}

.timeline-description {
    color: var(--text-secondary);
    line-height: var(--leading-relaxed);
    margin: 0;
}

/* Notification Component */
.notification-center {
    position: fixed;
    top: var(--space-6);
    right: var(--space-6);
    z-index: var(--z-toast);
    max-width: 400px;
    width: 100%;
}

.notification {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    margin-bottom: var(--space-3);
    box-shadow: var(--shadow-lg);
    transform: translateX(100%);
    transition: var(--transition-transform);
    position: relative;
    overflow: hidden;
}

.notification.show {
    transform: translateX(0);
}

.notification::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--info);
}

.notification.success::before {
    background: var(--success);
}

.notification.warning::before {
    background: var(--warning);
}

.notification.error::before {
    background: var(--error);
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-2);
}

.notification-title {
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    font-size: var(--text-sm);
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-tertiary);
    cursor: pointer;
    padding: 0;
    font-size: var(--text-lg);
    transition: var(--transition-colors);
}

.notification-close:hover {
    color: var(--text-primary);
}

.notification-content {
    color: var(--text-secondary);
    font-size: var(--text-sm);
    line-height: var(--leading-relaxed);
}

/* Search Component */
.search-advanced {
    position: relative;
    max-width: 500px;
    margin: 0 auto;
}

.search-input {
    width: 100%;
    padding: var(--space-4) var(--space-6) var(--space-4) var(--space-12);
    border: 2px solid var(--border-primary);
    border-radius: var(--radius-full);
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: var(--text-base);
    transition: var(--transition-all);
    outline: none;
}

.search-input:focus {
    border-color: var(--brand-primary);
    box-shadow: 0 0 0 4px rgba(26, 54, 93, 0.1);
}

.search-icon {
    position: absolute;
    left: var(--space-4);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-tertiary);
    font-size: var(--text-lg);
    pointer-events: none;
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    margin-top: var(--space-2);
    max-height: 400px;
    overflow-y: auto;
    z-index: var(--z-dropdown);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition-all);
}

.search-results.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.search-result-item {
    padding: var(--space-3) var(--space-4);
    border-bottom: 1px solid var(--border-primary);
    cursor: pointer;
    transition: var(--transition-colors);
}

.search-result-item:hover {
    background: var(--bg-tertiary);
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-title {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.search-result-description {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    line-height: var(--leading-snug);
}

/* Dropdown Component */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition-all);
    outline: none;
}

.dropdown-toggle:hover,
.dropdown-toggle:focus {
    background: var(--bg-tertiary);
    border-color: var(--border-secondary);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    margin-top: var(--space-1);
    z-index: var(--z-dropdown);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition-all);
}

.dropdown.show .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: var(--space-3) var(--space-4);
    color: var(--text-secondary);
    text-decoration: none;
    background: none;
    border: none;
    text-align: left;
    cursor: pointer;
    transition: var(--transition-colors);
    font-size: var(--text-sm);
}

.dropdown-item:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.dropdown-divider {
    height: 1px;
    background: var(--border-primary);
    margin: var(--space-2) 0;
}

/* Loading Components */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-primary);
    border-radius: 50%;
    border-top-color: var(--brand-primary);
    animation: spin 1s ease-in-out infinite;
}

.loading-skeleton {
    background: linear-gradient(90deg, var(--bg-tertiary) 25%, var(--bg-accent) 50%, var(--bg-tertiary) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: var(--radius-base);
}

.loading-dots {
    display: inline-flex;
    gap: var(--space-1);
}

.loading-dots span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--brand-primary);
    animation: bounce-dots 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes spin {
    to { transform: rotate(360deg); }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

@keyframes bounce-dots {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

/* Responsive Utilities */
@media (max-width: 768px) {
    .notification-center {
        left: var(--space-4);
        right: var(--space-4);
        max-width: none;
    }
    
    .search-advanced {
        max-width: none;
    }
    
    .timeline {
        padding-left: var(--space-6);
    }
    
    .timeline::before {
        left: var(--space-3);
    }
    
    .timeline-item::before {
        left: -var(--space-4);
    }
}
