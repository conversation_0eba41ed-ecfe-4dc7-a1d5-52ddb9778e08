
/* Dark mode colors */
:root {
    --background-color: #1E1E1E;
    --text-color: #CCCCCC;
    --header-color: #252526;
    --border-color: #3E3E42;
    --method-get: #61AFFE;
    --method-post: #49CC90;
    --method-put: #FCA130;
    --method-delete: #F93E3E;
    --method-patch: #50E3C2;
}

/* Main container */
.swagger-ui {
    background-color: var(--background-color);
    color: var(--text-color);
}

/* Header */
.swagger-ui .topbar {
    background-color: var(--header-color);
}

.swagger-ui .topbar .download-url-wrapper .select-label {
    color: var(--text-color);
}

/* Section headers */
.swagger-ui .opblock-tag {
    color: var(--text-color);
    border-bottom: 1px solid var(--border-color);
}

/* Method blocks */
.swagger-ui .opblock {
    background-color: var(--background-color);
    border: 1px solid var(--border-color);
}

.swagger-ui .opblock .opblock-summary-method {
    color: white;
}

.swagger-ui .opblock-description-wrapper p {
    color: var(--text-color);
}

/* Method colors */
.swagger-ui .opblock-get {
    border-color: var(--method-get);
}

.swagger-ui .opblock-get .opblock-summary-method {
    background: var(--method-get);
}

.swagger-ui .opblock-post {
    border-color: var(--method-post);
}

.swagger-ui .opblock-post .opblock-summary-method {
    background: var(--method-post);
}

.swagger-ui .opblock-put {
    border-color: var(--method-put);
}

.swagger-ui .opblock-put .opblock-summary-method {
    background: var(--method-put);
}

.swagger-ui .opblock-delete {
    border-color: var(--method-delete);
}

.swagger-ui .opblock-delete .opblock-summary-method {
    background: var(--method-delete);
}

.swagger-ui .opblock-patch .opblock-summary-method {
    background: var(--method-patch);
}

/* Models section */
.swagger-ui .models h4 {
    color: var(--text-color);
}

.swagger-ui .model-title {
    color: var(--text-color);
}

/* Input fields */
.swagger-ui input[type=email],
.swagger-ui input[type=file],
.swagger-ui input[type=password],
.swagger-ui input[type=search],
.swagger-ui input[type=text],
.swagger-ui textarea {
    background-color: var(--background-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

/* Response section */
.swagger-ui .responses-inner h4,
.swagger-ui .responses-inner h5 {
    color: var(--text-color);
}

.swagger-ui .response-col_status {
    color: var(--text-color);
}

.swagger-ui table thead tr td,
.swagger-ui table thead tr th {
    color: var(--text-color);
    border-bottom: 1px solid var(--border-color);
}

.swagger-ui .opblock-body pre.microlight {
    background-color: #2D2D30;
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

/* Scheme selection */
.swagger-ui .scheme-container {
    background-color: var(--background-color);
    box-shadow: 0 1px 2px 0 rgba(0,0,0,.15);
}

.swagger-ui select {
    background-color: var(--background-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

/* Tables */
.swagger-ui .table-container {
    color: var(--text-color);
}

/* Parameter tables */
.swagger-ui .parameters-col_description {
    color: var(--text-color);
}

.swagger-ui .parameter__name {
    color: var(--text-color);
}

/* Buttons */
.swagger-ui .btn {
    color: var(--text-color);
    border: 1px solid var(--border-color);
    background-color: transparent;
}

.swagger-ui .btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.swagger-ui .btn.execute {
    background-color: var(--method-get);
    color: white;
}

/* Code */
.swagger-ui code {
    color: #e83e8c;
    background-color: #2D2D30;
}

/* Authorization modal */
.swagger-ui .dialog-ux .modal-ux {
    background: var(--background-color);
    border: 1px solid var(--border-color);
}

.swagger-ui .dialog-ux .modal-ux-header h3 {
    color: var(--text-color);
}

.swagger-ui .dialog-ux .modal-ux-content {
    color: var(--text-color);
}

.swagger-ui .auth-container {
    color: var(--text-color);
}

.swagger-ui .auth-container h4 {
    color: var(--text-color);
}

/* JSON Schema Model */
.swagger-ui .model {
    color: var(--text-color);
}

.swagger-ui .model-toggle {
    color: var(--text-color);
}

.swagger-ui section.models .model-container {
    background: var(--background-color);
    border: 1px solid var(--border-color);
}

.swagger-ui section.models {
    border: 1px solid var(--border-color);
}

/* Make sure inline code is visible */
.swagger-ui .markdown code, .swagger-ui .renderedMarkdown code {
    background: rgba(255, 255, 255, 0.1);
}

/* Format JSON objects better */
.swagger-ui .property-format {
    color: #999;
}

/* Force light text for all elements */
.swagger-ui * {
    color: var(--text-color);
}

/* Override any specificity issues */
.swagger-ui .opblock .opblock-summary-operation-id, 
.swagger-ui .opblock .opblock-summary-path, 
.swagger-ui .opblock .opblock-summary-path__deprecated,
.swagger-ui .opblock-description-wrapper,
.swagger-ui .opblock-external-docs-wrapper,
.swagger-ui .opblock-title_normal {
    color: var(--text-color);
}
