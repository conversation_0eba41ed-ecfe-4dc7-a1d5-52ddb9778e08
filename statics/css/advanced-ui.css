/* Advanced UI System for RegulationGuru */

/* Enhanced Design Tokens */
:root {
    /* Brand Colors - Professional Palette */
    --brand-primary: #1a365d;
    --brand-primary-light: #2d5a87;
    --brand-primary-dark: #0f2a44;
    --brand-secondary: #38a169;
    --brand-accent: #ed8936;
    --brand-accent-light: #f6ad55;
    
    /* Semantic Colors */
    --success: #48bb78;
    --success-light: #68d391;
    --success-dark: #38a169;
    --warning: #ed8936;
    --warning-light: #f6ad55;
    --warning-dark: #dd6b20;
    --error: #e53e3e;
    --error-light: #fc8181;
    --error-dark: #c53030;
    --info: #3182ce;
    --info-light: #63b3ed;
    --info-dark: #2c5282;
    
    /* Neutral Palette */
    --gray-50: #f7fafc;
    --gray-100: #edf2f7;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e0;
    --gray-400: #a0aec0;
    --gray-500: #718096;
    --gray-600: #4a5568;
    --gray-700: #2d3748;
    --gray-800: #1a202c;
    --gray-900: #171923;
    
    /* Advanced Spacing Scale */
    --space-px: 1px;
    --space-0: 0;
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;
    --space-32: 8rem;
    
    /* Typography Scale */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    --text-6xl: 3.75rem;
    
    /* Line Heights */
    --leading-none: 1;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;
    
    /* Border Radius */
    --radius-none: 0;
    --radius-sm: 0.125rem;
    --radius-base: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;
    
    /* Shadows */
    --shadow-xs: 0 0 0 1px rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    
    /* Transitions */
    --transition-all: all 0.15s ease-in-out;
    --transition-colors: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
    --transition-opacity: opacity 0.15s ease-in-out;
    --transition-transform: transform 0.15s ease-in-out;
    
    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
}

/* Theme Variables */
[data-theme="light"] {
    --bg-primary: var(--gray-50);
    --bg-secondary: #ffffff;
    --bg-tertiary: var(--gray-100);
    --bg-accent: var(--gray-200);
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-700);
    --text-tertiary: var(--gray-500);
    --text-inverse: #ffffff;
    --border-primary: var(--gray-200);
    --border-secondary: var(--gray-300);
    --surface-overlay: rgba(255, 255, 255, 0.8);
}

[data-theme="dark"] {
    --bg-primary: var(--gray-900);
    --bg-secondary: var(--gray-800);
    --bg-tertiary: var(--gray-700);
    --bg-accent: var(--gray-600);
    --text-primary: var(--gray-50);
    --text-secondary: var(--gray-300);
    --text-tertiary: var(--gray-400);
    --text-inverse: var(--gray-900);
    --border-primary: var(--gray-700);
    --border-secondary: var(--gray-600);
    --surface-overlay: rgba(0, 0, 0, 0.8);
}

/* Base Styles */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: var(--text-base);
    line-height: var(--leading-normal);
    color: var(--text-primary);
    background-color: var(--bg-primary);
    margin: 0;
    padding: 0;
    min-height: 100vh;
    transition: var(--transition-colors);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Advanced Button System */
.btn-advanced {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    border: 1px solid transparent;
    border-radius: var(--radius-lg);
    font-size: var(--text-sm);
    font-weight: 500;
    line-height: var(--leading-tight);
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-all);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    user-select: none;
    outline: none;
    min-height: 2.5rem;
}

.btn-advanced:focus-visible {
    outline: 2px solid var(--brand-primary);
    outline-offset: 2px;
}

.btn-advanced:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* Button Variants */
.btn-primary {
    background: linear-gradient(135deg, var(--brand-primary), var(--brand-primary-light));
    color: var(--text-inverse);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--brand-primary-dark), var(--brand-primary));
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--border-primary);
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--bg-tertiary);
    border-color: var(--border-secondary);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: linear-gradient(135deg, var(--success), var(--success-light));
    color: var(--text-inverse);
    box-shadow: var(--shadow-sm);
}

.btn-success:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--success-dark), var(--success));
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: var(--brand-primary);
    border-color: var(--brand-primary);
}

.btn-outline:hover:not(:disabled) {
    background: var(--brand-primary);
    color: var(--text-inverse);
}

.btn-ghost {
    background: transparent;
    color: var(--text-secondary);
    border: none;
}

.btn-ghost:hover:not(:disabled) {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Button Sizes */
.btn-xs {
    padding: var(--space-1) var(--space-3);
    font-size: var(--text-xs);
    min-height: 1.75rem;
}

.btn-sm {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-sm);
    min-height: 2rem;
}

.btn-lg {
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-lg);
    min-height: 3rem;
}

.btn-xl {
    padding: var(--space-5) var(--space-10);
    font-size: var(--text-xl);
    min-height: 3.5rem;
}

/* Advanced Card System */
.card-advanced {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-all);
    overflow: hidden;
    position: relative;
}

.card-advanced:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-advanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--brand-primary), var(--brand-secondary));
    opacity: 0;
    transition: var(--transition-opacity);
}

.card-advanced:hover::before {
    opacity: 1;
}

.card-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--border-primary);
    background: var(--bg-tertiary);
}

.card-body {
    padding: var(--space-6);
}

.card-footer {
    padding: var(--space-6);
    border-top: 1px solid var(--border-primary);
    background: var(--bg-tertiary);
}

/* Enhanced Navigation */
.navbar-advanced {
    background: var(--surface-overlay);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-primary);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    transition: var(--transition-all);
}

.navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4) var(--space-6);
    max-width: 1200px;
    margin: 0 auto;
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition-colors);
}

.navbar-brand:hover {
    color: var(--brand-primary);
}

.navbar-nav {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--radius-lg);
    transition: var(--transition-all);
    font-weight: 500;
}

.nav-link:hover,
.nav-link.active {
    color: var(--brand-primary);
    background: var(--bg-tertiary);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 2px;
    background: var(--brand-primary);
    border-radius: var(--radius-full);
}

/* Advanced Form Components */
.form-group {
    margin-bottom: var(--space-6);
}

.form-label {
    display: block;
    margin-bottom: var(--space-2);
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.form-input {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: var(--text-base);
    transition: var(--transition-all);
    outline: none;
}

.form-input:focus {
    border-color: var(--brand-primary);
    box-shadow: 0 0 0 3px rgba(26, 54, 93, 0.1);
}

.form-input::placeholder {
    color: var(--text-tertiary);
}

/* Advanced Modal System */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--surface-overlay);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-all);
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--bg-secondary);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-2xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
    transform: scale(0.95);
    transition: var(--transition-transform);
}

.modal-overlay.show .modal-content {
    transform: scale(1);
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: var(--surface-overlay);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-primary);
}

.animate-fade-in {
    animation: fadeIn 0.5s ease-out;
}

.animate-slide-up {
    animation: slideUp 0.5s ease-out;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

/* Responsive Grid System */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-6);
}

.grid {
    display: grid;
    gap: var(--space-6);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
.grid-cols-12 { grid-template-columns: repeat(12, 1fr); }

/* Responsive Breakpoints */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--space-4);
    }
    
    .grid-cols-2,
    .grid-cols-3,
    .grid-cols-4,
    .grid-cols-6,
    .grid-cols-12 {
        grid-template-columns: 1fr;
    }
    
    .navbar-content {
        padding: var(--space-3) var(--space-4);
    }
    
    .navbar-nav {
        gap: var(--space-1);
    }
    
    .nav-link {
        padding: var(--space-2) var(--space-3);
        font-size: var(--text-sm);
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .grid-cols-3,
    .grid-cols-4,
    .grid-cols-6,
    .grid-cols-12 {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Modern Dashboard Specific Styles */

/* Hero Section */
.hero-modern {
    position: relative;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    padding: var(--space-20) 0 var(--space-16);
    overflow: hidden;
}

.hero-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(26, 54, 93, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(56, 161, 105, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.hero-content {
    text-align: center;
    position: relative;
    z-index: 1;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-full);
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: var(--space-6);
    box-shadow: var(--shadow-sm);
}

.hero-title {
    font-size: var(--text-5xl);
    font-weight: 800;
    line-height: var(--leading-tight);
    color: var(--text-primary);
    margin-bottom: var(--space-6);
    letter-spacing: -0.025em;
}

.hero-description {
    font-size: var(--text-xl);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto var(--space-8);
    line-height: var(--leading-relaxed);
}

.hero-actions {
    display: flex;
    gap: var(--space-4);
    justify-content: center;
    flex-wrap: wrap;
}

.hero-decorations {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
}

.floating-card {
    position: absolute;
    width: 60px;
    height: 60px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xl);
    color: var(--brand-primary);
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
}

.floating-card:nth-child(1) {
    top: 20%;
    left: 15%;
}

.floating-card:nth-child(2) {
    top: 30%;
    right: 20%;
}

.floating-card:nth-child(3) {
    bottom: 25%;
    left: 20%;
}

/* Statistics Section */
.stats-section {
    padding: var(--space-16) 0;
    background: var(--bg-secondary);
}

.section-title {
    font-size: var(--text-3xl);
    font-weight: 700;
    text-align: center;
    color: var(--text-primary);
    margin-bottom: var(--space-12);
}

.stats-grid {
    gap: var(--space-6);
}

.stat-card {
    text-align: center;
    transition: var(--transition-all);
}

.stat-card:hover {
    transform: translateY(-4px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-4);
    font-size: var(--text-2xl);
}

.stat-icon.success {
    background: linear-gradient(135deg, var(--success), var(--success-light));
    color: white;
}

.stat-icon.warning {
    background: linear-gradient(135deg, var(--warning), var(--warning-light));
    color: white;
}

.stat-icon.info {
    background: linear-gradient(135deg, var(--info), var(--info-light));
    color: white;
}

.stat-icon.primary {
    background: linear-gradient(135deg, var(--brand-primary), var(--brand-primary-light));
    color: white;
}

.stat-number {
    font-size: var(--text-3xl);
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.stat-label {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-bottom: var(--space-2);
    font-weight: 500;
}

.stat-change {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-1);
    font-size: var(--text-xs);
    font-weight: 500;
}

.stat-change.positive {
    color: var(--success);
}

.stat-change.negative {
    color: var(--error);
}

.stat-change.neutral {
    color: var(--text-tertiary);
}

/* Features Section */
.features-section {
    padding: var(--space-16) 0;
    background: var(--bg-primary);
}

.features-grid {
    gap: var(--space-8);
}

.feature-card {
    text-align: center;
    height: 100%;
    transition: var(--transition-all);
}

.feature-card:hover {
    transform: translateY(-6px);
}

.feature-icon {
    width: 80px;
    height: 80px;
    border-radius: var(--radius-2xl);
    background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-4);
    font-size: var(--text-2xl);
    color: white;
    box-shadow: var(--shadow-lg);
}

.feature-title {
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-3);
}

.feature-description {
    color: var(--text-secondary);
    line-height: var(--leading-relaxed);
    margin-bottom: var(--space-6);
}

/* Actions Section */
.actions-section {
    padding: var(--space-16) 0;
    background: var(--bg-secondary);
}

.actions-grid {
    gap: var(--space-8);
}

.action-panel {
    height: 100%;
}

.panel-title {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.action-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
}

.action-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-4);
    border: none;
    background: transparent;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--radius-lg);
    transition: var(--transition-all);
    cursor: pointer;
    font-size: var(--text-sm);
    width: 100%;
    text-align: left;
}

.action-item:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    transform: translateX(4px);
}

.action-item i:last-child {
    margin-left: auto;
    opacity: 0.5;
    transition: var(--transition-all);
}

.action-item:hover i:last-child {
    opacity: 1;
    transform: translateX(2px);
}

/* Footer */
.footer-modern {
    background: var(--bg-primary);
    border-top: 1px solid var(--border-primary);
    padding: var(--space-16) 0 var(--space-8);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-12);
    margin-bottom: var(--space-12);
}

.footer-brand {
    max-width: 400px;
}

.brand-logo {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    font-size: var(--text-2xl);
    font-weight: 700;
    margin-bottom: var(--space-4);
}

.brand-description {
    color: var(--text-secondary);
    line-height: var(--leading-relaxed);
}

.footer-links {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-8);
}

.link-title {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-4);
}

.link-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.link-list a,
.link-list button {
    color: var(--text-secondary);
    text-decoration: none;
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    font-size: var(--text-sm);
    transition: var(--transition-colors);
}

.link-list a:hover,
.link-list button:hover {
    color: var(--brand-primary);
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--space-8);
    border-top: 1px solid var(--border-primary);
    color: var(--text-tertiary);
    font-size: var(--text-sm);
}

.footer-actions {
    display: flex;
    gap: var(--space-2);
}

/* Skip Link for Accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--brand-primary);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: var(--radius-base);
    z-index: 1000;
    transition: var(--transition-all);
}

.skip-link:focus {
    top: 6px;
}

/* Modal Enhancements */
.modal-title {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin: 0;
    font-size: var(--text-lg);
    font-weight: 600;
}

.contact-form .form-group:last-child {
    margin-bottom: 0;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hero-title {
        font-size: var(--text-3xl);
    }

    .hero-description {
        font-size: var(--text-lg);
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .floating-card {
        display: none;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--space-8);
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }

    .footer-bottom {
        flex-direction: column;
        gap: var(--space-4);
        text-align: center;
    }
}

/* Interactive Dashboard Specific Styles */

/* Dashboard Header */
.dashboard-header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    padding: var(--space-8) 0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dashboard-title {
    font-size: var(--text-3xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--space-2);
}

.dashboard-subtitle {
    color: var(--text-secondary);
    margin: 0;
    font-size: var(--text-lg);
}

.header-actions {
    display: flex;
    gap: var(--space-3);
}

/* Metrics Section */
.metrics-section {
    padding: var(--space-12) 0;
    background: var(--bg-primary);
}

.metrics-grid {
    gap: var(--space-6);
}

.metric-card {
    text-align: center;
    transition: var(--transition-all);
}

.metric-card:hover {
    transform: translateY(-4px);
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-4);
}

.metric-title {
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--text-secondary);
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.metric-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-lg);
}

.metric-icon.success {
    background: linear-gradient(135deg, var(--success), var(--success-light));
    color: white;
}

.metric-icon.warning {
    background: linear-gradient(135deg, var(--warning), var(--warning-light));
    color: white;
}

.metric-icon.info {
    background: linear-gradient(135deg, var(--info), var(--info-light));
    color: white;
}

.metric-number {
    font-size: var(--text-4xl);
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.metric-label {
    font-size: var(--text-sm);
    color: var(--text-tertiary);
    margin-bottom: var(--space-3);
}

.metric-status {
    margin-bottom: var(--space-3);
}

.metric-change {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-1);
    font-size: var(--text-xs);
    font-weight: 500;
}

.metric-change.positive {
    color: var(--success);
}

.metric-change.negative {
    color: var(--error);
}

.metric-change.neutral {
    color: var(--text-tertiary);
}

/* Analytics Section */
.analytics-section {
    padding: var(--space-12) 0;
    background: var(--bg-secondary);
}

.analytics-grid {
    gap: var(--space-8);
}

/* Activity Section */
.activity-section {
    padding: var(--space-12) 0;
    background: var(--bg-primary);
}

.activity-grid {
    gap: var(--space-8);
}

.card-title {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

/* Quick Actions */
.quick-actions-section {
    padding: var(--space-12) 0;
    background: var(--bg-secondary);
}

.action-card {
    text-align: center;
    cursor: pointer;
    background: none;
    border: none;
    width: 100%;
    transition: var(--transition-all);
}

.action-card:hover {
    transform: translateY(-6px);
}

.action-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-xl);
    background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-4);
    font-size: var(--text-xl);
    color: white;
    box-shadow: var(--shadow-md);
}

.action-title {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-2);
}

.action-description {
    color: var(--text-secondary);
    font-size: var(--text-sm);
    line-height: var(--leading-relaxed);
    margin: 0;
}

/* Dropdown Enhancements */
.nav-item.dropdown {
    position: relative;
}

.dropdown-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-lg);
    transition: var(--transition-all);
    font-weight: 500;
}

.dropdown-toggle:hover {
    color: var(--brand-primary);
    background: var(--bg-tertiary);
}

.dropdown-toggle i:last-child {
    transition: var(--transition-transform);
}

.dropdown.show .dropdown-toggle i:last-child {
    transform: rotate(180deg);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: var(--space-4);
        text-align: center;
    }

    .dashboard-title {
        font-size: var(--text-2xl);
    }

    .dashboard-subtitle {
        font-size: var(--text-base);
    }

    .search-advanced {
        order: -1;
        width: 100%;
        max-width: none;
    }

    .navbar-content {
        flex-direction: column;
        gap: var(--space-4);
    }

    .navbar-nav {
        width: 100%;
        justify-content: center;
    }
}
