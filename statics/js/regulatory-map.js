/**
 * Enhanced Regulatory Map - Interactive Visualization
 * Provides interactive network visualization of regulatory entities and relationships
 */

class RegulatoryMap {
    constructor(containerId) {
        this.containerId = containerId;
        this.container = d3.select(`#${containerId}`);
        this.svg = d3.select('#regulatoryMapSvg');
        this.miniMapSvg = d3.select('#miniMapSvg');
        this.width = 0;
        this.height = 0;
        this.simulation = null;
        this.entities = [];
        this.relationships = [];
        this.filteredEntities = [];
        this.filteredRelationships = [];
        this.selectedEntity = null;
        this.highlightedEntities = new Set();
        this.layoutAlgorithm = 'force';
        this.animationSpeed = 1;
        
        // Color scheme for different entity types
        this.colorScheme = {
            regulation: '#007bff',
            requirement: '#28a745',
            control: '#ffc107',
            business_process: '#17a2b8',
            system: '#6f42c1',
            policy: '#fd7e14',
            procedure: '#e83e8c'
        };
        
        this.init();
    }
    
    init() {
        this.setupDimensions();
        this.setupEventListeners();
        this.loadData();
        
        // Setup zoom behavior
        this.zoom = d3.zoom()
            .scaleExtent([0.1, 3])
            .on('zoom', (event) => {
                this.svg.select('.map-group').attr('transform', event.transform);
            });
        
        this.svg.call(this.zoom);
        
        // Create main group for map elements
        this.mapGroup = this.svg.append('g').attr('class', 'map-group');

        // Setup mini map
        this.setupMiniMap();
    }
    
    setupDimensions() {
        const rect = this.container.node().getBoundingClientRect();
        this.width = rect.width;
        this.height = rect.height;
        this.svg.attr('width', this.width).attr('height', this.height);
    }
    
    setupEventListeners() {
        // Filter controls
        document.getElementById('applyFilters').addEventListener('click', () => {
            this.applyFilters();
        });

        document.getElementById('resetFilters').addEventListener('click', () => {
            this.resetFilters();
        });

        document.getElementById('resetZoom').addEventListener('click', () => {
            this.resetZoom();
        });

        document.getElementById('saveView').addEventListener('click', () => {
            this.saveView();
        });

        document.getElementById('exportMap').addEventListener('click', () => {
            this.exportMap();
        });

        document.getElementById('closeDetails').addEventListener('click', () => {
            this.hideEntityDetails();
        });

        // Toggle controls
        document.getElementById('toggleControls').addEventListener('click', () => {
            this.toggleControls();
        });

        document.getElementById('toggleLegend').addEventListener('click', () => {
            this.toggleLegend();
        });

        // Layout algorithm change
        document.getElementById('layoutAlgorithm').addEventListener('change', (e) => {
            this.layoutAlgorithm = e.target.value;
            this.applyLayout();
        });

        // Animation speed change
        document.getElementById('animationSpeed').addEventListener('input', (e) => {
            this.animationSpeed = parseFloat(e.target.value);
            if (this.simulation) {
                this.simulation.alpha(0.3 * this.animationSpeed).restart();
            }
        });

        // Real-time search
        document.getElementById('searchFilter').addEventListener('input', (e) => {
            this.debounce(() => this.applyFilters(), 300)();
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // Window resize
        window.addEventListener('resize', () => {
            this.debounce(() => this.setupDimensions(), 250)();
        });
    }
    
    async loadData() {
        try {
            // Show loading indicator
            this.showLoading();
            
            // Load entities and relationships
            const [entitiesResponse, relationshipsResponse] = await Promise.all([
                fetch('/api/v1/regulatory-map/entities?limit=1000'),
                fetch('/api/v1/regulatory-map/relationships?limit=1000')
            ]);
            
            this.entities = await entitiesResponse.json();
            this.relationships = await relationshipsResponse.json();
            
            // Load compliance status
            const complianceResponse = await fetch('/api/v1/regulatory-map/compliance-status?limit=1000');
            const complianceStatuses = await complianceResponse.json();
            
            // Merge compliance status with entities
            this.mergeComplianceStatus(complianceStatuses);
            
            // Initial render
            this.applyFilters();
            
            this.hideLoading();
            
        } catch (error) {
            console.error('Error loading regulatory map data:', error);
            this.showError('Failed to load regulatory map data. Please try again.');
        }
    }
    
    mergeComplianceStatus(complianceStatuses) {
        const statusMap = new Map();
        complianceStatuses.forEach(status => {
            statusMap.set(status.entity_id, status);
        });
        
        this.entities.forEach(entity => {
            entity.complianceStatus = statusMap.get(entity.id);
        });
    }
    
    applyFilters() {
        const entityTypeFilter = Array.from(document.getElementById('entityTypeFilter').selectedOptions)
            .map(option => option.value);
        const jurisdictionFilter = document.getElementById('jurisdictionFilter').value;
        const complianceFilter = document.getElementById('complianceFilter').value;
        const searchFilter = document.getElementById('searchFilter').value.toLowerCase();
        
        // Filter entities
        this.filteredEntities = this.entities.filter(entity => {
            // Entity type filter
            if (entityTypeFilter.length > 0 && !entityTypeFilter.includes(entity.type)) {
                return false;
            }
            
            // Jurisdiction filter
            if (jurisdictionFilter && entity.jurisdiction !== jurisdictionFilter) {
                return false;
            }
            
            // Compliance filter
            if (complianceFilter && (!entity.complianceStatus || 
                entity.complianceStatus.compliance_status !== complianceFilter)) {
                return false;
            }
            
            // Search filter
            if (searchFilter && !entity.name.toLowerCase().includes(searchFilter) &&
                !entity.description?.toLowerCase().includes(searchFilter)) {
                return false;
            }
            
            return true;
        });
        
        // Filter relationships to only include those between filtered entities
        const entityIds = new Set(this.filteredEntities.map(e => e.id));
        this.filteredRelationships = this.relationships.filter(rel => 
            entityIds.has(rel.source_id) && entityIds.has(rel.target_id)
        );
        
        this.renderMap();
    }
    
    renderMap() {
        // Clear existing elements
        this.mapGroup.selectAll('*').remove();
        
        if (this.filteredEntities.length === 0) {
            this.showEmptyState();
            return;
        }
        
        // Setup force simulation
        this.setupSimulation();
        
        // Render relationships
        if (document.getElementById('showRelationships').checked) {
            this.renderRelationships();
        }
        
        // Render entities
        this.renderEntities();
        
        // Start simulation
        this.simulation.nodes(this.filteredEntities);
        this.simulation.force('link').links(this.filteredRelationships);
        this.simulation.alpha(1).restart();
    }
    
    setupSimulation() {
        this.simulation = d3.forceSimulation()
            .force('link', d3.forceLink()
                .id(d => d.id)
                .distance(100)
                .strength(0.1))
            .force('charge', d3.forceManyBody()
                .strength(-300))
            .force('center', d3.forceCenter(this.width / 2, this.height / 2))
            .force('collision', d3.forceCollide()
                .radius(30));
    }
    
    renderEntities() {
        const entityGroups = this.mapGroup.selectAll('.entity-group')
            .data(this.filteredEntities)
            .enter()
            .append('g')
            .attr('class', 'entity-group')
            .call(this.drag());
        
        // Entity circles
        entityGroups.append('circle')
            .attr('class', 'entity-node')
            .attr('r', d => this.getEntityRadius(d))
            .attr('fill', d => this.getEntityColor(d))
            .attr('stroke', d => this.getEntityStroke(d))
            .attr('stroke-width', 2)
            .on('click', (event, d) => this.showEntityDetails(d))
            .on('mouseover', (event, d) => this.showTooltip(event, d))
            .on('mouseout', () => this.hideTooltip());
        
        // Entity labels
        if (document.getElementById('showLabels').checked) {
            entityGroups.append('text')
                .attr('class', 'entity-label')
                .attr('dy', d => this.getEntityRadius(d) + 15)
                .text(d => this.truncateText(d.name, 20))
                .style('font-size', '10px');
        }
        
        // Update positions on simulation tick
        this.simulation.on('tick', () => {
            entityGroups.attr('transform', d => `translate(${d.x},${d.y})`);

            this.mapGroup.selectAll('.relationship-link')
                .attr('x1', d => d.source.x)
                .attr('y1', d => d.source.y)
                .attr('x2', d => d.target.x)
                .attr('y2', d => d.target.y);

            // Update mini map periodically
            if (this.simulation.alpha() < 0.1) {
                this.updateMiniMap();
            }
        });
    }
    
    renderRelationships() {
        this.mapGroup.selectAll('.relationship-link')
            .data(this.filteredRelationships)
            .enter()
            .append('line')
            .attr('class', 'relationship-link')
            .attr('stroke-width', d => Math.max(1, (d.strength || 0.5) * 4))
            .on('mouseover', (event, d) => this.showRelationshipTooltip(event, d))
            .on('mouseout', () => this.hideTooltip());
    }
    
    getEntityRadius(entity) {
        const baseRadius = 15;
        const typeMultiplier = {
            regulation: 1.2,
            requirement: 1.0,
            control: 0.9,
            business_process: 1.1,
            system: 0.8,
            policy: 1.0,
            procedure: 0.8
        };
        return baseRadius * (typeMultiplier[entity.type] || 1.0);
    }
    
    getEntityColor(entity) {
        return this.colorScheme[entity.type] || '#6c757d';
    }
    
    getEntityStroke(entity) {
        if (!entity.complianceStatus) return '#6c757d';
        
        const statusColors = {
            compliant: '#28a745',
            at_risk: '#ffc107',
            non_compliant: '#dc3545',
            unknown: '#6c757d'
        };
        
        return statusColors[entity.complianceStatus.compliance_status] || '#6c757d';
    }
    
    toggleControls() {
        const content = document.getElementById('controlsContent');
        const toggle = document.getElementById('toggleControls').querySelector('i');

        if (content.classList.contains('collapsed')) {
            content.classList.remove('collapsed');
            toggle.className = 'fas fa-chevron-up';
        } else {
            content.classList.add('collapsed');
            toggle.className = 'fas fa-chevron-down';
        }
    }

    toggleLegend() {
        const content = document.getElementById('legendContent');
        const toggle = document.getElementById('toggleLegend').querySelector('i');

        if (content.style.display === 'none') {
            content.style.display = 'block';
            toggle.className = 'fas fa-eye';
        } else {
            content.style.display = 'none';
            toggle.className = 'fas fa-eye-slash';
        }
    }

    resetFilters() {
        // Reset all filter controls
        document.getElementById('entityTypeFilter').selectedIndex = -1;
        document.getElementById('jurisdictionFilter').value = '';
        document.getElementById('complianceFilter').value = '';
        document.getElementById('searchFilter').value = '';
        document.getElementById('showRelationships').checked = true;
        document.getElementById('showLabels').checked = true;
        document.getElementById('layoutAlgorithm').value = 'force';
        document.getElementById('animationSpeed').value = 1;

        this.layoutAlgorithm = 'force';
        this.animationSpeed = 1;
        this.selectedEntity = null;
        this.highlightedEntities.clear();

        this.applyFilters();
    }

    handleKeyboardShortcuts(event) {
        // Ctrl/Cmd + F: Focus search
        if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
            event.preventDefault();
            document.getElementById('searchFilter').focus();
        }

        // Escape: Clear selection and hide details
        if (event.key === 'Escape') {
            this.clearSelection();
            this.hideEntityDetails();
        }

        // R: Reset zoom
        if (event.key === 'r' || event.key === 'R') {
            this.resetZoom();
        }

        // Space: Pause/resume simulation
        if (event.key === ' ') {
            event.preventDefault();
            this.toggleSimulation();
        }
    }

    clearSelection() {
        this.selectedEntity = null;
        this.highlightedEntities.clear();

        // Remove visual selection
        this.mapGroup.selectAll('.entity-node')
            .classed('selected', false)
            .classed('highlighted', false);

        this.mapGroup.selectAll('.relationship-link')
            .classed('highlighted', false);
    }

    toggleSimulation() {
        if (this.simulation) {
            if (this.simulation.alpha() > 0) {
                this.simulation.stop();
            } else {
                this.simulation.alpha(0.3).restart();
            }
        }
    }

    applyLayout() {
        if (!this.simulation) return;

        // Clear existing forces
        this.simulation.force('link', null);
        this.simulation.force('charge', null);
        this.simulation.force('center', null);
        this.simulation.force('collision', null);
        this.simulation.force('x', null);
        this.simulation.force('y', null);

        switch (this.layoutAlgorithm) {
            case 'force':
                this.setupForceLayout();
                break;
            case 'hierarchical':
                this.setupHierarchicalLayout();
                break;
            case 'circular':
                this.setupCircularLayout();
                break;
            case 'grid':
                this.setupGridLayout();
                break;
        }

        this.simulation.alpha(1).restart();
    }

    setupForceLayout() {
        this.simulation
            .force('link', d3.forceLink()
                .id(d => d.id)
                .distance(100)
                .strength(0.1))
            .force('charge', d3.forceManyBody()
                .strength(-300))
            .force('center', d3.forceCenter(this.width / 2, this.height / 2))
            .force('collision', d3.forceCollide()
                .radius(30));
    }

    setupHierarchicalLayout() {
        this.simulation
            .force('link', d3.forceLink()
                .id(d => d.id)
                .distance(80)
                .strength(0.2))
            .force('charge', d3.forceManyBody()
                .strength(-200))
            .force('y', d3.forceY()
                .y(d => {
                    // Position based on entity type hierarchy
                    const typeOrder = {
                        regulation: 0,
                        requirement: 1,
                        control: 2,
                        business_process: 3,
                        system: 4,
                        policy: 2,
                        procedure: 3
                    };
                    return (typeOrder[d.type] || 0) * (this.height / 6) + 100;
                })
                .strength(0.3))
            .force('collision', d3.forceCollide()
                .radius(25));
    }

    setupCircularLayout() {
        const radius = Math.min(this.width, this.height) / 3;
        const centerX = this.width / 2;
        const centerY = this.height / 2;

        this.simulation
            .force('link', d3.forceLink()
                .id(d => d.id)
                .distance(50)
                .strength(0.1))
            .force('charge', d3.forceManyBody()
                .strength(-100))
            .force('radial', d3.forceRadial(radius, centerX, centerY)
                .strength(0.5))
            .force('collision', d3.forceCollide()
                .radius(20));
    }

    setupGridLayout() {
        const cols = Math.ceil(Math.sqrt(this.filteredEntities.length));
        const cellWidth = this.width / cols;
        const cellHeight = this.height / Math.ceil(this.filteredEntities.length / cols);

        this.simulation
            .force('x', d3.forceX()
                .x((d, i) => (i % cols) * cellWidth + cellWidth / 2)
                .strength(0.8))
            .force('y', d3.forceY()
                .y((d, i) => Math.floor(i / cols) * cellHeight + cellHeight / 2)
                .strength(0.8))
            .force('collision', d3.forceCollide()
                .radius(15));
    }

    showEntityDetails(entity) {
        // Clear previous selection
        this.clearSelection();

        // Set new selection
        this.selectedEntity = entity;
        this.highlightRelatedEntities(entity);

        const detailsPanel = document.getElementById('entityDetails');
        const content = document.getElementById('entityDetailsContent');
        
        const complianceStatus = entity.complianceStatus;
        const complianceBadge = complianceStatus ? 
            `<span class="compliance-badge compliance-${complianceStatus.compliance_status}">
                ${complianceStatus.compliance_status.replace('_', ' ').toUpperCase()}
            </span>` : 
            '<span class="compliance-badge compliance-unknown">UNKNOWN</span>';
        
        content.innerHTML = `
            <div class="mb-3">
                <h6 class="text-primary">${entity.name}</h6>
                <p class="text-muted mb-2">${entity.description || 'No description available'}</p>
                <div class="mb-2">
                    <strong>Type:</strong> 
                    <span class="badge" style="background-color: ${this.getEntityColor(entity)}">
                        ${entity.type.replace('_', ' ').toUpperCase()}
                    </span>
                </div>
                <div class="mb-2">
                    <strong>Jurisdiction:</strong> ${entity.jurisdiction || 'Not specified'}
                </div>
                <div class="mb-2">
                    <strong>Status:</strong> ${entity.status || 'Not specified'}
                </div>
                <div class="mb-2">
                    <strong>Compliance:</strong> ${complianceBadge}
                </div>
            </div>
            
            ${complianceStatus ? `
                <div class="mb-3">
                    <h6>Compliance Details</h6>
                    <div class="small">
                        <div><strong>Score:</strong> ${complianceStatus.compliance_score || 'N/A'}</div>
                        <div><strong>Risk Score:</strong> ${complianceStatus.risk_score || 'N/A'}</div>
                        <div><strong>Assessed By:</strong> ${complianceStatus.assessed_by || 'N/A'}</div>
                        <div><strong>Assessment Date:</strong> ${new Date(complianceStatus.assessment_date).toLocaleDateString()}</div>
                    </div>
                </div>
            ` : ''}
            
            <div class="d-grid gap-2">
                <button class="btn btn-outline-primary btn-sm" onclick="regulatoryMap.viewEntityHierarchy('${entity.id}')">
                    <i class="fas fa-sitemap me-1"></i>
                    View Hierarchy
                </button>
                <button class="btn btn-outline-secondary btn-sm" onclick="regulatoryMap.viewEntityRelationships('${entity.id}')">
                    <i class="fas fa-project-diagram me-1"></i>
                    View Relationships
                </button>
            </div>
        `;
        
        detailsPanel.classList.add('show');

        // Update visual selection
        this.updateVisualSelection();
    }

    highlightRelatedEntities(entity) {
        this.highlightedEntities.add(entity.id);

        // Find related entities through relationships
        this.filteredRelationships.forEach(rel => {
            if (rel.source_id === entity.id) {
                this.highlightedEntities.add(rel.target_id);
            } else if (rel.target_id === entity.id) {
                this.highlightedEntities.add(rel.source_id);
            }
        });
    }

    updateVisualSelection() {
        // Update entity visual states
        this.mapGroup.selectAll('.entity-node')
            .classed('selected', d => d.id === this.selectedEntity?.id)
            .classed('highlighted', d => this.highlightedEntities.has(d.id));

        // Update relationship visual states
        this.mapGroup.selectAll('.relationship-link')
            .classed('highlighted', d =>
                this.highlightedEntities.has(d.source_id) &&
                this.highlightedEntities.has(d.target_id)
            );
    }

    setupMiniMap() {
        const miniMapWidth = 150;
        const miniMapHeight = 100;

        this.miniMapSvg.attr('width', miniMapWidth).attr('height', miniMapHeight);

        // Create mini map viewport indicator
        this.miniMapViewport = this.miniMapSvg.append('rect')
            .attr('class', 'mini-map-viewport')
            .attr('fill', 'none')
            .attr('stroke', '#007bff')
            .attr('stroke-width', 2)
            .attr('opacity', 0.7);

        this.updateMiniMap();
    }

    updateMiniMap() {
        if (!this.filteredEntities.length) return;

        const miniMapWidth = 150;
        const miniMapHeight = 100;

        // Calculate bounds of main map
        const bounds = this.calculateMapBounds();
        const scaleX = miniMapWidth / (bounds.maxX - bounds.minX || 1);
        const scaleY = miniMapHeight / (bounds.maxY - bounds.minY || 1);
        const scale = Math.min(scaleX, scaleY) * 0.8;

        // Clear existing mini map content
        this.miniMapSvg.selectAll('.mini-entity').remove();

        // Add entities to mini map
        this.miniMapSvg.selectAll('.mini-entity')
            .data(this.filteredEntities)
            .enter()
            .append('circle')
            .attr('class', 'mini-entity')
            .attr('r', 2)
            .attr('fill', d => this.getEntityColor(d))
            .attr('cx', d => (d.x - bounds.minX) * scale + 10)
            .attr('cy', d => (d.y - bounds.minY) * scale + 10);
    }

    calculateMapBounds() {
        if (!this.filteredEntities.length) {
            return { minX: 0, maxX: this.width, minY: 0, maxY: this.height };
        }

        const xs = this.filteredEntities.map(d => d.x || 0);
        const ys = this.filteredEntities.map(d => d.y || 0);

        return {
            minX: Math.min(...xs) - 50,
            maxX: Math.max(...xs) + 50,
            minY: Math.min(...ys) - 50,
            maxY: Math.max(...ys) + 50
        };
    }
    
    hideEntityDetails() {
        const detailsPanel = document.getElementById('entityDetails');
        detailsPanel.classList.remove('show');
        this.clearSelection();
    }
    
    showTooltip(event, entity) {
        this.hideTooltip(); // Remove any existing tooltip

        const tooltip = d3.select('body').append('div')
            .attr('class', 'tooltip')
            .style('opacity', 0);

        const complianceStatus = entity.complianceStatus;
        const complianceBadge = complianceStatus ?
            `<span style="color: ${this.getComplianceColor(complianceStatus.compliance_status)}">${complianceStatus.compliance_status.toUpperCase()}</span>` :
            '<span style="color: #6c757d">UNKNOWN</span>';

        tooltip.html(`
            <div style="font-weight: bold; margin-bottom: 5px;">${entity.name}</div>
            <div style="margin-bottom: 3px;"><strong>Type:</strong> ${entity.type.replace('_', ' ').toUpperCase()}</div>
            <div style="margin-bottom: 3px;"><strong>Jurisdiction:</strong> ${entity.jurisdiction || 'Not specified'}</div>
            <div style="margin-bottom: 3px;"><strong>Status:</strong> ${entity.status || 'Not specified'}</div>
            <div><strong>Compliance:</strong> ${complianceBadge}</div>
            ${complianceStatus && complianceStatus.compliance_score ?
                `<div style="margin-top: 5px; font-size: 11px;">Score: ${complianceStatus.compliance_score}%</div>` : ''}
        `)
        .style('left', (event.pageX + 10) + 'px')
        .style('top', (event.pageY - 10) + 'px');

        tooltip.transition()
            .duration(200)
            .style('opacity', 1);
    }

    hideTooltip() {
        d3.selectAll('.tooltip').remove();
    }

    showRelationshipTooltip(event, relationship) {
        this.hideTooltip(); // Remove any existing tooltip

        const tooltip = d3.select('body').append('div')
            .attr('class', 'tooltip')
            .style('opacity', 0);

        const sourceEntity = this.filteredEntities.find(e => e.id === relationship.source_id);
        const targetEntity = this.filteredEntities.find(e => e.id === relationship.target_id);

        tooltip.html(`
            <div style="font-weight: bold; margin-bottom: 5px;">Relationship</div>
            <div style="margin-bottom: 3px;"><strong>Type:</strong> ${relationship.relationship_type.replace('_', ' ').toUpperCase()}</div>
            <div style="margin-bottom: 3px;"><strong>From:</strong> ${sourceEntity?.name || 'Unknown'}</div>
            <div style="margin-bottom: 3px;"><strong>To:</strong> ${targetEntity?.name || 'Unknown'}</div>
            ${relationship.strength ? `<div style="margin-bottom: 3px;"><strong>Strength:</strong> ${(relationship.strength * 100).toFixed(0)}%</div>` : ''}
            ${relationship.description ? `<div style="font-size: 11px; margin-top: 5px;">${relationship.description}</div>` : ''}
        `)
        .style('left', (event.pageX + 10) + 'px')
        .style('top', (event.pageY - 10) + 'px');

        tooltip.transition()
            .duration(200)
            .style('opacity', 1);
    }

    getComplianceColor(status) {
        const colors = {
            compliant: '#28a745',
            at_risk: '#ffc107',
            non_compliant: '#dc3545',
            unknown: '#6c757d'
        };
        return colors[status] || '#6c757d';
    }
    
    drag() {
        return d3.drag()
            .on('start', (event, d) => {
                if (!event.active) this.simulation.alphaTarget(0.3).restart();
                d.fx = d.x;
                d.fy = d.y;
            })
            .on('drag', (event, d) => {
                d.fx = event.x;
                d.fy = event.y;
            })
            .on('end', (event, d) => {
                if (!event.active) this.simulation.alphaTarget(0);
                d.fx = null;
                d.fy = null;
            });
    }
    
    resetZoom() {
        this.svg.transition().duration(750).call(
            this.zoom.transform,
            d3.zoomIdentity
        );
    }
    
    saveView() {
        // Implementation for saving current view
        alert('Save view functionality would be implemented here');
    }
    
    exportMap() {
        const exportOptions = [
            { label: 'Export as PNG', action: () => this.exportAsPNG() },
            { label: 'Export as SVG', action: () => this.exportAsSVG() },
            { label: 'Export Data as JSON', action: () => this.exportAsJSON() },
            { label: 'Export Report as PDF', action: () => this.exportAsPDF() }
        ];

        // Create export modal
        this.showExportModal(exportOptions);
    }

    exportAsPNG() {
        const svgElement = document.getElementById('regulatoryMapSvg');
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        canvas.width = this.width;
        canvas.height = this.height;

        const data = new XMLSerializer().serializeToString(svgElement);
        const img = new Image();
        const svgBlob = new Blob([data], { type: 'image/svg+xml;charset=utf-8' });
        const url = URL.createObjectURL(svgBlob);

        img.onload = () => {
            ctx.drawImage(img, 0, 0);
            const pngUrl = canvas.toDataURL('image/png');
            this.downloadFile(pngUrl, 'regulatory-map.png');
            URL.revokeObjectURL(url);
        };

        img.src = url;
    }

    exportAsSVG() {
        const svgElement = document.getElementById('regulatoryMapSvg');
        const serializer = new XMLSerializer();
        const svgString = serializer.serializeToString(svgElement);
        const blob = new Blob([svgString], { type: 'image/svg+xml' });
        const url = URL.createObjectURL(blob);
        this.downloadFile(url, 'regulatory-map.svg');
        URL.revokeObjectURL(url);
    }

    exportAsJSON() {
        const data = {
            entities: this.filteredEntities,
            relationships: this.filteredRelationships,
            metadata: {
                exportDate: new Date().toISOString(),
                totalEntities: this.filteredEntities.length,
                totalRelationships: this.filteredRelationships.length,
                layoutAlgorithm: this.layoutAlgorithm
            }
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        this.downloadFile(url, 'regulatory-map-data.json');
        URL.revokeObjectURL(url);
    }

    exportAsPDF() {
        // This would require a PDF library like jsPDF
        alert('PDF export would require additional PDF library integration');
    }

    downloadFile(url, filename) {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    showExportModal(options) {
        // Create a simple modal for export options
        const modal = document.createElement('div');
        modal.className = 'export-modal';
        modal.innerHTML = `
            <div class="export-modal-content">
                <h5>Export Regulatory Map</h5>
                <div class="export-options">
                    ${options.map((option, index) =>
                        `<button class="btn btn-outline-primary btn-sm mb-2 w-100" onclick="regulatoryMap.handleExport(${index})">${option.label}</button>`
                    ).join('')}
                </div>
                <button class="btn btn-secondary btn-sm" onclick="this.parentElement.parentElement.remove()">Cancel</button>
            </div>
        `;

        // Store options for later use
        this.exportOptions = options;

        document.body.appendChild(modal);
    }

    handleExport(index) {
        if (this.exportOptions && this.exportOptions[index]) {
            this.exportOptions[index].action();
        }
        // Remove modal
        document.querySelector('.export-modal')?.remove();
    }
    
    viewEntityHierarchy(entityId) {
        // Implementation for viewing entity hierarchy
        console.log('View hierarchy for entity:', entityId);
    }
    
    viewEntityRelationships(entityId) {
        // Implementation for viewing entity relationships
        console.log('View relationships for entity:', entityId);
    }
    
    showLoading() {
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'loading-spinner';
        loadingDiv.id = 'mapLoadingSpinner';
        loadingDiv.innerHTML = `
            <div class="spinner"></div>
            <div style="text-align: center; margin-top: 15px; color: #6c757d;">
                Loading regulatory map...
            </div>
        `;

        this.container.node().appendChild(loadingDiv);
    }

    hideLoading() {
        const spinner = document.getElementById('mapLoadingSpinner');
        if (spinner) {
            spinner.remove();
        }
    }
    
    showEmptyState() {
        this.mapGroup.append('text')
            .attr('x', this.width / 2)
            .attr('y', this.height / 2)
            .attr('text-anchor', 'middle')
            .style('font-size', '16px')
            .style('fill', '#6c757d')
            .text('No entities match the current filters');
    }
    
    showError(message) {
        this.mapGroup.append('text')
            .attr('x', this.width / 2)
            .attr('y', this.height / 2)
            .attr('text-anchor', 'middle')
            .style('font-size', '16px')
            .style('fill', '#dc3545')
            .text(message);
    }
    
    truncateText(text, maxLength) {
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize the regulatory map when the page loads
document.addEventListener('DOMContentLoaded', function() {
    window.regulatoryMap = new RegulatoryMap('mapContainer');
});
