/**
 * Regulatory Analytics Dashboard
 * Provides comprehensive analytics and insights for regulatory compliance
 */

class RegulatoryAnalytics {
    constructor() {
        this.charts = {};
        this.data = {};
        this.filters = {
            timePeriod: 90,
            jurisdiction: '',
            entityType: ''
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadData();
    }
    
    setupEventListeners() {
        document.getElementById('applyFilters').addEventListener('click', () => {
            this.updateFilters();
            this.loadData();
        });
        
        document.getElementById('refreshData').addEventListener('click', () => {
            this.loadData();
        });
        
        document.getElementById('exportReport').addEventListener('click', () => {
            this.exportReport();
        });
    }
    
    updateFilters() {
        this.filters.timePeriod = parseInt(document.getElementById('timePeriod').value);
        this.filters.jurisdiction = document.getElementById('jurisdictionFilter').value;
        this.filters.entityType = document.getElementById('entityTypeFilter').value;
    }
    
    async loadData() {
        try {
            this.showLoading();
            
            // Load regulatory map statistics
            const statsResponse = await fetch('/api/v1/regulatory-map/stats');
            const stats = await statsResponse.json();
            
            // Load entities with compliance status
            const entitiesResponse = await fetch('/api/v1/regulatory-map/entities?limit=1000');
            const entities = await entitiesResponse.json();
            
            // Load compliance status data
            const complianceResponse = await fetch('/api/v1/regulatory-map/compliance-status?limit=1000');
            const complianceStatuses = await complianceResponse.json();
            
            this.data = {
                stats,
                entities,
                complianceStatuses
            };
            
            this.processData();
            this.updateMetrics();
            this.updateCharts();
            this.updateRiskAnalysis();
            this.updateAIInsights();
            
            this.hideLoading();
            
        } catch (error) {
            console.error('Error loading analytics data:', error);
            this.showError('Failed to load analytics data');
        }
    }
    
    processData() {
        // Process raw data for analytics
        this.processedData = {
            totalEntities: this.data.entities.length,
            complianceRate: this.calculateComplianceRate(),
            atRiskEntities: this.calculateAtRiskEntities(),
            avgComplianceScore: this.calculateAverageComplianceScore(),
            entityDistribution: this.calculateEntityDistribution(),
            complianceStatusDistribution: this.calculateComplianceStatusDistribution(),
            jurisdictionDistribution: this.calculateJurisdictionDistribution(),
            complianceTrends: this.generateComplianceTrends(),
            riskFactors: this.analyzeRiskFactors()
        };
    }
    
    calculateComplianceRate() {
        const compliantEntities = this.data.complianceStatuses.filter(
            status => status.compliance_status === 'compliant'
        ).length;
        
        return this.data.complianceStatuses.length > 0 ? 
            Math.round((compliantEntities / this.data.complianceStatuses.length) * 100) : 0;
    }
    
    calculateAtRiskEntities() {
        return this.data.complianceStatuses.filter(
            status => status.compliance_status === 'at_risk'
        ).length;
    }
    
    calculateAverageComplianceScore() {
        const scores = this.data.complianceStatuses
            .filter(status => status.compliance_score !== null)
            .map(status => status.compliance_score);
        
        return scores.length > 0 ? 
            Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length) : 0;
    }
    
    calculateEntityDistribution() {
        const distribution = {};
        this.data.entities.forEach(entity => {
            distribution[entity.type] = (distribution[entity.type] || 0) + 1;
        });
        return distribution;
    }
    
    calculateComplianceStatusDistribution() {
        const distribution = {};
        this.data.complianceStatuses.forEach(status => {
            distribution[status.compliance_status] = (distribution[status.compliance_status] || 0) + 1;
        });
        return distribution;
    }
    
    calculateJurisdictionDistribution() {
        const distribution = {};
        this.data.entities.forEach(entity => {
            const jurisdiction = entity.jurisdiction || 'Unknown';
            distribution[jurisdiction] = (distribution[jurisdiction] || 0) + 1;
        });
        return distribution;
    }
    
    generateComplianceTrends() {
        // Generate mock trend data for demonstration
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
        const compliantTrend = [75, 78, 82, 85, 87, 89];
        const atRiskTrend = [15, 14, 12, 10, 9, 8];
        const nonCompliantTrend = [10, 8, 6, 5, 4, 3];
        
        return {
            labels: months,
            datasets: [
                {
                    label: 'Compliant',
                    data: compliantTrend,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                },
                {
                    label: 'At Risk',
                    data: atRiskTrend,
                    borderColor: '#ffc107',
                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                    tension: 0.4
                },
                {
                    label: 'Non-Compliant',
                    data: nonCompliantTrend,
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    tension: 0.4
                }
            ]
        };
    }
    
    analyzeRiskFactors() {
        const riskFactors = [];
        
        // Analyze compliance scores
        const lowScoreEntities = this.data.complianceStatuses.filter(
            status => status.compliance_score && status.compliance_score < 70
        ).length;
        
        if (lowScoreEntities > 0) {
            riskFactors.push({
                factor: 'Low Compliance Scores',
                count: lowScoreEntities,
                severity: lowScoreEntities > 5 ? 'high' : 'medium',
                description: `${lowScoreEntities} entities have compliance scores below 70%`
            });
        }
        
        // Analyze overdue assessments
        const overdueAssessments = this.data.complianceStatuses.filter(status => {
            if (!status.due_date) return false;
            return new Date(status.due_date) < new Date();
        }).length;
        
        if (overdueAssessments > 0) {
            riskFactors.push({
                factor: 'Overdue Assessments',
                count: overdueAssessments,
                severity: overdueAssessments > 3 ? 'high' : 'medium',
                description: `${overdueAssessments} compliance assessments are overdue`
            });
        }
        
        // Analyze unknown compliance status
        const unknownStatus = this.data.complianceStatuses.filter(
            status => status.compliance_status === 'unknown'
        ).length;
        
        if (unknownStatus > 0) {
            riskFactors.push({
                factor: 'Unknown Compliance Status',
                count: unknownStatus,
                severity: 'low',
                description: `${unknownStatus} entities have unknown compliance status`
            });
        }
        
        return riskFactors;
    }
    
    updateMetrics() {
        document.getElementById('totalEntities').textContent = this.processedData.totalEntities;
        document.getElementById('complianceRate').textContent = this.processedData.complianceRate + '%';
        document.getElementById('atRiskEntities').textContent = this.processedData.atRiskEntities;
        document.getElementById('avgComplianceScore').textContent = this.processedData.avgComplianceScore + '%';
    }
    
    updateCharts() {
        this.createComplianceTrendChart();
        this.createEntityDistributionChart();
        this.createComplianceStatusChart();
        this.createJurisdictionChart();
    }
    
    createComplianceTrendChart() {
        const ctx = document.getElementById('complianceTrendChart').getContext('2d');
        
        if (this.charts.complianceTrend) {
            this.charts.complianceTrend.destroy();
        }
        
        this.charts.complianceTrend = new Chart(ctx, {
            type: 'line',
            data: this.processedData.complianceTrends,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });
    }
    
    createEntityDistributionChart() {
        const ctx = document.getElementById('entityDistributionChart').getContext('2d');
        
        if (this.charts.entityDistribution) {
            this.charts.entityDistribution.destroy();
        }
        
        const data = this.processedData.entityDistribution;
        const colors = [
            '#007bff', '#28a745', '#ffc107', '#17a2b8', 
            '#6f42c1', '#fd7e14', '#e83e8c'
        ];
        
        this.charts.entityDistribution = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: Object.keys(data).map(key => key.replace('_', ' ').toUpperCase()),
                datasets: [{
                    data: Object.values(data),
                    backgroundColor: colors,
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });
    }
    
    createComplianceStatusChart() {
        const ctx = document.getElementById('complianceStatusChart').getContext('2d');
        
        if (this.charts.complianceStatus) {
            this.charts.complianceStatus.destroy();
        }
        
        const data = this.processedData.complianceStatusDistribution;
        
        this.charts.complianceStatus = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: Object.keys(data).map(key => key.replace('_', ' ').toUpperCase()),
                datasets: [{
                    label: 'Number of Entities',
                    data: Object.values(data),
                    backgroundColor: [
                        '#28a745', '#ffc107', '#dc3545', '#6c757d'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
    
    createJurisdictionChart() {
        const ctx = document.getElementById('jurisdictionChart').getContext('2d');
        
        if (this.charts.jurisdiction) {
            this.charts.jurisdiction.destroy();
        }
        
        const data = this.processedData.jurisdictionDistribution;
        
        this.charts.jurisdiction = new Chart(ctx, {
            type: 'polarArea',
            data: {
                labels: Object.keys(data),
                datasets: [{
                    data: Object.values(data),
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 205, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    updateRiskAnalysis() {
        const riskContainer = document.getElementById('riskAnalysis');
        const riskFactors = this.processedData.riskFactors;

        if (riskFactors.length === 0) {
            riskContainer.innerHTML = `
                <div class="risk-indicator risk-low">
                    <div>
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>No Critical Risk Factors Identified</strong>
                    </div>
                    <span class="badge bg-success">Good</span>
                </div>
            `;
            return;
        }

        riskContainer.innerHTML = riskFactors.map(risk => `
            <div class="risk-indicator risk-${risk.severity}">
                <div>
                    <i class="fas fa-${this.getRiskIcon(risk.severity)} me-2"></i>
                    <strong>${risk.factor}</strong>
                    <div class="small text-muted">${risk.description}</div>
                </div>
                <span class="badge bg-${this.getRiskBadgeColor(risk.severity)}">${risk.count}</span>
            </div>
        `).join('');
    }

    updateAIInsights() {
        const insightsContainer = document.getElementById('aiInsights');
        const insights = this.generateAIInsights();

        insightsContainer.innerHTML = insights.map(insight => `
            <div class="insight-card">
                <div class="insight-icon">
                    <i class="fas fa-${insight.icon}"></i>
                </div>
                <h6>${insight.title}</h6>
                <p class="mb-0">${insight.description}</p>
            </div>
        `).join('');
    }

    generateAIInsights() {
        const insights = [];

        // Compliance rate insight
        if (this.processedData.complianceRate > 85) {
            insights.push({
                icon: 'thumbs-up',
                title: 'Excellent Compliance',
                description: 'Your organization maintains a high compliance rate. Continue monitoring to sustain this performance.'
            });
        } else if (this.processedData.complianceRate < 70) {
            insights.push({
                icon: 'exclamation-triangle',
                title: 'Compliance Attention Needed',
                description: 'Consider implementing additional controls and regular assessments to improve compliance rates.'
            });
        }

        // Risk analysis insight
        const highRiskFactors = this.processedData.riskFactors.filter(r => r.severity === 'high');
        if (highRiskFactors.length > 0) {
            insights.push({
                icon: 'shield-alt',
                title: 'High Risk Areas',
                description: `${highRiskFactors.length} high-risk factors identified. Prioritize immediate remediation efforts.`
            });
        }

        // Entity distribution insight
        const entityTypes = Object.keys(this.processedData.entityDistribution);
        if (entityTypes.length > 5) {
            insights.push({
                icon: 'sitemap',
                title: 'Complex Regulatory Landscape',
                description: 'Your organization manages a diverse set of regulatory requirements. Consider consolidation opportunities.'
            });
        }

        // Default insight if no specific insights
        if (insights.length === 0) {
            insights.push({
                icon: 'chart-line',
                title: 'Steady Progress',
                description: 'Your compliance metrics show steady performance. Continue regular monitoring and assessments.'
            });
        }

        return insights;
    }

    getRiskIcon(severity) {
        const icons = {
            low: 'info-circle',
            medium: 'exclamation-triangle',
            high: 'exclamation-circle'
        };
        return icons[severity] || 'info-circle';
    }

    getRiskBadgeColor(severity) {
        const colors = {
            low: 'info',
            medium: 'warning',
            high: 'danger'
        };
        return colors[severity] || 'secondary';
    }

    exportReport() {
        const reportData = {
            generatedAt: new Date().toISOString(),
            filters: this.filters,
            metrics: this.processedData,
            summary: {
                totalEntities: this.processedData.totalEntities,
                complianceRate: this.processedData.complianceRate,
                atRiskEntities: this.processedData.atRiskEntities,
                avgComplianceScore: this.processedData.avgComplianceScore
            },
            riskFactors: this.processedData.riskFactors,
            insights: this.generateAIInsights()
        };

        // Create and download JSON report
        const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `regulatory-analytics-report-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        // Show success message
        this.showSuccessMessage('Analytics report exported successfully!');
    }

    showLoading() {
        // Add loading indicators to charts
        const chartContainers = document.querySelectorAll('.chart-container');
        chartContainers.forEach(container => {
            container.innerHTML = `
                <div class="d-flex justify-content-center align-items-center h-100">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            `;
        });
    }

    hideLoading() {
        // Loading will be hidden when charts are created
    }

    showError(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.container-fluid').firstChild);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }

    showSuccessMessage(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.container-fluid').firstChild);

        // Auto-dismiss after 3 seconds
        setTimeout(() => {
            alertDiv.remove();
        }, 3000);
    }
}

// Initialize analytics dashboard when page loads
document.addEventListener('DOMContentLoaded', function() {
    window.regulatoryAnalytics = new RegulatoryAnalytics();
});
