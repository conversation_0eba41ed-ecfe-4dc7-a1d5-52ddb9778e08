
document.addEventListener('DOMContentLoaded', function() {
    // Initialize language selector
    initializeLanguageSelector();

    // Theme toggle is now handled by theme-manager.js
    console.log('Main JS loaded - theme management delegated to ThemeManager');
});

function initializeLanguageSelector() {
    console.log("Main JS loaded");
    
    const languageButton = document.getElementById('nav-language-button');
    const languageDropdown = document.querySelector('.language-dropdown');
    
    if (!languageButton || !languageDropdown) {
        console.error("Language selector elements not found");
        return;
    }
    
    // Stop propagation on dropdown to prevent it from closing when clicking inside
    languageDropdown.addEventListener('click', function(e) {
        e.stopPropagation();
    });
    
    // Toggle dropdown when button is clicked
    languageButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        // Close dropdown if it's already open
        if (languageDropdown.classList.contains('show')) {
            languageDropdown.classList.remove('show');
            const arrow = languageButton.querySelector('.dropdown-arrow');
            if (arrow) {
                arrow.style.transform = 'rotate(0)';
            }
        } else {
            // Open dropdown
            languageDropdown.classList.add('show');
            const arrow = languageButton.querySelector('.dropdown-arrow');
            if (arrow) {
                arrow.style.transform = 'rotate(180deg)';
            }
        }
    });
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function() {
        if (languageDropdown.classList.contains('show')) {
            languageDropdown.classList.remove('show');
            const arrow = languageButton.querySelector('.dropdown-arrow');
            if (arrow) {
                arrow.style.transform = 'rotate(0)';
            }
        }
    });
}

// Theme toggle functionality is now handled by the ThemeManager class
// in theme-manager.js for better OS and browser integration
