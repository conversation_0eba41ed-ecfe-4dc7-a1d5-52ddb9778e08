
document.addEventListener('DOMContentLoaded', function() {
    // Initialize language selector
    initializeLanguageSelector();
    
    // Initialize theme toggle
    initializeThemeToggle();
});

function initializeLanguageSelector() {
    console.log("Main JS loaded");
    
    const languageButton = document.getElementById('nav-language-button');
    const languageDropdown = document.querySelector('.language-dropdown');
    
    if (!languageButton || !languageDropdown) {
        console.error("Language selector elements not found");
        return;
    }
    
    // Stop propagation on dropdown to prevent it from closing when clicking inside
    languageDropdown.addEventListener('click', function(e) {
        e.stopPropagation();
    });
    
    // Toggle dropdown when button is clicked
    languageButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        // Close dropdown if it's already open
        if (languageDropdown.classList.contains('show')) {
            languageDropdown.classList.remove('show');
            const arrow = languageButton.querySelector('.dropdown-arrow');
            if (arrow) {
                arrow.style.transform = 'rotate(0)';
            }
        } else {
            // Open dropdown
            languageDropdown.classList.add('show');
            const arrow = languageButton.querySelector('.dropdown-arrow');
            if (arrow) {
                arrow.style.transform = 'rotate(180deg)';
            }
        }
    });
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function() {
        if (languageDropdown.classList.contains('show')) {
            languageDropdown.classList.remove('show');
            const arrow = languageButton.querySelector('.dropdown-arrow');
            if (arrow) {
                arrow.style.transform = 'rotate(0)';
            }
        }
    });
}

function initializeThemeToggle() {
    const themeToggle = document.getElementById('themeToggle');
    
    if (!themeToggle) {
        console.error("Theme toggle not found");
        return;
    }
    
    const toggleIcon = themeToggle.querySelector('.toggle-icon');
    
    // Check for saved theme preference or prefer-color-scheme
    const savedTheme = localStorage.getItem('theme');
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
        document.body.classList.add('dark-mode');
        if (toggleIcon) {
            toggleIcon.classList.remove('fa-moon');
            toggleIcon.classList.add('fa-sun');
        }
    }
    
    // Theme toggle functionality
    themeToggle.addEventListener('click', function() {
        document.body.classList.toggle('dark-mode');
        
        if (document.body.classList.contains('dark-mode')) {
            if (toggleIcon) {
                toggleIcon.classList.remove('fa-moon');
                toggleIcon.classList.add('fa-sun');
            }
            localStorage.setItem('theme', 'dark');
        } else {
            if (toggleIcon) {
                toggleIcon.classList.remove('fa-sun');
                toggleIcon.classList.add('fa-moon');
            }
            localStorage.setItem('theme', 'light');
        }
    });
}
