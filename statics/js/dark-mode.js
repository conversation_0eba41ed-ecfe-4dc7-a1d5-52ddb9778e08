
// Dark Mode Functionality
document.addEventListener('DOMContentLoaded', function() {
    const themeSwitch = document.getElementById('themeSwitch');
    if (!themeSwitch) return; // Exit if the switch doesn't exist
    
    // Always default to dark mode
    document.body.classList.add('dark-mode');
    themeSwitch.checked = true;
    localStorage.setItem('theme', 'dark');
    
    // Theme switch event handler
    themeSwitch.addEventListener('change', function() {
        if (this.checked) {
            document.body.classList.add('dark-mode');
            localStorage.setItem('theme', 'dark');
        } else {
            document.body.classList.remove('dark-mode');
            localStorage.setItem('theme', 'light');
        }
        
        // Update chart colors if charts exist
        if (typeof updateChartColors === 'function') {
            updateChartColors();
        }
    });
});

// Function to update chart colors based on theme
function updateChartColors() {
    if (window.charts) {
        const isDarkMode = document.body.classList.contains('dark-mode');
        
        window.charts.forEach(chart => {
            // Update grid colors
            if (chart.options.scales) {
                Object.keys(chart.options.scales).forEach(axisKey => {
                    const axis = chart.options.scales[axisKey];
                    if (axis.grid) {
                        axis.grid.color = isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
                    }
                    if (axis.ticks) {
                        axis.ticks.color = isDarkMode ? '#e0e0e0' : '#333';
                    }
                });
            }
            
            // Update legend colors
            if (chart.options.plugins && chart.options.plugins.legend) {
                chart.options.plugins.legend.labels = {
                    color: isDarkMode ? '#e0e0e0' : '#333'
                };
            }
            
            chart.update();
        });
    }
}
