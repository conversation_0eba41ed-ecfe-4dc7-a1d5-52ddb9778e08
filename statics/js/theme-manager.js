/**
 * Advanced Theme Manager with OS and Browser Dark Mode Detection
 * 
 * Features:
 * - Automatic OS dark mode detection
 * - Browser preference detection
 * - Local storage persistence
 * - System preference change monitoring
 * - Smooth theme transitions
 * - Theme preference inheritance
 */

class ThemeManager {
    constructor() {
        this.STORAGE_KEY = 'regulationguru-theme';
        this.THEME_ATTRIBUTE = 'data-theme';
        this.DARK_CLASS = 'dark-mode';
        
        // Theme options
        this.THEMES = {
            LIGHT: 'light',
            DARK: 'dark',
            AUTO: 'auto'
        };
        
        // Initialize theme system
        this.init();
    }
    
    /**
     * Initialize the theme manager
     */
    init() {
        // Set up media query listener for system preference changes
        this.setupSystemPreferenceListener();
        
        // Apply initial theme
        this.applyInitialTheme();
        
        // Set up theme toggle functionality
        this.setupThemeToggle();
        
        // Set up documentation theme sync
        this.setupDocumentationThemeSync();
        
        console.log('ThemeManager initialized');
    }
    
    /**
     * Set up listener for system dark mode preference changes
     */
    setupSystemPreferenceListener() {
        if (window.matchMedia) {
            this.darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            
            // Listen for changes in system preference
            this.darkModeMediaQuery.addEventListener('change', (e) => {
                console.log('System dark mode preference changed:', e.matches);
                
                // Only auto-update if user hasn't set a manual preference
                const userPreference = this.getUserPreference();
                if (userPreference === this.THEMES.AUTO || !userPreference) {
                    this.applyTheme(this.getEffectiveTheme());
                }
            });
        }
    }
    
    /**
     * Get user's stored theme preference
     */
    getUserPreference() {
        try {
            return localStorage.getItem(this.STORAGE_KEY);
        } catch (e) {
            console.warn('Could not access localStorage for theme preference:', e);
            return null;
        }
    }
    
    /**
     * Set user's theme preference
     */
    setUserPreference(theme) {
        try {
            localStorage.setItem(this.STORAGE_KEY, theme);
            console.log('Theme preference saved:', theme);
        } catch (e) {
            console.warn('Could not save theme preference to localStorage:', e);
        }
    }
    
    /**
     * Detect system dark mode preference
     */
    getSystemPreference() {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            return this.THEMES.DARK;
        }
        return this.THEMES.LIGHT;
    }
    
    /**
     * Get the effective theme (considering user preference and system preference)
     */
    getEffectiveTheme() {
        const userPreference = this.getUserPreference();
        
        // If user has set a specific preference, use it
        if (userPreference === this.THEMES.LIGHT || userPreference === this.THEMES.DARK) {
            return userPreference;
        }
        
        // Otherwise, use system preference
        return this.getSystemPreference();
    }
    
    /**
     * Apply initial theme on page load
     */
    applyInitialTheme() {
        const effectiveTheme = this.getEffectiveTheme();
        this.applyTheme(effectiveTheme);
        
        console.log('Initial theme applied:', effectiveTheme);
    }
    
    /**
     * Apply a theme to the document
     */
    applyTheme(theme) {
        const body = document.body;
        const html = document.documentElement;
        
        // Remove existing theme classes
        body.classList.remove(this.DARK_CLASS);
        
        // Apply new theme
        if (theme === this.THEMES.DARK) {
            body.classList.add(this.DARK_CLASS);
            html.setAttribute(this.THEME_ATTRIBUTE, this.THEMES.DARK);
        } else {
            html.setAttribute(this.THEME_ATTRIBUTE, this.THEMES.LIGHT);
        }
        
        // Update theme toggle button
        this.updateThemeToggleButton(theme);
        
        // Sync with documentation if in iframe
        this.syncDocumentationTheme(theme);
        
        // Dispatch theme change event
        this.dispatchThemeChangeEvent(theme);
        
        console.log('Theme applied:', theme);
    }
    
    /**
     * Toggle between light and dark themes
     */
    toggleTheme() {
        const currentTheme = this.getEffectiveTheme();
        const newTheme = currentTheme === this.THEMES.DARK ? this.THEMES.LIGHT : this.THEMES.DARK;
        
        // Save user preference
        this.setUserPreference(newTheme);
        
        // Apply new theme
        this.applyTheme(newTheme);
        
        console.log('Theme toggled from', currentTheme, 'to', newTheme);
    }
    
    /**
     * Set theme to auto (follow system preference)
     */
    setAutoTheme() {
        this.setUserPreference(this.THEMES.AUTO);
        const systemTheme = this.getSystemPreference();
        this.applyTheme(systemTheme);
        
        console.log('Theme set to auto, using system preference:', systemTheme);
    }
    
    /**
     * Update theme toggle button appearance
     */
    updateThemeToggleButton(theme) {
        const toggleButton = document.getElementById('themeToggle');
        const toggleIcon = toggleButton?.querySelector('.toggle-icon');
        
        if (toggleIcon) {
            if (theme === this.THEMES.DARK) {
                toggleIcon.className = 'fas fa-sun toggle-icon';
                toggleButton.setAttribute('aria-label', 'Switch to light mode');
                toggleButton.setAttribute('title', 'Switch to light mode');
            } else {
                toggleIcon.className = 'fas fa-moon toggle-icon';
                toggleButton.setAttribute('aria-label', 'Switch to dark mode');
                toggleButton.setAttribute('title', 'Switch to dark mode');
            }
        }
    }
    
    /**
     * Set up theme toggle button functionality
     */
    setupThemeToggle() {
        const toggleButton = document.getElementById('themeToggle');
        
        if (toggleButton) {
            toggleButton.addEventListener('click', () => {
                this.toggleTheme();
            });
            
            // Add keyboard support
            toggleButton.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.toggleTheme();
                }
            });
            
            // Make button focusable
            toggleButton.setAttribute('tabindex', '0');
            toggleButton.setAttribute('role', 'button');
        }
    }
    
    /**
     * Sync theme with documentation pages
     */
    syncDocumentationTheme(theme) {
        // If we're in the main app, try to sync with documentation iframes
        const docIframes = document.querySelectorAll('iframe[src*="/docs"]');
        docIframes.forEach(iframe => {
            try {
                if (iframe.contentDocument) {
                    const docBody = iframe.contentDocument.body;
                    if (theme === this.THEMES.DARK) {
                        docBody.classList.add(this.DARK_CLASS);
                    } else {
                        docBody.classList.remove(this.DARK_CLASS);
                    }
                }
            } catch (e) {
                // Cross-origin restrictions may prevent access
                console.log('Could not sync theme with documentation iframe:', e);
            }
        });
    }
    
    /**
     * Set up documentation theme synchronization
     */
    setupDocumentationThemeSync() {
        // Listen for messages from documentation pages
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'theme-sync-request') {
                const currentTheme = this.getEffectiveTheme();
                event.source.postMessage({
                    type: 'theme-sync-response',
                    theme: currentTheme
                }, event.origin);
            }
        });
    }
    
    /**
     * Dispatch custom theme change event
     */
    dispatchThemeChangeEvent(theme) {
        const event = new CustomEvent('themechange', {
            detail: { theme: theme }
        });
        document.dispatchEvent(event);
    }
    
    /**
     * Get current theme information
     */
    getThemeInfo() {
        return {
            userPreference: this.getUserPreference(),
            systemPreference: this.getSystemPreference(),
            effectiveTheme: this.getEffectiveTheme(),
            supportsSystemPreference: !!(window.matchMedia)
        };
    }
    
    /**
     * Export theme settings for debugging
     */
    exportSettings() {
        return {
            ...this.getThemeInfo(),
            storageKey: this.STORAGE_KEY,
            themes: this.THEMES
        };
    }
}

// Initialize theme manager when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.themeManager = new ThemeManager();
    });
} else {
    window.themeManager = new ThemeManager();
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeManager;
}
