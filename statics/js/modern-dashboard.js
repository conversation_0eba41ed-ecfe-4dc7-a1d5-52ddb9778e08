/**
 * Modern Dashboard JavaScript
 * Advanced interactions and animations for RegulationGuru
 */

class ModernDashboard {
    constructor() {
        this.theme = localStorage.getItem('theme') || 'light';
        this.animations = new Map();
        this.observers = new Map();
        this.init();
    }

    init() {
        this.initTheme();
        this.initAnimations();
        this.initInteractions();
        this.initAccessibility();
        this.initPerformanceOptimizations();
        this.initAnalytics();
    }

    // Theme Management
    initTheme() {
        document.documentElement.setAttribute('data-theme', this.theme);
        
        // Theme toggle buttons
        const themeToggles = document.querySelectorAll('#themeToggle, #footerThemeToggle');
        themeToggles.forEach(toggle => {
            if (toggle) {
                toggle.addEventListener('click', () => this.toggleTheme());
                this.updateThemeIcon(toggle);
            }
        });

        // Listen for system theme changes
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        mediaQuery.addEventListener('change', (e) => {
            if (!localStorage.getItem('theme')) {
                this.setTheme(e.matches ? 'dark' : 'light');
            }
        });
    }

    toggleTheme() {
        const newTheme = this.theme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
    }

    setTheme(theme) {
        this.theme = theme;
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);
        
        // Update all theme toggle icons
        const themeToggles = document.querySelectorAll('#themeToggle, #footerThemeToggle');
        themeToggles.forEach(toggle => this.updateThemeIcon(toggle));
        
        // Trigger theme change event
        window.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme } }));
    }

    updateThemeIcon(toggle) {
        const icon = toggle.querySelector('i');
        if (icon) {
            icon.className = this.theme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }

    // Advanced Animations
    initAnimations() {
        this.initScrollAnimations();
        this.initHoverEffects();
        this.initCounterAnimations();
        this.initParallaxEffects();
    }

    initScrollAnimations() {
        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const scrollObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                    
                    // Trigger counter animation for stat cards
                    if (entry.target.classList.contains('stat-card')) {
                        this.animateCounter(entry.target);
                    }
                }
            });
        }, observerOptions);

        // Observe elements for scroll animations
        const animatedElements = document.querySelectorAll(
            '.animate-slide-up, .animate-fade-in, .stat-card, .feature-card'
        );
        
        animatedElements.forEach(el => {
            scrollObserver.observe(el);
        });

        this.observers.set('scroll', scrollObserver);
    }

    initHoverEffects() {
        // Enhanced hover effects for cards
        const cards = document.querySelectorAll('.card-advanced');
        cards.forEach(card => {
            card.addEventListener('mouseenter', (e) => {
                this.createRippleEffect(e);
            });
        });

        // Magnetic effect for buttons
        const buttons = document.querySelectorAll('.btn-advanced');
        buttons.forEach(button => {
            button.addEventListener('mousemove', (e) => {
                this.createMagneticEffect(e, button);
            });
            
            button.addEventListener('mouseleave', () => {
                button.style.transform = '';
            });
        });
    }

    createRippleEffect(event) {
        const card = event.currentTarget;
        const rect = card.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        const ripple = document.createElement('div');
        ripple.style.cssText = `
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            transform: scale(0);
            animation: ripple 0.6s linear;
            left: ${x - 10}px;
            top: ${y - 10}px;
            width: 20px;
            height: 20px;
            pointer-events: none;
            z-index: 1;
        `;

        card.style.position = 'relative';
        card.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    createMagneticEffect(event, element) {
        const rect = element.getBoundingClientRect();
        const x = event.clientX - rect.left - rect.width / 2;
        const y = event.clientY - rect.top - rect.height / 2;
        
        const distance = Math.sqrt(x * x + y * y);
        const maxDistance = Math.max(rect.width, rect.height);
        
        if (distance < maxDistance) {
            const strength = (maxDistance - distance) / maxDistance;
            const moveX = x * strength * 0.1;
            const moveY = y * strength * 0.1;
            
            element.style.transform = `translate(${moveX}px, ${moveY}px)`;
        }
    }

    animateCounter(statCard) {
        const numberElement = statCard.querySelector('.stat-number');
        if (!numberElement || numberElement.dataset.animated) return;

        const finalNumber = parseInt(numberElement.textContent.replace(/,/g, ''));
        const duration = 2000;
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Easing function
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentNumber = Math.floor(finalNumber * easeOutQuart);
            
            numberElement.textContent = currentNumber.toLocaleString();
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                numberElement.dataset.animated = 'true';
            }
        };

        requestAnimationFrame(animate);
    }

    initParallaxEffects() {
        const parallaxElements = document.querySelectorAll('.floating-card');
        
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            
            parallaxElements.forEach((element, index) => {
                const speed = 0.5 + (index * 0.1);
                element.style.transform = `translateY(${rate * speed}px)`;
            });
        });
    }

    // Interactive Features
    initInteractions() {
        this.initModalSystem();
        this.initTooltips();
        this.initKeyboardNavigation();
        this.initFormValidation();
    }

    initModalSystem() {
        // Contact modal functionality
        window.openContactModal = () => {
            const modal = document.getElementById('contactModal');
            if (modal) {
                modal.classList.add('show');
                modal.setAttribute('aria-hidden', 'false');
                
                // Focus first input
                const firstInput = modal.querySelector('input, textarea');
                if (firstInput) {
                    setTimeout(() => firstInput.focus(), 100);
                }
                
                // Prevent body scroll
                document.body.style.overflow = 'hidden';
            }
        };

        window.closeContactModal = () => {
            const modal = document.getElementById('contactModal');
            if (modal) {
                modal.classList.remove('show');
                modal.setAttribute('aria-hidden', 'true');
                document.body.style.overflow = '';
            }
        };

        window.submitContact = () => {
            const form = document.querySelector('.contact-form');
            const formData = new FormData(form);
            
            // Simulate form submission
            this.showNotification('Message sent successfully!', 'success');
            window.closeContactModal();
            form.reset();
        };

        // Close modal on overlay click
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                window.closeContactModal();
            }
        });

        // Close modal on Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                window.closeContactModal();
            }
        });
    }

    initTooltips() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.showTooltip(e.target, e.target.dataset.tooltip);
            });
            
            element.addEventListener('mouseleave', () => {
                this.hideTooltip();
            });
        });
    }

    showTooltip(element, text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = text;
        tooltip.style.cssText = `
            position: absolute;
            background: var(--bg-secondary);
            color: var(--text-primary);
            padding: var(--space-2) var(--space-3);
            border-radius: var(--radius-lg);
            font-size: var(--text-sm);
            box-shadow: var(--shadow-lg);
            z-index: var(--z-tooltip);
            pointer-events: none;
            opacity: 0;
            transition: var(--transition-opacity);
            border: 1px solid var(--border-primary);
        `;

        document.body.appendChild(tooltip);

        const rect = element.getBoundingClientRect();
        tooltip.style.left = `${rect.left + rect.width / 2 - tooltip.offsetWidth / 2}px`;
        tooltip.style.top = `${rect.top - tooltip.offsetHeight - 8}px`;

        requestAnimationFrame(() => {
            tooltip.style.opacity = '1';
        });

        this.currentTooltip = tooltip;
    }

    hideTooltip() {
        if (this.currentTooltip) {
            this.currentTooltip.style.opacity = '0';
            setTimeout(() => {
                if (this.currentTooltip && this.currentTooltip.parentNode) {
                    this.currentTooltip.parentNode.removeChild(this.currentTooltip);
                }
                this.currentTooltip = null;
            }, 150);
        }
    }

    initKeyboardNavigation() {
        // Enhanced keyboard navigation
        document.addEventListener('keydown', (e) => {
            // Tab navigation enhancement
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });

        document.addEventListener('mousedown', () => {
            document.body.classList.remove('keyboard-navigation');
        });
    }

    initFormValidation() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            const inputs = form.querySelectorAll('input, textarea');
            
            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    this.validateField(input);
                });
                
                input.addEventListener('input', () => {
                    if (input.classList.contains('error')) {
                        this.validateField(input);
                    }
                });
            });
        });
    }

    validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let errorMessage = '';

        if (field.hasAttribute('required') && !value) {
            isValid = false;
            errorMessage = 'This field is required';
        } else if (field.type === 'email' && value && !this.isValidEmail(value)) {
            isValid = false;
            errorMessage = 'Please enter a valid email address';
        }

        if (isValid) {
            field.classList.remove('error');
            this.removeFieldError(field);
        } else {
            field.classList.add('error');
            this.showFieldError(field, errorMessage);
        }

        return isValid;
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    showFieldError(field, message) {
        this.removeFieldError(field);
        
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error';
        errorElement.textContent = message;
        errorElement.style.cssText = `
            color: var(--error);
            font-size: var(--text-xs);
            margin-top: var(--space-1);
        `;
        
        field.parentNode.appendChild(errorElement);
    }

    removeFieldError(field) {
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
    }

    // Accessibility Enhancements
    initAccessibility() {
        // Announce theme changes to screen readers
        window.addEventListener('themeChanged', (e) => {
            this.announceToScreenReader(`Theme changed to ${e.detail.theme} mode`);
        });

        // Enhanced focus management
        this.initFocusManagement();
    }

    initFocusManagement() {
        // Trap focus in modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                const modal = document.querySelector('.modal-overlay.show');
                if (modal) {
                    this.trapFocus(e, modal);
                }
            }
        });
    }

    trapFocus(event, container) {
        const focusableElements = container.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (event.shiftKey && document.activeElement === firstElement) {
            event.preventDefault();
            lastElement.focus();
        } else if (!event.shiftKey && document.activeElement === lastElement) {
            event.preventDefault();
            firstElement.focus();
        }
    }

    announceToScreenReader(message) {
        const announcement = document.createElement('div');
        announcement.setAttribute('aria-live', 'polite');
        announcement.setAttribute('aria-atomic', 'true');
        announcement.style.cssText = `
            position: absolute;
            left: -10000px;
            width: 1px;
            height: 1px;
            overflow: hidden;
        `;
        
        document.body.appendChild(announcement);
        announcement.textContent = message;
        
        setTimeout(() => {
            document.body.removeChild(announcement);
        }, 1000);
    }

    // Performance Optimizations
    initPerformanceOptimizations() {
        // Lazy load images
        this.initLazyLoading();
        
        // Debounce scroll events
        this.initScrollOptimization();
        
        // Preload critical resources
        this.preloadResources();
    }

    initLazyLoading() {
        const images = document.querySelectorAll('img[data-src]');
        
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.removeAttribute('data-src');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    }

    initScrollOptimization() {
        let ticking = false;
        
        const optimizedScroll = () => {
            // Scroll-based animations and effects
            this.updateScrollProgress();
            ticking = false;
        };

        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(optimizedScroll);
                ticking = true;
            }
        });
    }

    updateScrollProgress() {
        const scrolled = window.pageYOffset;
        const maxScroll = document.documentElement.scrollHeight - window.innerHeight;
        const progress = (scrolled / maxScroll) * 100;
        
        // Update any scroll progress indicators
        const progressBars = document.querySelectorAll('.scroll-progress');
        progressBars.forEach(bar => {
            bar.style.width = `${progress}%`;
        });
    }

    preloadResources() {
        // Preload critical CSS and JS
        const criticalResources = [
            '/static/css/advanced-ui.css',
            '/static/js/enhanced-ui.js'
        ];

        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource;
            link.as = resource.endsWith('.css') ? 'style' : 'script';
            document.head.appendChild(link);
        });
    }

    // Analytics and Monitoring
    initAnalytics() {
        // Track user interactions
        this.trackInteractions();
        
        // Monitor performance
        this.monitorPerformance();
    }

    trackInteractions() {
        // Track button clicks
        document.addEventListener('click', (e) => {
            if (e.target.matches('.btn-advanced')) {
                this.logEvent('button_click', {
                    button_text: e.target.textContent.trim(),
                    button_type: e.target.className
                });
            }
        });

        // Track navigation
        document.addEventListener('click', (e) => {
            if (e.target.matches('.nav-link')) {
                this.logEvent('navigation', {
                    destination: e.target.href,
                    text: e.target.textContent.trim()
                });
            }
        });
    }

    monitorPerformance() {
        // Monitor Core Web Vitals
        if ('web-vital' in window) {
            // This would integrate with a real analytics service
            console.log('Performance monitoring initialized');
        }
    }

    logEvent(eventName, data) {
        // This would send to your analytics service
        console.log(`Event: ${eventName}`, data);
    }

    // Utility Functions
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--${type === 'success' ? 'success' : type === 'error' ? 'error' : 'info'});
            color: white;
            padding: var(--space-4) var(--space-6);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            z-index: var(--z-toast);
            transform: translateX(100%);
            transition: var(--transition-transform);
        `;

        document.body.appendChild(notification);

        requestAnimationFrame(() => {
            notification.style.transform = 'translateX(0)';
        });

        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    // Tutorial System
    showTutorial() {
        this.showNotification('Tutorial feature coming soon!', 'info');
    }

    // Cleanup
    destroy() {
        // Clean up observers and event listeners
        this.observers.forEach(observer => observer.disconnect());
        this.observers.clear();
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.modernDashboard = new ModernDashboard();
    
    // Make tutorial function globally available
    window.showTutorial = () => window.modernDashboard.showTutorial();
});

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .animate-in {
        animation: slideInUp 0.6s ease-out forwards;
    }
    
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .keyboard-navigation *:focus {
        outline: 2px solid var(--brand-primary) !important;
        outline-offset: 2px !important;
    }
    
    .form-input.error {
        border-color: var(--error) !important;
        box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1) !important;
    }
`;
document.head.appendChild(style);
