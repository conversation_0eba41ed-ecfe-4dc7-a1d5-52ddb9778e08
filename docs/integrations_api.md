
# External Integrations API

The External Integrations API provides endpoints for connecting RegulationGuru with third-party governance, risk, and compliance (GRC) tools through middleware services like SuperGlu.

## Overview

The integrations API enables bidirectional data synchronization between RegulationGuru and external systems. This allows for seamless workflow integration and data consistency across your regulatory compliance ecosystem.

## SuperGlu Integration

### Status Endpoint

```
GET /api/v1/integrations/superglu/status
```

Checks the connection status with the SuperGlu middleware service.

**Response Structure:**
```json
{
  "status": "connected",
  "last_sync": "2023-02-15T14:30:00Z",
  "connection_health": "good",
  "details": {
    "api_version": "1.2.3",
    "rate_limit_remaining": 950
  }
}
```

### Field Mappings

```
GET /api/v1/integrations/superglu/mappings
```

Retrieves the current field mappings between RegulationGuru and external systems.

**Response Structure:**
```json
{
  "mappings": [
    {
      "entity_type": "regulation",
      "regulator_guru_field": "title",
      "external_system": "ComplianceHub",
      "external_field": "regulation_name"
    },
    // Additional mapping entries...
  ]
}
```

### Entity Synchronization

```
POST /api/v1/integrations/superglu/sync/{entity_type}
```

Triggers synchronization of entities between RegulationGuru and external systems.

**Path Parameters:**
- `entity_type`: Type of entity to synchronize (regulation, alert, task, assessment)

**Request Body:**
```json
{
  "direction": "pull",
  "external_system": "ComplianceHub",
  "filters": {
    "modified_after": "2023-01-01T00:00:00Z"
  }
}
```

**Response Structure:**
```json
{
  "sync_id": "sync_12345",
  "status": "started",
  "estimated_completion": "2023-02-15T14:35:00Z",
  "entity_count": 157
}
```

### Webhook Receiver

```
POST /api/v1/integrations/superglu/webhook
```

Endpoint for receiving real-time notifications from external systems.

**Request Body:**
```json
{
  "event_type": "entity_updated",
  "timestamp": "2023-02-15T14:32:27Z",
  "entity_type": "regulation",
  "entity_id": "reg_12345",
  "changes": {
    "status": {
      "old": "draft",
      "new": "active"
    }
  }
}
```

**Response Structure:**
```json
{
  "received": true,
  "processing_id": "proc_67890",
  "status": "accepted"
}
```

## Security Considerations

All integration endpoints require proper authentication. API keys must be stored securely in the Secrets tool.

## Implementation Guidelines

When implementing integrations with new systems:

1. Create field mappings between RegulationGuru and the external system
2. Test connection with the status endpoint
3. Perform initial data synchronization
4. Configure webhooks for real-time updates
5. Verify data consistency across systems
