# Regulations CSV Schema Documentation

## Overview

This document describes the comprehensive schema for the `regulations_list.csv` file and its associated database models, API endpoints, and utilities. The schema follows the existing codebase patterns including soft-delete functionality and timestamp tracking.

## Database Schema

### RegulationCSVRecord Table

The main table storing regulation data from the CSV file with soft-delete support.

```sql
CREATE TABLE regulations_csv_records (
    -- Primary key
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Basic identification fields
    country_name VARCHAR(255) NOT NULL,
    country_code VARCHAR(3) NOT NULL,
    document_title TEXT NOT NULL,
    document_type VARCHAR(100) NOT NULL,
    issuing_authority VARCHAR(500) NOT NULL,
    
    -- Date fields
    publication_date DATE,
    effective_date DATE,
    
    -- Legal framework
    legal_status VARCHAR(50) NOT NULL,
    document_url TEXT,
    language VARCHAR(100) NOT NULL,
    
    -- Detailed provisions (stored as TEXT due to length)
    scope_application TEXT NOT NULL,
    key_compliance_requirements TEXT NOT NULL,
    enforcement_mechanisms TEXT NOT NULL,
    penalties TEXT NOT NULL,
    cross_border_elements TEXT NOT NULL,
    extraterritorial_reach TEXT NOT NULL,
    international_standards_alignment TEXT NOT NULL,
    data_protection_provisions TEXT NOT NULL,
    incident_reporting_requirements TEXT NOT NULL,
    risk_management_mandates TEXT NOT NULL,
    third_party_requirements TEXT NOT NULL,
    audit_obligations TEXT NOT NULL,
    certification_requirements TEXT NOT NULL,
    implementation_timeline TEXT NOT NULL,
    safe_harbor_provisions TEXT NOT NULL,
    industry_specific_provisions TEXT NOT NULL,
    technology_specific_provisions TEXT NOT NULL,
    
    -- Import tracking
    import_batch_id VARCHAR(100),
    data_quality_score FLOAT,
    validation_errors JSON,
    source_file_name VARCHAR(255),
    source_file_hash VARCHAR(64),
    
    -- Soft delete support
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    deleted_at TIMESTAMP,
    deleted_by_id UUID,
    
    -- Timestamp tracking
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    changed_on TIMESTAMP NOT NULL DEFAULT NOW()
);
```

### Supporting Tables

#### RegulationCSVImportLog
Tracks import operations for auditing and troubleshooting.

```sql
CREATE TABLE regulations_csv_import_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    batch_id VARCHAR(100) UNIQUE NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_hash VARCHAR(64),
    file_size FLOAT,
    total_records FLOAT NOT NULL DEFAULT 0,
    successful_imports FLOAT NOT NULL DEFAULT 0,
    failed_imports FLOAT NOT NULL DEFAULT 0,
    updated_records FLOAT NOT NULL DEFAULT 0,
    processing_time FLOAT,
    import_status VARCHAR(50) NOT NULL DEFAULT 'pending',
    error_summary JSON,
    warnings_summary JSON,
    imported_by_id UUID,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    changed_on TIMESTAMP NOT NULL DEFAULT NOW()
);
```

#### RegulationCSVExportLog
Tracks export operations for auditing.

```sql
CREATE TABLE regulations_csv_export_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    export_id VARCHAR(100) UNIQUE NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    filters_applied JSON,
    include_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    total_records_exported FLOAT NOT NULL DEFAULT 0,
    processing_time FLOAT,
    export_status VARCHAR(50) NOT NULL DEFAULT 'pending',
    file_path VARCHAR(500),
    exported_by_id UUID,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    changed_on TIMESTAMP NOT NULL DEFAULT NOW()
);
```

## CSV File Structure

The `regulations_list.csv` file contains 27 columns with comprehensive regulatory data:

### Column Definitions

| Column Name | Type | Required | Description |
|-------------|------|----------|-------------|
| Country_Name | String | Yes | Full country name |
| Country_Code | String(2-3) | Yes | ISO country code |
| Document_Title | Text | Yes | Official regulation title |
| Document_Type | Enum | Yes | Type of regulatory document |
| Issuing_Authority | String | Yes | Authority that issued the regulation |
| Publication_Date | Date | No | Date when regulation was published |
| Effective_Date | Date | No | Date when regulation becomes effective |
| Legal_Status | Enum | Yes | Legal binding status |
| Document_URL | URL | No | Link to official document |
| Language | String | Yes | Primary language(s) |
| Scope_Application | Text | Yes | Scope and covered entities |
| Key_Compliance_Requirements | Text | Yes | Key compliance requirements |
| Enforcement_Mechanisms | Text | Yes | Enforcement mechanisms |
| Penalties | Text | Yes | Penalty structure |
| Cross_Border_Elements | Text | Yes | Cross-border applicability |
| Data_Protection_Provisions | Text | Yes | Data protection provisions |
| Incident_Reporting_Requirements | Text | Yes | Incident reporting obligations |
| Risk_Management_Mandates | Text | Yes | Risk management requirements |
| Third_Party_Requirements | Text | Yes | Third-party requirements |
| Audit_Obligations | Text | Yes | Audit obligations |
| Certification_Requirements | Text | Yes | Certification requirements |
| Implementation_Timeline | Text | Yes | Implementation timeline |
| International_Standards_Alignment | Text | Yes | International standards alignment |
| Extraterritorial_Reach | Text | Yes | Extraterritorial application |
| Safe_Harbor_Provisions | Text | Yes | Safe harbor provisions |
| Industry_Specific_Provisions | Text | Yes | Industry-specific provisions |
| Technology_Specific_Provisions | Text | Yes | Technology-specific provisions |

### Enumerations

#### Document Types
- Federal Law
- State Law
- Provincial Law
- Local Law
- National Law
- EU Regulation
- EU Directive
- Federal Regulation
- Financial Regulation
- Financial Guidance
- Financial Standard
- Administrative Regulation
- Administrative Direction
- Voluntary Framework
- Pending Legislation
- Financial Law
- Financial Zone Law

#### Legal Status
- Binding
- Non-binding guidance
- Pending
- Draft
- Superseded

## API Endpoints

### Records Management

- `GET /api/v1/regulations-csv/records` - List records with filtering
- `GET /api/v1/regulations-csv/records/{id}` - Get specific record
- `POST /api/v1/regulations-csv/records` - Create new record
- `PUT /api/v1/regulations-csv/records/{id}` - Update record
- `DELETE /api/v1/regulations-csv/records/{id}` - Soft delete record
- `POST /api/v1/regulations-csv/records/{id}/restore` - Restore deleted record

### Import/Export Operations

- `POST /api/v1/regulations-csv/import` - Import CSV file
- `POST /api/v1/regulations-csv/export` - Export to CSV file
- `GET /api/v1/regulations-csv/import-logs` - Get import logs
- `GET /api/v1/regulations-csv/export-logs` - Get export logs

### Statistics

- `GET /api/v1/regulations-csv/stats` - Get data statistics

## Soft Delete Implementation

The schema implements soft delete functionality following the existing codebase patterns:

### Fields
- `is_deleted`: Boolean flag indicating deletion status
- `deleted_at`: Timestamp when record was deleted
- `deleted_by_id`: UUID of user who performed deletion

### Methods
- `soft_delete()`: Marks record as deleted
- Queries automatically filter out deleted records unless explicitly requested
- Restore functionality available through API

## Data Validation

### Pydantic Schemas
- `RegulationCSVRecordBase`: Base validation schema
- `RegulationCSVRecordCreate`: Creation schema
- `RegulationCSVRecordUpdate`: Update schema
- `RegulationCSVRecord`: Full schema with database fields

### Validation Rules
- Country code format validation (2-3 uppercase letters)
- URL format validation
- Date parsing and validation
- Date relationship validation (publication ≤ effective date)
- Required field validation

## Import/Export Features

### Import Capabilities
- Batch processing with unique batch IDs
- Duplicate detection and handling
- Comprehensive error reporting
- Progress tracking
- File hash verification
- Data quality scoring

### Export Capabilities
- Flexible filtering options
- Soft-delete inclusion control
- Background processing for large exports
- Export tracking and logging
- Multiple format support

## Usage Examples

### Import CSV File
```python
from app.utils.regulations_csv_utils import RegulationCSVProcessor

processor = RegulationCSVProcessor(db_session)
result = processor.import_csv_file(
    file_path="regulations_list.csv",
    batch_id="import_2025_01_28",
    user_id="user_123"
)
```

### Export with Filters
```python
from app.schemas.regulations_csv import RegulationCSVExportRequest

export_request = RegulationCSVExportRequest(
    filters={"country_code": "US", "legal_status": "Binding"},
    include_deleted=False
)

export_id = processor.export_csv_file(
    file_path="us_regulations.csv",
    export_request=export_request,
    user_id="user_123"
)
```

### Soft Delete Record
```python
record = db.query(RegulationCSVRecord).filter_by(id=record_id).first()
record.soft_delete()
record.deleted_by_id = user_id
db.commit()
```

## Migration

The database schema is managed through Alembic migrations:

```bash
# Apply the migration
alembic upgrade head

# Rollback if needed
alembic downgrade -1
```

Migration file: `migrations/alembic/versions/add_regulations_csv_tables.py`

## Testing

A comprehensive test suite is available:

```bash
python scripts/test_csv_import.py
```

Tests cover:
- CSV import functionality
- Soft delete operations
- Export functionality
- Database queries
- Error handling

## Performance Considerations

- Indexed fields for efficient querying
- Batch processing for large imports
- Pagination support for API endpoints
- Background processing for exports
- Connection pooling for database operations

## Security

- Input validation through Pydantic schemas
- SQL injection prevention through ORM
- File upload validation
- User tracking for audit trails
- Soft delete for data retention compliance
