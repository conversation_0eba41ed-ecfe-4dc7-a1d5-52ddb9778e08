
# Regulatory Alerts API

The Regulatory Alerts API enables access to notifications about new, updated, or upcoming regulatory changes across different jurisdictions and industries.

## Endpoints

### List Alerts

```
GET /api/v1/alerts
```

Retrieves a list of regulatory alerts with optional filtering.

**Query Parameters:**
- `country` (optional): Filter alerts by country code
- `industry` (optional): Filter alerts by industry sector
- `severity` (optional): Filter alerts by severity level (low, medium, high, critical)
- `since` (optional): Return alerts published after this date (ISO format)
- `limit` (optional): Maximum number of alerts to return (default: 50)
- `offset` (optional): Pagination offset (default: 0)

**Response Structure:**
```json
{
  "alerts": [
    {
      "id": "alert_12345",
      "title": "GDPR Enforcement Update",
      "summary": "New guidance on GDPR enforcement for cross-border data transfers",
      "country": "EU",
      "industry": "All",
      "severity": "medium",
      "published_date": "2023-01-15T10:30:00Z",
      "effective_date": "2023-03-01T00:00:00Z",
      "source_url": "https://example.com/gdpr-guidance",
      "regulator": "European Data Protection Board"
    },
    // Additional alert entries...
  ],
  "total_count": 156,
  "next_offset": 50
}
```

### Get Alert Details

```
GET /api/v1/alerts/{alert_id}
```

Retrieves detailed information about a specific alert.

**Path Parameters:**
- `alert_id`: Unique identifier of the alert

**Response Structure:**
```json
{
  "id": "alert_12345",
  "title": "GDPR Enforcement Update",
  "summary": "New guidance on GDPR enforcement for cross-border data transfers",
  "detailed_description": "The European Data Protection Board has published new guidance clarifying the enforcement approach for cross-border data transfers under GDPR...",
  "country": "EU",
  "industry": "All",
  "severity": "medium",
  "published_date": "2023-01-15T10:30:00Z",
  "effective_date": "2023-03-01T00:00:00Z",
  "source_url": "https://example.com/gdpr-guidance",
  "regulator": "European Data Protection Board",
  "related_regulations": [
    {
      "id": "reg_gdpr",
      "title": "General Data Protection Regulation",
      "reference": "2016/679"
    }
  ],
  "action_items": [
    "Review current cross-border data transfer mechanisms",
    "Update privacy impact assessments",
    "Review data processing agreements with processors"
  ]
}
```

### Subscribe to Alerts

```
POST /api/v1/alerts/subscriptions
```

Creates a new alert subscription based on specified criteria.

**Request Body:**
```json
{
  "name": "EU Data Protection Updates",
  "filters": {
    "countries": ["EU", "DE", "FR"],
    "industries": ["Technology", "Financial Services"],
    "keywords": ["GDPR", "data protection", "privacy"],
    "min_severity": "medium"
  },
  "delivery_method": "email",
  "delivery_config": {
    "email_address": "<EMAIL>",
    "frequency": "daily"
  }
}
```

**Response Structure:**
```json
{
  "subscription_id": "sub_67890",
  "name": "EU Data Protection Updates",
  "status": "active",
  "created_at": "2023-02-15T14:30:00Z",
  "estimated_alert_volume": "5-10 per week"
}
```

### Manage Alert Subscriptions

```
GET /api/v1/alerts/subscriptions
```

Retrieves all active alert subscriptions.

**Response Structure:**
```json
{
  "subscriptions": [
    {
      "subscription_id": "sub_67890",
      "name": "EU Data Protection Updates",
      "filters": {
        "countries": ["EU", "DE", "FR"],
        "industries": ["Technology", "Financial Services"],
        "keywords": ["GDPR", "data protection", "privacy"],
        "min_severity": "medium"
      },
      "delivery_method": "email",
      "status": "active",
      "created_at": "2023-02-15T14:30:00Z"
    },
    // Additional subscription entries...
  ]
}
```

## Example Usage

### Filtering Alerts by Country and Severity

```
GET /api/v1/alerts?country=US&severity=high&since=2023-01-01
```

This returns high-severity regulatory alerts for the United States published since January 1, 2023.

### Creating a Weekly Email Subscription

```
POST /api/v1/alerts/subscriptions
```

Request body:
```json
{
  "name": "Financial Services Privacy Updates",
  "filters": {
    "industries": ["Financial Services"],
    "keywords": ["privacy", "data protection", "consumer rights"]
  },
  "delivery_method": "email",
  "delivery_config": {
    "email_address": "<EMAIL>",
    "frequency": "weekly",
    "day_of_week": "Monday"
  }
}
```

## Integration with Other APIs

The Alerts API works in conjunction with:
- Benchmarks API to contextualize alerts within the regulatory landscape
- Compliance Calendar API to track deadlines associated with alerts
- Impact Assessment API to evaluate the potential impact of regulatory changes
