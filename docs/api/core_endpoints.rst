Core API Endpoints
==================

This section documents the core API endpoints that form the foundation of the RegulationGuru platform.

.. contents:: Table of Contents
   :local:
   :depth: 2

Countries API
-------------

Manage country information and regulatory jurisdictions.

List Countries
~~~~~~~~~~~~~~

.. http:get:: /api/v1/countries

   Retrieve a list of all countries with regulatory information.

   **Example Request:**

   .. code-block:: http

      GET /api/v1/countries HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>

   **Example Response:**

   .. code-block:: json

      {
        "data": [
          {
            "id": 1,
            "name": "United States",
            "code": "US",
            "iso_code": "USA",
            "region": "North America",
            "regulatory_complexity": 8,
            "total_regulations": 1250,
            "active_regulators": 15
          }
        ],
        "status": 200
      }

   :query string search: Search countries by name
   :query string region: Filter by region
   :query int min_complexity: Minimum regulatory complexity (1-10)
   :statuscode 200: Success
   :statuscode 401: Unauthorized

Get Country Details
~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/countries/(int:country_id)

   Retrieve detailed information about a specific country.

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "id": 1,
          "name": "United States",
          "code": "US",
          "iso_code": "USA",
          "region": "North America",
          "regulatory_complexity": 8,
          "total_regulations": 1250,
          "active_regulators": 15,
          "major_regulators": [
            {"id": 1, "name": "SEC", "focus": "Securities"},
            {"id": 2, "name": "FDA", "focus": "Food & Drug"}
          ],
          "regulation_categories": [
            {"id": 1, "name": "Financial Services", "count": 450},
            {"id": 2, "name": "Healthcare", "count": 320}
          ]
        },
        "status": 200
      }

   :param country_id: Country ID
   :type country_id: int
   :statuscode 200: Success
   :statuscode 404: Country not found

Regulators API
--------------

Manage regulatory authorities and their information.

List Regulators
~~~~~~~~~~~~~~~

.. http:get:: /api/v1/regulators

   Retrieve a list of regulatory authorities.

   **Example Request:**

   .. code-block:: http

      GET /api/v1/regulators?country=US&active=true HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>

   **Example Response:**

   .. code-block:: json

      {
        "data": [
          {
            "id": 1,
            "name": "Securities and Exchange Commission",
            "abbreviation": "SEC",
            "country_id": 1,
            "country_name": "United States",
            "website": "https://www.sec.gov",
            "focus_areas": ["Securities", "Investment"],
            "is_active": true,
            "total_regulations": 245
          }
        ],
        "pagination": {
          "total": 150,
          "page": 1,
          "per_page": 20
        },
        "status": 200
      }

   :query string country: Filter by country code
   :query boolean active: Filter by active status
   :query string focus: Filter by focus area
   :statuscode 200: Success

Create Regulator
~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/regulators

   Create a new regulatory authority.

   **Example Request:**

   .. code-block:: http

      POST /api/v1/regulators HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>
      Content-Type: application/json

      {
        "name": "Financial Conduct Authority",
        "abbreviation": "FCA",
        "country_id": 2,
        "website": "https://www.fca.org.uk",
        "focus_areas": ["Financial Services", "Consumer Protection"],
        "description": "UK financial services regulator"
      }

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "id": 25,
          "name": "Financial Conduct Authority",
          "abbreviation": "FCA",
          "country_id": 2,
          "website": "https://www.fca.org.uk",
          "focus_areas": ["Financial Services", "Consumer Protection"],
          "is_active": true,
          "created_at": "2023-12-01T10:00:00Z"
        },
        "status": 201
      }

   :statuscode 201: Created successfully
   :statuscode 400: Invalid input data
   :statuscode 409: Regulator already exists

Health Check API
----------------

System health and status monitoring.

System Health
~~~~~~~~~~~~~

.. http:get:: /api/v1/health

   Check the overall system health and status.

   **Example Response:**

   .. code-block:: json

      {
        "status": "healthy",
        "timestamp": "2023-12-01T10:00:00Z",
        "database": "healthy",
        "services": {
          "regulation_import": "healthy",
          "data_enrichment": "healthy",
          "analytics": "healthy"
        },
        "metrics": {
          "total_regulations": 15420,
          "active_countries": 195,
          "total_regulators": 850,
          "last_update": "2023-12-01T09:30:00Z"
        },
        "test_coverage": {
          "overall": 85.2,
          "api": 92.1,
          "services": 78.5
        }
      }

   :statuscode 200: System is healthy
   :statuscode 503: System is unhealthy

Detailed Health Check
~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/health/detailed

   Get detailed health information for all system components.

   **Example Response:**

   .. code-block:: json

      {
        "status": "healthy",
        "components": {
          "database": {
            "status": "healthy",
            "response_time": "2ms",
            "connections": {
              "active": 5,
              "max": 100
            }
          },
          "cache": {
            "status": "healthy",
            "hit_rate": 0.95,
            "memory_usage": "45%"
          },
          "external_apis": {
            "status": "degraded",
            "services": {
              "sec_edgar": "healthy",
              "eu_lex": "timeout"
            }
          }
        }
      }

   :statuscode 200: Health check completed
   :statuscode 503: Critical components unhealthy

Authentication API
------------------

User authentication and token management.

Obtain Token
~~~~~~~~~~~~

.. http:post:: /api/v1/auth/token

   Obtain a JWT access token for API authentication.

   **Example Request:**

   .. code-block:: http

      POST /api/v1/auth/token HTTP/1.1
      Host: api.regulationguru.com
      Content-Type: application/x-www-form-urlencoded

      username=<EMAIL>&password=secretpassword

   **Example Response:**

   .. code-block:: json

      {
        "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "token_type": "bearer",
        "expires_in": 3600,
        "scope": "read write"
      }

   :form username: User email or username
   :form password: User password
   :statuscode 200: Authentication successful
   :statuscode 401: Invalid credentials

Refresh Token
~~~~~~~~~~~~~

.. http:post:: /api/v1/auth/refresh

   Refresh an existing JWT token.

   **Example Request:**

   .. code-block:: http

      POST /api/v1/auth/refresh HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <current-token>

   **Example Response:**

   .. code-block:: json

      {
        "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "token_type": "bearer",
        "expires_in": 3600
      }

   :statuscode 200: Token refreshed successfully
   :statuscode 401: Invalid or expired token
