API Overview
============

The RegulationGuru API provides comprehensive access to regulatory compliance data and functionality. This RESTful API enables organizations to integrate regulatory compliance management into their existing systems and workflows.

.. contents:: Table of Contents
   :local:
   :depth: 2

Base URL
--------

All API endpoints are available under the base URL:

.. code-block:: text

   https://api.regulationguru.com/api/v1/

For local development:

.. code-block:: text

   http://localhost:8000/api/v1/

API Architecture
----------------

The RegulationGuru API follows RESTful principles and is organized into logical modules:

Core Modules
~~~~~~~~~~~~

* **Regulations**: Manage regulatory information and URLs
* **Compliance**: Track compliance requirements and assessments
* **Data Management**: Handle document import/export and data enrichment
* **Analytics**: Provide insights through trends, benchmarks, and impact analysis
* **Integrations**: Connect with external systems and data sources
* **Utilities**: Support functions like health checks and system status

Authentication
--------------

The API uses JWT (JSON Web Token) authentication for secure access:

.. code-block:: http

   Authorization: Bearer <your-jwt-token>

**Obtaining a Token**

.. code-block:: http

   POST /api/v1/auth/token
   Content-Type: application/json

   {
     "username": "your-username",
     "password": "your-password"
   }

**Token Response**

.. code-block:: json

   {
     "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
     "token_type": "bearer",
     "expires_in": 3600
   }

Rate Limiting
-------------

API requests are rate-limited to prevent abuse:

* **Standard Rate**: 100 requests per minute per API key
* **Burst Rate**: 1000 requests per hour per API key
* **Enterprise Rate**: Custom limits available

Rate limit headers are included in responses:

.. code-block:: http

   X-RateLimit-Limit: 100
   X-RateLimit-Remaining: 95
   X-RateLimit-Reset: **********

Response Format
---------------

All API responses follow a consistent JSON structure:

**Success Response**

.. code-block:: json

   {
     "data": {
       "id": 1,
       "name": "GDPR",
       "description": "General Data Protection Regulation"
     },
     "message": "Success",
     "status": 200,
     "metadata": {
       "timestamp": "2023-12-01T10:00:00Z",
       "version": "1.0.0"
     }
   }

**List Response with Pagination**

.. code-block:: json

   {
     "data": [
       {"id": 1, "name": "GDPR"},
       {"id": 2, "name": "CCPA"}
     ],
     "message": "Success",
     "status": 200,
     "pagination": {
       "total": 150,
       "page": 1,
       "per_page": 20,
       "pages": 8
     }
   }

Error Handling
--------------

Errors are returned with appropriate HTTP status codes and detailed error information:

**Client Error (4xx)**

.. code-block:: json

   {
     "error": {
       "code": "VALIDATION_ERROR",
       "message": "Invalid input data",
       "details": {
         "field": "email",
         "issue": "Invalid email format"
       }
     },
     "status": 400,
     "timestamp": "2023-12-01T10:00:00Z"
   }

**Server Error (5xx)**

.. code-block:: json

   {
     "error": {
       "code": "INTERNAL_ERROR",
       "message": "An unexpected error occurred",
       "request_id": "req_123456789"
     },
     "status": 500,
     "timestamp": "2023-12-01T10:00:00Z"
   }

**Common Error Codes**

.. list-table::
   :header-rows: 1
   :widths: 15 15 70

   * - HTTP Code
     - Error Code
     - Description
   * - 400
     - VALIDATION_ERROR
     - Request data validation failed
   * - 401
     - UNAUTHORIZED
     - Authentication required or invalid
   * - 403
     - FORBIDDEN
     - Insufficient permissions
   * - 404
     - NOT_FOUND
     - Resource not found
   * - 409
     - CONFLICT
     - Resource conflict (e.g., duplicate)
   * - 422
     - UNPROCESSABLE_ENTITY
     - Semantic validation error
   * - 429
     - RATE_LIMITED
     - Rate limit exceeded
   * - 500
     - INTERNAL_ERROR
     - Server error

Pagination
----------

List endpoints support pagination using query parameters:

.. code-block:: http

   GET /api/v1/regulations?page=2&per_page=50&sort=name&order=asc

**Parameters**

* ``page``: Page number (default: 1)
* ``per_page``: Items per page (default: 20, max: 100)
* ``sort``: Sort field (varies by endpoint)
* ``order``: Sort order (``asc`` or ``desc``)

Filtering and Search
--------------------

Most list endpoints support filtering and search:

.. code-block:: http

   GET /api/v1/regulations?country=US&status=active&search=data%20protection

**Common Filter Parameters**

* ``search``: Full-text search across relevant fields
* ``country``: Filter by country code
* ``status``: Filter by status (active, draft, archived)
* ``category``: Filter by regulation category
* ``date_from``, ``date_to``: Date range filtering

Data Formats
------------

**Date and Time**

All dates and times are in ISO 8601 format with UTC timezone:

.. code-block:: json

   {
     "created_at": "2023-12-01T10:00:00Z",
     "effective_date": "2023-12-01"
   }

**Currency**

Currency amounts include both value and currency code:

.. code-block:: json

   {
     "penalty": {
       "amount": 20000000,
       "currency": "EUR"
     }
   }

**Localization**

The API supports multiple languages through the ``Accept-Language`` header:

.. code-block:: http

   Accept-Language: en-US,en;q=0.9,es;q=0.8

Versioning
----------

The API uses URL-based versioning:

* **Current Version**: ``/api/v1/``
* **Future Versions**: ``/api/v2/``, etc.

Version-specific changes are documented in the changelog.

OpenAPI Specification
---------------------

The complete API specification is available in OpenAPI 3.0 format:

* **Interactive Documentation**: ``/api/v1/docs``
* **ReDoc Documentation**: ``/api/v1/redoc``
* **OpenAPI JSON**: ``/api/v1/openapi.json``

Available Endpoints
-------------------

The API provides comprehensive endpoints organized by functionality:

**Core Endpoints**

* ``/api/v1/regulations/`` - Regulation management
* ``/api/v1/compliance/`` - Compliance tracking
* ``/api/v1/countries/`` - Country information
* ``/api/v1/regulators/`` - Regulatory authorities

**Data Management**

* ``/api/v1/document_import/`` - Document import
* ``/api/v1/data_enrichment/`` - Data enrichment
* ``/api/v1/bulk_processor/`` - Bulk operations

**Analytics**

* ``/api/v1/trends/`` - Trend analysis
* ``/api/v1/benchmarks/`` - Benchmarking
* ``/api/v1/impact/`` - Impact assessment

**Integrations**

* ``/api/v1/integrations/`` - External integrations
* ``/api/v1/data-sources/`` - Data source management

**Utilities**

* ``/api/v1/health`` - System health
* ``/api/v1/calendar/`` - Compliance calendar

SDK and Libraries
-----------------

Official SDKs are available for popular programming languages:

* **Python**: ``pip install regulationguru-python``
* **JavaScript/Node.js**: ``npm install regulationguru-js``
* **Java**: Maven/Gradle packages available
* **C#/.NET**: NuGet package available

Support and Resources
---------------------

* **API Status**: ``/api/v1/health``
* **Documentation**: ``/docs/``
* **Support**: <EMAIL>
* **GitHub**: https://github.com/regulationguru/api
