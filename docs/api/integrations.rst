Integrations API
================

The Integrations API provides tools for connecting with external systems, managing data sources, and synchronizing regulatory information.

.. contents:: Table of Contents
   :local:
   :depth: 2

Integration Management
----------------------

Manage external system integrations and configurations.

List Integrations
~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/integrations

   Retrieve a list of configured integrations.

   **Example Response:**

   .. code-block:: json

      {
        "data": [
          {
            "id": 1,
            "name": "SEC EDGAR Integration",
            "integration_type": "api",
            "provider": "SEC",
            "endpoint_url": "https://www.sec.gov/Archives/edgar/",
            "sync_frequency": "daily",
            "is_active": true,
            "last_sync": "2023-12-01T08:00:00Z",
            "next_sync": "2023-12-02T08:00:00Z",
            "status": "healthy",
            "records_synced": 1250,
            "last_error": null
          }
        ],
        "status": 200
      }

   :query string integration_type: Filter by integration type
   :query string provider: Filter by provider
   :query boolean active_only: Show only active integrations
   :statuscode 200: Success

Create Integration
~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/integrations

   Create a new external integration.

   **Example Request:**

   .. code-block:: http

      POST /api/v1/integrations HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>
      Content-Type: application/json

      {
        "name": "EU Lex Integration",
        "integration_type": "api",
        "provider": "European Union",
        "endpoint_url": "https://eur-lex.europa.eu/",
        "authentication": {
          "type": "api_key",
          "credentials": {
            "api_key": "encrypted_key_here"
          }
        },
        "sync_frequency": "weekly",
        "configuration": {
          "data_types": ["regulations", "directives"],
          "languages": ["en", "fr", "de"],
          "date_range": "last_year"
        }
      }

   :statuscode 201: Created successfully
   :statuscode 400: Invalid configuration

Data Sources
------------

Manage external data sources for regulatory information.

List Data Sources
~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/integrations/data-sources

   Retrieve available data sources.

   **Example Response:**

   .. code-block:: json

      {
        "data": [
          {
            "id": 1,
            "name": "SEC EDGAR Database",
            "description": "US Securities and Exchange Commission EDGAR database",
            "source_type": "government_database",
            "country": "US",
            "regulator": "SEC",
            "data_types": ["regulations", "filings", "enforcement"],
            "update_frequency": "real_time",
            "reliability_score": 0.98,
            "coverage": {
              "start_date": "1994-01-01",
              "end_date": null,
              "total_documents": 25000000
            },
            "access_method": "api",
            "cost": "free",
            "is_available": true
          }
        ],
        "status": 200
      }

   :query string country: Filter by country
   :query string source_type: Filter by source type
   :query string data_type: Filter by data type
   :statuscode 200: Success

Sync Operations
---------------

Manage data synchronization operations.

Start Sync Operation
~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/integrations/(int:integration_id)/sync

   Start a data synchronization operation.

   **Example Request:**

   .. code-block:: http

      POST /api/v1/integrations/1/sync HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>
      Content-Type: application/json

      {
        "sync_type": "incremental",
        "date_range": {
          "start_date": "2023-11-01",
          "end_date": "2023-12-01"
        },
        "data_types": ["regulations", "amendments"],
        "priority": "high"
      }

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "sync_id": "sync_20231201_001",
          "integration_id": 1,
          "status": "processing",
          "sync_type": "incremental",
          "estimated_completion": "2023-12-01T10:30:00Z",
          "progress": {
            "total_records": 500,
            "processed_records": 0,
            "percentage": 0
          }
        },
        "status": 202
      }

   :param integration_id: Integration ID
   :statuscode 202: Sync started
   :statuscode 404: Integration not found

Get Sync Status
~~~~~~~~~~~~~~~

.. http:get:: /api/v1/integrations/sync/(str:sync_id)/status

   Check the status of a sync operation.

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "sync_id": "sync_20231201_001",
          "status": "completed",
          "progress": {
            "total_records": 500,
            "processed_records": 500,
            "percentage": 100
          },
          "results": {
            "records_created": 45,
            "records_updated": 123,
            "records_failed": 2,
            "records_skipped": 330
          },
          "timing": {
            "started_at": "2023-12-01T10:00:00Z",
            "completed_at": "2023-12-01T10:25:00Z",
            "duration": "25m"
          },
          "errors": [
            {
              "record_id": "reg_12345",
              "error": "Invalid date format",
              "details": "Effective date could not be parsed"
            }
          ]
        },
        "status": 200
      }

   :param sync_id: Sync operation ID
   :statuscode 200: Success
   :statuscode 404: Sync not found

Webhooks
--------

Manage webhook integrations for real-time updates.

List Webhooks
~~~~~~~~~~~~~

.. http:get:: /api/v1/integrations/webhooks

   Retrieve configured webhooks.

   **Example Response:**

   .. code-block:: json

      {
        "data": [
          {
            "id": 1,
            "name": "Compliance Alert Webhook",
            "url": "https://your-system.com/webhooks/compliance",
            "events": ["regulation_updated", "compliance_deadline"],
            "is_active": true,
            "secret": "webhook_secret_hash",
            "retry_policy": {
              "max_retries": 3,
              "retry_delay": 300
            },
            "last_triggered": "2023-12-01T09:30:00Z",
            "success_rate": 0.98
          }
        ],
        "status": 200
      }

   :statuscode 200: Success

Create Webhook
~~~~~~~~~~~~~~

.. http:post:: /api/v1/integrations/webhooks

   Create a new webhook integration.

   **Example Request:**

   .. code-block:: http

      POST /api/v1/integrations/webhooks HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>
      Content-Type: application/json

      {
        "name": "Regulation Update Notifications",
        "url": "https://your-system.com/webhooks/regulations",
        "events": [
          "regulation_created",
          "regulation_updated",
          "regulation_archived"
        ],
        "filters": {
          "countries": ["US", "EU"],
          "categories": ["data_protection"]
        },
        "secret": "your_webhook_secret",
        "retry_policy": {
          "max_retries": 5,
          "retry_delay": 600
        }
      }

   :statuscode 201: Created successfully
   :statuscode 400: Invalid webhook configuration

API Connectors
--------------

Manage API connections to external systems.

Test Connection
~~~~~~~~~~~~~~~

.. http:post:: /api/v1/integrations/(int:integration_id)/test

   Test the connection to an external system.

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "connection_status": "success",
          "response_time": 245,
          "api_version": "v2.1",
          "rate_limit": {
            "limit": 1000,
            "remaining": 987,
            "reset_time": "2023-12-01T11:00:00Z"
          },
          "authentication_status": "valid",
          "last_successful_request": "2023-12-01T10:00:00Z"
        },
        "status": 200
      }

   :param integration_id: Integration ID
   :statuscode 200: Test completed
   :statuscode 400: Test failed

Data Mapping
~~~~~~~~~~~~

.. http:get:: /api/v1/integrations/(int:integration_id)/mapping

   Get data field mapping configuration.

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "source_schema": {
            "title": "string",
            "description": "string",
            "effective_date": "date",
            "reference_number": "string"
          },
          "target_schema": {
            "title": "regulation.title",
            "description": "regulation.description",
            "effective_date": "regulation.effective_date",
            "reference_number": "regulation.reference_number"
          },
          "transformations": [
            {
              "field": "effective_date",
              "transformation": "date_format",
              "parameters": {
                "input_format": "MM/DD/YYYY",
                "output_format": "YYYY-MM-DD"
              }
            }
          ]
        },
        "status": 200
      }

   :param integration_id: Integration ID
   :statuscode 200: Success

External APIs
-------------

Interact with external regulatory APIs.

Query External API
~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/integrations/external-query

   Query an external regulatory API.

   **Example Request:**

   .. code-block:: http

      POST /api/v1/integrations/external-query HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>
      Content-Type: application/json

      {
        "provider": "sec_edgar",
        "query_type": "search",
        "parameters": {
          "keywords": "data protection",
          "date_range": {
            "start": "2023-01-01",
            "end": "2023-12-31"
          },
          "document_types": ["regulation", "guidance"]
        },
        "limit": 50
      }

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "query_id": "ext_query_20231201_001",
          "provider": "sec_edgar",
          "results": [
            {
              "external_id": "34-12345",
              "title": "Data Protection Guidelines",
              "url": "https://www.sec.gov/rules/final/34-12345.pdf",
              "publication_date": "2023-06-15",
              "relevance_score": 0.92
            }
          ],
          "total_results": 125,
          "query_time": "1.2s",
          "rate_limit_remaining": 995
        },
        "status": 200
      }

   :statuscode 200: Query successful
   :statuscode 400: Invalid query parameters
   :statuscode 429: Rate limit exceeded

Integration Monitoring
----------------------

Monitor integration health and performance.

Integration Health
~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/integrations/health

   Get health status of all integrations.

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "overall_status": "healthy",
          "total_integrations": 5,
          "healthy_integrations": 4,
          "degraded_integrations": 1,
          "failed_integrations": 0,
          "integrations": [
            {
              "id": 1,
              "name": "SEC EDGAR",
              "status": "healthy",
              "last_check": "2023-12-01T10:00:00Z",
              "response_time": 245,
              "success_rate": 0.98
            }
          ]
        },
        "status": 200
      }

   :statuscode 200: Success
