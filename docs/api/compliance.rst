Compliance API
==============

The Compliance API provides comprehensive tools for managing compliance requirements, assessments, and tracking compliance status across regulations.

.. contents:: Table of Contents
   :local:
   :depth: 2

Compliance Requirements
-----------------------

Manage compliance requirements associated with regulations.

List Compliance Requirements
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/compliance/requirements

   Retrieve a list of compliance requirements with filtering options.

   **Example Request:**

   .. code-block:: http

      GET /api/v1/compliance/requirements?regulation_id=1&priority=high HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>

   **Example Response:**

   .. code-block:: json

      {
        "data": [
          {
            "id": 1,
            "title": "Data Subject Access Request Response",
            "description": "Organizations must respond to data subject access requests within 30 days",
            "requirement_type": "procedural",
            "priority": "high",
            "deadline": "2024-01-01",
            "frequency": "as_needed",
            "penalty_description": "Fines up to €20 million or 4% of annual turnover",
            "penalty_amount": 20000000,
            "penalty_currency": "EUR",
            "is_mandatory": true,
            "regulation": {
              "id": 1,
              "title": "General Data Protection Regulation"
            },
            "compliance_status": "not_assessed",
            "last_assessment_date": null,
            "next_review_date": "2024-03-01"
          }
        ],
        "pagination": {
          "total": 45,
          "page": 1,
          "per_page": 20
        },
        "status": 200
      }

   :query int regulation_id: Filter by regulation ID
   :query string priority: Filter by priority (low, medium, high, critical)
   :query string requirement_type: Filter by requirement type
   :query string compliance_status: Filter by compliance status
   :query boolean mandatory_only: Show only mandatory requirements
   :statuscode 200: Success

Create Compliance Requirement
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/compliance/requirements

   Create a new compliance requirement.

   **Example Request:**

   .. code-block:: http

      POST /api/v1/compliance/requirements HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>
      Content-Type: application/json

      {
        "title": "Annual Privacy Impact Assessment",
        "description": "Conduct annual privacy impact assessment for high-risk processing",
        "requirement_type": "audit",
        "priority": "high",
        "deadline": "2024-12-31",
        "frequency": "annually",
        "regulation_id": 1,
        "category_ids": [1, 2],
        "industry_ids": [1, 3]
      }

   :statuscode 201: Created successfully
   :statuscode 400: Invalid input data

Compliance Assessments
----------------------

Manage compliance assessments and evaluations.

List Assessments
~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/compliance/assessments

   Retrieve a list of compliance assessments.

   **Example Response:**

   .. code-block:: json

      {
        "data": [
          {
            "id": 1,
            "assessment_name": "Q4 2023 GDPR Compliance Review",
            "assessment_type": "internal_audit",
            "scope": "Data processing activities for EU customers",
            "methodology": "ISO 27001 based audit framework",
            "assessment_date": "2023-12-01",
            "assessor_name": "Internal Audit Team",
            "overall_status": "partially_compliant",
            "compliance_score": 85.5,
            "findings_summary": "Minor gaps in documentation and training",
            "requirements": [
              {
                "id": 1,
                "title": "Data Subject Access Request Response"
              }
            ],
            "action_items_count": 3,
            "next_assessment_date": "2024-03-01"
          }
        ],
        "status": 200
      }

   :query string assessment_type: Filter by assessment type
   :query string status: Filter by overall status
   :query string date_from: Filter by assessment date
   :statuscode 200: Success

Create Assessment
~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/compliance/assessments

   Create a new compliance assessment.

   **Example Request:**

   .. code-block:: http

      POST /api/v1/compliance/assessments HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>
      Content-Type: application/json

      {
        "assessment_name": "Q1 2024 CCPA Compliance Review",
        "assessment_type": "self_assessment",
        "scope": "California customer data processing",
        "assessment_date": "2024-01-15",
        "assessor_name": "Privacy Team",
        "requirement_ids": [1, 2, 3, 4],
        "overall_status": "compliant",
        "compliance_score": 92.0
      }

   :statuscode 201: Created successfully
   :statuscode 400: Invalid input data

Assessment Findings
~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/compliance/assessments/(int:assessment_id)/findings

   Get detailed findings for a specific assessment.

   **Example Response:**

   .. code-block:: json

      {
        "data": [
          {
            "requirement_id": 1,
            "finding_type": "non_compliant",
            "severity": "medium",
            "description": "Data retention policy not clearly documented",
            "evidence": "No formal policy document found",
            "recommendation": "Create and publish data retention policy",
            "due_date": "2024-02-01",
            "assigned_to": "privacy_team"
          }
        ],
        "status": 200
      }

   :param assessment_id: Assessment ID
   :statuscode 200: Success
   :statuscode 404: Assessment not found

Compliance Calendar
-------------------

Manage compliance deadlines and scheduled activities.

List Calendar Events
~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/compliance/calendar

   Retrieve compliance calendar events.

   **Example Request:**

   .. code-block:: http

      GET /api/v1/compliance/calendar?start_date=2024-01-01&end_date=2024-12-31 HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>

   **Example Response:**

   .. code-block:: json

      {
        "data": [
          {
            "id": 1,
            "title": "GDPR Annual Review Due",
            "description": "Annual review of GDPR compliance status",
            "event_type": "review",
            "start_date": "2024-05-25",
            "end_date": null,
            "is_deadline": true,
            "reminder_days": [30, 7, 1],
            "requirement_id": 1,
            "regulation": {
              "id": 1,
              "title": "General Data Protection Regulation"
            }
          }
        ],
        "status": 200
      }

   :query string start_date: Start date for calendar range
   :query string end_date: End date for calendar range
   :query string event_type: Filter by event type
   :query boolean deadlines_only: Show only deadline events
   :statuscode 200: Success

Create Calendar Event
~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/compliance/calendar

   Create a new compliance calendar event.

   **Example Request:**

   .. code-block:: http

      POST /api/v1/compliance/calendar HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>
      Content-Type: application/json

      {
        "title": "Privacy Training Session",
        "description": "Quarterly privacy training for all employees",
        "event_type": "training",
        "start_date": "2024-04-15",
        "end_date": "2024-04-15",
        "is_deadline": false,
        "reminder_days": [14, 7, 1],
        "requirement_id": 5
      }

   :statuscode 201: Created successfully
   :statuscode 400: Invalid input data

Compliance Dashboard
--------------------

Get compliance overview and metrics.

Compliance Overview
~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/compliance/dashboard

   Get comprehensive compliance dashboard data.

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "summary": {
            "total_requirements": 156,
            "compliant": 98,
            "non_compliant": 23,
            "partially_compliant": 35,
            "not_assessed": 0,
            "compliance_percentage": 62.8
          },
          "by_regulation": [
            {
              "regulation_id": 1,
              "regulation_name": "GDPR",
              "total_requirements": 45,
              "compliant": 30,
              "compliance_percentage": 66.7
            }
          ],
          "by_priority": {
            "critical": {"total": 12, "compliant": 8},
            "high": {"total": 34, "compliant": 22},
            "medium": {"total": 67, "compliant": 45},
            "low": {"total": 43, "compliant": 23}
          },
          "upcoming_deadlines": [
            {
              "requirement_id": 1,
              "title": "Annual Privacy Review",
              "deadline": "2024-01-15",
              "days_remaining": 14
            }
          ],
          "recent_assessments": [
            {
              "id": 1,
              "assessment_name": "Q4 2023 GDPR Review",
              "assessment_date": "2023-12-01",
              "overall_status": "partially_compliant"
            }
          ]
        },
        "status": 200
      }

   :statuscode 200: Success

Compliance Reports
------------------

Generate compliance reports and analytics.

Generate Compliance Report
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/compliance/reports

   Generate a comprehensive compliance report.

   **Example Request:**

   .. code-block:: http

      POST /api/v1/compliance/reports HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>
      Content-Type: application/json

      {
        "report_type": "compliance_status",
        "regulation_ids": [1, 2],
        "date_range": {
          "start_date": "2023-01-01",
          "end_date": "2023-12-31"
        },
        "include_sections": [
          "executive_summary",
          "detailed_findings",
          "action_items",
          "recommendations"
        ],
        "format": "pdf"
      }

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "report_id": "report_20231201_001",
          "status": "processing",
          "estimated_completion": "2023-12-01T10:05:00Z",
          "download_url": null
        },
        "status": 202
      }

   :statuscode 202: Report generation started
   :statuscode 400: Invalid report parameters

Download Report
~~~~~~~~~~~~~~~

.. http:get:: /api/v1/compliance/reports/(str:report_id)/download

   Download a generated compliance report.

   :param report_id: Report ID
   :statuscode 200: File download
   :statuscode 404: Report not found
   :statuscode 202: Report still processing
