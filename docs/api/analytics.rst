Analytics API
=============

The Analytics API provides comprehensive insights and reporting capabilities for regulatory data, compliance trends, and system metrics.

.. contents:: Table of Contents
   :local:
   :depth: 2

Regulation Analytics
--------------------

Analyze regulation data and trends.

Regulation Trends
~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/analytics/regulations/trends

   Get regulation trends over time.

   **Example Request:**

   .. code-block:: http

      GET /api/v1/analytics/regulations/trends?period=monthly&start_date=2023-01-01&end_date=2023-12-31 HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "trends": [
            {
              "period": "2023-01",
              "new_regulations": 15,
              "updated_regulations": 8,
              "archived_regulations": 2,
              "total_active": 1245
            },
            {
              "period": "2023-02",
              "new_regulations": 12,
              "updated_regulations": 6,
              "archived_regulations": 1,
              "total_active": 1256
            }
          ],
          "summary": {
            "total_new": 156,
            "total_updated": 89,
            "total_archived": 23,
            "net_growth": 133,
            "growth_rate": 0.12
          }
        },
        "metadata": {
          "execution_time": 0.125,
          "data_freshness": "2023-12-01T08:00:00Z"
        },
        "status": 200
      }

   :query string period: Time period (daily, weekly, monthly, quarterly)
   :query string start_date: Start date for analysis
   :query string end_date: End date for analysis
   :query string country: Filter by country code
   :query string category: Filter by category
   :statuscode 200: Success

Regulation Distribution
~~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/analytics/regulations/distribution

   Get regulation distribution by various dimensions.

   **Example Request:**

   .. code-block:: http

      GET /api/v1/analytics/regulations/distribution?dimension=country&top=10 HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "distribution": [
            {
              "dimension_value": "United States",
              "dimension_code": "US",
              "count": 1250,
              "percentage": 28.5,
              "active_count": 1100,
              "recent_updates": 45
            },
            {
              "dimension_value": "European Union",
              "dimension_code": "EU",
              "count": 890,
              "percentage": 20.3,
              "active_count": 820,
              "recent_updates": 32
            }
          ],
          "total_regulations": 4385,
          "dimensions_covered": 195
        },
        "status": 200
      }

   :query string dimension: Distribution dimension (country, category, industry, regulator)
   :query int top: Number of top results to return (default: 10)
   :query boolean include_inactive: Include inactive regulations
   :statuscode 200: Success

Compliance Analytics
--------------------

Analyze compliance data and performance.

Compliance Metrics
~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/analytics/compliance/metrics

   Get comprehensive compliance metrics.

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "overall_metrics": {
            "total_requirements": 2456,
            "assessed_requirements": 2100,
            "compliant_requirements": 1680,
            "compliance_rate": 80.0,
            "assessment_coverage": 85.5
          },
          "by_regulation": [
            {
              "regulation_id": 1,
              "regulation_name": "GDPR",
              "total_requirements": 45,
              "compliant": 36,
              "compliance_rate": 80.0,
              "last_assessment": "2023-11-15"
            }
          ],
          "by_priority": {
            "critical": {"compliance_rate": 95.0, "total": 120},
            "high": {"compliance_rate": 85.0, "total": 340},
            "medium": {"compliance_rate": 78.0, "total": 890},
            "low": {"compliance_rate": 72.0, "total": 650}
          },
          "trends": {
            "monthly_improvement": 2.5,
            "quarterly_improvement": 8.2,
            "yearly_improvement": 15.7
          }
        },
        "status": 200
      }

   :query string regulation_id: Filter by specific regulation
   :query string industry: Filter by industry
   :query string date_from: Start date for metrics
   :statuscode 200: Success

Compliance Benchmarks
~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/analytics/compliance/benchmarks

   Get compliance benchmarks and industry comparisons.

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "industry_benchmarks": [
            {
              "industry": "Financial Services",
              "average_compliance_rate": 87.5,
              "top_quartile": 95.0,
              "median": 85.0,
              "bottom_quartile": 78.0,
              "your_position": "top_quartile",
              "your_score": 92.0
            }
          ],
          "regulation_benchmarks": [
            {
              "regulation": "GDPR",
              "industry_average": 82.0,
              "global_average": 78.5,
              "your_score": 85.0,
              "percentile": 75
            }
          ],
          "improvement_opportunities": [
            {
              "area": "Data Retention Policies",
              "current_score": 65.0,
              "industry_average": 82.0,
              "potential_improvement": 17.0
            }
          ]
        },
        "status": 200
      }

   :query string industry: Industry for benchmarking
   :query string regulation: Specific regulation for benchmarking
   :statuscode 200: Success

Risk Analytics
--------------

Analyze regulatory risks and impact.

Risk Assessment
~~~~~~~~~~~~~~~

.. http:get:: /api/v1/analytics/risk/assessment

   Get comprehensive risk assessment data.

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "overall_risk_score": 6.5,
          "risk_level": "medium",
          "risk_factors": [
            {
              "factor": "Non-compliance with GDPR",
              "impact": "high",
              "probability": "medium",
              "risk_score": 8.0,
              "mitigation_status": "in_progress"
            }
          ],
          "regulatory_changes": [
            {
              "regulation": "CCPA Amendment",
              "effective_date": "2024-01-01",
              "impact_assessment": "medium",
              "preparation_status": "not_started"
            }
          ],
          "compliance_gaps": [
            {
              "requirement": "Data Subject Access Requests",
              "gap_severity": "high",
              "estimated_penalty": 2000000,
              "currency": "EUR"
            }
          ]
        },
        "status": 200
      }

   :statuscode 200: Success

Impact Analysis
~~~~~~~~~~~~~~~

.. http:post:: /api/v1/analytics/impact/analyze

   Analyze the impact of regulatory changes.

   **Example Request:**

   .. code-block:: http

      POST /api/v1/analytics/impact/analyze HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>
      Content-Type: application/json

      {
        "regulation_id": 1,
        "change_type": "amendment",
        "effective_date": "2024-06-01",
        "affected_areas": ["data_processing", "consent_management"],
        "organization_profile": {
          "industry": "technology",
          "size": "large",
          "geographic_scope": ["US", "EU"]
        }
      }

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "analysis_id": "impact_20231201_001",
          "overall_impact": "high",
          "impact_score": 8.5,
          "affected_requirements": 12,
          "estimated_compliance_cost": 150000,
          "implementation_timeline": "6-9 months",
          "key_impacts": [
            {
              "area": "Data Processing Procedures",
              "impact_level": "high",
              "description": "Significant changes to consent management required",
              "estimated_effort": "3-4 months"
            }
          ],
          "recommendations": [
            {
              "priority": "high",
              "action": "Update privacy policies",
              "timeline": "30 days"
            }
          ]
        },
        "status": 200
      }

   :statuscode 200: Analysis completed
   :statuscode 202: Analysis in progress

Custom Analytics
----------------

Create and execute custom analytics queries.

Execute Custom Query
~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/analytics/custom/query

   Execute a custom analytics query.

   **Example Request:**

   .. code-block:: http

      POST /api/v1/analytics/custom/query HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>
      Content-Type: application/json

      {
        "query_name": "Quarterly Compliance Review",
        "metrics": ["compliance_rate", "assessment_count"],
        "dimensions": ["regulation", "quarter"],
        "filters": {
          "regulation_category": "data_protection",
          "assessment_date": {
            "from": "2023-01-01",
            "to": "2023-12-31"
          }
        },
        "aggregation": "average",
        "limit": 100
      }

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "query_id": "query_20231201_001",
          "results": [
            {
              "regulation": "GDPR",
              "quarter": "Q1 2023",
              "compliance_rate": 82.5,
              "assessment_count": 15
            }
          ],
          "metadata": {
            "execution_time": 0.245,
            "total_records": 45,
            "data_freshness": "2023-12-01T08:00:00Z"
          }
        },
        "status": 200
      }

   :statuscode 200: Query executed successfully
   :statuscode 400: Invalid query parameters

Export Analytics Data
~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/analytics/export

   Export analytics data in various formats.

   **Example Request:**

   .. code-block:: http

      POST /api/v1/analytics/export HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>
      Content-Type: application/json

      {
        "export_type": "compliance_dashboard",
        "format": "excel",
        "date_range": {
          "start_date": "2023-01-01",
          "end_date": "2023-12-31"
        },
        "include_charts": true,
        "filters": {
          "regulation_ids": [1, 2, 3]
        }
      }

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "export_id": "export_analytics_20231201_001",
          "status": "processing",
          "estimated_completion": "2023-12-01T10:03:00Z",
          "download_url": null
        },
        "status": 202
      }

   :statuscode 202: Export started
   :statuscode 400: Invalid export parameters

System Analytics
----------------

Monitor system performance and usage.

System Metrics
~~~~~~~~~~~~~~

.. http:get:: /api/v1/analytics/system/metrics

   Get system performance and usage metrics.

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "performance": {
            "average_response_time": 125,
            "api_requests_per_minute": 450,
            "error_rate": 0.02,
            "uptime_percentage": 99.95
          },
          "usage": {
            "active_users": 1250,
            "total_api_calls": 2500000,
            "data_storage_gb": 125.5,
            "bandwidth_usage_gb": 45.2
          },
          "features": {
            "most_used_endpoints": [
              {"endpoint": "/api/v1/regulations", "usage_count": 125000},
              {"endpoint": "/api/v1/compliance/requirements", "usage_count": 89000}
            ],
            "search_queries": 45000,
            "report_generations": 1200
          }
        },
        "status": 200
      }

   :statuscode 200: Success
