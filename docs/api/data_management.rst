Data Management API
===================

The Data Management API provides comprehensive tools for importing, exporting, and managing regulatory data within the RegulationGuru platform.

.. contents:: Table of Contents
   :local:
   :depth: 2

Local Regulation Import
-----------------------

Import regulations from CSV files with comprehensive validation and processing.

Import Regulations from CSV
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/local-regulation-import

   Import regulations from a CSV file with validation and processing.

   **Example Request:**

   .. code-block:: http

      POST /api/v1/local-regulation-import HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>
      Content-Type: multipart/form-data

      file: regulations.csv
      user_id: user_123
      validate_only: false
      batch_size: 100

   **CSV Format:**

   The CSV file should contain the following columns:

   .. code-block:: text

      title,description,reference_number,status,effective_date,jurisdiction,source_url,regulator_name,country_code,categories,industries,tags
      "GDPR","General Data Protection Regulation","2016/679","active","2018-05-25","European Union","https://eur-lex.europa.eu/...","European Commission","EU","Data Protection;Privacy","Technology;Financial Services","GDPR;Privacy"

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "import_id": "import_20231201_001",
          "status": "processing",
          "file_info": {
            "filename": "regulations.csv",
            "size": 1048576,
            "rows": 150,
            "columns": 12
          },
          "validation_summary": {
            "total_rows": 150,
            "valid_rows": 145,
            "invalid_rows": 5,
            "warnings": 12
          },
          "processing_info": {
            "batch_size": 100,
            "estimated_duration": "5-10 minutes",
            "started_at": "2023-12-01T10:00:00Z"
          }
        },
        "status": 202
      }

   :form file: CSV file containing regulation data
   :form user_id: ID of the user performing the import
   :form validate_only: If true, only validate without importing (default: false)
   :form batch_size: Number of records to process per batch (default: 100)
   :form skip_duplicates: Skip duplicate regulations (default: true)
   :form update_existing: Update existing regulations if found (default: false)
   :statuscode 202: Import started successfully
   :statuscode 400: Invalid file or parameters
   :statuscode 413: File too large
   :statuscode 422: Validation errors

Get Import Status
~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/local-regulation-import/(str:import_id)/status

   Check the status of an ongoing import operation.

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "import_id": "import_20231201_001",
          "status": "completed",
          "progress": {
            "total_rows": 150,
            "processed_rows": 150,
            "successful_imports": 145,
            "failed_imports": 5,
            "skipped_duplicates": 8,
            "percentage_complete": 100
          },
          "timing": {
            "started_at": "2023-12-01T10:00:00Z",
            "completed_at": "2023-12-01T10:07:30Z",
            "duration": "7m30s"
          },
          "results": {
            "created_regulations": 137,
            "updated_regulations": 8,
            "failed_regulations": 5,
            "created_regulators": 3,
            "created_categories": 2
          },
          "errors": [
            {
              "row": 15,
              "error": "Invalid country code: 'XX'",
              "data": {"title": "Invalid Regulation", "country_code": "XX"}
            }
          ]
        },
        "status": 200
      }

   :param import_id: Import operation ID
   :statuscode 200: Status retrieved successfully
   :statuscode 404: Import not found

List Import History
~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/local-regulation-import/history

   Retrieve a list of past import operations.

   **Example Response:**

   .. code-block:: json

      {
        "data": [
          {
            "import_id": "import_20231201_001",
            "filename": "regulations.csv",
            "status": "completed",
            "user_id": "user_123",
            "started_at": "2023-12-01T10:00:00Z",
            "completed_at": "2023-12-01T10:07:30Z",
            "total_rows": 150,
            "successful_imports": 145,
            "failed_imports": 5
          }
        ],
        "pagination": {
          "total": 25,
          "page": 1,
          "per_page": 20
        },
        "status": 200
      }

   :query string user_id: Filter by user ID
   :query string status: Filter by import status
   :query string date_from: Filter by start date
   :query string date_to: Filter by end date
   :statuscode 200: Success

Data Export
-----------

Export regulatory data in various formats.

Export Regulations
~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/data-export/regulations

   Export regulations to CSV, JSON, or Excel format.

   **Example Request:**

   .. code-block:: http

      POST /api/v1/data-export/regulations HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>
      Content-Type: application/json

      {
        "format": "csv",
        "filters": {
          "country_codes": ["US", "EU"],
          "status": "active",
          "date_from": "2020-01-01",
          "categories": ["Data Protection", "Financial Services"]
        },
        "fields": [
          "title", "description", "reference_number", 
          "effective_date", "regulator_name", "country_name"
        ],
        "include_metadata": true
      }

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "export_id": "export_20231201_001",
          "status": "processing",
          "format": "csv",
          "estimated_records": 1250,
          "estimated_size": "2.5MB",
          "download_url": null,
          "expires_at": "2023-12-08T10:00:00Z"
        },
        "status": 202
      }

   :statuscode 202: Export started successfully
   :statuscode 400: Invalid export parameters

Download Export
~~~~~~~~~~~~~~~

.. http:get:: /api/v1/data-export/(str:export_id)/download

   Download the completed export file.

   **Example Response:**

   .. code-block:: http

      HTTP/1.1 200 OK
      Content-Type: application/octet-stream
      Content-Disposition: attachment; filename="regulations_export_20231201.csv"
      Content-Length: 2621440

      [Binary file content]

   :param export_id: Export operation ID
   :statuscode 200: File download
   :statuscode 404: Export not found
   :statuscode 410: Export expired

Data Enrichment
---------------

Enhance regulation data with additional information and validation.

Enrich Regulation Data
~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/data-enrichment/regulations/(int:regulation_id)

   Enrich a regulation with additional data from external sources.

   **Example Request:**

   .. code-block:: http

      POST /api/v1/data-enrichment/regulations/1 HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>
      Content-Type: application/json

      {
        "sources": ["official_website", "legal_database", "ai_analysis"],
        "fields": ["summary", "compliance_requirements", "related_regulations"],
        "confidence_threshold": 0.8
      }

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "enrichment_id": "enrich_20231201_001",
          "regulation_id": 1,
          "status": "processing",
          "sources_requested": ["official_website", "legal_database", "ai_analysis"],
          "estimated_completion": "2023-12-01T10:05:00Z"
        },
        "status": 202
      }

   :param regulation_id: Regulation ID to enrich
   :statuscode 202: Enrichment started
   :statuscode 404: Regulation not found

Bulk Operations
---------------

Perform operations on multiple regulations simultaneously.

Bulk Update Regulations
~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/bulk-operations/regulations/update

   Update multiple regulations with the same changes.

   **Example Request:**

   .. code-block:: http

      POST /api/v1/bulk-operations/regulations/update HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>
      Content-Type: application/json

      {
        "regulation_ids": [1, 2, 3, 4, 5],
        "updates": {
          "status": "archived",
          "is_verified": true,
          "verified_by": "admin_user"
        },
        "reason": "Bulk archival of outdated regulations"
      }

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "operation_id": "bulk_20231201_001",
          "status": "processing",
          "total_regulations": 5,
          "estimated_completion": "2023-12-01T10:02:00Z"
        },
        "status": 202
      }

   :statuscode 202: Bulk operation started
   :statuscode 400: Invalid update parameters

Bulk Delete Regulations
~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/bulk-operations/regulations/delete

   Soft delete multiple regulations.

   **Example Request:**

   .. code-block:: http

      POST /api/v1/bulk-operations/regulations/delete HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>
      Content-Type: application/json

      {
        "regulation_ids": [10, 11, 12],
        "reason": "Duplicate regulations identified",
        "permanent": false
      }

   :statuscode 202: Bulk deletion started
   :statuscode 403: Insufficient permissions

Data Validation
---------------

Validate regulation data for consistency and accuracy.

Validate Regulation Data
~~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/data-validation/regulations

   Validate regulation data against business rules and external sources.

   **Example Request:**

   .. code-block:: http

      POST /api/v1/data-validation/regulations HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>
      Content-Type: application/json

      {
        "regulation_ids": [1, 2, 3],
        "validation_rules": [
          "url_accessibility",
          "date_consistency",
          "regulator_verification",
          "duplicate_detection"
        ],
        "fix_issues": false
      }

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "validation_id": "valid_20231201_001",
          "status": "processing",
          "regulations_count": 3,
          "rules_count": 4,
          "estimated_completion": "2023-12-01T10:03:00Z"
        },
        "status": 202
      }

   :statuscode 202: Validation started
   :statuscode 400: Invalid validation parameters
