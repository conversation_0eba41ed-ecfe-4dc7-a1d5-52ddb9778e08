Regulations API
===============

The Regulations API provides comprehensive management of regulatory information, including regulation metadata, URLs, documents, and relationships.

.. contents:: Table of Contents
   :local:
   :depth: 2

Regulation Management
---------------------

Core regulation management endpoints for CRUD operations.

List Regulations
~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/regulations

   Retrieve a paginated list of regulations with optional filtering.

   **Example Request:**

   .. code-block:: http

      GET /api/v1/regulations?country=US&status=active&search=data%20protection HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>

   **Example Response:**

   .. code-block:: json

      {
        "data": [
          {
            "id": 1,
            "title": "General Data Protection Regulation",
            "description": "EU regulation on data protection and privacy",
            "reference_number": "2016/679",
            "status": "active",
            "effective_date": "2018-05-25T00:00:00Z",
            "publication_date": "2016-04-27T00:00:00Z",
            "jurisdiction": "European Union",
            "regulator": {
              "id": 5,
              "name": "European Commission",
              "abbreviation": "EC"
            },
            "country": {
              "id": 10,
              "name": "European Union",
              "code": "EU"
            },
            "categories": [
              {"id": 1, "name": "Data Protection"},
              {"id": 2, "name": "Privacy"}
            ],
            "industries": [
              {"id": 1, "name": "Technology"},
              {"id": 2, "name": "Financial Services"}
            ],
            "confidence_level": 0.95,
            "is_verified": true,
            "created_at": "2023-01-15T10:00:00Z",
            "updated_at": "2023-11-20T14:30:00Z"
          }
        ],
        "pagination": {
          "total": 1250,
          "page": 1,
          "per_page": 20,
          "pages": 63
        },
        "status": 200
      }

   :query string search: Full-text search across regulation fields
   :query string country: Filter by country code (e.g., "US", "EU")
   :query string status: Filter by status (active, draft, archived)
   :query int regulator_id: Filter by regulator ID
   :query int category_id: Filter by category ID
   :query int industry_id: Filter by industry ID
   :query string date_from: Filter by effective date (ISO format)
   :query string date_to: Filter by effective date (ISO format)
   :query float min_confidence: Minimum confidence level (0.0-1.0)
   :query boolean verified_only: Show only verified regulations
   :query int page: Page number (default: 1)
   :query int per_page: Items per page (default: 20, max: 100)
   :query string sort: Sort field (title, effective_date, updated_at)
   :query string order: Sort order (asc, desc)
   :statuscode 200: Success
   :statuscode 400: Invalid query parameters
   :statuscode 401: Unauthorized

Get Regulation Details
~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/regulations/(int:regulation_id)

   Retrieve detailed information about a specific regulation.

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "id": 1,
          "title": "General Data Protection Regulation",
          "description": "Comprehensive data protection regulation for the EU",
          "reference_number": "2016/679",
          "status": "active",
          "effective_date": "2018-05-25T00:00:00Z",
          "publication_date": "2016-04-27T00:00:00Z",
          "jurisdiction": "European Union",
          "source_url": "https://eur-lex.europa.eu/eli/reg/2016/679/oj",
          "summary": "The GDPR strengthens data protection for individuals within the EU",
          "regulator": {
            "id": 5,
            "name": "European Commission",
            "abbreviation": "EC",
            "website": "https://ec.europa.eu"
          },
          "country": {
            "id": 10,
            "name": "European Union",
            "code": "EU",
            "region": "Europe"
          },
          "categories": [
            {"id": 1, "name": "Data Protection", "description": "Privacy and data protection laws"}
          ],
          "industries": [
            {"id": 1, "name": "Technology", "description": "Technology and software companies"}
          ],
          "tags": [
            {"id": 1, "name": "GDPR", "color": "#2196F3"},
            {"id": 2, "name": "Privacy", "color": "#4CAF50"}
          ],
          "compliance_requirements": [
            {
              "id": 1,
              "title": "Data Subject Access Requests",
              "description": "Respond to data subject access requests within 30 days",
              "priority": "high",
              "deadline": "2024-01-01T00:00:00Z"
            }
          ],
          "related_regulations": [
            {
              "id": 15,
              "title": "ePrivacy Regulation",
              "relationship": "complementary"
            }
          ],
          "confidence_level": 0.95,
          "is_verified": true,
          "verified_by": "regulatory_expert_001",
          "verification_date": "2023-11-15T10:00:00Z",
          "created_at": "2023-01-15T10:00:00Z",
          "updated_at": "2023-11-20T14:30:00Z"
        },
        "status": 200
      }

   :param regulation_id: Regulation ID
   :type regulation_id: int
   :statuscode 200: Success
   :statuscode 404: Regulation not found

Create Regulation
~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/regulations

   Create a new regulation entry.

   **Example Request:**

   .. code-block:: http

      POST /api/v1/regulations HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>
      Content-Type: application/json

      {
        "title": "California Consumer Privacy Act",
        "description": "California state law intended to enhance privacy rights",
        "reference_number": "AB-375",
        "status": "active",
        "effective_date": "2020-01-01T00:00:00Z",
        "publication_date": "2018-06-28T00:00:00Z",
        "jurisdiction": "California, United States",
        "source_url": "https://leginfo.legislature.ca.gov/faces/billTextClient.xhtml?bill_id=201720180AB375",
        "summary": "The CCPA gives California residents rights over their personal information",
        "regulator_id": 12,
        "country_id": 1,
        "category_ids": [1, 2],
        "industry_ids": [1, 2, 3],
        "tag_ids": [5, 6]
      }

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "id": 1251,
          "title": "California Consumer Privacy Act",
          "description": "California state law intended to enhance privacy rights",
          "reference_number": "AB-375",
          "status": "active",
          "effective_date": "2020-01-01T00:00:00Z",
          "confidence_level": 0.8,
          "is_verified": false,
          "created_at": "2023-12-01T10:00:00Z",
          "updated_at": "2023-12-01T10:00:00Z"
        },
        "status": 201
      }

   :statuscode 201: Created successfully
   :statuscode 400: Invalid input data
   :statuscode 409: Regulation already exists
   :statuscode 422: Validation error

Update Regulation
~~~~~~~~~~~~~~~~~

.. http:put:: /api/v1/regulations/(int:regulation_id)

   Update an existing regulation.

   **Example Request:**

   .. code-block:: http

      PUT /api/v1/regulations/1251 HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>
      Content-Type: application/json

      {
        "title": "California Consumer Privacy Act (Amended)",
        "description": "Updated California state law with enhanced privacy rights",
        "status": "active",
        "is_verified": true,
        "verified_by": "legal_team_001"
      }

   :param regulation_id: Regulation ID
   :type regulation_id: int
   :statuscode 200: Updated successfully
   :statuscode 404: Regulation not found
   :statuscode 422: Validation error

Delete Regulation
~~~~~~~~~~~~~~~~~

.. http:delete:: /api/v1/regulations/(int:regulation_id)

   Soft delete a regulation (marks as deleted but preserves data).

   **Example Response:**

   .. code-block:: json

      {
        "message": "Regulation soft deleted successfully",
        "status": 200,
        "data": {
          "id": 1251,
          "is_deleted": true,
          "deleted_at": "2023-12-01T10:00:00Z",
          "deleted_by": "user_123"
        }
      }

   :param regulation_id: Regulation ID
   :type regulation_id: int
   :statuscode 200: Deleted successfully
   :statuscode 404: Regulation not found
   :statuscode 403: Insufficient permissions

Regulation URLs
---------------

Manage regulation URLs and source links.

List Regulation URLs
~~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/regulation-urls

   Retrieve a list of regulation URLs with metadata.

   **Example Response:**

   .. code-block:: json

      {
        "data": [
          {
            "id": 1,
            "url": "https://eur-lex.europa.eu/eli/reg/2016/679/oj",
            "title": "GDPR Official Text",
            "description": "Official EU legislation text",
            "url_type": "official",
            "language": "en",
            "is_active": true,
            "last_checked": "2023-12-01T08:00:00Z",
            "status_code": 200,
            "regulation": {
              "id": 1,
              "title": "General Data Protection Regulation"
            },
            "regulator": {
              "id": 5,
              "name": "European Commission"
            }
          }
        ],
        "status": 200
      }

   :query int regulation_id: Filter by regulation ID
   :query string url_type: Filter by URL type (official, guidance, commentary)
   :query string language: Filter by language code
   :query boolean active_only: Show only active URLs
   :statuscode 200: Success

Regulation Search
-----------------

Advanced search capabilities for regulations.

Full-Text Search
~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/regulations/search

   Perform advanced full-text search across regulations.

   **Example Request:**

   .. code-block:: http

      GET /api/v1/regulations/search?q=data%20protection%20privacy&fields=title,description&boost=title:2.0 HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <token>

   **Example Response:**

   .. code-block:: json

      {
        "data": [
          {
            "id": 1,
            "title": "General Data Protection Regulation",
            "description": "EU regulation on data protection and privacy",
            "score": 0.95,
            "highlights": {
              "title": ["General <em>Data Protection</em> Regulation"],
              "description": ["EU regulation on <em>data protection</em> and <em>privacy</em>"]
            }
          }
        ],
        "search_metadata": {
          "query": "data protection privacy",
          "total_results": 45,
          "search_time": "12ms",
          "suggestions": ["data privacy", "protection regulation"]
        },
        "status": 200
      }

   :query string q: Search query
   :query string fields: Fields to search (comma-separated)
   :query string boost: Field boost weights (field:weight)
   :query int limit: Maximum results (default: 20)
   :statuscode 200: Success
   :statuscode 400: Invalid search query
