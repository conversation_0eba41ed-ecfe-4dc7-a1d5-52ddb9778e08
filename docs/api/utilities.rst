Utilities API
=============

The Utilities API provides system utilities, health checks, configuration management, and administrative functions.

.. contents:: Table of Contents
   :local:
   :depth: 2

System Health
-------------

Monitor system health and status.

Health Check
~~~~~~~~~~~~

.. http:get:: /api/v1/health

   Get overall system health status.

   **Example Response:**

   .. code-block:: json

      {
        "status": "healthy",
        "timestamp": "2023-12-01T10:00:00Z",
        "database": "healthy",
        "services": {
          "regulation_import": "healthy",
          "data_enrichment": "healthy",
          "analytics": "healthy",
          "integrations": "degraded"
        },
        "metrics": {
          "total_regulations": 15420,
          "active_countries": 195,
          "total_regulators": 850,
          "last_update": "2023-12-01T09:30:00Z"
        },
        "test_coverage": {
          "overall": 85.2,
          "api": 92.1,
          "services": 78.5
        }
      }

   :statuscode 200: System is healthy
   :statuscode 503: System is unhealthy

Detailed Health Check
~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/health/detailed

   Get detailed health information for all system components.

   **Example Response:**

   .. code-block:: json

      {
        "status": "healthy",
        "components": {
          "database": {
            "status": "healthy",
            "response_time": "2ms",
            "connections": {
              "active": 5,
              "max": 100,
              "usage_percentage": 5.0
            },
            "storage": {
              "used_gb": 125.5,
              "total_gb": 500.0,
              "usage_percentage": 25.1
            }
          },
          "cache": {
            "status": "healthy",
            "hit_rate": 0.95,
            "memory_usage": "45%",
            "keys": 12500
          },
          "external_apis": {
            "status": "degraded",
            "services": {
              "sec_edgar": {
                "status": "healthy",
                "response_time": "245ms"
              },
              "eu_lex": {
                "status": "timeout",
                "last_error": "Connection timeout after 30s"
              }
            }
          },
          "background_jobs": {
            "status": "healthy",
            "active_jobs": 3,
            "queued_jobs": 12,
            "failed_jobs": 0
          }
        },
        "performance": {
          "cpu_usage": 45.2,
          "memory_usage": 67.8,
          "disk_usage": 25.1,
          "network_io": {
            "bytes_in": 1048576,
            "bytes_out": 2097152
          }
        }
      }

   :statuscode 200: Health check completed
   :statuscode 503: Critical components unhealthy

Configuration Management
------------------------

Manage system configuration and settings.

Get Configuration
~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/config

   Retrieve system configuration settings.

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "system": {
            "version": "1.0.0",
            "environment": "production",
            "debug_mode": false,
            "maintenance_mode": false
          },
          "features": {
            "regulation_import": true,
            "compliance_tracking": true,
            "analytics": true,
            "integrations": true,
            "ai_enrichment": false
          },
          "limits": {
            "max_file_size_mb": 100,
            "max_import_rows": 10000,
            "api_rate_limit": 1000,
            "concurrent_users": 500
          },
          "integrations": {
            "external_apis_enabled": true,
            "webhook_retries": 3,
            "sync_frequency": "daily"
          }
        },
        "status": 200
      }

   :statuscode 200: Success
   :statuscode 403: Insufficient permissions

Update Configuration
~~~~~~~~~~~~~~~~~~~~

.. http:patch:: /api/v1/config

   Update system configuration settings.

   **Example Request:**

   .. code-block:: http

      PATCH /api/v1/config HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <admin-token>
      Content-Type: application/json

      {
        "features": {
          "ai_enrichment": true
        },
        "limits": {
          "max_file_size_mb": 150
        }
      }

   :statuscode 200: Configuration updated
   :statuscode 400: Invalid configuration
   :statuscode 403: Insufficient permissions

System Information
------------------

Get system information and statistics.

System Info
~~~~~~~~~~~

.. http:get:: /api/v1/system/info

   Get comprehensive system information.

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "application": {
            "name": "RegulationGuru",
            "version": "1.0.0",
            "build": "20231201.1",
            "environment": "production",
            "uptime": "15d 4h 32m",
            "started_at": "2023-11-16T06:00:00Z"
          },
          "database": {
            "type": "PostgreSQL",
            "version": "15.4",
            "size_gb": 125.5,
            "tables": 45,
            "indexes": 128
          },
          "statistics": {
            "total_regulations": 15420,
            "total_requirements": 45680,
            "total_assessments": 1250,
            "active_users": 850,
            "api_calls_today": 125000
          },
          "deployment": {
            "platform": "Docker",
            "orchestration": "Kubernetes",
            "region": "us-east-1",
            "availability_zones": ["us-east-1a", "us-east-1b"]
          }
        },
        "status": 200
      }

   :statuscode 200: Success

Data Management
---------------

Utilities for data management and maintenance.

Database Maintenance
~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/system/maintenance

   Perform database maintenance operations.

   **Example Request:**

   .. code-block:: http

      POST /api/v1/system/maintenance HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <admin-token>
      Content-Type: application/json

      {
        "operations": [
          "vacuum_database",
          "reindex_tables",
          "update_statistics"
        ],
        "schedule": "immediate"
      }

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "maintenance_id": "maint_20231201_001",
          "status": "scheduled",
          "operations": [
            "vacuum_database",
            "reindex_tables", 
            "update_statistics"
          ],
          "estimated_duration": "30-45 minutes",
          "scheduled_time": "2023-12-01T02:00:00Z"
        },
        "status": 202
      }

   :statuscode 202: Maintenance scheduled
   :statuscode 403: Insufficient permissions

Cache Management
~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/system/cache/clear

   Clear system caches.

   **Example Request:**

   .. code-block:: http

      POST /api/v1/system/cache/clear HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <admin-token>
      Content-Type: application/json

      {
        "cache_types": ["regulation_data", "analytics", "user_sessions"],
        "confirm": true
      }

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "cleared_caches": [
            "regulation_data",
            "analytics", 
            "user_sessions"
          ],
          "keys_cleared": 12500,
          "memory_freed_mb": 256.5
        },
        "status": 200
      }

   :statuscode 200: Cache cleared
   :statuscode 403: Insufficient permissions

Backup and Restore
------------------

Manage system backups and restore operations.

Create Backup
~~~~~~~~~~~~~

.. http:post:: /api/v1/system/backup

   Create a system backup.

   **Example Request:**

   .. code-block:: http

      POST /api/v1/system/backup HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <admin-token>
      Content-Type: application/json

      {
        "backup_type": "full",
        "include_files": true,
        "compression": true,
        "encryption": true,
        "description": "Pre-deployment backup"
      }

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "backup_id": "backup_20231201_001",
          "status": "processing",
          "backup_type": "full",
          "estimated_size_gb": 150.5,
          "estimated_completion": "2023-12-01T11:30:00Z"
        },
        "status": 202
      }

   :statuscode 202: Backup started
   :statuscode 403: Insufficient permissions

List Backups
~~~~~~~~~~~~

.. http:get:: /api/v1/system/backups

   List available system backups.

   **Example Response:**

   .. code-block:: json

      {
        "data": [
          {
            "backup_id": "backup_20231201_001",
            "created_at": "2023-12-01T10:00:00Z",
            "backup_type": "full",
            "size_gb": 148.2,
            "status": "completed",
            "description": "Pre-deployment backup",
            "retention_until": "2024-01-01T00:00:00Z"
          }
        ],
        "status": 200
      }

   :statuscode 200: Success

Logging and Monitoring
----------------------

Access system logs and monitoring data.

Get Logs
~~~~~~~~

.. http:get:: /api/v1/system/logs

   Retrieve system logs.

   **Example Request:**

   .. code-block:: http

      GET /api/v1/system/logs?level=error&start_time=2023-12-01T00:00:00Z&limit=100 HTTP/1.1
      Host: api.regulationguru.com
      Authorization: Bearer <admin-token>

   **Example Response:**

   .. code-block:: json

      {
        "data": [
          {
            "timestamp": "2023-12-01T10:15:30Z",
            "level": "error",
            "component": "regulation_import",
            "message": "Failed to parse CSV row 15: Invalid date format",
            "context": {
              "user_id": "user_123",
              "import_id": "import_20231201_001",
              "row_data": {"title": "Test Regulation", "date": "invalid"}
            },
            "trace_id": "trace_abc123"
          }
        ],
        "pagination": {
          "total": 250,
          "page": 1,
          "per_page": 100
        },
        "status": 200
      }

   :query string level: Log level filter (debug, info, warning, error)
   :query string component: Component filter
   :query string start_time: Start time for log range
   :query string end_time: End time for log range
   :query int limit: Maximum number of logs to return
   :statuscode 200: Success
   :statuscode 403: Insufficient permissions

System Metrics
~~~~~~~~~~~~~~

.. http:get:: /api/v1/system/metrics

   Get detailed system metrics.

   **Example Response:**

   .. code-block:: json

      {
        "data": {
          "performance": {
            "cpu_usage": {
              "current": 45.2,
              "average_1h": 42.8,
              "average_24h": 38.5
            },
            "memory_usage": {
              "current": 67.8,
              "available_gb": 16.2,
              "total_gb": 32.0
            },
            "disk_io": {
              "reads_per_sec": 125,
              "writes_per_sec": 89,
              "queue_depth": 2.1
            }
          },
          "application": {
            "active_connections": 245,
            "requests_per_minute": 1250,
            "average_response_time": 125,
            "error_rate": 0.02
          },
          "business": {
            "regulations_processed_today": 156,
            "compliance_assessments_today": 23,
            "api_calls_today": 125000,
            "active_users_now": 89
          }
        },
        "status": 200
      }

   :statuscode 200: Success

Administrative Tools
--------------------

Administrative utilities and tools.

User Management
~~~~~~~~~~~~~~~

.. http:get:: /api/v1/admin/users

   List and manage system users (admin only).

   **Example Response:**

   .. code-block:: json

      {
        "data": [
          {
            "user_id": "user_123",
            "username": "john.doe",
            "email": "<EMAIL>",
            "role": "analyst",
            "is_active": true,
            "last_login": "2023-12-01T09:30:00Z",
            "created_at": "2023-01-15T10:00:00Z"
          }
        ],
        "pagination": {
          "total": 850,
          "page": 1,
          "per_page": 50
        },
        "status": 200
      }

   :statuscode 200: Success
   :statuscode 403: Insufficient permissions

System Alerts
~~~~~~~~~~~~~

.. http:get:: /api/v1/system/alerts

   Get system alerts and notifications.

   **Example Response:**

   .. code-block:: json

      {
        "data": [
          {
            "alert_id": "alert_001",
            "type": "warning",
            "title": "High API Usage",
            "message": "API usage is approaching rate limits",
            "severity": "medium",
            "created_at": "2023-12-01T10:00:00Z",
            "acknowledged": false,
            "auto_resolve": true
          }
        ],
        "status": 200
      }

   :statuscode 200: Success
