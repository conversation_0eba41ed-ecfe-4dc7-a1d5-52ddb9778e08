Welcome to RegulationGuru Documentation
=====================================

.. image:: _static/logo.png
   :width: 200px
   :alt: RegulationGuru Logo
   :align: center

RegulationGuru is a comprehensive regulatory compliance management platform designed to help organizations navigate the complex landscape of global regulations.

Features
--------

* **Regulation Management**: Track and manage regulations relevant to your organization
* **Compliance Requirements**: Define and track compliance requirements for each regulation
* **Document Management**: Store and manage regulatory documents
* **Tagging and Categorization**: Organize regulations with tags and categories
* **Industry Classification**: Filter regulations by industry
* **API Integration**: Comprehensive API for integration with other systems

Getting Started
--------------

.. toctree::
   :maxdepth: 2
   :caption: Getting Started

   installation
   quickstart
   configuration

User Guide
---------

.. toctree::
   :maxdepth: 2
   :caption: User Guide

   user/regulations
   user/compliance_requirements
   user/documents
   user/tags_categories
   user/industries
   user/dashboard

API Reference
------------

.. toctree::
   :maxdepth: 3
   :caption: API Reference

   api/overview
   api/authentication
   api/core_endpoints
   api/regulations
   api/compliance
   api/data_management
   api/analytics
   api/integrations
   api/utilities

Schema Reference
---------------

.. toctree::
   :maxdepth: 3
   :caption: Schema Reference

   schemas/overview
   schemas/core_models
   schemas/regulation_models
   schemas/compliance_models
   schemas/governance_models
   schemas/utility_models

Developer Guide
-------------

.. toctree::
   :maxdepth: 2
   :caption: Developer Guide

   dev/architecture
   dev/database
   dev/frontend
   dev/backend
   dev/testing
   dev/deployment

Contributing
-----------

.. toctree::
   :maxdepth: 2
   :caption: Contributing

   contributing/guidelines
   contributing/code_of_conduct
   contributing/development_setup

Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
