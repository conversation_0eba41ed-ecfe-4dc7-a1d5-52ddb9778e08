/**
 * Custom Dark Mode Theme for Sphinx Documentation
 * 
 * This CSS provides dark mode support for the Sphinx documentation
 * that integrates with the main application's theme system.
 */

/* CSS Variables for theme consistency */
:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --success-color: #4CAF50;
    --danger-color: #F44336;
    --warning-color: #FFEB3B;
    --info-color: #00BCD4;
    
    /* Light theme colors */
    --bg-color: #ffffff;
    --text-color: #212121;
    --border-color: #e0e0e0;
    --code-bg: #f5f5f5;
    --sidebar-bg: #f9f9f9;
    --nav-bg: #ffffff;
    --link-color: #3498db;
    --link-hover-color: #2980b9;
    
    /* Dark theme colors */
    --dark-bg-color: #121212;
    --dark-text-color: #eeeeee;
    --dark-border-color: #333333;
    --dark-code-bg: #1e1e1e;
    --dark-sidebar-bg: #1a1a1a;
    --dark-nav-bg: #1e1e1e;
    --dark-link-color: #64b5f6;
    --dark-link-hover-color: #90caf9;
}

/* Default light theme */
body {
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Dark mode styles */
body.dark-mode {
    background-color: var(--dark-bg-color);
    color: var(--dark-text-color);
}

/* Navigation and header */
.wy-nav-top {
    background-color: var(--nav-bg);
    color: var(--text-color);
}

body.dark-mode .wy-nav-top {
    background-color: var(--dark-nav-bg);
    color: var(--dark-text-color);
}

/* Sidebar */
.wy-nav-side {
    background-color: var(--sidebar-bg);
}

body.dark-mode .wy-nav-side {
    background-color: var(--dark-sidebar-bg);
}

.wy-menu-vertical a {
    color: var(--text-color);
}

body.dark-mode .wy-menu-vertical a {
    color: var(--dark-text-color);
}

.wy-menu-vertical a:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Main content area */
.wy-nav-content-wrap {
    background-color: var(--bg-color);
}

body.dark-mode .wy-nav-content-wrap {
    background-color: var(--dark-bg-color);
}

.wy-nav-content {
    background-color: var(--bg-color);
}

body.dark-mode .wy-nav-content {
    background-color: var(--dark-bg-color);
}

/* Code blocks */
.highlight {
    background-color: var(--code-bg);
    border: 1px solid var(--border-color);
}

body.dark-mode .highlight {
    background-color: var(--dark-code-bg);
    border: 1px solid var(--dark-border-color);
}

.highlight pre {
    background-color: transparent;
    color: var(--text-color);
}

body.dark-mode .highlight pre {
    color: var(--dark-text-color);
}

/* Inline code */
code.literal {
    background-color: var(--code-bg);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

body.dark-mode code.literal {
    background-color: var(--dark-code-bg);
    color: var(--dark-text-color);
    border: 1px solid var(--dark-border-color);
}

/* Links */
a {
    color: var(--link-color);
}

a:hover {
    color: var(--link-hover-color);
}

body.dark-mode a {
    color: var(--dark-link-color);
}

body.dark-mode a:hover {
    color: var(--dark-link-hover-color);
}

/* Tables */
.wy-table-responsive table td,
.wy-table-responsive table th {
    border-bottom: 1px solid var(--border-color);
}

body.dark-mode .wy-table-responsive table td,
body.dark-mode .wy-table-responsive table th {
    border-bottom: 1px solid var(--dark-border-color);
}

.wy-table-odd td {
    background-color: var(--code-bg);
}

body.dark-mode .wy-table-odd td {
    background-color: var(--dark-code-bg);
}

/* Admonitions */
.admonition {
    border: 1px solid var(--border-color);
    background-color: var(--bg-color);
}

body.dark-mode .admonition {
    border: 1px solid var(--dark-border-color);
    background-color: var(--dark-bg-color);
}

.admonition.note {
    border-left: 4px solid var(--info-color);
}

.admonition.warning {
    border-left: 4px solid var(--warning-color);
}

.admonition.danger {
    border-left: 4px solid var(--danger-color);
}

/* Search */
.wy-side-nav-search {
    background-color: var(--primary-color);
}

.wy-side-nav-search input[type="text"] {
    background-color: var(--bg-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

body.dark-mode .wy-side-nav-search input[type="text"] {
    background-color: var(--dark-bg-color);
    color: var(--dark-text-color);
    border: 1px solid var(--dark-border-color);
}

/* Breadcrumbs */
.wy-breadcrumbs {
    background-color: var(--bg-color);
    border-bottom: 1px solid var(--border-color);
}

body.dark-mode .wy-breadcrumbs {
    background-color: var(--dark-bg-color);
    border-bottom: 1px solid var(--dark-border-color);
}

/* Footer */
.rst-footer-buttons {
    border-top: 1px solid var(--border-color);
}

body.dark-mode .rst-footer-buttons {
    border-top: 1px solid var(--dark-border-color);
}

/* Theme toggle button for documentation */
.doc-theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.doc-theme-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

body.dark-mode .doc-theme-toggle {
    background-color: var(--dark-link-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .doc-theme-toggle {
        top: 10px;
        right: 10px;
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
}

/* Smooth transitions for all theme changes */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Custom scrollbar for dark mode */
body.dark-mode ::-webkit-scrollbar {
    width: 8px;
}

body.dark-mode ::-webkit-scrollbar-track {
    background: var(--dark-bg-color);
}

body.dark-mode ::-webkit-scrollbar-thumb {
    background: var(--dark-border-color);
    border-radius: 4px;
}

body.dark-mode ::-webkit-scrollbar-thumb:hover {
    background: var(--dark-link-color);
}

/* Print styles */
@media print {
    body.dark-mode {
        background-color: white !important;
        color: black !important;
    }
    
    body.dark-mode * {
        background-color: white !important;
        color: black !important;
    }
}
