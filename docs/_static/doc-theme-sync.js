/**
 * Documentation Theme Synchronization
 * 
 * This script handles theme synchronization between the main application
 * and the Sphinx documentation, ensuring consistent theming across the platform.
 */

class DocumentationThemeSync {
    constructor() {
        this.STORAGE_KEY = 'regulationguru-theme';
        this.DARK_CLASS = 'dark-mode';
        this.THEME_ATTRIBUTE = 'data-theme';
        
        this.init();
    }
    
    init() {
        // Apply initial theme
        this.applyInitialTheme();
        
        // Set up theme toggle button
        this.setupThemeToggle();
        
        // Set up communication with parent window
        this.setupParentCommunication();
        
        // Listen for storage changes (for multi-tab sync)
        this.setupStorageListener();
        
        console.log('Documentation theme sync initialized');
    }
    
    /**
     * Get stored theme preference
     */
    getStoredTheme() {
        try {
            return localStorage.getItem(this.STORAGE_KEY);
        } catch (e) {
            console.warn('Could not access localStorage:', e);
            return null;
        }
    }
    
    /**
     * Set theme preference
     */
    setStoredTheme(theme) {
        try {
            localStorage.setItem(this.STORAGE_KEY, theme);
        } catch (e) {
            console.warn('Could not save to localStorage:', e);
        }
    }
    
    /**
     * Detect system dark mode preference
     */
    getSystemPreference() {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            return 'dark';
        }
        return 'light';
    }
    
    /**
     * Get effective theme
     */
    getEffectiveTheme() {
        const stored = this.getStoredTheme();
        if (stored === 'light' || stored === 'dark') {
            return stored;
        }
        return this.getSystemPreference();
    }
    
    /**
     * Apply initial theme
     */
    applyInitialTheme() {
        const theme = this.getEffectiveTheme();
        this.applyTheme(theme);
    }
    
    /**
     * Apply theme to documentation
     */
    applyTheme(theme) {
        const body = document.body;
        const html = document.documentElement;
        
        // Remove existing theme classes
        body.classList.remove(this.DARK_CLASS);
        
        // Apply new theme
        if (theme === 'dark') {
            body.classList.add(this.DARK_CLASS);
            html.setAttribute(this.THEME_ATTRIBUTE, 'dark');
        } else {
            html.setAttribute(this.THEME_ATTRIBUTE, 'light');
        }
        
        // Update toggle button
        this.updateToggleButton(theme);
        
        console.log('Documentation theme applied:', theme);
    }
    
    /**
     * Toggle theme
     */
    toggleTheme() {
        const currentTheme = this.getEffectiveTheme();
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        this.setStoredTheme(newTheme);
        this.applyTheme(newTheme);
        
        // Notify parent window if in iframe
        this.notifyParentThemeChange(newTheme);
        
        console.log('Documentation theme toggled to:', newTheme);
    }
    
    /**
     * Create and setup theme toggle button
     */
    setupThemeToggle() {
        // Create toggle button
        const toggleButton = document.createElement('button');
        toggleButton.className = 'doc-theme-toggle';
        toggleButton.setAttribute('aria-label', 'Toggle theme');
        toggleButton.setAttribute('title', 'Toggle dark/light theme');
        
        const icon = document.createElement('i');
        icon.className = 'fas fa-moon';
        toggleButton.appendChild(icon);
        
        // Add click handler
        toggleButton.addEventListener('click', () => {
            this.toggleTheme();
        });
        
        // Add keyboard support
        toggleButton.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.toggleTheme();
            }
        });
        
        // Add to page
        document.body.appendChild(toggleButton);
        
        this.toggleButton = toggleButton;
        
        // Update initial state
        this.updateToggleButton(this.getEffectiveTheme());
    }
    
    /**
     * Update toggle button appearance
     */
    updateToggleButton(theme) {
        if (!this.toggleButton) return;
        
        const icon = this.toggleButton.querySelector('i');
        if (icon) {
            if (theme === 'dark') {
                icon.className = 'fas fa-sun';
                this.toggleButton.setAttribute('aria-label', 'Switch to light theme');
                this.toggleButton.setAttribute('title', 'Switch to light theme');
            } else {
                icon.className = 'fas fa-moon';
                this.toggleButton.setAttribute('aria-label', 'Switch to dark theme');
                this.toggleButton.setAttribute('title', 'Switch to dark theme');
            }
        }
    }
    
    /**
     * Set up communication with parent window
     */
    setupParentCommunication() {
        // Listen for theme sync messages from parent
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'theme-sync-response') {
                const theme = event.data.theme;
                if (theme !== this.getEffectiveTheme()) {
                    this.setStoredTheme(theme);
                    this.applyTheme(theme);
                }
            }
        });
        
        // Request initial theme from parent if in iframe
        if (window.parent !== window) {
            window.parent.postMessage({
                type: 'theme-sync-request'
            }, '*');
        }
    }
    
    /**
     * Notify parent window of theme change
     */
    notifyParentThemeChange(theme) {
        if (window.parent !== window) {
            window.parent.postMessage({
                type: 'theme-change',
                theme: theme
            }, '*');
        }
    }
    
    /**
     * Set up storage listener for multi-tab sync
     */
    setupStorageListener() {
        window.addEventListener('storage', (e) => {
            if (e.key === this.STORAGE_KEY) {
                const newTheme = e.newValue || this.getSystemPreference();
                this.applyTheme(newTheme);
            }
        });
    }
    
    /**
     * Set up system preference change listener
     */
    setupSystemPreferenceListener() {
        if (window.matchMedia) {
            const darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            
            darkModeMediaQuery.addEventListener('change', (e) => {
                const stored = this.getStoredTheme();
                if (!stored || stored === 'auto') {
                    const systemTheme = e.matches ? 'dark' : 'light';
                    this.applyTheme(systemTheme);
                }
            });
        }
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.docThemeSync = new DocumentationThemeSync();
    });
} else {
    window.docThemeSync = new DocumentationThemeSync();
}

// Also initialize immediately to prevent flash
(function() {
    const STORAGE_KEY = 'regulationguru-theme';
    const DARK_CLASS = 'dark-mode';
    
    function getStoredTheme() {
        try {
            return localStorage.getItem(STORAGE_KEY);
        } catch (e) {
            return null;
        }
    }
    
    function getSystemPreference() {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            return 'dark';
        }
        return 'light';
    }
    
    function getEffectiveTheme() {
        const stored = getStoredTheme();
        if (stored === 'light' || stored === 'dark') {
            return stored;
        }
        return getSystemPreference();
    }
    
    // Apply theme immediately to prevent flash
    const theme = getEffectiveTheme();
    if (theme === 'dark') {
        document.body.classList.add(DARK_CLASS);
        document.documentElement.setAttribute('data-theme', 'dark');
    } else {
        document.documentElement.setAttribute('data-theme', 'light');
    }
})();
