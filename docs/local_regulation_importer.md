# Local Regulation Importer

## Overview

The Local Regulation Importer is a comprehensive service for importing regulation data from local CSV files with robust file management, validation, and soft-delete support. It provides automatic file discovery, processing, backup, and archival capabilities while maintaining data integrity and audit trails.

## Features

### 🔍 **File Discovery & Management**
- Automatic discovery of CSV files in import directories
- Support for Unicode filenames and various encodings
- File validation before processing
- Comprehensive error handling and logging

### 📁 **Local Storage Organization**
- Structured directory layout for different file states
- Automatic file movement based on processing results
- Backup creation with timestamp and reason tracking
- Configurable storage paths (ignored by git)

### ✅ **Data Validation**
- CSV structure validation
- Required column checking
- File size and format validation
- Data quality assessment and scoring

### 🔄 **Import Processing**
- Batch processing with progress tracking
- Duplicate detection by file hash
- Soft-delete support following existing patterns
- User attribution and audit trails

### 📊 **Monitoring & Reporting**
- Comprehensive import/export logging
- Storage statistics and usage tracking
- Detailed error reporting and warnings
- Processing time and performance metrics

## Directory Structure

```
local_storage/
├── imports/          # Files ready for import
├── processed/        # Successfully processed files
├── failed/          # Files that failed processing
├── temp/            # Temporary processing files
├── backups/         # File backups with timestamps
└── exports/         # Generated export files
```

## Installation & Setup

### Prerequisites

```bash
# Install required dependencies
pip install pandas sqlalchemy pydantic

# Install test dependencies
pip install pytest pytest-cov behave
```

### Configuration

The importer uses local storage directories that are automatically ignored by git:

```python
from app.services.local_regulation_importer import LocalRegulationImporter, LocalStorageConfig

# Default configuration
config = LocalStorageConfig()  # Uses 'local_storage' directory

# Custom configuration
config = LocalStorageConfig("custom_storage_path")

# Initialize importer
importer = LocalRegulationImporter(db_session, config)
```

## Usage Examples

### Basic Import Operations

```python
from app.services.local_regulation_importer import LocalRegulationImporter

# Initialize importer
importer = LocalRegulationImporter(db_session)

# Discover files in import directory
files = importer.discover_import_files()
print(f"Found {len(files)} files to import")

# Import a single file
result = importer.import_single_file(
    file_path=Path("local_storage/imports/regulations.csv"),
    user_id="user123",
    validate_first=True
)

# Import all files in directory
results = importer.import_all_files(user_id="user123")

# Get storage statistics
stats = importer.get_storage_stats()
print(f"Total files: {sum(s['file_count'] for s in stats.values())}")
```

### File Validation

```python
# Validate a file before processing
is_valid, errors = importer.validate_file(file_path)

if is_valid:
    print("File is valid for import")
else:
    print(f"Validation errors: {errors}")
```

### Backup Management

```python
# Create backup with custom reason
backup_path = importer.backup_file(file_path, "pre_migration")

# Clean up old backups (older than 30 days)
cleaned_count = importer.cleanup_old_backups(days_to_keep=30)
print(f"Cleaned up {cleaned_count} old backup files")
```

## API Integration

The importer integrates with the REST API for web-based operations:

```bash
# Upload and import CSV file
curl -X POST "http://localhost:8000/api/v1/regulations-csv/import" \
  -F "file=@regulations.csv" \
  -F "user_id=user123"

# Get import logs
curl "http://localhost:8000/api/v1/regulations-csv/import-logs"

# Get storage statistics
curl "http://localhost:8000/api/v1/regulations-csv/stats"
```

## Testing

### Running Tests

```bash
# Run all tests
python scripts/run_local_regulation_tests.py

# Run only unit tests
python -m pytest tests/test_local_regulation_importer.py -v

# Run only BDD tests
python -m behave features/local_regulation_import.feature

# Run with coverage
python -m pytest tests/ --cov=app.services.local_regulation_importer --cov-report=html
```

### Test Data Generation

```bash
# Generate comprehensive test dataset
python scripts/generate_test_regulation_data.py --type all

# Generate specific test data
python scripts/generate_test_regulation_data.py --type valid --count 100
python scripts/generate_test_regulation_data.py --type unicode
python scripts/generate_test_regulation_data.py --type large --count 1000
```

### Test Coverage

The test suite includes:

- **Unit Tests**: 50+ test cases covering all functionality
- **Edge Case Tests**: Error handling, permission issues, Unicode support
- **BDD Tests**: 20+ scenarios covering user workflows
- **Integration Tests**: End-to-end workflow validation
- **Performance Tests**: Large file handling and concurrent processing

## File Format Requirements

### Required CSV Columns

The CSV files must contain these required columns:

- `Country_Name`: Full country name
- `Country_Code`: 2-3 character ISO country code
- `Document_Title`: Official regulation title
- `Document_Type`: Type of regulatory document
- `Issuing_Authority`: Authority that issued the regulation
- `Legal_Status`: Legal binding status

### Complete Column List

See [regulations_csv_schema.md](regulations_csv_schema.md) for the complete list of 27 columns and their descriptions.

### File Validation Rules

- File size limit: 100MB
- Encoding: UTF-8 (with BOM support)
- Format: Standard CSV with comma separators
- Headers: Must match expected column names
- Content: Required fields must not be empty

## Error Handling

### Common Error Scenarios

1. **File Validation Errors**
   - Missing required columns
   - Invalid file format
   - File too large
   - Permission denied

2. **Processing Errors**
   - Database connection issues
   - Data validation failures
   - Duplicate file detection
   - Storage permission problems

3. **Recovery Mechanisms**
   - Failed files moved to failed directory
   - Detailed error logging
   - Backup preservation
   - Graceful error handling

### Error Resolution

```python
# Check import logs for errors
import_logs = db.query(RegulationCSVImportLog).filter(
    RegulationCSVImportLog.import_status == 'failed'
).all()

for log in import_logs:
    print(f"Failed import: {log.batch_id}")
    print(f"Errors: {log.error_summary}")
```

## Performance Considerations

### Optimization Features

- **Batch Processing**: Files processed in configurable batches
- **Memory Management**: Streaming CSV processing for large files
- **Database Optimization**: Bulk inserts and efficient queries
- **File Operations**: Atomic moves and efficient copying
- **Concurrent Safety**: Thread-safe operations

### Performance Metrics

- **Small files** (< 1MB): < 5 seconds
- **Medium files** (1-10MB): < 30 seconds  
- **Large files** (10-100MB): < 5 minutes
- **Batch processing**: 100+ files per hour

## Monitoring & Maintenance

### Regular Maintenance Tasks

```bash
# Clean up old backups (run weekly)
python -c "
from app.services.local_regulation_importer import LocalRegulationImporter
importer = LocalRegulationImporter(db_session)
cleaned = importer.cleanup_old_backups(days_to_keep=30)
print(f'Cleaned {cleaned} old backups')
"

# Check storage usage
python -c "
from app.services.local_regulation_importer import LocalRegulationImporter
importer = LocalRegulationImporter(db_session)
stats = importer.get_storage_stats()
for dir_name, info in stats.items():
    print(f'{dir_name}: {info[\"file_count\"]} files, {info[\"total_size_mb\"]}MB')
"
```

### Monitoring Queries

```sql
-- Check recent import activity
SELECT batch_id, file_name, import_status, total_records, created_at
FROM regulations_csv_import_logs
WHERE created_at > NOW() - INTERVAL '7 days'
ORDER BY created_at DESC;

-- Check error rates
SELECT import_status, COUNT(*) as count
FROM regulations_csv_import_logs
GROUP BY import_status;

-- Check storage usage trends
SELECT DATE(created_at) as date, 
       SUM(total_records) as records_imported,
       COUNT(*) as files_processed
FROM regulations_csv_import_logs
WHERE import_status = 'completed'
GROUP BY DATE(created_at)
ORDER BY date DESC;
```

## Security Considerations

### File Security

- Input validation and sanitization
- File type and size restrictions
- Secure temporary file handling
- Permission-based access control

### Data Security

- Soft-delete for data retention compliance
- Audit trails for all operations
- User attribution and tracking
- Backup encryption (configurable)

## Troubleshooting

### Common Issues

1. **Permission Denied**
   ```bash
   # Fix directory permissions
   chmod 755 local_storage/
   chmod 755 local_storage/*/
   ```

2. **Import Failures**
   ```bash
   # Check failed files
   ls -la local_storage/failed/
   
   # Review error logs
   tail -f logs/import_errors.log
   ```

3. **Storage Full**
   ```bash
   # Clean up old backups
   python scripts/cleanup_old_backups.py --days 7
   ```

### Debug Mode

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Enable detailed logging
importer = LocalRegulationImporter(db_session)
result = importer.import_single_file(file_path, validate_first=True)
```

## Contributing

### Development Setup

```bash
# Install development dependencies
pip install -e .[dev]

# Run pre-commit hooks
pre-commit install

# Run full test suite
python scripts/run_local_regulation_tests.py
```

### Adding New Features

1. Update the `LocalRegulationImporter` class
2. Add corresponding unit tests
3. Update BDD scenarios if needed
4. Update documentation
5. Run full test suite

## License

This module is part of the RegulationGuru project and follows the same licensing terms.
