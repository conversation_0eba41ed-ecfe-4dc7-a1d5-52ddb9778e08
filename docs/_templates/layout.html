{# Custom layout template for RegulationGuru documentation with dark mode support #}
{% extends "!layout.html" %}

{# Add custom meta tags and theme detection #}
{% block extrahead %}
{{ super() }}
<meta name="color-scheme" content="light dark">
<meta name="theme-color" content="#3498db" media="(prefers-color-scheme: light)">
<meta name="theme-color" content="#1565C0" media="(prefers-color-scheme: dark)">

{# Immediate theme detection script to prevent flash #}
<script>
(function() {
    const STORAGE_KEY = 'regulationguru-theme';
    const DARK_CLASS = 'dark-mode';
    
    function getStoredTheme() {
        try {
            return localStorage.getItem(STORAGE_KEY);
        } catch (e) {
            return null;
        }
    }
    
    function getSystemPreference() {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            return 'dark';
        }
        return 'light';
    }
    
    function getEffectiveTheme() {
        const stored = getStoredTheme();
        if (stored === 'light' || stored === 'dark') {
            return stored;
        }
        return getSystemPreference();
    }
    
    // Apply theme immediately to prevent flash
    const theme = getEffectiveTheme();
    if (theme === 'dark') {
        document.documentElement.classList.add(DARK_CLASS);
        document.documentElement.setAttribute('data-theme', 'dark');
    } else {
        document.documentElement.setAttribute('data-theme', 'light');
    }
})();
</script>
{% endblock %}

{# Add custom navigation items #}
{% block menu %}
{{ super() }}
<li class="toctree-l1">
    <a class="reference internal" href="/api/v1/docs" target="_parent">
        <i class="fas fa-code"></i> API Documentation
    </a>
</li>
<li class="toctree-l1">
    <a class="reference internal" href="/" target="_parent">
        <i class="fas fa-home"></i> Main Dashboard
    </a>
</li>
{% endblock %}

{# Add custom footer content #}
{% block footer %}
{{ super() }}
<div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
    <div style="text-align: center; padding: 20px 0; border-top: 1px solid var(--border-color, #e0e0e0);">
        <p style="margin: 0; color: var(--text-color, #666);">
            <i class="fas fa-shield-alt"></i>
            <strong>RegulationGuru</strong> - Comprehensive Regulatory Compliance Platform
        </p>
        <p style="margin: 5px 0 0 0; font-size: 0.9em; color: var(--text-color, #888);">
            <a href="/" target="_parent" style="color: var(--link-color, #3498db); text-decoration: none;">
                Return to Dashboard
            </a>
            |
            <a href="/api/v1/docs" target="_parent" style="color: var(--link-color, #3498db); text-decoration: none;">
                API Docs
            </a>
            |
            <a href="/compliance-calendar" target="_parent" style="color: var(--link-color, #3498db); text-decoration: none;">
                Compliance Calendar
            </a>
        </p>
    </div>
</div>
{% endblock %}

{# Add version info and theme indicator #}
{% block versions %}
{{ super() }}
<div class="rst-versions" data-toggle="rst-versions" role="note" aria-label="versions">
    <span class="rst-current-version" data-toggle="rst-current-version">
        <span class="fa fa-book"> RegulationGuru Docs</span>
        v: {{ version }}
        <span class="fa fa-caret-down"></span>
    </span>
    <div class="rst-other-versions">
        <dl>
            <dt>Theme</dt>
            <dd>
                <span id="theme-indicator">Auto</span>
                <button id="docs-theme-toggle" style="margin-left: 10px; padding: 2px 8px; border: 1px solid #ccc; background: transparent; cursor: pointer; border-radius: 3px;">
                    <i class="fas fa-adjust"></i> Toggle
                </button>
            </dd>
        </dl>
        <dl>
            <dt>Links</dt>
            <dd><a href="/" target="_parent">Dashboard</a></dd>
            <dd><a href="/api/v1/docs" target="_parent">API Docs</a></dd>
            <dd><a href="/compliance-calendar" target="_parent">Calendar</a></dd>
        </dl>
        <dl>
            <dt>Resources</dt>
            <dd><a href="https://github.com/forkrul/replit-10Baht-RegulationGuru" target="_blank">GitHub</a></dd>
            <dd><a href="/api/v1/health" target="_parent">System Health</a></dd>
        </dl>
    </div>
</div>

<script>
// Enhanced theme toggle for documentation
document.addEventListener('DOMContentLoaded', function() {
    const themeToggle = document.getElementById('docs-theme-toggle');
    const themeIndicator = document.getElementById('theme-indicator');
    
    if (themeToggle && themeIndicator) {
        function updateThemeIndicator() {
            const theme = document.documentElement.getAttribute('data-theme') || 'light';
            const isDark = document.documentElement.classList.contains('dark-mode');
            themeIndicator.textContent = isDark ? 'Dark' : 'Light';
        }
        
        themeToggle.addEventListener('click', function() {
            if (window.docThemeSync) {
                window.docThemeSync.toggleTheme();
                setTimeout(updateThemeIndicator, 100);
            }
        });
        
        // Initial update
        updateThemeIndicator();
        
        // Listen for theme changes
        document.addEventListener('themechange', updateThemeIndicator);
    }
});
</script>
{% endblock %}
