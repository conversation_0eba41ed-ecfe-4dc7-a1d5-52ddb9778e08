
# RegulationGuru API Documentation

This document serves as a comprehensive index of all API endpoints available in the RegulationGuru platform.

## Core API Endpoints

| API | Description | Documentation Link |
|-----|-------------|-------------------|
| Alerts API | Notifications about regulatory changes | [Alerts API Documentation](alerts_api.md) |
| Benchmarks API | Regulatory compliance benchmarks | [Benchmarks API Documentation](benchmarks_api.md) |
| Country API | Country-specific regulatory information | [Country API Documentation](country_api.md) |
| Regulator API | Information about regulatory authorities | [Regulator API Documentation](regulator_api.md) |
| Compliance API | Compliance assessment and tracking | [Compliance API Documentation](compliance_api.md) |
| Compliance Calendar API | Schedule of regulatory deadlines | [Compliance Calendar API Documentation](compliance_calendar_api.md) |

## Integration APIs

| API | Description | Documentation Link |
|-----|-------------|-------------------|
| External Integrations API | Integration with third-party tools | [Integrations API Documentation](integrations_api.md) |

## Data Processing APIs

| API | Description | Documentation Link |
|-----|-------------|-------------------|
| Document Import API | Import regulatory documents | [Document Import API Documentation](document_import_api.md) |
| Data Collection API | Collection of regulatory data | [Data Collection API Documentation](data_collection_api.md) |
| Data Enrichment API | Enrichment of regulatory data | [Data Enrichment API Documentation](data_enrichment_api.md) |

## Analysis APIs

| API | Description | Documentation Link |
|-----|-------------|-------------------|
| Impact Assessment API | Assessment of regulatory impact | [Impact Assessment API Documentation](impact_assessment_api.md) |
| Trend Analysis API | Analysis of regulatory trends | [Trend Analysis API Documentation](trend_analysis_api.md) |
| PDF Analysis API | Analysis of PDF documents | [PDF Analysis API Documentation](pdf_analysis_api.md) |

## Utility APIs

| API | Description | Documentation Link |
|-----|-------------|-------------------|
| Language API | Language and localization settings | [Language API Documentation](language_api.md) |
| Health Check API | System health monitoring | [Health Check API Documentation](health_check_api.md) |
