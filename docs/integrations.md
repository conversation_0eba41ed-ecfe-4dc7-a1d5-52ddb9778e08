
# External API Integrations

## SuperGlu Integration

RegulationGuru uses SuperGlu as a middleware integration layer to connect with external governance, risk, and compliance tools.

### Features

- **Bidirectional Data Synchronization**: Keep data synchronized between RegulationGuru and external systems
- **Field Mapping**: Configure custom field mappings between systems
- **Webhook Support**: Receive real-time notifications from external systems
- **Status Monitoring**: Monitor the health and connectivity of integrations

### Supported Entity Types

The following entities can be synchronized through SuperGlu:

- Regulations
- Compliance Tasks
- Regulatory Alerts
- Risk Assessments

### Integration Setup

1. Configure your SuperGlu account credentials in the Secrets management tool
2. Set up field mappings through the `/api/v1/integrations/superglu/mappings` endpoint
3. Test connectivity using the `/api/v1/integrations/superglu/status` endpoint
4. Configure webhooks for real-time data exchange

### Example Integration Flow

```mermaid
sequenceDiagram
    participant RegGuru as RegulationGuru
    participant SuperGlu as SuperGlu Integration Layer
    participant External as External GRC Tool
    
    RegGuru->>SuperGlu: Initiate sync
    SuperGlu->>External: Request data
    External->>SuperGlu: Return data
    SuperGlu->>RegGuru: Transform and store data
    
    External->>SuperGlu: Send webhook notification
    SuperGlu->>RegGuru: Process notification
    
    RegGuru->>SuperGlu: Update entity
    SuperGlu->>External: Propagate changes
```

### API Reference

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/integrations/superglu/status` | GET | Check integration status |
| `/api/v1/integrations/superglu/mappings` | GET | Retrieve field mappings |
| `/api/v1/integrations/superglu/sync/{entity_type}` | POST | Synchronize entities |
| `/api/v1/integrations/superglu/webhook` | POST | Receive webhook notifications |

## Adding New Integrations

To add new integration providers beyond SuperGlu:

1. Create a new module in `app/api/integrations/`
2. Define router and endpoints for the new integration
3. Include the router in `app/api/integrations/router.py`
4. Add appropriate tests in `tests/test_integrations.py`
