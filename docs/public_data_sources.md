# Public Regulatory Data Sources

RegulationGuru now integrates with several public regulatory data sources to provide comprehensive access to regulatory information. This document describes the available data sources and how to use them.

## Available Data Sources

RegulationGuru integrates with the following public regulatory data sources:

1. **SEC EDGAR** - Securities and Exchange Commission's Electronic Data Gathering, Analysis, and Retrieval system
2. **CFPB Consumer Complaints** - Consumer Financial Protection Bureau's Consumer Complaint Database
3. **FINRA Rules** - Financial Industry Regulatory Authority's rules and regulatory notices
4. **Federal Reserve** - Federal Reserve's regulatory data and economic indicators
5. **USAspending.gov** - Federal spending data and contract information

## API Endpoints

### List Available Public Sources

```
GET /api/v1/data-sources/public/available
```

Returns a list of all available public data sources with information about each source.

Example response:
```json
[
  {
    "name": "sec_edgar",
    "description": "SEC EDGAR database of corporate filings and regulatory data",
    "base_url": "https://www.sec.gov/",
    "requires_api_key": false,
    "data_types": ["regulatory_data", "company_filings", "xbrl_data"]
  },
  {
    "name": "cfpb_complaints",
    "description": "CFPB Consumer Complaint Database",
    "base_url": "https://www.consumerfinance.gov/data-research/consumer-complaints/",
    "requires_api_key": false,
    "data_types": ["consumer_complaints"]
  },
  ...
]
```

### Test Connections to Public Sources

```
GET /api/v1/data-sources/public/test-connections
```

Tests connections to all available public data sources and returns the status of each connection.

Example response:
```json
{
  "sec_edgar": true,
  "cfpb_complaints": true,
  "finra_rules": true,
  "federal_reserve": true,
  "usa_spending": true
}
```

### Fetch Data from a Public Source

```
GET /api/v1/data-sources/public/{source_name}/data
```

Fetches data from a specific public data source.

Parameters:
- `source_name` (path) - Name of the public data source
- `data_type` (query, optional) - Type of data to fetch
- `start_date` (query, optional) - Start date for data
- `end_date` (query, optional) - End date for data
- `limit` (query, optional) - Maximum number of items to fetch (default: 10)

Example request:
```
GET /api/v1/data-sources/public/sec_edgar/data?data_type=regulatory_data&limit=5
```

Example response:
```json
[
  {
    "id": "33-11151",
    "title": "Final Rule: Enhanced Reporting of Proxy Votes",
    "url": "https://www.sec.gov/rules/final/2023/33-11151.pdf",
    "filing_date": "2023-07-13",
    "category": "final_rule",
    "document_type": "pdf",
    "source": "SEC EDGAR"
  },
  ...
]
```

### Manage External API Sources

RegulationGuru allows you to create and manage external API sources, which are configurations for connecting to public data sources.

#### Create an External API Source

```
POST /api/v1/data-sources/external
```

Creates a new external API source.

Example request body:
```json
{
  "name": "SEC EDGAR Source",
  "source_type": "sec_edgar",
  "base_url": "https://www.sec.gov/",
  "api_key": null,
  "description": "SEC EDGAR data source for regulatory filings",
  "is_active": true,
  "sync_frequency": 24
}
```

#### List External API Sources

```
GET /api/v1/data-sources/external
```

Lists all external API sources.

Parameters:
- `skip` (query, optional) - Number of items to skip (default: 0)
- `limit` (query, optional) - Maximum number of items to return (default: 100)
- `source_type` (query, optional) - Filter by source type
- `is_active` (query, optional) - Filter by active status

#### Get an External API Source

```
GET /api/v1/data-sources/external/{source_id}
```

Gets an external API source by ID.

#### Update an External API Source

```
PUT /api/v1/data-sources/external/{source_id}
```

Updates an external API source.

#### Delete an External API Source

```
DELETE /api/v1/data-sources/external/{source_id}
```

Deletes an external API source.

#### Synchronize Data from an External API Source

```
POST /api/v1/data-sources/external/{source_id}/sync
```

Synchronizes data from an external API source.

Parameters:
- `force` (query, optional) - Force synchronization even if not due (default: false)

#### Get Synchronization Logs for an External API Source

```
GET /api/v1/data-sources/external/{source_id}/logs
```

Gets synchronization logs for an external API source.

Parameters:
- `skip` (query, optional) - Number of items to skip (default: 0)
- `limit` (query, optional) - Maximum number of items to return (default: 100)

## Data Source Details

### SEC EDGAR

The SEC EDGAR database provides access to corporate filings, financial statements, and regulatory data. The following data types are available:

- `regulatory_data` - Regulatory data such as rules, regulations, and interpretations
- `company_filings` - Corporate filings such as 10-K, 10-Q, and 8-K reports
- `xbrl_data` - XBRL financial data from corporate filings

### CFPB Consumer Complaints

The CFPB Consumer Complaint Database provides access to consumer complaints about financial products and services. The following data types are available:

- `consumer_complaints` - Consumer complaints about financial products and services

### FINRA Rules

The FINRA Rules database provides access to FINRA rules and regulatory notices. The following data types are available:

- `rules` - FINRA rules
- `regulatory_notices` - FINRA regulatory notices

### Federal Reserve

The Federal Reserve provides access to regulatory data and economic indicators. The following data types are available:

- `regulations` - Federal Reserve regulations
- `economic_data` - Economic indicators and data series

### USAspending.gov

USAspending.gov provides access to federal spending data and contract information. The following data types are available:

- `federal_accounts` - Federal account information
- `awards` - Federal contract and grant awards
