# Changelog

All notable changes to this project will be documented in this file.

## [Unreleased]

### Added
- Starlette-Admin integration under `/ui/manage` for database administration

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

### Added
- API versioning with `/api/v1/` prefix on all endpoints
- Improved URL processing to handle space-separated URLs in input files
- Regulatory URL processor and database integration
  - Added models for Countries, Regulators, and RegulationURLs
  - Implemented URL processing with country and regulator identification
  - Added confidence scoring system for regulator associations
  - Created API endpoints for accessing regulatory information
  - Implemented PostgreSQL integration with proper normalization
- Test coverage reporting system
  - Added HTML and XML coverage reports in the coverage/ directory
  - Created generate_coverage.py script for easy report generation
- Health check endpoint logging for monitoring access
  - Added console logging for each health check access with timestamp and client IP
  - Implemented persistent logging to health_check_log.txt file
- Created initial project structure with FastAPI and PostgreSQL
- Added soft delete functionality for database records
- Implemented internationalization (i18n) support
- Enabled API documentation endpoints (/docs, /redoc, /openapi.json)
- Maintained /health endpoint at top level while also providing it at /api/v1/health

### Changed

### Fixed

## [0.1.0] - YYYY-MM-DD
- Initial release
## 2024-02-27: Server Running, Infrastructure Updated

### 🚀 Server Milestone
- Server now running stably with proper environment configuration
- Updated document templates with Jinja2
- Fixed JavaScript template literals in Python f-strings

### 🏗️ Infrastructure Updates
- Documentation improved with template patterns guide
- Added template escaping guidance for JavaScript in Python strings
- Structured project documentation for better developer onboarding

### 🔧 Technical Improvements
- Fixed SyntaxError with f-string unterminated strings
- Improved FastAPI pattern documentation
- Added detailed explanation of JavaScript handling within templates
