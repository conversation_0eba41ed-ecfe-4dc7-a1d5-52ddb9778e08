
# Project Infrastructure

## Libraries and Dependencies

This project relies on the following key libraries and dependencies:

### Core Framework
- **FastAPI**: Modern, high-performance web framework for building APIs
- **Uvicorn**: ASGI server for running FastAPI applications
- **Pydantic**: Data validation and settings management using Python type hints

### Database
- **SQLAlchemy**: SQL toolkit and Object-Relational Mapping (ORM)
- **Alembic**: Database migration tool for SQLAlchemy
- **PostgreSQL**: Primary database (managed by Replit)

### Internationalization
- **Babel**: Internationalization (i18n) library for Python
- **Gettext**: GNU translation utilities for internationalization

### Admin Interface
- **Starlette-Admin**: Admin interface for database management
- **WTForms**: Form validation and rendering library
- **SQLAlchemy-Utils**: Utility functions for SQLAlchemy

### Testing
- **Pytest**: Testing framework
- **Pytest-cov**: Test coverage plugin for Pytest
- **Pytest-xdist**: Distributed testing plugin for Pytest
- **Httpx**: HTTP client for testing FastAPI applications
- **Selenium**: Browser automation for UI testing

### Visualization
- **Folium**: Python wrapper for Leaflet.js maps
- **Python-multipart**: Multipart form data parsing

### Utilities
- **Python-dotenv**: Environment variable management
- **Requests**: HTTP library for Python
- **Black**: Code formatter for Python

## Architecture Diagram

```mermaid
graph TD
    User[User] --> |HTTP Request| FE[Frontend]
    FE --> |API Request| API[FastAPI Application]
    
    subgraph Application
        API --> R[Routers]
        R --> H[Handlers/Endpoints]
        H --> S[Services]
        S --> DB[Database ORM]
        DB --> PG[PostgreSQL]
        
        H --> I18N[Internationalization]
        I18N --> TR[Translation Files]
        
        API --> MW[Middleware]
        MW --> Auth[Authentication]
        MW --> Lang[Language Selection]
        
        H --> VIZ[Visualization]
        VIZ --> Map[World Map]
    end
    
    subgraph Admin
        API --> AdminI[Admin Interface]
        AdminI --> |CRUD Operations| PG
    end
    
    subgraph Testing
        Test[Pytest Suite] --> |Unit Tests| H
        Test --> |Integration Tests| S
        Test --> |API Tests| API
        Test --> |UI Tests| FE
        Test --> Cov[Coverage Reports]
    end
    
    subgraph CI/CD
        CICD[CI/CD Pipeline] --> Test
        CICD --> Deploy[Replit Deployment]
    end
```

## System Components

1. **FastAPI Application**: Core application that handles HTTP requests and responses
2. **PostgreSQL Database**: Persistent storage for application data
3. **SQLAlchemy ORM**: Object-Relational Mapping for database interactions
4. **Internationalization Layer**: Handles translations across multiple languages
5. **Admin Interface**: Starlette-Admin UI for database management
6. **Testing Framework**: Comprehensive test suite with coverage reporting
7. **Visualization Tools**: Generate maps and visual representations
8. **Integration Layer**: SuperGlu module enabling seamless integration with external governance/risk tools

## Deployment Architecture

The application is deployed on Replit's infrastructure:

1. **Web Service**: Served via Replit's HTTP server
2. **Database**: Uses Replit's managed PostgreSQL service
3. **Static Assets**: Served directly by Replit's CDN
4. **Secret Management**: Environment variables stored in Replit Secrets
5. **Deployment**: Handled through Replit's Deployment system

## Development Workflow

1. Local development in Replit IDE
2. Changes tracked in version control
3. Tests run via pytest with coverage reporting
4. Database migrations managed with Alembic
5. Deployment via Replit Deployments

## Template Structure

The application uses a combination of server-side templates and client-side JavaScript:

1. **HTML Templates**: Located in the `templates/` directory
2. **Embedded JavaScript**: Some endpoint handlers in `app/main.py` include embedded JavaScript within HTML templates
3. **JavaScript Escaping**: When using JavaScript template literals (`${variable}`) within Python f-strings, they must be escaped with a backslash (`\${variable}`)

### JavaScript in Templates

When embedding JavaScript in Python strings that will be returned as HTML:

```python
# Correct way to embed JavaScript template literals in Python f-strings
html_content = f"""
<script>
    // JavaScript code with template literals
    const data = {{ value: 123 }};
    document.getElementById("output").innerHTML = `Value: \${data.value}`;
    //                                                     ^ Note the backslash
</script>
"""
```

This pattern is used particularly in health check and visualization endpoints where dynamic client-side rendering is required.
