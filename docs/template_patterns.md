
# Template Patterns

This document outlines the template patterns used in the application.

## Template Approaches

The application uses two main approaches for rendering content:

1. **Server-Side Templates**: Using Jinja2 templates located in the `templates/` directory
2. **Embedded HTML**: Some endpoints return HTML with embedded JavaScript directly from FastAPI endpoints

## JavaScript in Templates

When JavaScript is embedded in HTML responses from FastAPI endpoints, special care must be taken when using JavaScript template literals (`${variable}`) inside Python f-strings. These must be escaped with a backslash:

```python
html_content = f"""
<script>
    // CORRECT: JavaScript template literals in Python f-strings
    fetch('/api/data')
        .then(response => response.json())
        .then(data => {{
            document.getElementById("result").innerHTML = `Value: \${data.value}`;
            //                                                    ^ Note the backslash
        }});
</script>
"""
```

## Common Patterns

### Health Check Dashboard

The health check dashboard uses embedded JavaScript to fetch and display real-time system status:

```python
@app.get("/health/dashboard", response_class=HTMLResponse)
def health_dashboard():
    """Render a health dashboard with live updates via JavaScript."""
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Health Dashboard</title>
        <script>
            fetch('/api/health')
                .then(response => response.json())
                .then(data => {{
                    // Note the escaped JavaScript template literals
                    document.getElementById("status").innerHTML = \`Status: \${data.status}\`;
                }});
        </script>
    </head>
    <body>
        <div id="status">Loading...</div>
    </body>
    </html>
    """
```

### Interactive Maps

The world map visualization uses Folium for server-side map generation, but also includes JavaScript for interactivity:

```python
@app.get("/map", response_class=HTMLResponse)
def display_map(lat: float = 0, lon: float = 0, zoom: int = 2):
    map_obj = worldmap.create_world_map(center=(lat, lon), zoom_start=zoom)
    return worldmap.get_map_html(map_obj)
```

## Best Practices

1. **Separate Complex Templates**: Move complex HTML/JS to template files rather than embedding in Python code
2. **Escape JS Template Literals**: Always escape `${...}` with a backslash when inside Python f-strings
3. **Consistent Styling**: Use consistent naming conventions for CSS classes and JavaScript functions
4. **Error Handling**: Include error handling in JavaScript fetch operations
5. **Documentation**: Document JavaScript behavior in Python docstrings
