Governance Models Schema
========================

This section documents the data models for governance structures including categories, tags, industries, and document management within the RegulationGuru platform.

.. contents:: Table of Contents
   :local:
   :depth: 2

Category Schemas
----------------

CategoryBase
~~~~~~~~~~~~

Base schema for regulation categories.

.. code-block:: python

   class CategoryBase(BaseSchema):
       """
       Base category schema for regulation classification.
       
       Categories provide a hierarchical way to organize
       regulations by subject matter and regulatory domain.
       """
       
       name: str = Field(
           ...,
           min_length=1,
           max_length=100,
           description="Category name",
           example="Data Protection"
       )
       
       description: Optional[str] = Field(
           default=None,
           max_length=500,
           description="Category description",
           example="Regulations related to data protection and privacy"
       )
       
       icon: Optional[str] = Field(
           default=None,
           max_length=50,
           description="Icon identifier for UI display",
           example="shield"
       )
       
       color: Optional[str] = Field(
           default=None,
           regex=r'^#[0-9A-Fa-f]{6}$',
           description="Hex color code for UI display",
           example="#2196F3"
       )
       
       parent_id: Optional[int] = Field(
           default=None,
           description="Parent category ID for hierarchical structure"
       )
       
       sort_order: int = Field(
           default=0,
           description="Sort order for display",
           example=10
       )
       
       is_active: bool = Field(
           default=True,
           description="Whether the category is active"
       )

CategoryCreate
~~~~~~~~~~~~~~

Schema for creating new categories.

.. code-block:: python

   class CategoryCreate(CategoryBase):
       """Schema for creating new regulation categories."""
       pass

CategoryResponse
~~~~~~~~~~~~~~~~

Complete category response with hierarchy and statistics.

.. code-block:: python

   class CategoryResponse(CategoryBase, TimestampMixin):
       """
       Complete category response schema.
       
       Includes category information plus hierarchy
       and usage statistics.
       """
       
       id: int = Field(
           ...,
           description="Unique category identifier",
           example=1
       )
       
       parent: Optional['CategorySummary'] = Field(
           default=None,
           description="Parent category information"
       )
       
       children: List['CategorySummary'] = Field(
           default=[],
           description="Child categories"
       )
       
       regulation_count: int = Field(
           default=0,
           description="Number of regulations in this category",
           example=45
       )
       
       path: str = Field(
           ...,
           description="Full category path",
           example="Legal > Data Protection"
       )
       
       level: int = Field(
           ...,
           description="Hierarchy level (0 for root categories)",
           example=1
       )

Tag Schemas
-----------

TagBase
~~~~~~~

Base schema for regulation tags.

.. code-block:: python

   class TagBase(BaseSchema):
       """
       Base tag schema for flexible regulation labeling.
       
       Tags provide a flexible way to label and categorize
       regulations with multiple, non-hierarchical attributes.
       """
       
       name: str = Field(
           ...,
           min_length=1,
           max_length=50,
           description="Tag name",
           example="GDPR"
       )
       
       description: Optional[str] = Field(
           default=None,
           max_length=200,
           description="Tag description",
           example="General Data Protection Regulation related"
       )
       
       color: Optional[str] = Field(
           default=None,
           regex=r'^#[0-9A-Fa-f]{6}$',
           description="Hex color code for UI display",
           example="#4CAF50"
       )
       
       tag_type: TagType = Field(
           default=TagType.GENERAL,
           description="Type of tag for organization"
       )
       
       is_system: bool = Field(
           default=False,
           description="Whether this is a system-generated tag"
       )
       
       is_active: bool = Field(
           default=True,
           description="Whether the tag is active"
       )

TagResponse
~~~~~~~~~~~

Complete tag response with usage statistics.

.. code-block:: python

   class TagResponse(TagBase, TimestampMixin):
       """
       Complete tag response schema.
       
       Includes tag information plus usage statistics
       and related data.
       """
       
       id: int = Field(
           ...,
           description="Unique tag identifier",
           example=1
       )
       
       usage_count: int = Field(
           default=0,
           description="Number of regulations using this tag",
           example=125
       )
       
       related_tags: List['TagSummary'] = Field(
           default=[],
           description="Related or similar tags"
       )

Industry Schemas
----------------

IndustryBase
~~~~~~~~~~~~

Base schema for industry classification.

.. code-block:: python

   class IndustryBase(BaseSchema):
       """
       Base industry schema for regulation applicability.
       
       Industries define which business sectors and
       economic activities regulations apply to.
       """
       
       name: str = Field(
           ...,
           min_length=1,
           max_length=100,
           description="Industry name",
           example="Financial Services"
       )
       
       description: Optional[str] = Field(
           default=None,
           max_length=500,
           description="Industry description",
           example="Banking, insurance, investment, and other financial institutions"
       )
       
       sector: Optional[str] = Field(
           default=None,
           max_length=100,
           description="Broader economic sector",
           example="Finance"
       )
       
       naics_code: Optional[str] = Field(
           default=None,
           max_length=10,
           description="NAICS (North American Industry Classification System) code",
           example="52"
       )
       
       sic_code: Optional[str] = Field(
           default=None,
           max_length=10,
           description="SIC (Standard Industrial Classification) code",
           example="60-67"
       )
       
       is_active: bool = Field(
           default=True,
           description="Whether the industry is active"
       )

IndustryResponse
~~~~~~~~~~~~~~~~

Complete industry response with regulatory information.

.. code-block:: python

   class IndustryResponse(IndustryBase, TimestampMixin):
       """
       Complete industry response schema.
       
       Includes industry information plus regulatory
       statistics and related data.
       """
       
       id: int = Field(
           ...,
           description="Unique industry identifier",
           example=1
       )
       
       regulation_count: int = Field(
           default=0,
           description="Number of regulations applicable to this industry",
           example=78
       )
       
       compliance_requirements_count: int = Field(
           default=0,
           description="Number of compliance requirements for this industry",
           example=156
       )
       
       major_regulations: List['RegulationSummary'] = Field(
           default=[],
           description="Major regulations affecting this industry"
       )

Document Schemas
----------------

DocumentBase
~~~~~~~~~~~~

Base schema for document management.

.. code-block:: python

   class DocumentBase(BaseSchema):
       """
       Base document schema for regulation-related documents.
       
       Documents include regulation texts, guidance documents,
       compliance templates, and other related materials.
       """
       
       title: str = Field(
           ...,
           min_length=1,
           max_length=300,
           description="Document title",
           example="GDPR Implementation Guide"
       )
       
       description: Optional[str] = Field(
           default=None,
           max_length=1000,
           description="Document description"
       )
       
       document_type: DocumentType = Field(
           ...,
           description="Type of document"
       )
       
       file_name: Optional[str] = Field(
           default=None,
           max_length=255,
           description="Original file name",
           example="gdpr_guide.pdf"
       )
       
       file_size: Optional[int] = Field(
           default=None,
           ge=0,
           description="File size in bytes",
           example=1048576
       )
       
       mime_type: Optional[str] = Field(
           default=None,
           max_length=100,
           description="MIME type of the document",
           example="application/pdf"
       )
       
       language: Optional[str] = Field(
           default=None,
           regex=r'^[a-z]{2}$',
           description="Document language (ISO 639-1 code)",
           example="en"
       )
       
       version: Optional[str] = Field(
           default=None,
           max_length=20,
           description="Document version",
           example="1.2"
       )
       
       is_public: bool = Field(
           default=False,
           description="Whether the document is publicly accessible"
       )

DocumentResponse
~~~~~~~~~~~~~~~~

Complete document response with metadata and relationships.

.. code-block:: python

   class DocumentResponse(DocumentBase, TimestampMixin, SoftDeleteMixin):
       """
       Complete document response schema.
       
       Includes document information plus metadata,
       relationships, and access information.
       """
       
       id: int = Field(
           ...,
           description="Unique document identifier",
           example=1
       )
       
       regulation_id: Optional[int] = Field(
           default=None,
           description="Associated regulation ID"
       )
       
       regulation: Optional['RegulationSummary'] = Field(
           default=None,
           description="Associated regulation information"
       )
       
       download_url: Optional[str] = Field(
           default=None,
           description="URL for downloading the document"
       )
       
       preview_url: Optional[str] = Field(
           default=None,
           description="URL for previewing the document"
       )
       
       checksum: Optional[str] = Field(
           default=None,
           description="File checksum for integrity verification"
       )
       
       download_count: int = Field(
           default=0,
           description="Number of times the document has been downloaded",
           example=42
       )
       
       tags: List['TagSummary'] = Field(
           default=[],
           description="Associated tags"
       )

Organization Schemas
--------------------

OrganizationBase
~~~~~~~~~~~~~~~~

Base schema for organizations (for compliance tracking).

.. code-block:: python

   class OrganizationBase(BaseSchema):
       """
       Base organization schema for compliance tracking.
       
       Organizations represent entities that need to
       comply with regulations and track their compliance status.
       """
       
       name: str = Field(
           ...,
           min_length=1,
           max_length=200,
           description="Organization name",
           example="Acme Corporation"
       )
       
       description: Optional[str] = Field(
           default=None,
           max_length=1000,
           description="Organization description"
       )
       
       organization_type: OrganizationType = Field(
           ...,
           description="Type of organization"
       )
       
       industry_id: Optional[int] = Field(
           default=None,
           description="Primary industry ID"
       )
       
       country_id: Optional[int] = Field(
           default=None,
           description="Primary country of operation"
       )
       
       website: Optional[HttpUrl] = Field(
           default=None,
           description="Organization website"
       )
       
       employee_count: Optional[int] = Field(
           default=None,
           ge=0,
           description="Number of employees"
       )
       
       annual_revenue: Optional[Decimal] = Field(
           default=None,
           ge=0,
           description="Annual revenue"
       )
       
       revenue_currency: Optional[str] = Field(
           default=None,
           regex=r'^[A-Z]{3}$',
           description="Currency code for revenue"
       )

Enums
-----

Governance-related enumerations.

.. code-block:: python

   class TagType(str, Enum):
       """Types of tags for organization."""
       GENERAL = "general"
       REGULATORY = "regulatory"
       TECHNICAL = "technical"
       GEOGRAPHIC = "geographic"
       TEMPORAL = "temporal"
       PRIORITY = "priority"
   
   class DocumentType(str, Enum):
       """Types of documents."""
       REGULATION_TEXT = "regulation_text"
       GUIDANCE = "guidance"
       TEMPLATE = "template"
       CHECKLIST = "checklist"
       POLICY = "policy"
       PROCEDURE = "procedure"
       TRAINING = "training"
       REPORT = "report"
       CERTIFICATE = "certificate"
   
   class OrganizationType(str, Enum):
       """Types of organizations."""
       CORPORATION = "corporation"
       PARTNERSHIP = "partnership"
       LLC = "llc"
       NONPROFIT = "nonprofit"
       GOVERNMENT = "government"
       EDUCATIONAL = "educational"
       HEALTHCARE = "healthcare"
       FINANCIAL = "financial"

Summary Schemas
---------------

Condensed schemas for use in relationships and lists.

.. code-block:: python

   class CategorySummary(BaseSchema):
       """Condensed category information."""
       id: int = Field(..., description="Category ID")
       name: str = Field(..., description="Category name")
       icon: Optional[str] = Field(default=None, description="Category icon")
       color: Optional[str] = Field(default=None, description="Category color")
   
   class TagSummary(BaseSchema):
       """Condensed tag information."""
       id: int = Field(..., description="Tag ID")
       name: str = Field(..., description="Tag name")
       color: Optional[str] = Field(default=None, description="Tag color")
   
   class IndustrySummary(BaseSchema):
       """Condensed industry information."""
       id: int = Field(..., description="Industry ID")
       name: str = Field(..., description="Industry name")
       sector: Optional[str] = Field(default=None, description="Industry sector")
   
   class OrganizationSummary(BaseSchema):
       """Condensed organization information."""
       id: int = Field(..., description="Organization ID")
       name: str = Field(..., description="Organization name")
       organization_type: OrganizationType = Field(..., description="Organization type")
   
   class CategoryWithCount(CategorySummary):
       """Category summary with regulation count."""
       regulation_count: int = Field(..., description="Number of regulations in category")
