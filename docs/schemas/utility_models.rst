Utility Models Schema
=====================

This section documents utility data models for import/export operations, analytics, integrations, and system utilities within the RegulationGuru platform.

.. contents:: Table of Contents
   :local:
   :depth: 2

Import/Export Schemas
---------------------

ImportOperation
~~~~~~~~~~~~~~~

Schema for data import operations.

.. code-block:: python

   class ImportOperation(BaseSchema, TimestampMixin):
       """
       Import operation tracking schema.
       
       Tracks the status and progress of data import operations
       including CSV imports, API imports, and bulk data loads.
       """
       
       import_id: str = Field(
           ...,
           description="Unique import operation identifier",
           example="import_20231201_001"
       )
       
       import_type: ImportType = Field(
           ...,
           description="Type of import operation"
       )
       
       status: ProcessingStatus = Field(
           default=ProcessingStatus.PENDING,
           description="Current status of the import operation"
       )
       
       file_info: Optional['FileInfo'] = Field(
           default=None,
           description="Information about the imported file"
       )
       
       user_id: str = Field(
           ...,
           description="ID of the user who initiated the import",
           example="user_123"
       )
       
       total_rows: Optional[int] = Field(
           default=None,
           ge=0,
           description="Total number of rows to process",
           example=150
       )
       
       processed_rows: int = Field(
           default=0,
           ge=0,
           description="Number of rows processed so far",
           example=75
       )
       
       successful_imports: int = Field(
           default=0,
           ge=0,
           description="Number of successful imports",
           example=70
       )
       
       failed_imports: int = Field(
           default=0,
           ge=0,
           description="Number of failed imports",
           example=5
       )
       
       skipped_rows: int = Field(
           default=0,
           ge=0,
           description="Number of skipped rows (duplicates, etc.)",
           example=8
       )
       
       error_details: List['ImportError'] = Field(
           default=[],
           description="Detailed error information"
       )
       
       started_at: Optional[datetime] = Field(
           default=None,
           description="Import start timestamp"
       )
       
       completed_at: Optional[datetime] = Field(
           default=None,
           description="Import completion timestamp"
       )
       
       estimated_completion: Optional[datetime] = Field(
           default=None,
           description="Estimated completion time"
       )

FileInfo
~~~~~~~~

Information about uploaded files.

.. code-block:: python

   class FileInfo(BaseSchema):
       """
       File information schema.
       
       Contains metadata about uploaded files including
       size, format, and validation information.
       """
       
       filename: str = Field(
           ...,
           description="Original filename",
           example="regulations.csv"
       )
       
       size: int = Field(
           ...,
           ge=0,
           description="File size in bytes",
           example=1048576
       )
       
       mime_type: str = Field(
           ...,
           description="MIME type of the file",
           example="text/csv"
       )
       
       encoding: Optional[str] = Field(
           default=None,
           description="File encoding",
           example="utf-8"
       )
       
       rows: Optional[int] = Field(
           default=None,
           ge=0,
           description="Number of rows in the file",
           example=150
       )
       
       columns: Optional[int] = Field(
           default=None,
           ge=0,
           description="Number of columns in the file",
           example=12
       )
       
       checksum: Optional[str] = Field(
           default=None,
           description="File checksum for integrity verification"
       )

ImportError
~~~~~~~~~~~

Detailed error information for import operations.

.. code-block:: python

   class ImportError(BaseSchema):
       """
       Import error details.
       
       Provides detailed information about errors
       that occur during import operations.
       """
       
       row_number: Optional[int] = Field(
           default=None,
           ge=1,
           description="Row number where the error occurred",
           example=15
       )
       
       column: Optional[str] = Field(
           default=None,
           description="Column name where the error occurred",
           example="country_code"
       )
       
       error_code: str = Field(
           ...,
           description="Machine-readable error code",
           example="INVALID_COUNTRY_CODE"
       )
       
       error_message: str = Field(
           ...,
           description="Human-readable error message",
           example="Invalid country code: 'XX'"
       )
       
       provided_value: Optional[str] = Field(
           default=None,
           description="The value that caused the error",
           example="XX"
       )
       
       suggested_value: Optional[str] = Field(
           default=None,
           description="Suggested correct value",
           example="US"
       )
       
       row_data: Optional[Dict[str, Any]] = Field(
           default=None,
           description="Complete row data for context"
       )

ExportOperation
~~~~~~~~~~~~~~~

Schema for data export operations.

.. code-block:: python

   class ExportOperation(BaseSchema, TimestampMixin):
       """
       Export operation tracking schema.
       
       Tracks the status and progress of data export operations
       including CSV exports, JSON exports, and report generation.
       """
       
       export_id: str = Field(
           ...,
           description="Unique export operation identifier",
           example="export_20231201_001"
       )
       
       export_type: ExportType = Field(
           ...,
           description="Type of export operation"
       )
       
       format: ExportFormat = Field(
           ...,
           description="Export file format"
       )
       
       status: ProcessingStatus = Field(
           default=ProcessingStatus.PENDING,
           description="Current status of the export operation"
       )
       
       user_id: str = Field(
           ...,
           description="ID of the user who requested the export"
       )
       
       filters: Optional[Dict[str, Any]] = Field(
           default=None,
           description="Filters applied to the export"
       )
       
       fields: Optional[List[str]] = Field(
           default=None,
           description="Specific fields to include in export"
       )
       
       estimated_records: Optional[int] = Field(
           default=None,
           description="Estimated number of records to export"
       )
       
       actual_records: Optional[int] = Field(
           default=None,
           description="Actual number of records exported"
       )
       
       file_size: Optional[int] = Field(
           default=None,
           description="Size of the generated export file"
       )
       
       download_url: Optional[str] = Field(
           default=None,
           description="URL for downloading the export file"
       )
       
       expires_at: Optional[datetime] = Field(
           default=None,
           description="Expiration time for the download URL"
       )

Analytics Schemas
-----------------

AnalyticsQuery
~~~~~~~~~~~~~~

Schema for analytics queries and reports.

.. code-block:: python

   class AnalyticsQuery(BaseSchema):
       """
       Analytics query schema.
       
       Defines parameters for analytics queries including
       metrics, dimensions, filters, and time ranges.
       """
       
       query_name: str = Field(
           ...,
           description="Name of the analytics query",
           example="Regulation Trends by Country"
       )
       
       metrics: List[str] = Field(
           ...,
           min_items=1,
           description="Metrics to calculate",
           example=["regulation_count", "compliance_score"]
       )
       
       dimensions: List[str] = Field(
           default=[],
           description="Dimensions to group by",
           example=["country", "category", "month"]
       )
       
       filters: Optional[Dict[str, Any]] = Field(
           default=None,
           description="Filters to apply to the data"
       )
       
       date_range: Optional['DateRange'] = Field(
           default=None,
           description="Date range for the analysis"
       )
       
       aggregation: AggregationType = Field(
           default=AggregationType.SUM,
           description="Aggregation method for metrics"
       )
       
       limit: Optional[int] = Field(
           default=None,
           ge=1,
           le=10000,
           description="Maximum number of results to return"
       )

DateRange
~~~~~~~~~

Date range specification for analytics.

.. code-block:: python

   class DateRange(BaseSchema):
       """
       Date range schema for analytics queries.
       
       Specifies time periods for analytics calculations
       and trend analysis.
       """
       
       start_date: date = Field(
           ...,
           description="Start date of the range",
           example="2023-01-01"
       )
       
       end_date: date = Field(
           ...,
           description="End date of the range",
           example="2023-12-31"
       )
       
       granularity: TimeGranularity = Field(
           default=TimeGranularity.MONTH,
           description="Time granularity for aggregation"
       )

AnalyticsResult
~~~~~~~~~~~~~~~

Result of analytics queries.

.. code-block:: python

   class AnalyticsResult(BaseSchema):
       """
       Analytics result schema.
       
       Contains the results of analytics queries including
       data points, metadata, and visualization hints.
       """
       
       query_id: str = Field(
           ...,
           description="Unique identifier for the query"
       )
       
       data: List[Dict[str, Any]] = Field(
           ...,
           description="Query result data"
       )
       
       metadata: 'AnalyticsMetadata' = Field(
           ...,
           description="Query metadata and statistics"
       )
       
       visualization_hints: Optional[Dict[str, Any]] = Field(
           default=None,
           description="Suggestions for data visualization"
       )

AnalyticsMetadata
~~~~~~~~~~~~~~~~~

Metadata for analytics results.

.. code-block:: python

   class AnalyticsMetadata(BaseSchema):
       """
       Analytics metadata schema.
       
       Provides information about query execution,
       data quality, and result characteristics.
       """
       
       execution_time: float = Field(
           ...,
           description="Query execution time in seconds",
           example=0.125
       )
       
       total_records: int = Field(
           ...,
           description="Total number of records processed"
       )
       
       returned_records: int = Field(
           ...,
           description="Number of records returned"
       )
       
       data_freshness: datetime = Field(
           ...,
           description="Timestamp of the most recent data used"
       )
       
       confidence_score: Optional[float] = Field(
           default=None,
           ge=0.0,
           le=1.0,
           description="Confidence score for the results"
       )

Integration Schemas
-------------------

IntegrationConfig
~~~~~~~~~~~~~~~~~

Configuration for external integrations.

.. code-block:: python

   class IntegrationConfig(BaseSchema, TimestampMixin):
       """
       Integration configuration schema.
       
       Defines configuration for external system integrations
       including API connections, data sources, and sync settings.
       """
       
       name: str = Field(
           ...,
           description="Integration name",
           example="SEC EDGAR Integration"
       )
       
       integration_type: IntegrationType = Field(
           ...,
           description="Type of integration"
       )
       
       provider: str = Field(
           ...,
           description="Integration provider or service",
           example="SEC"
       )
       
       endpoint_url: Optional[HttpUrl] = Field(
           default=None,
           description="API endpoint URL"
       )
       
       authentication: Optional[Dict[str, Any]] = Field(
           default=None,
           description="Authentication configuration (encrypted)"
       )
       
       sync_frequency: SyncFrequency = Field(
           default=SyncFrequency.DAILY,
           description="How often to sync data"
       )
       
       is_active: bool = Field(
           default=True,
           description="Whether the integration is active"
       )
       
       last_sync: Optional[datetime] = Field(
           default=None,
           description="Last successful sync timestamp"
       )
       
       next_sync: Optional[datetime] = Field(
           default=None,
           description="Next scheduled sync timestamp"
       )

SyncOperation
~~~~~~~~~~~~~

Schema for integration sync operations.

.. code-block:: python

   class SyncOperation(BaseSchema, TimestampMixin):
       """
       Sync operation tracking schema.
       
       Tracks the progress and results of data synchronization
       operations with external systems.
       """
       
       sync_id: str = Field(
           ...,
           description="Unique sync operation identifier"
       )
       
       integration_id: int = Field(
           ...,
           description="ID of the associated integration"
       )
       
       status: ProcessingStatus = Field(
           default=ProcessingStatus.PENDING,
           description="Current sync status"
       )
       
       sync_type: SyncType = Field(
           ...,
           description="Type of sync operation"
       )
       
       records_processed: int = Field(
           default=0,
           description="Number of records processed"
       )
       
       records_created: int = Field(
           default=0,
           description="Number of new records created"
       )
       
       records_updated: int = Field(
           default=0,
           description="Number of existing records updated"
       )
       
       records_failed: int = Field(
           default=0,
           description="Number of records that failed to sync"
       )
       
       error_summary: Optional[str] = Field(
           default=None,
           description="Summary of sync errors"
       )

System Schemas
--------------

HealthCheck
~~~~~~~~~~~

System health check schema.

.. code-block:: python

   class HealthCheck(BaseSchema):
       """
       System health check schema.
       
       Provides comprehensive health information about
       the system and its components.
       """
       
       status: HealthStatus = Field(
           ...,
           description="Overall system health status"
       )
       
       timestamp: datetime = Field(
           ...,
           description="Health check timestamp"
       )
       
       database: ComponentHealth = Field(
           ...,
           description="Database health status"
       )
       
       services: Dict[str, ComponentHealth] = Field(
           default={},
           description="Individual service health statuses"
       )
       
       metrics: Optional[Dict[str, Any]] = Field(
           default=None,
           description="System metrics and statistics"
       )
       
       test_coverage: Optional[Dict[str, float]] = Field(
           default=None,
           description="Test coverage information"
       )

ComponentHealth
~~~~~~~~~~~~~~~

Health status for individual system components.

.. code-block:: python

   class ComponentHealth(BaseSchema):
       """
       Component health schema.
       
       Provides health information for individual
       system components and services.
       """
       
       status: HealthStatus = Field(
           ...,
           description="Component health status"
       )
       
       response_time: Optional[str] = Field(
           default=None,
           description="Component response time",
           example="2ms"
       )
       
       details: Optional[Dict[str, Any]] = Field(
           default=None,
           description="Additional component details"
       )
       
       last_check: datetime = Field(
           ...,
           description="Last health check timestamp"
       )

Enums
-----

Utility-related enumerations.

.. code-block:: python

   class ImportType(str, Enum):
       """Types of import operations."""
       CSV_REGULATIONS = "csv_regulations"
       JSON_BULK = "json_bulk"
       API_SYNC = "api_sync"
       MANUAL_ENTRY = "manual_entry"
   
   class ExportType(str, Enum):
       """Types of export operations."""
       REGULATIONS = "regulations"
       COMPLIANCE_REPORT = "compliance_report"
       ANALYTICS_DATA = "analytics_data"
       AUDIT_LOG = "audit_log"
   
   class ExportFormat(str, Enum):
       """Export file formats."""
       CSV = "csv"
       JSON = "json"
       EXCEL = "excel"
       PDF = "pdf"
   
   class AggregationType(str, Enum):
       """Analytics aggregation types."""
       SUM = "sum"
       COUNT = "count"
       AVERAGE = "average"
       MIN = "min"
       MAX = "max"
   
   class TimeGranularity(str, Enum):
       """Time granularity for analytics."""
       DAY = "day"
       WEEK = "week"
       MONTH = "month"
       QUARTER = "quarter"
       YEAR = "year"
   
   class IntegrationType(str, Enum):
       """Types of external integrations."""
       API = "api"
       DATABASE = "database"
       FILE_SYNC = "file_sync"
       WEBHOOK = "webhook"
   
   class SyncFrequency(str, Enum):
       """Sync frequency options."""
       REAL_TIME = "real_time"
       HOURLY = "hourly"
       DAILY = "daily"
       WEEKLY = "weekly"
       MONTHLY = "monthly"
       MANUAL = "manual"
   
   class SyncType(str, Enum):
       """Types of sync operations."""
       FULL = "full"
       INCREMENTAL = "incremental"
       DELTA = "delta"
   
   class HealthStatus(str, Enum):
       """Health status values."""
       HEALTHY = "healthy"
       DEGRADED = "degraded"
       UNHEALTHY = "unhealthy"
       UNKNOWN = "unknown"
