Schema Reference Overview
=========================

The RegulationGuru API uses Pydantic models for request/response validation and serialization. This section provides comprehensive documentation of all data schemas used throughout the platform.

.. contents:: Table of Contents
   :local:
   :depth: 2

Schema Organization
-------------------

Schemas are organized into logical groups based on their functionality:

Core Models
~~~~~~~~~~~

* **Base Models**: Common base classes and mixins
* **User Models**: User authentication and profile schemas
* **System Models**: Health checks, configuration, and system status

Regulation Models
~~~~~~~~~~~~~~~~~

* **Regulation Schemas**: Core regulation data structures
* **Regulator Schemas**: Regulatory authority information
* **Country Schemas**: Geographic and jurisdictional data
* **URL Schemas**: Regulation source URLs and links

Compliance Models
~~~~~~~~~~~~~~~~~

* **Compliance Requirement Schemas**: Compliance tracking and requirements
* **Assessment Schemas**: Compliance assessments and evaluations
* **Calendar Schemas**: Compliance calendar and deadlines

Governance Models
~~~~~~~~~~~~~~~~~

* **Category Schemas**: Regulation categorization
* **Tag Schemas**: Tagging and labeling systems
* **Industry Schemas**: Industry classification
* **Document Schemas**: Document management

Utility Models
~~~~~~~~~~~~~~

* **Import/Export Schemas**: Data import and export operations
* **Analytics Schemas**: Reporting and analytics data
* **Integration Schemas**: External system integrations
* **Validation Schemas**: Data validation and error handling

Common Patterns
---------------

Field Types
~~~~~~~~~~~

The API uses consistent field types across all schemas:

.. code-block:: python

   # Standard field types
   id: int                           # Primary keys
   name: str                         # Names and titles
   description: Optional[str]        # Optional descriptions
   created_at: datetime             # Creation timestamps
   updated_at: datetime             # Update timestamps
   is_active: bool = True           # Active/inactive flags
   
   # Validation patterns
   email: EmailStr                  # Email validation
   url: HttpUrl                     # URL validation
   country_code: str = Field(regex=r'^[A-Z]{2}$')  # ISO country codes

Pagination Schema
~~~~~~~~~~~~~~~~~

List endpoints use consistent pagination:

.. code-block:: python

   class PaginationInfo(BaseModel):
       total: int = Field(..., description="Total number of items")
       page: int = Field(..., description="Current page number")
       per_page: int = Field(..., description="Items per page")
       pages: int = Field(..., description="Total number of pages")
       has_next: bool = Field(..., description="Whether there is a next page")
       has_prev: bool = Field(..., description="Whether there is a previous page")

Response Wrapper
~~~~~~~~~~~~~~~~

All API responses use a consistent wrapper:

.. code-block:: python

   class APIResponse(BaseModel, Generic[T]):
       data: T = Field(..., description="Response data")
       message: str = Field(default="Success", description="Response message")
       status: int = Field(..., description="HTTP status code")
       metadata: Optional[Dict[str, Any]] = Field(default=None, description="Additional metadata")
       pagination: Optional[PaginationInfo] = Field(default=None, description="Pagination info for list responses")

Error Schema
~~~~~~~~~~~~

Error responses follow a standard format:

.. code-block:: python

   class ErrorDetail(BaseModel):
       code: str = Field(..., description="Error code")
       message: str = Field(..., description="Human-readable error message")
       details: Optional[Dict[str, Any]] = Field(default=None, description="Additional error details")
       field: Optional[str] = Field(default=None, description="Field that caused the error")

   class ErrorResponse(BaseModel):
       error: ErrorDetail = Field(..., description="Error information")
       status: int = Field(..., description="HTTP status code")
       timestamp: datetime = Field(..., description="Error timestamp")
       request_id: Optional[str] = Field(default=None, description="Request ID for tracking")

Validation Rules
----------------

Common Validation Patterns
~~~~~~~~~~~~~~~~~~~~~~~~~~~

The API enforces consistent validation rules:

**String Fields**

.. code-block:: python

   # Title fields
   title: str = Field(..., min_length=1, max_length=500, description="Title")
   
   # Description fields
   description: Optional[str] = Field(default=None, max_length=2000, description="Description")
   
   # Reference numbers
   reference_number: Optional[str] = Field(default=None, max_length=100, description="Reference number")

**Numeric Fields**

.. code-block:: python

   # Confidence levels
   confidence_level: float = Field(..., ge=0.0, le=1.0, description="Confidence level (0.0-1.0)")
   
   # Priority levels
   priority: int = Field(..., ge=1, le=5, description="Priority level (1-5)")

**Date Fields**

.. code-block:: python

   # ISO 8601 datetime with timezone
   created_at: datetime = Field(..., description="Creation timestamp (ISO 8601)")
   
   # Date only
   effective_date: Optional[date] = Field(default=None, description="Effective date")

**Enum Fields**

.. code-block:: python

   class RegulationStatus(str, Enum):
       ACTIVE = "active"
       DRAFT = "draft"
       ARCHIVED = "archived"
       PENDING = "pending"
   
   status: RegulationStatus = Field(default=RegulationStatus.ACTIVE, description="Regulation status")

Custom Validators
~~~~~~~~~~~~~~~~~

The API includes custom validators for complex validation:

.. code-block:: python

   @validator('email')
   def validate_email(cls, v):
       if v and not re.match(r'^[^@]+@[^@]+\.[^@]+$', v):
           raise ValueError('Invalid email format')
       return v
   
   @validator('country_code')
   def validate_country_code(cls, v):
       if v and len(v) != 2:
           raise ValueError('Country code must be 2 characters')
       return v.upper() if v else v

Schema Inheritance
------------------

Base Schema Classes
~~~~~~~~~~~~~~~~~~~

All schemas inherit from common base classes:

.. code-block:: python

   class BaseSchema(BaseModel):
       """Base schema with common configuration"""
       
       class Config:
           # Enable ORM mode for SQLAlchemy integration
           orm_mode = True
           # Allow population by field name or alias
           allow_population_by_field_name = True
           # Validate assignment
           validate_assignment = True
           # Use enum values
           use_enum_values = True

   class TimestampMixin(BaseModel):
       """Mixin for timestamp fields"""
       created_at: datetime = Field(..., description="Creation timestamp")
       updated_at: datetime = Field(..., description="Last update timestamp")

   class SoftDeleteMixin(BaseModel):
       """Mixin for soft delete functionality"""
       is_deleted: bool = Field(default=False, description="Soft delete flag")
       deleted_at: Optional[datetime] = Field(default=None, description="Deletion timestamp")
       deleted_by: Optional[str] = Field(default=None, description="User who deleted the record")

Documentation Standards
-----------------------

Field Documentation
~~~~~~~~~~~~~~~~~~~

All schema fields include comprehensive documentation:

.. code-block:: python

   class RegulationSchema(BaseSchema):
       id: int = Field(..., description="Unique regulation identifier")
       title: str = Field(
           ..., 
           min_length=1, 
           max_length=500,
           description="Regulation title or name",
           example="General Data Protection Regulation"
       )
       description: Optional[str] = Field(
           default=None,
           max_length=2000,
           description="Detailed description of the regulation",
           example="EU regulation on data protection and privacy for individuals within the EU"
       )

Schema Examples
~~~~~~~~~~~~~~~

Each schema includes realistic examples:

.. code-block:: python

   class Config:
       schema_extra = {
           "example": {
               "id": 1,
               "title": "General Data Protection Regulation",
               "description": "EU regulation on data protection and privacy",
               "status": "active",
               "effective_date": "2018-05-25",
               "confidence_level": 0.95
           }
       }

Version Compatibility
---------------------

Schema Versioning
~~~~~~~~~~~~~~~~~

Schemas are versioned to maintain backward compatibility:

* **v1**: Current stable version
* **v2**: Future version (in development)

Breaking changes are introduced only in major version updates, with appropriate migration paths provided.

Deprecation Policy
~~~~~~~~~~~~~~~~~~

Deprecated fields are marked and supported for at least one major version:

.. code-block:: python

   # Deprecated field
   old_field: Optional[str] = Field(
       default=None,
       deprecated=True,
       description="DEPRECATED: Use new_field instead"
   )
   
   # New field
   new_field: Optional[str] = Field(
       default=None,
       description="Replacement for old_field"
   )
