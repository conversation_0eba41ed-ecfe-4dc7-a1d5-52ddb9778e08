Compliance Models Schema
========================

This section documents the data models for compliance requirements, assessments, and tracking within the RegulationGuru platform.

.. contents:: Table of Contents
   :local:
   :depth: 2

Compliance Requirement Schemas
------------------------------

ComplianceRequirementBase
~~~~~~~~~~~~~~~~~~~~~~~~~

Base schema for compliance requirements.

.. code-block:: python

   class ComplianceRequirementBase(BaseSchema):
       """
       Base compliance requirement schema.
       
       Defines the core structure for compliance requirements
       that organizations must meet to satisfy regulations.
       """
       
       title: str = Field(
           ...,
           min_length=1,
           max_length=300,
           description="Compliance requirement title",
           example="Data Subject Access Request Response"
       )
       
       description: Optional[str] = Field(
           default=None,
           max_length=2000,
           description="Detailed description of the requirement",
           example="Organizations must respond to data subject access requests within 30 days"
       )
       
       requirement_type: ComplianceType = Field(
           ...,
           description="Type of compliance requirement"
       )
       
       priority: Priority = Field(
           default=Priority.MEDIUM,
           description="Priority level of the requirement"
       )
       
       deadline: Optional[date] = Field(
           default=None,
           description="Deadline for compliance (if applicable)",
           example="2024-01-01"
       )
       
       frequency: Optional[ComplianceFrequency] = Field(
           default=None,
           description="How often the requirement must be met"
       )
       
       penalty_description: Optional[str] = Field(
           default=None,
           max_length=1000,
           description="Description of penalties for non-compliance"
       )
       
       penalty_amount: Optional[Decimal] = Field(
           default=None,
           ge=0,
           description="Maximum penalty amount",
           example=20000000
       )
       
       penalty_currency: Optional[str] = Field(
           default=None,
           regex=r'^[A-Z]{3}$',
           description="Currency code for penalty amount",
           example="EUR"
       )
       
       is_mandatory: bool = Field(
           default=True,
           description="Whether compliance is mandatory or optional"
       )

ComplianceRequirementCreate
~~~~~~~~~~~~~~~~~~~~~~~~~~~

Schema for creating new compliance requirements.

.. code-block:: python

   class ComplianceRequirementCreate(ComplianceRequirementBase):
       """Schema for creating new compliance requirements."""
       
       regulation_id: int = Field(
           ...,
           description="ID of the associated regulation",
           example=1
       )
       
       category_ids: Optional[List[int]] = Field(
           default=None,
           description="List of category IDs for classification",
           example=[1, 2]
       )
       
       industry_ids: Optional[List[int]] = Field(
           default=None,
           description="List of industry IDs this requirement applies to",
           example=[1, 3, 5]
       )

ComplianceRequirementResponse
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Complete compliance requirement response with related data.

.. code-block:: python

   class ComplianceRequirementResponse(ComplianceRequirementBase, TimestampMixin, SoftDeleteMixin):
       """
       Complete compliance requirement response schema.
       
       Includes all requirement data plus related entities
       and compliance tracking information.
       """
       
       id: int = Field(
           ...,
           description="Unique compliance requirement identifier",
           example=1
       )
       
       regulation: 'RegulationSummary' = Field(
           ...,
           description="Associated regulation information"
       )
       
       categories: List['CategorySummary'] = Field(
           default=[],
           description="Associated categories"
       )
       
       industries: List['IndustrySummary'] = Field(
           default=[],
           description="Applicable industries"
       )
       
       assessments: List['ComplianceAssessmentSummary'] = Field(
           default=[],
           description="Related compliance assessments"
       )
       
       compliance_status: ComplianceStatus = Field(
           default=ComplianceStatus.NOT_ASSESSED,
           description="Current compliance status"
       )
       
       last_assessment_date: Optional[datetime] = Field(
           default=None,
           description="Date of last compliance assessment"
       )
       
       next_review_date: Optional[date] = Field(
           default=None,
           description="Date when next review is due"
       )

Compliance Assessment Schemas
-----------------------------

ComplianceAssessmentBase
~~~~~~~~~~~~~~~~~~~~~~~~

Base schema for compliance assessments.

.. code-block:: python

   class ComplianceAssessmentBase(BaseSchema):
       """
       Base compliance assessment schema.
       
       Defines the structure for assessments that evaluate
       an organization's compliance with specific requirements.
       """
       
       assessment_name: str = Field(
           ...,
           min_length=1,
           max_length=200,
           description="Name of the compliance assessment",
           example="Q4 2023 GDPR Compliance Review"
       )
       
       assessment_type: AssessmentType = Field(
           ...,
           description="Type of assessment being performed"
       )
       
       scope: Optional[str] = Field(
           default=None,
           max_length=1000,
           description="Scope and boundaries of the assessment",
           example="Data processing activities for EU customers"
       )
       
       methodology: Optional[str] = Field(
           default=None,
           max_length=500,
           description="Assessment methodology used",
           example="ISO 27001 based audit framework"
       )
       
       assessment_date: date = Field(
           ...,
           description="Date when the assessment was conducted",
           example="2023-12-01"
       )
       
       assessor_name: Optional[str] = Field(
           default=None,
           max_length=100,
           description="Name of the person/organization conducting the assessment"
       )
       
       overall_status: ComplianceStatus = Field(
           ...,
           description="Overall compliance status from the assessment"
       )
       
       compliance_score: Optional[float] = Field(
           default=None,
           ge=0.0,
           le=100.0,
           description="Numerical compliance score (0-100)",
           example=85.5
       )
       
       findings_summary: Optional[str] = Field(
           default=None,
           max_length=2000,
           description="Summary of key findings from the assessment"
       )
       
       recommendations: Optional[str] = Field(
           default=None,
           max_length=2000,
           description="Recommendations for improving compliance"
       )

ComplianceAssessmentCreate
~~~~~~~~~~~~~~~~~~~~~~~~~~

Schema for creating new compliance assessments.

.. code-block:: python

   class ComplianceAssessmentCreate(ComplianceAssessmentBase):
       """Schema for creating new compliance assessments."""
       
       requirement_ids: List[int] = Field(
           ...,
           min_items=1,
           description="List of compliance requirement IDs being assessed",
           example=[1, 2, 3]
       )
       
       organization_id: Optional[int] = Field(
           default=None,
           description="ID of the organization being assessed"
       )

ComplianceAssessmentResponse
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Complete compliance assessment response.

.. code-block:: python

   class ComplianceAssessmentResponse(ComplianceAssessmentBase, TimestampMixin):
       """
       Complete compliance assessment response schema.
       
       Includes all assessment data plus related requirements
       and detailed findings.
       """
       
       id: int = Field(
           ...,
           description="Unique assessment identifier",
           example=1
       )
       
       requirements: List['ComplianceRequirementSummary'] = Field(
           default=[],
           description="Compliance requirements assessed"
       )
       
       organization: Optional['OrganizationSummary'] = Field(
           default=None,
           description="Organization that was assessed"
       )
       
       detailed_findings: List['AssessmentFinding'] = Field(
           default=[],
           description="Detailed findings for each requirement"
       )
       
       action_items: List['ActionItem'] = Field(
           default=[],
           description="Action items to address compliance gaps"
       )
       
       next_assessment_date: Optional[date] = Field(
           default=None,
           description="Recommended date for next assessment"
       )

Assessment Finding Schemas
--------------------------

AssessmentFinding
~~~~~~~~~~~~~~~~~

Individual finding within a compliance assessment.

.. code-block:: python

   class AssessmentFinding(BaseSchema):
       """
       Individual finding within a compliance assessment.
       
       Represents a specific finding related to a compliance
       requirement during an assessment.
       """
       
       requirement_id: int = Field(
           ...,
           description="ID of the compliance requirement",
           example=1
       )
       
       finding_type: FindingType = Field(
           ...,
           description="Type of finding (compliant, non-compliant, etc.)"
       )
       
       severity: FindingSeverity = Field(
           default=FindingSeverity.MEDIUM,
           description="Severity level of the finding"
       )
       
       description: str = Field(
           ...,
           min_length=1,
           max_length=1000,
           description="Detailed description of the finding",
           example="Data retention policy not clearly documented"
       )
       
       evidence: Optional[str] = Field(
           default=None,
           max_length=2000,
           description="Evidence supporting the finding"
       )
       
       recommendation: Optional[str] = Field(
           default=None,
           max_length=1000,
           description="Specific recommendation to address the finding"
       )
       
       due_date: Optional[date] = Field(
           default=None,
           description="Due date for addressing the finding"
       )
       
       assigned_to: Optional[str] = Field(
           default=None,
           description="Person responsible for addressing the finding"
       )

ActionItem
~~~~~~~~~~

Action item to address compliance gaps.

.. code-block:: python

   class ActionItem(BaseSchema):
       """
       Action item to address compliance gaps.
       
       Represents a specific action that needs to be taken
       to improve compliance status.
       """
       
       title: str = Field(
           ...,
           min_length=1,
           max_length=200,
           description="Action item title",
           example="Update data retention policy"
       )
       
       description: Optional[str] = Field(
           default=None,
           max_length=1000,
           description="Detailed description of the action required"
       )
       
       priority: Priority = Field(
           default=Priority.MEDIUM,
           description="Priority level of the action item"
       )
       
       status: ActionStatus = Field(
           default=ActionStatus.OPEN,
           description="Current status of the action item"
       )
       
       due_date: Optional[date] = Field(
           default=None,
           description="Due date for completing the action"
       )
       
       assigned_to: Optional[str] = Field(
           default=None,
           description="Person responsible for the action"
       )
       
       estimated_effort: Optional[str] = Field(
           default=None,
           description="Estimated effort required",
           example="2-3 days"
       )

Compliance Calendar Schemas
---------------------------

ComplianceEvent
~~~~~~~~~~~~~~~

Calendar event for compliance deadlines and activities.

.. code-block:: python

   class ComplianceEvent(BaseSchema):
       """
       Compliance calendar event.
       
       Represents important compliance dates, deadlines,
       and scheduled activities.
       """
       
       title: str = Field(
           ...,
           min_length=1,
           max_length=200,
           description="Event title",
           example="GDPR Annual Review Due"
       )
       
       description: Optional[str] = Field(
           default=None,
           max_length=1000,
           description="Event description"
       )
       
       event_type: EventType = Field(
           ...,
           description="Type of compliance event"
       )
       
       start_date: date = Field(
           ...,
           description="Event start date",
           example="2024-01-15"
       )
       
       end_date: Optional[date] = Field(
           default=None,
           description="Event end date (for multi-day events)"
       )
       
       is_deadline: bool = Field(
           default=False,
           description="Whether this event represents a deadline"
       )
       
       reminder_days: List[int] = Field(
           default=[7, 1],
           description="Days before event to send reminders",
           example=[30, 7, 1]
       )
       
       requirement_id: Optional[int] = Field(
           default=None,
           description="Associated compliance requirement ID"
       )
       
       regulation_id: Optional[int] = Field(
           default=None,
           description="Associated regulation ID"
       )

Enums
-----

Compliance-related enumerations.

.. code-block:: python

   class ComplianceType(str, Enum):
       """Types of compliance requirements."""
       REPORTING = "reporting"
       DOCUMENTATION = "documentation"
       TRAINING = "training"
       TECHNICAL = "technical"
       PROCEDURAL = "procedural"
       AUDIT = "audit"
   
   class ComplianceFrequency(str, Enum):
       """Frequency of compliance activities."""
       ONCE = "once"
       DAILY = "daily"
       WEEKLY = "weekly"
       MONTHLY = "monthly"
       QUARTERLY = "quarterly"
       ANNUALLY = "annually"
       AS_NEEDED = "as_needed"
   
   class ComplianceStatus(str, Enum):
       """Compliance status values."""
       COMPLIANT = "compliant"
       NON_COMPLIANT = "non_compliant"
       PARTIALLY_COMPLIANT = "partially_compliant"
       NOT_ASSESSED = "not_assessed"
       IN_PROGRESS = "in_progress"
   
   class AssessmentType(str, Enum):
       """Types of compliance assessments."""
       SELF_ASSESSMENT = "self_assessment"
       INTERNAL_AUDIT = "internal_audit"
       EXTERNAL_AUDIT = "external_audit"
       REGULATORY_REVIEW = "regulatory_review"
       THIRD_PARTY = "third_party"
   
   class FindingType(str, Enum):
       """Types of assessment findings."""
       COMPLIANT = "compliant"
       NON_COMPLIANT = "non_compliant"
       OBSERVATION = "observation"
       BEST_PRACTICE = "best_practice"
   
   class FindingSeverity(str, Enum):
       """Severity levels for findings."""
       LOW = "low"
       MEDIUM = "medium"
       HIGH = "high"
       CRITICAL = "critical"
   
   class ActionStatus(str, Enum):
       """Status of action items."""
       OPEN = "open"
       IN_PROGRESS = "in_progress"
       COMPLETED = "completed"
       CANCELLED = "cancelled"
       OVERDUE = "overdue"
   
   class EventType(str, Enum):
       """Types of compliance events."""
       DEADLINE = "deadline"
       REVIEW = "review"
       TRAINING = "training"
       AUDIT = "audit"
       REPORTING = "reporting"
       RENEWAL = "renewal"
