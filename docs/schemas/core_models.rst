Core Models Schema
==================

This section documents the core data models that form the foundation of the RegulationGuru platform.

.. contents:: Table of Contents
   :local:
   :depth: 2

Base Models
-----------

BaseSchema
~~~~~~~~~~

The foundational schema class that all other schemas inherit from.

.. code-block:: python

   class BaseSchema(BaseModel):
       """
       Base schema with common configuration and validation rules.
       
       Provides consistent behavior across all API schemas including:
       - ORM mode for SQLAlchemy integration
       - Field name and alias population
       - Assignment validation
       - Enum value usage
       """
       
       class Config:
           orm_mode = True
           allow_population_by_field_name = True
           validate_assignment = True
           use_enum_values = True
           json_encoders = {
               datetime: lambda v: v.isoformat() if v else None,
               date: lambda v: v.isoformat() if v else None
           }

**Configuration Options:**

* ``orm_mode``: Enables compatibility with SQLAlchemy ORM objects
* ``allow_population_by_field_name``: Allows using both field names and aliases
* ``validate_assignment``: Validates data when assigning to model fields
* ``use_enum_values``: Uses enum values instead of enum objects in serialization

TimestampMixin
~~~~~~~~~~~~~~

Provides standard timestamp fields for tracking creation and modification times.

.. code-block:: python

   class TimestampMixin(BaseModel):
       """
       Mixin providing standard timestamp fields.
       
       Used by models that need to track creation and modification times.
       All timestamps are in UTC and follow ISO 8601 format.
       """
       
       created_at: datetime = Field(
           ...,
           description="Record creation timestamp (UTC)",
           example="2023-12-01T10:00:00Z"
       )
       
       updated_at: datetime = Field(
           ...,
           description="Last modification timestamp (UTC)",
           example="2023-12-01T14:30:00Z"
       )

**Field Details:**

* ``created_at``: Automatically set when the record is created
* ``updated_at``: Automatically updated whenever the record is modified
* Both fields use UTC timezone and ISO 8601 format

SoftDeleteMixin
~~~~~~~~~~~~~~~

Provides soft delete functionality to preserve data while marking it as deleted.

.. code-block:: python

   class SoftDeleteMixin(BaseModel):
       """
       Mixin providing soft delete functionality.
       
       Allows marking records as deleted without actually removing them
       from the database, preserving data integrity and audit trails.
       """
       
       is_deleted: bool = Field(
           default=False,
           description="Indicates if the record is soft deleted"
       )
       
       deleted_at: Optional[datetime] = Field(
           default=None,
           description="Timestamp when the record was deleted (UTC)",
           example="2023-12-01T16:00:00Z"
       )
       
       deleted_by: Optional[str] = Field(
           default=None,
           description="Identifier of the user who deleted the record",
           example="user_123"
       )

**Field Details:**

* ``is_deleted``: Boolean flag indicating deletion status
* ``deleted_at``: Timestamp of deletion (null if not deleted)
* ``deleted_by``: User identifier who performed the deletion

Response Models
---------------

APIResponse
~~~~~~~~~~~

Generic response wrapper used by all API endpoints.

.. code-block:: python

   from typing import Generic, TypeVar, Optional, Dict, Any
   
   T = TypeVar('T')
   
   class APIResponse(BaseModel, Generic[T]):
       """
       Generic API response wrapper.
       
       Provides consistent response structure across all endpoints
       with support for data, metadata, and pagination information.
       """
       
       data: T = Field(
           ...,
           description="Response data of the specified type"
       )
       
       message: str = Field(
           default="Success",
           description="Human-readable response message",
           example="Operation completed successfully"
       )
       
       status: int = Field(
           ...,
           description="HTTP status code",
           example=200
       )
       
       metadata: Optional[Dict[str, Any]] = Field(
           default=None,
           description="Additional response metadata",
           example={
               "timestamp": "2023-12-01T10:00:00Z",
               "version": "1.0.0",
               "request_id": "req_123456"
           }
       )
       
       pagination: Optional['PaginationInfo'] = Field(
           default=None,
           description="Pagination information for list responses"
       )

**Usage Examples:**

.. code-block:: python

   # Single item response
   APIResponse[RegulationSchema]
   
   # List response
   APIResponse[List[RegulationSchema]]
   
   # Custom data response
   APIResponse[Dict[str, Any]]

PaginationInfo
~~~~~~~~~~~~~~

Pagination metadata for list responses.

.. code-block:: python

   class PaginationInfo(BaseModel):
       """
       Pagination information for list responses.
       
       Provides comprehensive pagination metadata to help clients
       navigate through large datasets efficiently.
       """
       
       total: int = Field(
           ...,
           description="Total number of items across all pages",
           example=1250,
           ge=0
       )
       
       page: int = Field(
           ...,
           description="Current page number (1-based)",
           example=2,
           ge=1
       )
       
       per_page: int = Field(
           ...,
           description="Number of items per page",
           example=20,
           ge=1,
           le=100
       )
       
       pages: int = Field(
           ...,
           description="Total number of pages",
           example=63,
           ge=0
       )
       
       has_next: bool = Field(
           ...,
           description="Whether there is a next page available",
           example=True
       )
       
       has_prev: bool = Field(
           ...,
           description="Whether there is a previous page available",
           example=True
       )
       
       next_page: Optional[int] = Field(
           default=None,
           description="Next page number (null if no next page)",
           example=3
       )
       
       prev_page: Optional[int] = Field(
           default=None,
           description="Previous page number (null if no previous page)",
           example=1
       )

Error Models
------------

ErrorDetail
~~~~~~~~~~~

Detailed error information for API responses.

.. code-block:: python

   class ErrorDetail(BaseModel):
       """
       Detailed error information.
       
       Provides structured error details to help clients
       understand and handle API errors effectively.
       """
       
       code: str = Field(
           ...,
           description="Machine-readable error code",
           example="VALIDATION_ERROR"
       )
       
       message: str = Field(
           ...,
           description="Human-readable error message",
           example="Invalid input data provided"
       )
       
       details: Optional[Dict[str, Any]] = Field(
           default=None,
           description="Additional error context and details",
           example={
               "field": "email",
               "issue": "Invalid email format",
               "provided_value": "invalid-email"
           }
       )
       
       field: Optional[str] = Field(
           default=None,
           description="Specific field that caused the error",
           example="email"
       )
       
       suggestion: Optional[str] = Field(
           default=None,
           description="Suggested fix for the error",
           example="Please provide a valid email address"
       )

ErrorResponse
~~~~~~~~~~~~~

Complete error response structure.

.. code-block:: python

   class ErrorResponse(BaseModel):
       """
       Complete error response structure.
       
       Used for all API error responses to provide consistent
       error handling and debugging information.
       """
       
       error: ErrorDetail = Field(
           ...,
           description="Detailed error information"
       )
       
       status: int = Field(
           ...,
           description="HTTP status code",
           example=400
       )
       
       timestamp: datetime = Field(
           ...,
           description="Error occurrence timestamp (UTC)",
           example="2023-12-01T10:00:00Z"
       )
       
       request_id: Optional[str] = Field(
           default=None,
           description="Unique request identifier for tracking",
           example="req_123456789"
       )
       
       path: Optional[str] = Field(
           default=None,
           description="API endpoint path where error occurred",
           example="/api/v1/regulations"
       )

Validation Models
-----------------

ValidationError
~~~~~~~~~~~~~~~

Structured validation error information.

.. code-block:: python

   class ValidationError(BaseModel):
       """
       Validation error details for form and data validation.
       
       Provides specific information about validation failures
       to help clients correct their requests.
       """
       
       field: str = Field(
           ...,
           description="Field name that failed validation",
           example="email"
       )
       
       message: str = Field(
           ...,
           description="Validation error message",
           example="Invalid email format"
       )
       
       code: str = Field(
           ...,
           description="Validation error code",
           example="INVALID_FORMAT"
       )
       
       value: Optional[Any] = Field(
           default=None,
           description="Value that failed validation",
           example="invalid-email"
       )
       
       constraint: Optional[Dict[str, Any]] = Field(
           default=None,
           description="Validation constraint that was violated",
           example={
               "pattern": r"^[^@]+@[^@]+\.[^@]+$",
               "type": "email"
           }
       )

Common Enums
------------

Status Enums
~~~~~~~~~~~~

Standard status enumerations used across the platform.

.. code-block:: python

   class RecordStatus(str, Enum):
       """Standard record status values."""
       ACTIVE = "active"
       INACTIVE = "inactive"
       DRAFT = "draft"
       ARCHIVED = "archived"
       PENDING = "pending"
   
   class ProcessingStatus(str, Enum):
       """Processing status for async operations."""
       PENDING = "pending"
       PROCESSING = "processing"
       COMPLETED = "completed"
       FAILED = "failed"
       CANCELLED = "cancelled"
   
   class Priority(str, Enum):
       """Priority levels."""
       LOW = "low"
       MEDIUM = "medium"
       HIGH = "high"
       CRITICAL = "critical"

**Usage in Schemas:**

.. code-block:: python

   class ExampleSchema(BaseSchema):
       status: RecordStatus = Field(
           default=RecordStatus.ACTIVE,
           description="Record status"
       )
       
       priority: Priority = Field(
           default=Priority.MEDIUM,
           description="Priority level"
       )
