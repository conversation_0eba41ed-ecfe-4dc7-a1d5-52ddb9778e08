Regulation Models Schema
========================

This section documents the data models for regulations, regulators, countries, and related entities.

.. contents:: Table of Contents
   :local:
   :depth: 2

Regulation Schemas
------------------

RegulationBase
~~~~~~~~~~~~~~

Base regulation schema with core fields.

.. code-block:: python

   class RegulationBase(BaseSchema):
       """
       Base regulation schema containing core regulation information.
       
       This schema defines the fundamental fields that describe
       a regulation, including identification, status, and metadata.
       """
       
       title: str = Field(
           ...,
           min_length=1,
           max_length=500,
           description="Regulation title or official name",
           example="General Data Protection Regulation"
       )
       
       description: Optional[str] = Field(
           default=None,
           max_length=2000,
           description="Detailed description of the regulation",
           example="EU regulation on data protection and privacy for individuals within the EU"
       )
       
       reference_number: Optional[str] = Field(
           default=None,
           max_length=100,
           description="Official reference number or identifier",
           example="2016/679"
       )
       
       status: RegulationStatus = Field(
           default=RegulationStatus.ACTIVE,
           description="Current status of the regulation"
       )
       
       effective_date: Optional[date] = Field(
           default=None,
           description="Date when the regulation becomes effective",
           example="2018-05-25"
       )
       
       publication_date: Optional[date] = Field(
           default=None,
           description="Date when the regulation was published",
           example="2016-04-27"
       )
       
       jurisdiction: Optional[str] = Field(
           default=None,
           max_length=200,
           description="Geographic or legal jurisdiction",
           example="European Union"
       )
       
       source_url: Optional[HttpUrl] = Field(
           default=None,
           description="Primary source URL for the regulation",
           example="https://eur-lex.europa.eu/eli/reg/2016/679/oj"
       )
       
       summary: Optional[str] = Field(
           default=None,
           max_length=1000,
           description="Brief summary of the regulation",
           example="The GDPR strengthens data protection for individuals within the EU"
       )

RegulationCreate
~~~~~~~~~~~~~~~~

Schema for creating new regulations.

.. code-block:: python

   class RegulationCreate(RegulationBase):
       """
       Schema for creating new regulations.
       
       Extends the base regulation schema with fields required
       for regulation creation, including relationships.
       """
       
       regulator_id: Optional[int] = Field(
           default=None,
           description="ID of the regulatory authority",
           example=5
       )
       
       country_id: Optional[int] = Field(
           default=None,
           description="ID of the country/jurisdiction",
           example=10
       )
       
       category_ids: Optional[List[int]] = Field(
           default=None,
           description="List of category IDs to associate with the regulation",
           example=[1, 2, 3]
       )
       
       industry_ids: Optional[List[int]] = Field(
           default=None,
           description="List of industry IDs that the regulation applies to",
           example=[1, 2]
       )
       
       tag_ids: Optional[List[int]] = Field(
           default=None,
           description="List of tag IDs for categorization",
           example=[5, 6, 7]
       )
       
       confidence_level: float = Field(
           default=0.8,
           ge=0.0,
           le=1.0,
           description="Confidence level in the regulation data (0.0-1.0)",
           example=0.95
       )

RegulationUpdate
~~~~~~~~~~~~~~~~

Schema for updating existing regulations.

.. code-block:: python

   class RegulationUpdate(BaseSchema):
       """
       Schema for updating existing regulations.
       
       All fields are optional to support partial updates.
       Only provided fields will be updated.
       """
       
       title: Optional[str] = Field(
           default=None,
           min_length=1,
           max_length=500,
           description="Updated regulation title"
       )
       
       description: Optional[str] = Field(
           default=None,
           max_length=2000,
           description="Updated regulation description"
       )
       
       status: Optional[RegulationStatus] = Field(
           default=None,
           description="Updated regulation status"
       )
       
       effective_date: Optional[date] = Field(
           default=None,
           description="Updated effective date"
       )
       
       is_verified: Optional[bool] = Field(
           default=None,
           description="Whether the regulation has been verified"
       )
       
       verified_by: Optional[str] = Field(
           default=None,
           description="Identifier of the user who verified the regulation"
       )
       
       confidence_level: Optional[float] = Field(
           default=None,
           ge=0.0,
           le=1.0,
           description="Updated confidence level"
       )

RegulationResponse
~~~~~~~~~~~~~~~~~~

Complete regulation response schema with all related data.

.. code-block:: python

   class RegulationResponse(RegulationBase, TimestampMixin, SoftDeleteMixin):
       """
       Complete regulation response schema.
       
       Includes all regulation data plus related entities
       and metadata for comprehensive regulation information.
       """
       
       id: int = Field(
           ...,
           description="Unique regulation identifier",
           example=1
       )
       
       regulator: Optional['RegulatorSummary'] = Field(
           default=None,
           description="Regulatory authority information"
       )
       
       country: Optional['CountrySummary'] = Field(
           default=None,
           description="Country/jurisdiction information"
       )
       
       categories: List['CategorySummary'] = Field(
           default=[],
           description="Associated regulation categories"
       )
       
       industries: List['IndustrySummary'] = Field(
           default=[],
           description="Applicable industries"
       )
       
       tags: List['TagSummary'] = Field(
           default=[],
           description="Associated tags"
       )
       
       compliance_requirements: List['ComplianceRequirementSummary'] = Field(
           default=[],
           description="Associated compliance requirements"
       )
       
       related_regulations: List['RelatedRegulation'] = Field(
           default=[],
           description="Related regulations"
       )
       
       confidence_level: float = Field(
           ...,
           ge=0.0,
           le=1.0,
           description="Confidence level in the regulation data",
           example=0.95
       )
       
       is_verified: bool = Field(
           default=False,
           description="Whether the regulation has been verified by an expert"
       )
       
       verified_by: Optional[str] = Field(
           default=None,
           description="Identifier of the user who verified the regulation"
       )
       
       verification_date: Optional[datetime] = Field(
           default=None,
           description="Date when the regulation was verified"
       )

Regulator Schemas
-----------------

RegulatorBase
~~~~~~~~~~~~~

Base regulator schema with core information.

.. code-block:: python

   class RegulatorBase(BaseSchema):
       """
       Base regulator schema for regulatory authorities.
       
       Contains core information about regulatory bodies
       and government agencies that issue regulations.
       """
       
       name: str = Field(
           ...,
           min_length=1,
           max_length=200,
           description="Full name of the regulatory authority",
           example="Securities and Exchange Commission"
       )
       
       abbreviation: Optional[str] = Field(
           default=None,
           max_length=20,
           description="Common abbreviation or acronym",
           example="SEC"
       )
       
       description: Optional[str] = Field(
           default=None,
           max_length=1000,
           description="Description of the regulator's role and responsibilities"
       )
       
       website: Optional[HttpUrl] = Field(
           default=None,
           description="Official website URL",
           example="https://www.sec.gov"
       )
       
       focus_areas: List[str] = Field(
           default=[],
           description="Primary areas of regulatory focus",
           example=["Securities", "Investment", "Market Regulation"]
       )
       
       is_active: bool = Field(
           default=True,
           description="Whether the regulator is currently active"
       )

RegulatorCreate
~~~~~~~~~~~~~~~

Schema for creating new regulators.

.. code-block:: python

   class RegulatorCreate(RegulatorBase):
       """Schema for creating new regulatory authorities."""
       
       country_id: int = Field(
           ...,
           description="ID of the country where the regulator operates",
           example=1
       )

RegulatorResponse
~~~~~~~~~~~~~~~~~

Complete regulator response with related data.

.. code-block:: python

   class RegulatorResponse(RegulatorBase, TimestampMixin):
       """
       Complete regulator response schema.
       
       Includes regulator information plus statistics
       and related data for comprehensive information.
       """
       
       id: int = Field(
           ...,
           description="Unique regulator identifier",
           example=1
       )
       
       country: 'CountrySummary' = Field(
           ...,
           description="Country where the regulator operates"
       )
       
       total_regulations: int = Field(
           default=0,
           description="Total number of regulations issued by this regulator",
           example=245
       )
       
       active_regulations: int = Field(
           default=0,
           description="Number of currently active regulations",
           example=198
       )

Country Schemas
---------------

CountryBase
~~~~~~~~~~~

Base country schema with geographic and regulatory information.

.. code-block:: python

   class CountryBase(BaseSchema):
       """
       Base country schema for geographic and regulatory jurisdictions.
       
       Contains information about countries and their
       regulatory environments and complexity.
       """
       
       name: str = Field(
           ...,
           min_length=1,
           max_length=100,
           description="Official country name",
           example="United States"
       )
       
       code: str = Field(
           ...,
           regex=r'^[A-Z]{2}$',
           description="ISO 3166-1 alpha-2 country code",
           example="US"
       )
       
       iso_code: Optional[str] = Field(
           default=None,
           regex=r'^[A-Z]{3}$',
           description="ISO 3166-1 alpha-3 country code",
           example="USA"
       )
       
       region: Optional[str] = Field(
           default=None,
           max_length=100,
           description="Geographic region",
           example="North America"
       )
       
       regulatory_complexity: Optional[int] = Field(
           default=None,
           ge=1,
           le=10,
           description="Regulatory complexity score (1-10, 10 being most complex)",
           example=8
       )

CountryResponse
~~~~~~~~~~~~~~~

Complete country response with regulatory statistics.

.. code-block:: python

   class CountryResponse(CountryBase, TimestampMixin):
       """
       Complete country response with regulatory statistics.
       
       Includes country information plus regulatory
       statistics and related data.
       """
       
       id: int = Field(
           ...,
           description="Unique country identifier",
           example=1
       )
       
       total_regulations: int = Field(
           default=0,
           description="Total number of regulations in this country",
           example=1250
       )
       
       active_regulators: int = Field(
           default=0,
           description="Number of active regulatory authorities",
           example=15
       )
       
       major_regulators: List['RegulatorSummary'] = Field(
           default=[],
           description="Major regulatory authorities in this country"
       )
       
       regulation_categories: List['CategoryWithCount'] = Field(
           default=[],
           description="Regulation categories with counts"
       )

Enums
-----

RegulationStatus
~~~~~~~~~~~~~~~~

Enumeration of possible regulation statuses.

.. code-block:: python

   class RegulationStatus(str, Enum):
       """
       Regulation status enumeration.
       
       Defines the possible states of a regulation
       throughout its lifecycle.
       """
       
       ACTIVE = "active"           # Currently in effect
       DRAFT = "draft"             # Under development
       PROPOSED = "proposed"       # Proposed but not yet effective
       ARCHIVED = "archived"       # No longer in effect
       PENDING = "pending"         # Awaiting approval or implementation
       SUPERSEDED = "superseded"   # Replaced by another regulation

Summary Schemas
---------------

These schemas provide condensed information for use in lists and relationships.

RegulatorSummary
~~~~~~~~~~~~~~~~

.. code-block:: python

   class RegulatorSummary(BaseSchema):
       """Condensed regulator information for use in relationships."""
       
       id: int = Field(..., description="Regulator ID")
       name: str = Field(..., description="Regulator name")
       abbreviation: Optional[str] = Field(default=None, description="Abbreviation")
       website: Optional[HttpUrl] = Field(default=None, description="Website URL")

CountrySummary
~~~~~~~~~~~~~~

.. code-block:: python

   class CountrySummary(BaseSchema):
       """Condensed country information for use in relationships."""
       
       id: int = Field(..., description="Country ID")
       name: str = Field(..., description="Country name")
       code: str = Field(..., description="ISO country code")
       region: Optional[str] = Field(default=None, description="Geographic region")
