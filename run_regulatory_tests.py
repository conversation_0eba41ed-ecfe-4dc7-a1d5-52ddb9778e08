
#!/usr/bin/env python
"""
Run regulatory specific tests with various options.
"""
import argparse
import subprocess
import sys
import os
import time


def run_tests(test_files=None, test_pattern=None, verbose=False, coverage=False, 
             junit_report=False, benchmark=False):
    """Run the regulatory tests with specified options."""
    command = ["pytest"]
    
    # Add test files or pattern
    if test_files:
        command.extend(test_files)
    elif test_pattern:
        command.append(f"-k={test_pattern}")
    else:
        command.extend([
            "tests/test_regulatory_parser.py",
            "tests/test_relationship_mapper.py",
            "tests/test_regulatory_suite.py",
            "tests/test_integration_regulatory.py",
            "tests/test_workflow_regulatory.py",
            "tests/test_regulatory_benchmarks.py",
            "tests/test_impact_assessment_integration.py"
        ])
    
    # Add options
    if verbose:
        command.append("-v")
    
    if coverage:
        command.extend(["--cov=app", "--cov-report=term", "--cov-report=html:coverage/html"])
    
    if junit_report:
        command.append("--junitxml=test_results.xml")
    
    if benchmark:
        command.append("-xvs")  # Show extra test output for benchmarks
    
    # Run the tests
    print(f"Running command: {' '.join(command)}")
    start_time = time.time()
    result = subprocess.run(command)
    end_time = time.time()
    
    # Print summary
    print(f"\nTests completed in {end_time - start_time:.2f} seconds")
    print(f"Exit code: {result.returncode}")
    
    return result.returncode


def main():
    """Parse arguments and run tests."""
    parser = argparse.ArgumentParser(description="Run regulatory tests")
    
    parser.add_argument(
        "--files", "-f",
        nargs="+",
        help="Specific test files to run"
    )
    
    parser.add_argument(
        "--pattern", "-p",
        help="Pattern to match for test names"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Run tests in verbose mode"
    )
    
    parser.add_argument(
        "--coverage", "-c",
        action="store_true",
        help="Generate coverage report"
    )
    
    parser.add_argument(
        "--junit", "-j",
        action="store_true",
        help="Generate JUnit XML report"
    )
    
    parser.add_argument(
        "--benchmark", "-b",
        action="store_true",
        help="Run benchmark tests with detailed output"
    )
    
    args = parser.parse_args()
    
    sys.exit(run_tests(
        test_files=args.files,
        test_pattern=args.pattern,
        verbose=args.verbose,
        coverage=args.coverage,
        junit_report=args.junit,
        benchmark=args.benchmark
    ))


if __name__ == "__main__":
    main()
