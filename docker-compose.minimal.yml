version: '3.8'

services:
  traefik:
    image: traefik:v3.0
    container_name: regulationguru-traefik-minimal
    command:
      - "--api.dashboard=false"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--log.level=INFO"
      - "--ping=true"
    ports:
      - "80:80"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - traefik
    labels:
      - "traefik.enable=true"

  db:
    image: postgres:14-alpine
    container_name: regulationguru-db-minimal
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: regulationguru
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - backend
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: regulationguru-api-minimal
    depends_on:
      db:
        condition: service_healthy
    environment:
      DATABASE_URL: **************************************/regulationguru
      SECRET_KEY: test-secret-key
      ENVIRONMENT: development
      LOG_LEVEL: info
    volumes:
      - ./app:/app/app
      - ./templates:/app/templates
      - ./statics:/app/statics
    networks:
      - traefik
      - backend
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.api-minimal.rule=Host(\`api.guru.localhost\`)"
      - "traefik.http.routers.api-minimal.service=api-minimal"
      - "traefik.http.services.api-minimal.loadbalancer.server.port=8000"
      - "traefik.http.services.api-minimal.loadbalancer.healthcheck.path=/api/v1/health"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  traefik:
    external: true
    name: traefik
  backend:
    name: regulationguru-backend-minimal
    driver: bridge

volumes:
  postgres_data:
    name: regulationguru_postgres_data_minimal
