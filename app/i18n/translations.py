"""
Translations module to handle i18n with Babel.
"""
import os
from typing import Callable, Dict
from fastapi import Request, Depends
import gettext

# Define available languages with their flags and labels
AVAILABLE_LANGUAGES = {
    "en_US": {"flag": "🇺🇸", "label": "English (US)"},
    "en_GB": {"flag": "🇬🇧", "label": "English (UK)"},
    "af": {"flag": "🇿🇦", "label": "Afrikaans"},
    "de": {"flag": "🇩🇪", "label": "Deutsch"},
    "zu": {"flag": "🇿🇦", "label": "isiZulu"},
    "ro": {"flag": "🇷🇴", "label": "Română"}
}

DEFAULT_LANGUAGE = "en_US"
TRANSLATIONS_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "lang")


def setup_translation(locale: str) -> gettext.NullTranslations:
    """
    Set up translation for a specific locale.

    Args:
        locale: The locale to use for translation

    Returns:
        A translation object
    """
    try:
        return gettext.translation("messages", TRANSLATIONS_DIR, languages=[locale])
    except FileNotFoundError:
        # Fallback to default language if translation file not found
        try:
            return gettext.translation("messages", TRANSLATIONS_DIR, languages=[DEFAULT_LANGUAGE])
        except FileNotFoundError:
            # Final fallback to NullTranslations (just returns the original string)
            return gettext.NullTranslations()


def get_locale(request: Request) -> str:
    """
    Get the current locale from cookie or query parameter.

    Args:
        request: The FastAPI request object

    Returns:
        The current locale string
    """
    # Check query parameter first
    lang = request.query_params.get("lang")

    # Then check for cookie
    if not lang:
        lang = request.cookies.get("lang")

    # Fallback to default language
    if not lang or lang not in AVAILABLE_LANGUAGES:
        lang = DEFAULT_LANGUAGE

    return lang


def get_translator(request: Request = Depends()) -> Callable[[str], str]:
    """
    Get a translator function for the current locale.

    Args:
        request: The FastAPI request object

    Returns:
        A function that translates strings
    """
    locale = get_locale(request)
    translator = setup_translation(locale)

    def translate(text: str) -> str:
        return translator.gettext(text)

    return translate


def get_translations_context(request: Request) -> Dict:
    """
    Get translation context for templates.

    Args:
        request: The FastAPI request object

    Returns:
        Dictionary with translation context
    """
    from app.i18n import default_languages
    current_lang = get_locale(request)
    translator = get_translator(request)

    return {
        "current_lang": current_lang,
        "languages": default_languages,
        "_": translator  # This allows using {{ _("text") }} in templates
    }

def get_available_languages():
    # Placeholder - Replace with your actual implementation to fetch available languages
    return AVAILABLE_LANGUAGES