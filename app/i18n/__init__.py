"""Internationalization package."""
import gettext
import os
from typing import Callable, Dict, Any
from fastapi import Request
from functools import lru_cache

# Internal imports
from app.i18n.translations import get_translator as get_translator_func
from app.i18n.translations import get_locale as get_locale_func

# Language definitions
if 'default_languages' not in locals() and 'default_languages' not in globals():
    default_languages = {
        "en_US": {"label": "English (US)", "flag": "🇺🇸"},
        "en_GB": {"label": "English (UK)", "flag": "🇬🇧"},
        "es": {"label": "Español", "flag": "🇪🇸"},
        "de": {"label": "Deutsch", "flag": "🇩🇪"},
        "af": {"label": "Afrikaans", "flag": "🇿🇦"},
        "zu": {"label": "isiZulu", "flag": "🇿🇦"}
    }

def get_locale() -> str:
    """Return the current locale."""
    # Default to English
    return os.environ.get('LANG', 'en_US')

def get_translator() -> Callable:
    """Return a translator function for the current locale."""
    locale = get_locale()
    translations = gettext.translation(
        'messages', 
        localedir='lang', 
        languages=[locale], 
        fallback=True
    )
    return translations.gettext

from app.i18n.i18n import get_translator, get_locale
from app.i18n.translations import get_translations_context

__all__ = ['get_translator', 'get_locale', 'get_translations_context', 'default_languages']