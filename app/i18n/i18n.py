"""Internationalization module for FastAPI application."""
from typing import List, Dict, Optional, Callable, Any
import os
from fastapi import Request, Depends
from fastapi.templating import Jinja2Templates
from babel.support import Translations

# Default language
DEFAULT_LOCALE = "en_US"

# Available languages
SUPPORTED_LOCALES = ["en_US", "en_GB", "es", "de", "af", "zu"]

# Path to translation files
LOCALE_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "lang")


def get_locale(request: Request) -> str:
    """
    Determine the best language for the user.

    Args:
        request (Request): The FastAPI request

    Returns:
        str: Locale code (e.g., 'en', 'es')
    """
    # Get locale from query parameter, cookie, or accept-language header
    locale = request.query_params.get("lang", None)

    if not locale:
        locale = request.cookies.get("lang", None)

    if not locale:
        accept_language = request.headers.get("accept-language", "")
        if accept_language:
            # Parse the Accept-Language header
            locales = [lang.split(";")[0].strip() for lang in accept_language.split(",")]
            for loc in locales:
                if loc[:2] in SUPPORTED_LOCALES:
                    locale = loc[:2]
                    break

    # Default to English if no match
    if not locale or locale not in SUPPORTED_LOCALES:
        locale = DEFAULT_LOCALE

    return locale


def get_translator(locale: str = Depends(get_locale)) -> Callable:
    """
    Get translation function for the current locale.

    Args:
        locale (str): The locale code

    Returns:
        Callable: Translation function
    """
    # Simple implementation - return identity function
    # In a real application, this would use gettext or a similar library
    def translate(text: str) -> str:
        return text

    return translate