"""Database models with soft-delete and timestamp support."""
from datetime import datetime
from typing import Optional, List

from sqlalchemy import Column, DateTime, Boolean, String, Integer, Float, ForeignKey, Text, UniqueConstraint
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

# Export Base so it can be imported from app.db.models
Base = declarative_base()

# Make Base available at the package level
__all__ = ['Base']


class SoftDeleteMixin:
    """Mixin for soft delete functionality."""

    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    deleted_at = Column(DateTime, nullable=True)

    def soft_delete(self):
        """Mark the record as deleted."""
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()


class TimestampMixin:
    """Mixin for created_at and changed_on timestamps."""

    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    changed_on = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)


class Item(Base, SoftDeleteMixin, TimestampMixin):
    """Example Item model with soft-delete and timestamp functionality."""

    __tablename__ = "items"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    description = Column(String, nullable=True)


class Country(Base, TimestampMixin):
    """Model for countries."""
    __tablename__ = "countries"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    code = Column(String(3), nullable=False, index=True, unique=True)
    region = Column(String(100), nullable=True, index=True)
    subregion = Column(String(100), nullable=True)
    flag_emoji = Column(String(10), nullable=True)

    # Relationships
    regulators = relationship("Regulator", back_populates="country")
    regulation_urls = relationship("RegulationURL", back_populates="country")
    regulatory_sources = relationship("RegulatorySource", back_populates="country")

    def __repr__(self):
        return f"<Country {self.name}>"


class Regulator(Base, TimestampMixin):
    """Regulator model to store regulatory authority information."""

    __tablename__ = "regulators"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    type = Column(String(100), nullable=True)
    website = Column(String(255), nullable=True)
    country_id = Column(Integer, ForeignKey("countries.id"), nullable=False, index=True)

    # Relationships
    country = relationship("Country", back_populates="regulators")
    regulation_urls = relationship("RegulationURL", back_populates="regulator")
    changes = relationship("RegulatoryChange", back_populates="regulator") #Added relationship
    sources = relationship("RegulatorySource", back_populates="regulator")
    alert_subscriptions = relationship("AlertSubscription", back_populates="regulator")

    # Ensure name is unique per country
    __table_args__ = (UniqueConstraint('name', 'country_id', name='_regulator_country_uc'),)

    def __repr__(self):
        return f"<Regulator {self.name}>"


class RegulationURL(Base, TimestampMixin):
    """Model for storing regulation URLs with normalized relationships."""

    __tablename__ = "regulation_urls"

    id = Column(Integer, primary_key=True, index=True)
    url = Column(String(1024), nullable=False, index=True, unique=True)
    domain = Column(String(255), nullable=True, index=True)
    normalized_url = Column(String(1024), nullable=True, index=True)
    category = Column(String(100), nullable=True, index=True)
    confidence_level = Column(Float, nullable=False, default=0.0)
    regulator_id = Column(Integer, ForeignKey("regulators.id"), nullable=True, index=True)
    source_id = Column(Integer, ForeignKey("regulatory_sources.id"), nullable=True, index=True)
    title = Column(String(500), nullable=True)
    country_id = Column(Integer, ForeignKey("countries.id"), nullable=True, index=True)
    publication_date = Column(DateTime, nullable=True)
    status = Column(String(50), nullable=True, default="new")

    # Relationships
    regulator = relationship("Regulator", back_populates="regulation_urls")
    source = relationship("RegulatorySource", back_populates="regulations")
    country = relationship("Country", back_populates="regulation_urls")
    changes = relationship("RegulatoryChange", back_populates="regulation_url", cascade="all, delete-orphan")


class AlertSubscription(Base):
    """Model for alert subscriptions."""
    __tablename__ = "alert_subscriptions"

    id = Column(Integer, primary_key=True, index=True)
    webhook_url = Column(String, nullable=False)
    countries = Column(String, nullable=True)  # Comma-separated list of country codes
    min_importance = Column(Integer, nullable=True)
    description = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    last_notified_at = Column(DateTime, nullable=True)
    is_active = Column(Boolean, default=True)

    # Add foreign key for regulator relationship
    regulator_id = Column(Integer, ForeignKey("regulators.id"), nullable=True)

    # Add foreign key for source relationship
    source_id = Column(Integer, ForeignKey("regulatory_sources.id"), nullable=True)

    # Relationships
    regulator = relationship("Regulator", back_populates="alert_subscriptions")
    source = relationship("RegulatorySource", back_populates="alert_subscriptions")

    def to_dict(self):
        """Convert to dictionary."""
        return {
            "id": self.id,
            "webhook_url": self.webhook_url,
            "countries": self.countries.split(",") if self.countries else None,
            "min_importance": self.min_importance,
            "description": self.description,
            "created_at": self.created_at,
            "last_notified_at": self.last_notified_at,
            "is_active": self.is_active
        }

    def __repr__(self):
        return f"<AlertSubscription {self.id}>"


class Document(Base):
    """Model for uploaded documents."""
    __tablename__ = "documents"

    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String, nullable=False)
    content_type = Column(String, nullable=True)
    content_length = Column(Integer, nullable=True)
    confidence_score = Column(Float, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    summaries = relationship("DocumentSummary", back_populates="document")


class DocumentSummary(Base):
    """Model for AI-generated document summaries."""
    __tablename__ = "document_summaries"

    id = Column(Integer, primary_key=True, index=True)
    document_id = Column(Integer, ForeignKey("documents.id"), nullable=False)
    summary_text = Column(Text, nullable=False)
    key_requirements = Column(Text, nullable=True)  # Store as JSON string
    deadlines = Column(Text, nullable=True)  # Store as JSON string
    penalties = Column(Text, nullable=True)  # Store as JSON string
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    document = relationship("Document", back_populates="summaries")
    
    @property
    def key_requirements_list(self):
        """Convert JSON string to list."""
        import json
        return json.loads(self.key_requirements) if self.key_requirements else []
        
    @key_requirements_list.setter
    def key_requirements_list(self, value):
        """Convert list to JSON string."""
        import json
        self.key_requirements = json.dumps(value) if value else None
        
    @property
    def deadlines_list(self):
        """Convert JSON string to list."""
        import json
        return json.loads(self.deadlines) if self.deadlines else []
        
    @deadlines_list.setter
    def deadlines_list(self, value):
        """Convert list to JSON string."""
        import json
        self.deadlines = json.dumps(value) if value else None
        
    @property
    def penalties_list(self):
        """Convert JSON string to list."""
        import json
        return json.loads(self.penalties) if self.penalties else []
        
    @penalties_list.setter
    def penalties_list(self, value):
        """Convert list to JSON string."""
        import json
        self.penalties = json.dumps(value) if value else None


class RegulatoryChange(Base, TimestampMixin):
    __tablename__ = "regulatory_changes"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(500), nullable=True)
    description = Column(Text, nullable=True)
    change_date = Column(DateTime, nullable=False, index=True)
    importance = Column(Integer, default=5, nullable=False, index=True)  # 1-10 scale
    change_type = Column(String(50), nullable=True, index=True)

    # Foreign key for regulation URL
    regulation_url_id = Column(Integer, ForeignKey("regulation_urls.id"), nullable=False)

    # Relationship
    regulation_url = relationship("RegulationURL", back_populates="changes")
    regulator_id = Column(Integer, ForeignKey("regulators.id"))
    country_id = Column(Integer, ForeignKey("countries.id"))
    publication_date = Column(DateTime)
    effective_date = Column(DateTime)
    status = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    regulator = relationship("Regulator", back_populates="changes")
    impacts = relationship("ChangeImpact", back_populates="change", cascade="all, delete-orphan")


class ChangeImpact(Base, TimestampMixin):
    __tablename__ = "change_impacts"

    id = Column(Integer, primary_key=True, index=True)
    change_id = Column(Integer, ForeignKey("regulatory_changes.id"))
    impact_area = Column(String, index=True)
    severity = Column(Integer)  # 1-5 scale
    description = Column(Text)
    mitigation_plan = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    change = relationship("RegulatoryChange", back_populates="impacts")

class RegulatorySource(Base, TimestampMixin):
    """
    Model for regulatory data sources such as regulator websites, RSS feeds, or APIs.
    """
    __tablename__ = "regulatory_sources"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    url = Column(String(2048), nullable=False, index=True, unique=True)
    country_id = Column(Integer, ForeignKey("countries.id"), nullable=False)
    regulator_id = Column(Integer, ForeignKey("regulators.id"), nullable=True)
    description = Column(Text, nullable=True)
    source_type = Column(String(50), nullable=False)  # website, api, rss, etc.
    collection_frequency = Column(Integer, default=24)  # hours
    last_collection = Column(DateTime, nullable=True)
    last_error = Column(Text, nullable=True)

    # Relationships
    country = relationship("Country", back_populates="regulatory_sources")
    regulator = relationship("Regulator", back_populates="sources")
    regulations = relationship("RegulationURL", back_populates="source")
    alert_subscriptions = relationship("AlertSubscription", back_populates="source")

    def __repr__(self):
        return f"<RegulatorySource {self.name}>"