from sqlalchemy import Column, Integer, String, ForeignKey
from sqlalchemy.orm import relationship

class Country(db.Model):
    __tablename__ = "countries"
    id = Column(Integer, primary_key=True)
    name = Column(String)
    regulation_urls = relationship("RegulationURL", back_populates="country")

class Regulator(db.Model):
    __tablename__ = "regulators"
    id = Column(Integer, primary_key=True)
    name = Column(String)
    regulation_urls = relationship("RegulationURL", back_populates="regulator")

class RegulatorySource(db.Model):
    __tablename__ = "regulatory_sources"
    id = Column(Integer, primary_key=True)
    name = Column(String)
    regulations = relationship("RegulationURL", back_populates="source")

class RegulationURL(db.Model):
    __tablename__ = "regulation_urls"
    id = Column(Integer, primary_key=True)
    url = Column(String)
    # Foreign keys
    country_id = Column(Integer, ForeignKey("countries.id"), nullable=True)
    regulator_id = Column(Integer, ForeignKey("regulators.id"), nullable=True)
    source_id = Column(Integer, ForeignKey("regulatory_sources.id"), nullable=True)
    # Relationships
    country = relationship("Country", back_populates="regulation_urls")
    regulator = relationship("Regulator", back_populates="regulation_urls")
    source = relationship("RegulatorySource", back_populates="regulations")