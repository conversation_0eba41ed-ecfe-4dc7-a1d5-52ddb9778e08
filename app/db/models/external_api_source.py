"""
Database model for external API sources.
"""
from sqlalchemy import <PERSON>umn, Integer, String, ForeignKey, DateTime, Text, Float, Boolean, JSON
from sqlalchemy.orm import relationship
from datetime import datetime

from app.db.models import Base, TimestampMixin

class ExternalAPISource(Base, TimestampMixin):
    """
    Model for external API data sources such as SEC EDGAR, CFPB, etc.
    """
    __tablename__ = "external_api_sources"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    source_type = Column(String(50), nullable=False)  # sec_edgar, cfpb_complaints, etc.
    base_url = Column(String(2048), nullable=False)
    api_key = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)
    config = Column(JSON, nullable=True)  # Additional configuration as JSON
    is_active = Column(Boolean, default=True)
    last_sync = Column(DateTime, nullable=True)
    last_error = Column(Text, nullable=True)
    sync_frequency = Column(Integer, default=24)  # hours

    # Relationships
    regulations = relationship("RegulationURL", back_populates="external_api_source")

    def __repr__(self):
        return f"<ExternalAPISource {self.name}>"


class ExternalAPISyncLog(Base, TimestampMixin):
    """
    Model for logging external API synchronization events.
    """
    __tablename__ = "external_api_sync_logs"

    id = Column(Integer, primary_key=True, index=True)
    source_id = Column(Integer, ForeignKey("external_api_sources.id"), nullable=False)
    status = Column(String(50), nullable=False)  # success, error, partial
    items_processed = Column(Integer, default=0)
    items_created = Column(Integer, default=0)
    items_updated = Column(Integer, default=0)
    items_failed = Column(Integer, default=0)
    error_message = Column(Text, nullable=True)
    duration_seconds = Column(Float, nullable=True)

    # Relationships
    source = relationship("ExternalAPISource")

    def __repr__(self):
        return f"<ExternalAPISyncLog {self.id} - {self.status}>"
