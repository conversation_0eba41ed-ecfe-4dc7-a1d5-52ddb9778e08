
"""
Models for tracking regulatory changes over time.
"""
from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, Float, Boolean, Enum
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from datetime import datetime

from app.db.base_class import Base, TimestampMixin

class ChangeType(str, PyEnum):
    """Enum for types of regulatory changes."""
    NEW = "new"
    AMENDMENT = "amendment"
    REPEAL = "repeal"
    INTERPRETATION = "interpretation"
    GUIDANCE = "guidance"

class ChangeImpact(str, PyEnum):
    """Enum for impact levels of regulatory changes."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class RegulatoryChange(Base, TimestampMixin):
    """Model for tracking regulatory changes over time."""
    
    __tablename__ = "regulatory_changes"
    
    id = Column(Integer, primary_key=True, index=True)
    regulation_url_id = Column(Integer, ForeignKey("regulation_urls.id"), nullable=False, index=True)
    change_type = Column(Enum(ChangeType), nullable=False, index=True)
    change_date = Column(DateTime(timezone=True), nullable=False, index=True)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    impact_level = Column(Enum(ChangeImpact), nullable=False, index=True)
    analysis = Column(Text, nullable=True)
    is_verified = Column(Boolean, default=False, nullable=False)
    
    # Relationships
    regulation_url = relationship("RegulationURL", back_populates="changes")
    
    def __repr__(self):
        return f"<RegulatoryChange {self.title[:50]}... ({self.change_type.value})>"

class ComplianceRequirement(Base, TimestampMixin):
    """Model for specific compliance requirements extracted from regulations."""
    
    __tablename__ = "compliance_requirements"
    
    id = Column(Integer, primary_key=True, index=True)
    regulation_url_id = Column(Integer, ForeignKey("regulation_urls.id"), nullable=False, index=True)
    category = Column(String(100), nullable=False, index=True)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    implementation_difficulty = Column(Float, nullable=True)  # Scale of 1-10
    priority = Column(Enum("low", "medium", "high", "critical", name="requirement_priority"), nullable=False, index=True)
    deadline = Column(DateTime(timezone=True), nullable=True)
    is_mandatory = Column(Boolean, default=True, nullable=False)
    
    # Relationships
    regulation_url = relationship("RegulationURL", back_populates="requirements")
    
    def __repr__(self):
        return f"<ComplianceRequirement {self.title[:50]}...>"

# Add relationships to RegulationURL model
def add_relationships():
    from app.db.models import RegulationURL
    RegulationURL.changes = relationship("RegulatoryChange", back_populates="regulation_url")
    RegulationURL.requirements = relationship("ComplianceRequirement", back_populates="regulation_url")
