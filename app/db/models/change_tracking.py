
"""Models for tracking regulatory changes and alerts."""
from datetime import datetime
from typing import Optional, List

from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Boolean, Text, Float
from sqlalchemy.orm import relationship

from app.db.database import Base
from app.db.models.mixins import TimestampMixin


class RegulationChange(Base, TimestampMixin):
    """Model for tracking changes to regulatory URLs."""
    __tablename__ = "regulation_changes"

    id = Column(Integer, primary_key=True, index=True)
    regulation_url_id = Column(Integer, ForeignKey("regulation_urls.id"), nullable=False)
    change_type = Column(String(50), nullable=False)  # 'content', 'metadata', 'status'
    description = Column(Text)
    confidence_level = Column(Float, default=0.0)
    verified = Column(Boolean, default=False)
    
    # Relationships
    regulation_url = relationship("RegulationURL", back_populates="changes")
    alerts = relationship("RegulationAlert", back_populates="regulation_change")


class RegulationAlert(Base, TimestampMixin):
    """Model for alerts about regulatory changes."""
    __tablename__ = "regulation_alerts"

    id = Column(Integer, primary_key=True, index=True)
    regulation_change_id = Column(Integer, ForeignKey("regulation_changes.id"), nullable=False)
    severity = Column(String(20), nullable=False)  # 'low', 'medium', 'high', 'critical'
    message = Column(Text, nullable=False)
    acknowledged = Column(Boolean, default=False)
    acknowledged_at = Column(DateTime)
    acknowledged_by = Column(String(100))
    
    # Relationships
    regulation_change = relationship("RegulationChange", back_populates="alerts")
    subscriptions = relationship("AlertSubscription", back_populates="alert")


class AlertSubscription(Base, TimestampMixin):
    """Model for user subscriptions to regulatory alerts."""
    __tablename__ = "alert_subscriptions"

    id = Column(Integer, primary_key=True, index=True)
    user_email = Column(String(255), nullable=False, index=True)
    country_id = Column(Integer, ForeignKey("countries.id"), nullable=True)
    regulator_id = Column(Integer, ForeignKey("regulators.id"), nullable=True)
    category = Column(String(100), nullable=True)
    active = Column(Boolean, default=True)
    
    # Alert specific subscription
    alert_id = Column(Integer, ForeignKey("regulation_alerts.id"), nullable=True)
    
    # Relationships
    country = relationship("Country", backref="alert_subscriptions")
    regulator = relationship("Regulator", backref="alert_subscriptions")
    alert = relationship("RegulationAlert", back_populates="subscriptions")
