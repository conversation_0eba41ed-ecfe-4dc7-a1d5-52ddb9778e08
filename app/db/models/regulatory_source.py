
"""
Database model for regulatory sources.
"""
from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, Text, Float, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime

from app.db.models.base import Base

class RegulatorySource(Base):
    """
    Model for regulatory data sources such as regulator websites, RSS feeds, or APIs.
    """
    __tablename__ = "regulatory_sources"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    url = Column(String(2048), nullable=False, index=True, unique=True)
    country_id = Column(Integer, ForeignKey("countries.id"), nullable=False)
    regulator_id = Column(Integer, ForeignKey("regulators.id"), nullable=True)
    description = Column(Text, nullable=True)
    source_type = Column(String(50), nullable=False)  # website, api, rss, etc.
    collection_frequency = Column(Integer, default=24)  # hours
    last_collection = Column(DateTime, nullable=True)
    last_error = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    country = relationship("Country", back_populates="regulatory_sources")
    regulator = relationship("Regulator", back_populates="sources")
    regulations = relationship("RegulationURL", back_populates="source")
