"""
Database models for regulations_list.csv data with soft-delete support.

This module provides SQLAlchemy models that follow the existing codebase
patterns including soft-delete functionality and timestamp tracking.
"""

from datetime import datetime
from typing import Optional
from uuid import uuid4

from sqlalchemy import Column, String, Text, DateTime, Boolean, Float, JSON, Date, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.models import Base, SoftDeleteMixin, TimestampMixin


class RegulationCSVRecord(Base, SoftDeleteMixin, TimestampMixin):
    """
    Model for storing regulations_list.csv data with soft-delete support.
    
    This model stores the comprehensive regulatory data from the CSV file
    while maintaining the existing codebase patterns for soft deletion
    and timestamp tracking.
    """
    
    __tablename__ = "regulations_csv_records"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    
    # Basic identification fields
    country_name = Column(String(255), nullable=False, index=True)
    country_code = Column(String(3), nullable=False, index=True)
    document_title = Column(Text, nullable=False, index=True)
    document_type = Column(String(100), nullable=False, index=True)
    issuing_authority = Column(String(500), nullable=False, index=True)
    
    # Date fields
    publication_date = Column(Date, nullable=True, index=True)
    effective_date = Column(Date, nullable=True, index=True)
    
    # Legal framework
    legal_status = Column(String(50), nullable=False, index=True)
    document_url = Column(Text, nullable=True)
    language = Column(String(100), nullable=False)
    
    # Scope and application (stored as text due to length)
    scope_application = Column(Text, nullable=False)
    key_compliance_requirements = Column(Text, nullable=False)
    enforcement_mechanisms = Column(Text, nullable=False)
    penalties = Column(Text, nullable=False)
    
    # Cross-border and international aspects
    cross_border_elements = Column(Text, nullable=False)
    extraterritorial_reach = Column(Text, nullable=False)
    international_standards_alignment = Column(Text, nullable=False)
    
    # Data protection and privacy
    data_protection_provisions = Column(Text, nullable=False)
    incident_reporting_requirements = Column(Text, nullable=False)
    
    # Risk and compliance management
    risk_management_mandates = Column(Text, nullable=False)
    third_party_requirements = Column(Text, nullable=False)
    audit_obligations = Column(Text, nullable=False)
    certification_requirements = Column(Text, nullable=False)
    
    # Implementation and timeline
    implementation_timeline = Column(Text, nullable=False)
    safe_harbor_provisions = Column(Text, nullable=False)
    
    # Industry and technology focus
    industry_specific_provisions = Column(Text, nullable=False)
    technology_specific_provisions = Column(Text, nullable=False)
    
    # Additional metadata for tracking and quality
    import_batch_id = Column(String(100), nullable=True, index=True)
    data_quality_score = Column(Float, nullable=True)
    validation_errors = Column(JSON, nullable=True)
    source_file_name = Column(String(255), nullable=True)
    source_file_hash = Column(String(64), nullable=True)
    
    # Soft delete tracking (inherited from SoftDeleteMixin but adding user tracking)
    deleted_by_id = Column(UUID(as_uuid=True), nullable=True)
    
    def __repr__(self):
        return f"<RegulationCSVRecord {self.country_code}: {self.document_title[:50]}...>"
    
    def to_dict(self):
        """Convert model to dictionary for CSV export."""
        return {
            'Country_Name': self.country_name,
            'Country_Code': self.country_code,
            'Document_Title': self.document_title,
            'Document_Type': self.document_type,
            'Issuing_Authority': self.issuing_authority,
            'Publication_Date': self.publication_date.isoformat() if self.publication_date else None,
            'Effective_Date': self.effective_date.isoformat() if self.effective_date else None,
            'Legal_Status': self.legal_status,
            'Document_URL': self.document_url,
            'Language': self.language,
            'Scope_Application': self.scope_application,
            'Key_Compliance_Requirements': self.key_compliance_requirements,
            'Enforcement_Mechanisms': self.enforcement_mechanisms,
            'Penalties': self.penalties,
            'Cross_Border_Elements': self.cross_border_elements,
            'Data_Protection_Provisions': self.data_protection_provisions,
            'Incident_Reporting_Requirements': self.incident_reporting_requirements,
            'Risk_Management_Mandates': self.risk_management_mandates,
            'Third_Party_Requirements': self.third_party_requirements,
            'Audit_Obligations': self.audit_obligations,
            'Certification_Requirements': self.certification_requirements,
            'Implementation_Timeline': self.implementation_timeline,
            'International_Standards_Alignment': self.international_standards_alignment,
            'Extraterritorial_Reach': self.extraterritorial_reach,
            'Safe_Harbor_Provisions': self.safe_harbor_provisions,
            'Industry_Specific_Provisions': self.industry_specific_provisions,
            'Technology_Specific_Provisions': self.technology_specific_provisions,
        }
    
    @classmethod
    def from_csv_row(cls, row_data: dict, batch_id: str = None):
        """Create model instance from CSV row data."""
        return cls(
            country_name=row_data.get('Country_Name', ''),
            country_code=row_data.get('Country_Code', ''),
            document_title=row_data.get('Document_Title', ''),
            document_type=row_data.get('Document_Type', ''),
            issuing_authority=row_data.get('Issuing_Authority', ''),
            publication_date=cls._parse_date(row_data.get('Publication_Date')),
            effective_date=cls._parse_date(row_data.get('Effective_Date')),
            legal_status=row_data.get('Legal_Status', ''),
            document_url=row_data.get('Document_URL'),
            language=row_data.get('Language', ''),
            scope_application=row_data.get('Scope_Application', ''),
            key_compliance_requirements=row_data.get('Key_Compliance_Requirements', ''),
            enforcement_mechanisms=row_data.get('Enforcement_Mechanisms', ''),
            penalties=row_data.get('Penalties', ''),
            cross_border_elements=row_data.get('Cross_Border_Elements', ''),
            extraterritorial_reach=row_data.get('Extraterritorial_Reach', ''),
            international_standards_alignment=row_data.get('International_Standards_Alignment', ''),
            data_protection_provisions=row_data.get('Data_Protection_Provisions', ''),
            incident_reporting_requirements=row_data.get('Incident_Reporting_Requirements', ''),
            risk_management_mandates=row_data.get('Risk_Management_Mandates', ''),
            third_party_requirements=row_data.get('Third_Party_Requirements', ''),
            audit_obligations=row_data.get('Audit_Obligations', ''),
            certification_requirements=row_data.get('Certification_Requirements', ''),
            implementation_timeline=row_data.get('Implementation_Timeline', ''),
            safe_harbor_provisions=row_data.get('Safe_Harbor_Provisions', ''),
            industry_specific_provisions=row_data.get('Industry_Specific_Provisions', ''),
            technology_specific_provisions=row_data.get('Technology_Specific_Provisions', ''),
            import_batch_id=batch_id,
        )
    
    @staticmethod
    def _parse_date(date_str):
        """Parse date string to date object."""
        if not date_str or date_str.strip() == '':
            return None
        try:
            return datetime.strptime(date_str, '%Y-%m-%d').date()
        except (ValueError, TypeError):
            return None


class RegulationCSVImportLog(Base, TimestampMixin):
    """
    Model for tracking CSV import operations.
    
    This model tracks import batches, their status, and results
    for auditing and troubleshooting purposes.
    """
    
    __tablename__ = "regulations_csv_import_logs"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    
    # Import tracking
    batch_id = Column(String(100), nullable=False, unique=True, index=True)
    file_name = Column(String(255), nullable=False)
    file_hash = Column(String(64), nullable=True)
    file_size = Column(Float, nullable=True)  # Size in MB
    
    # Import results
    total_records = Column(Float, nullable=False, default=0)
    successful_imports = Column(Float, nullable=False, default=0)
    failed_imports = Column(Float, nullable=False, default=0)
    updated_records = Column(Float, nullable=False, default=0)
    
    # Processing details
    processing_time = Column(Float, nullable=True)  # Time in seconds
    import_status = Column(String(50), nullable=False, default='pending', index=True)
    error_summary = Column(JSON, nullable=True)
    warnings_summary = Column(JSON, nullable=True)
    
    # User tracking
    imported_by_id = Column(UUID(as_uuid=True), nullable=True)
    
    def __repr__(self):
        return f"<RegulationCSVImportLog {self.batch_id}: {self.import_status}>"


class RegulationCSVExportLog(Base, TimestampMixin):
    """
    Model for tracking CSV export operations.
    """
    
    __tablename__ = "regulations_csv_export_logs"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    
    # Export tracking
    export_id = Column(String(100), nullable=False, unique=True, index=True)
    file_name = Column(String(255), nullable=False)
    
    # Export parameters
    filters_applied = Column(JSON, nullable=True)
    include_deleted = Column(Boolean, nullable=False, default=False)
    total_records_exported = Column(Float, nullable=False, default=0)
    
    # Processing details
    processing_time = Column(Float, nullable=True)  # Time in seconds
    export_status = Column(String(50), nullable=False, default='pending', index=True)
    file_path = Column(String(500), nullable=True)
    
    # User tracking
    exported_by_id = Column(UUID(as_uuid=True), nullable=True)
    
    def __repr__(self):
        return f"<RegulationCSVExportLog {self.export_id}: {self.export_status}>"
