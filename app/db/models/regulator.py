from sqlalchemy import create_engine, Column, Integer, String, ForeignKey
from sqlalchemy.orm import declarative_base, relationship, sessionmaker

# Database setup
engine = create_engine('sqlite:///regulatory_compliance.db')
Base = declarative_base()

# Database models
class Country(Base):
    __tablename__ = 'countries'
    id = Column(Integer, primary_key=True)
    name = Column(String)
    regulators = relationship("Regulator", back_populates="country")

class Regulator(Base):
    __tablename__ = 'regulators'
    id = Column(Integer, primary_key=True)
    name = Column(String)
    country_id = Column(Integer, ForeignKey('countries.id'))
    # Relationships
    country = relationship("Country", back_populates="regulators")
    regulation_urls = relationship("RegulationURL", back_populates="regulator")
    sources = relationship("RegulatorySource", back_populates="regulator")

class RegulationURL(Base):
    __tablename__ = 'regulation_urls'
    id = Column(Integer, primary_key=True)
    url = Column(String)
    regulator_id = Column(Integer, ForeignKey('regulators.id'))
    regulator = relationship("Regulator", back_populates="regulation_urls")

class RegulatorySource(Base):
    __tablename__ = 'regulatory_sources'
    id = Column(Integer, primary_key=True)
    name = Column(String)
    url = Column(String)
    regulator_id = Column(Integer, ForeignKey('regulators.id'))
    regulator = relationship("Regulator", back_populates="sources")


# Create tables
Base.metadata.create_all(engine)

# Session setup
Session = sessionmaker(bind=engine)
session = Session()

# Example usage (add your data collection logic here)
# ...