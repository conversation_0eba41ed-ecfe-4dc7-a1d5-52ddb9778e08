"""
Update to the RegulationURL model to include external API source relationship.
"""
from sqlalchemy import <PERSON><PERSON>n, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey
from sqlalchemy.orm import relationship

# Import the necessary models
from app.db.models import RegulationURL

# Add the external API source foreign key and relationship
def update_regulation_url_model():
    """Update the RegulationURL model with external API source relationship."""
    # Add the foreign key column
    RegulationURL.external_api_source_id = Column(
        Integer, 
        ForeignKey("external_api_sources.id"), 
        nullable=True, 
        index=True
    )
    
    # Add the relationship
    RegulationURL.external_api_source = relationship(
        "ExternalAPISource", 
        back_populates="regulations"
    )
