
"""
API endpoints for collecting regulatory data from various sources.
"""
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field, HttpUrl
from datetime import datetime, timedelta
import logging

from app.db import get_db
from app.db.models import RegulatorySource, RegulationURL, Regulator, Country
from app.utils.regulatory_scraper import RegulatoryScraper

router = APIRouter()
logger = logging.getLogger(__name__)

class RegulatorSourceCreate(BaseModel):
    """Model for creating a new regulatory source."""
    name: str = Field(..., description="Name of the regulatory source")
    url: HttpUrl = Field(..., description="Base URL of the regulatory source")
    country_code: str = Field(..., description="ISO country code", min_length=2, max_length=3)
    regulator_name: Optional[str] = Field(None, description="Name of the regulator if known")
    description: Optional[str] = Field(None, description="Description of the source")
    source_type: str = Field(..., description="Type of source (website, api, rss, etc.)")
    collection_frequency: int = Field(24, description="How often to collect data in hours")

class RegulatoryDataCollectionStatus(BaseModel):
    """Model for regulatory data collection status."""
    source_id: int
    source_name: str
    last_collection: Optional[datetime] = None
    next_collection: Optional[datetime] = None
    status: str
    url_count: int
    error: Optional[str] = None

@router.post("/sources", response_model=Dict[str, Any])
def add_regulatory_source(
    source: RegulatorSourceCreate,
    db: Session = Depends(get_db)
):
    """
    Add a new regulatory source for data collection.
    
    Args:
        source: Source details
        db: Database session
        
    Returns:
        Dictionary with source details and status
    """
    # Find or create country
    country = db.query(Country).filter(Country.code == source.country_code).first()
    if not country:
        raise HTTPException(status_code=404, detail=f"Country with code {source.country_code} not found")
    
    # Find or create regulator if provided
    regulator_id = None
    if source.regulator_name:
        regulator = db.query(Regulator).filter(
            Regulator.name == source.regulator_name,
            Regulator.country_id == country.id
        ).first()
        
        if not regulator:
            # Create new regulator
            regulator = Regulator(
                name=source.regulator_name,
                country_id=country.id,
                website=str(source.url),
                type="unknown"
            )
            db.add(regulator)
            db.commit()
            db.refresh(regulator)
        
        regulator_id = regulator.id
    
    # Check if source already exists
    existing_source = db.query(RegulatorySource).filter(
        RegulatorySource.url == str(source.url)
    ).first()
    
    if existing_source:
        raise HTTPException(status_code=400, detail=f"Source with URL {source.url} already exists")
    
    # Create new source
    new_source = RegulatorySource(
        name=source.name,
        url=str(source.url),
        country_id=country.id,
        regulator_id=regulator_id,
        description=source.description,
        source_type=source.source_type,
        collection_frequency=source.collection_frequency
    )
    
    db.add(new_source)
    db.commit()
    db.refresh(new_source)
    
    return {
        "id": new_source.id,
        "name": new_source.name,
        "url": new_source.url,
        "status": "added",
        "message": f"Regulatory source added successfully. ID: {new_source.id}"
    }

@router.get("/sources", response_model=List[Dict[str, Any]])
def get_regulatory_sources(
    country_code: Optional[str] = None,
    source_type: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """
    Get all regulatory sources.
    
    Args:
        country_code: Filter by country code
        source_type: Filter by source type
        skip: Number of records to skip
        limit: Maximum number of records to return
        db: Database session
        
    Returns:
        List of regulatory sources
    """
    query = db.query(RegulatorySource)
    
    if country_code:
        country = db.query(Country).filter(Country.code == country_code).first()
        if not country:
            raise HTTPException(status_code=404, detail=f"Country with code {country_code} not found")
        query = query.filter(RegulatorySource.country_id == country.id)
    
    if source_type:
        query = query.filter(RegulatorySource.source_type == source_type)
    
    sources = query.offset(skip).limit(limit).all()
    
    result = []
    for source in sources:
        # Get URL count
        url_count = db.query(RegulationURL).filter(
            RegulationURL.source_id == source.id
        ).count()
        
        result.append({
            "id": source.id,
            "name": source.name,
            "url": source.url,
            "country_id": source.country_id,
            "regulator_id": source.regulator_id,
            "description": source.description,
            "source_type": source.source_type,
            "collection_frequency": source.collection_frequency,
            "last_collection": source.last_collection,
            "url_count": url_count
        })
    
    return result

@router.post("/collect/{source_id}", response_model=Dict[str, Any])
def collect_from_source(
    source_id: int,
    background_tasks: BackgroundTasks,
    force: bool = False,
    max_pages: int = 5,
    db: Session = Depends(get_db)
):
    """
    Collect data from a specific regulatory source.
    
    Args:
        source_id: ID of the source to collect from
        background_tasks: Background tasks
        force: Force collection even if it's not due
        max_pages: Maximum number of pages to collect
        db: Database session
        
    Returns:
        Dictionary with collection status
    """
    # Find the source
    source = db.query(RegulatorySource).filter(RegulatorySource.id == source_id).first()
    if not source:
        raise HTTPException(status_code=404, detail=f"Source with ID {source_id} not found")
    
    # Check if collection is due
    now = datetime.utcnow()
    collection_due = True
    
    if not force and source.last_collection:
        next_collection = source.last_collection + timedelta(hours=source.collection_frequency)
        if next_collection > now:
            collection_due = False
    
    if not collection_due:
        next_collection = source.last_collection + timedelta(hours=source.collection_frequency)
        return {
            "source_id": source.id,
            "name": source.name,
            "status": "skipped",
            "message": f"Collection not due until {next_collection}",
            "last_collection": source.last_collection,
            "next_collection": next_collection
        }
    
    # Start collection in background
    background_tasks.add_task(
        collect_regulatory_data_from_source,
        source_id=source_id,
        max_pages=max_pages,
        db_factory=get_db
    )
    
    return {
        "source_id": source.id,
        "name": source.name,
        "status": "started",
        "message": "Data collection started in background"
    }

@router.get("/status", response_model=List[RegulatoryDataCollectionStatus])
def get_collection_status(
    country_code: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Get status of all regulatory data collection sources.
    
    Args:
        country_code: Filter by country code
        db: Database session
        
    Returns:
        List of collection status objects
    """
    query = db.query(RegulatorySource)
    
    if country_code:
        country = db.query(Country).filter(Country.code == country_code).first()
        if not country:
            raise HTTPException(status_code=404, detail=f"Country with code {country_code} not found")
        query = query.filter(RegulatorySource.country_id == country.id)
    
    sources = query.all()
    
    result = []
    for source in sources:
        # Get URL count
        url_count = db.query(RegulationURL).filter(
            RegulationURL.source_id == source.id
        ).count()
        
        # Calculate next collection time
        next_collection = None
        if source.last_collection:
            next_collection = source.last_collection + timedelta(hours=source.collection_frequency)
        
        # Determine status
        status = "unknown"
        if not source.last_collection:
            status = "never_run"
        elif source.last_error:
            status = "error"
        elif next_collection and next_collection < datetime.utcnow():
            status = "due"
        else:
            status = "up_to_date"
        
        result.append(RegulatoryDataCollectionStatus(
            source_id=source.id,
            source_name=source.name,
            last_collection=source.last_collection,
            next_collection=next_collection,
            status=status,
            url_count=url_count,
            error=source.last_error
        ))
    
    return result

@router.post("/collect-all", response_model=Dict[str, Any])
def collect_from_all_sources(
    background_tasks: BackgroundTasks,
    force: bool = False,
    country_code: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Collect data from all due regulatory sources.
    
    Args:
        background_tasks: Background tasks
        force: Force collection even if it's not due
        country_code: Filter by country code
        db: Database session
        
    Returns:
        Dictionary with collection status
    """
    query = db.query(RegulatorySource)
    
    if country_code:
        country = db.query(Country).filter(Country.code == country_code).first()
        if not country:
            raise HTTPException(status_code=404, detail=f"Country with code {country_code} not found")
        query = query.filter(RegulatorySource.country_id == country.id)
    
    sources = query.all()
    now = datetime.utcnow()
    
    sources_to_collect = []
    for source in sources:
        collection_due = True
        
        if not force and source.last_collection:
            next_collection = source.last_collection + timedelta(hours=source.collection_frequency)
            if next_collection > now:
                collection_due = False
        
        if collection_due:
            sources_to_collect.append(source.id)
    
    for source_id in sources_to_collect:
        background_tasks.add_task(
            collect_regulatory_data_from_source,
            source_id=source_id,
            db_factory=get_db
        )
    
    return {
        "status": "started",
        "message": f"Data collection started for {len(sources_to_collect)} sources",
        "sources": len(sources_to_collect)
    }

async def collect_regulatory_data_from_source(source_id: int, db_factory, max_pages: int = 5):
    """
    Background task to collect regulatory data from a source.
    
    Args:
        source_id: ID of the source to collect from
        db_factory: Function to get database session
        max_pages: Maximum number of pages to collect
    """
    # Create a new db session for this background task
    db = next(db_factory())
    
    try:
        # Find the source
        source = db.query(RegulatorySource).filter(RegulatorySource.id == source_id).first()
        if not source:
            logger.error(f"Source with ID {source_id} not found")
            return
        
        logger.info(f"Starting data collection from {source.name} (ID: {source.id})")
        
        # Update source with collection time
        source.last_collection = datetime.utcnow()
        source.last_error = None
        db.commit()
        
        # Create scraper
        scraper = RegulatoryScraper()
        
        # Collect data
        regulations = scraper.scrape_regulator_site(source.url, max_pages)
        
        # Process each regulation
        added_count = 0
        for reg in regulations:
            # Check if URL already exists
            existing_url = db.query(RegulationURL).filter(
                RegulationURL.url == reg['url']
            ).first()
            
            if existing_url:
                # Update existing URL if needed
                if 'publication_date' in reg and reg['publication_date']:
                    existing_url.publication_date = reg['publication_date']
                
                continue
            
            # Create new URL
            new_url = RegulationURL(
                url=reg['url'],
                title=reg.get('title', 'Unknown'),
                source_id=source.id,
                regulator_id=source.regulator_id,
                country_id=source.country_id,
                publication_date=reg.get('publication_date'),
                confidence_level=0.8,  # Default confidence
                status='new'
            )
            
            db.add(new_url)
            added_count += 1
        
        db.commit()
        
        logger.info(f"Added {added_count} new regulatory URLs from {source.name}")
        
    except Exception as e:
        logger.error(f"Error collecting data from source {source_id}: {str(e)}")
        
        # Update source with error
        source = db.query(RegulatorySource).filter(RegulatorySource.id == source_id).first()
        if source:
            source.last_error = str(e)
            db.commit()
    finally:
        db.close()
