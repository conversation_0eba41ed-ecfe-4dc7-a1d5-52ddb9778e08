
"""
Router for Regulatory Alerts API endpoints.
"""
from fastapi import APIRouter, Depends, Query, HTTPException, status
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from app.db.database import get_db
from app.db import models
from app.schemas import alerts as schemas

router = APIRouter()

@router.get("/recent", response_model=List[schemas.AlertResponse])
async def get_recent_alerts(
    days: Optional[int] = Query(30, description="Number of days to look back"),
    country_code: Optional[str] = Query(None, description="Filter by country code"),
    importance: Optional[int] = Query(None, ge=1, le=10, description="Filter by minimum importance level"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """
    Get recent regulatory alerts.
    
    - **days**: Number of days to look back for recent alerts (default 30)
    - **country_code**: Optional filter by country code
    - **importance**: Optional filter for minimum importance level (1-10)
    - **skip**: Number of records to skip for pagination
    - **limit**: Maximum number of records to return
    """
    # Calculate the date threshold
    date_threshold = datetime.now() - timedelta(days=days)
    
    # Start building the query
    query = db.query(models.RegulationURL).filter(
        models.RegulationURL.discovery_date >= date_threshold
    )
    
    # Apply filters if provided
    if country_code:
        query = query.join(models.Regulator).join(models.Country).filter(
            models.Country.code == country_code.upper()
        )
    
    if importance:
        query = query.filter(models.RegulationURL.importance >= importance)
    
    # Apply pagination and execute
    total = query.count()
    alerts = query.order_by(models.RegulationURL.discovery_date.desc()).offset(skip).limit(limit).all()
    
    # Transform to response model
    response = [
        schemas.AlertResponse(
            id=alert.id,
            title=alert.title,
            url=alert.url,
            importance=alert.importance,
            discovery_date=alert.discovery_date,
            country_code=alert.regulator.country.code if alert.regulator and alert.regulator.country else None,
            regulator_name=alert.regulator.name if alert.regulator else None
        )
        for alert in alerts
    ]
    
    return response

@router.post("/subscribe", response_model=schemas.SubscriptionResponse, status_code=status.HTTP_201_CREATED)
async def subscribe_to_alerts(
    subscription: schemas.SubscriptionRequest,
    db: Session = Depends(get_db)
):
    """
    Subscribe to receive regulatory alerts via webhook.
    
    - **webhook_url**: URL to receive webhook notifications
    - **countries**: Optional list of country codes to filter alerts
    - **min_importance**: Optional minimum importance level (1-10)
    - **description**: Optional description of the subscription
    """
    # Implementation would create a subscription record and return details
    # This is a simplified example
    
    new_subscription = models.AlertSubscription(
        webhook_url=subscription.webhook_url,
        countries=",".join(subscription.countries) if subscription.countries else None,
        min_importance=subscription.min_importance,
        description=subscription.description,
        created_at=datetime.now()
    )
    
    db.add(new_subscription)
    db.commit()
    db.refresh(new_subscription)
    
    return schemas.SubscriptionResponse(
        id=new_subscription.id,
        webhook_url=new_subscription.webhook_url,
        countries=subscription.countries,
        min_importance=new_subscription.min_importance,
        description=new_subscription.description,
        created_at=new_subscription.created_at
    )

@router.delete("/subscriptions/{subscription_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_subscription(
    subscription_id: int,
    db: Session = Depends(get_db)
):
    """
    Delete an alert subscription.
    
    - **subscription_id**: ID of the subscription to delete
    """
    subscription = db.query(models.AlertSubscription).filter(
        models.AlertSubscription.id == subscription_id
    ).first()
    
    if not subscription:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Subscription not found"
        )
    
    db.delete(subscription)
    db.commit()
    
    return None
