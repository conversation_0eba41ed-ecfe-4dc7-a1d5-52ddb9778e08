
"""
API for regulatory benchmarks and comparisons.
"""
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db import get_db
from app.db.models import Country, Regulator, RegulationURL
from app.utils.regulatory_analysis import calculate_compliance_scores

router = APIRouter()

@router.get("/global", response_model=Dict[str, Any])
async def get_global_benchmarks(
    industry: Optional[str] = Query(None, description="Filter by industry"),
    region: Optional[str] = Query(None, description="Filter by region"),
    db: Session = Depends(get_db)
):
    """
    Get global regulatory benchmarks with optional filtering.
    
    Returns benchmark data for comparison across different regions and countries.
    """
    try:
        # This would normally load from database
        # Here we'll use mock data similar to the compliance API
        benchmarks_data = [
            {"country": "US", "code": "US", "data_protection": 85, "breach_notification": 80, 
             "penalties": 70, "overall_score": 78, "region": "Americas", "industry": "All"},
            {"country": "UK", "code": "UK", "data_protection": 90, "breach_notification": 85, 
             "penalties": 80, "overall_score": 85, "region": "Europe", "industry": "All"},
            {"country": "DE", "code": "DE", "data_protection": 92, "breach_notification": 88, 
             "penalties": 85, "overall_score": 88, "region": "Europe", "industry": "All"},
            {"country": "JP", "code": "JP", "data_protection": 75, "breach_notification": 70, 
             "penalties": 65, "overall_score": 70, "region": "Asia", "industry": "All"},
            {"country": "CA", "code": "CA", "data_protection": 83, "breach_notification": 78, 
             "penalties": 72, "overall_score": 77, "region": "Americas", "industry": "All"},
        ]
        
        # Apply filters
        if region:
            benchmarks_data = [b for b in benchmarks_data if b["region"] == region]
            
        if industry and industry != "All":
            industry_specific_data = [b for b in benchmarks_data if b.get("industry") == industry]
            if industry_specific_data:
                benchmarks_data = industry_specific_data
        
        # Calculate averages by region
        regions = {}
        for benchmark in benchmarks_data:
            region_name = benchmark["region"]
            if region_name not in regions:
                regions[region_name] = {
                    "countries": 0,
                    "total_score": 0
                }
            regions[region_name]["countries"] += 1
            regions[region_name]["total_score"] += benchmark["overall_score"]
            
        # Process the regional data
        regional_averages = []
        for region_name, data in regions.items():
            if data["countries"] > 0:
                regional_averages.append({
                    "region": region_name,
                    "average_score": round(data["total_score"] / data["countries"], 1),
                    "country_count": data["countries"]
                })
                
        return {
            "benchmarks": benchmarks_data,
            "regional_averages": regional_averages,
            "global_average": round(sum(b["overall_score"] for b in benchmarks_data) / len(benchmarks_data), 1) if benchmarks_data else 0
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving benchmarks: {str(e)}")

@router.get("/compare", response_model=Dict[str, Any])
async def compare_countries(
    countries: List[str] = Query(..., description="List of country codes to compare"),
    db: Session = Depends(get_db)
):
    """
    Compare regulatory compliance between specified countries.
    
    Provides detailed side-by-side comparison data for the selected countries.
    """
    try:
        # This would normally load from database
        # Mock data for demonstration
        all_countries = [
            {"country": "US", "code": "US", "data_protection": 85, "breach_notification": 80, 
             "penalties": 70, "overall_score": 78, "region": "Americas"},
            {"country": "UK", "code": "UK", "data_protection": 90, "breach_notification": 85, 
             "penalties": 80, "overall_score": 85, "region": "Europe"},
            {"country": "DE", "code": "DE", "data_protection": 92, "breach_notification": 88, 
             "penalties": 85, "overall_score": 88, "region": "Europe"},
            {"country": "JP", "code": "JP", "data_protection": 75, "breach_notification": 70, 
             "penalties": 65, "overall_score": 70, "region": "Asia"},
            {"country": "CA", "code": "CA", "data_protection": 83, "breach_notification": 78, 
             "penalties": 72, "overall_score": 77, "region": "Americas"},
        ]
        
        # Filter to only requested countries
        selected_countries = [c for c in all_countries if c["code"] in countries]
        
        if not selected_countries:
            raise HTTPException(status_code=404, detail="No matching countries found")
            
        # Identify the highest score in each category for highlighting
        categories = ["data_protection", "breach_notification", "penalties", "overall_score"]
        highlights = {}
        
        for category in categories:
            max_value = max(c[category] for c in selected_countries)
            highlights[category] = max_value
            
        # Add a field to identify if this country has the highest score
        for country in selected_countries:
            country["highlights"] = {
                category: country[category] == highlights[category]
                for category in categories
            }
            
        return {
            "countries": selected_countries,
            "comparison_date": "2023-03-01",  # This would be the current date in a real app
            "categories": [
                {"key": "data_protection", "name": "Data Protection", "description": "Laws related to personal data protection"},
                {"key": "breach_notification", "name": "Breach Notification", "description": "Requirements for reporting data breaches"},
                {"key": "penalties", "name": "Penalties", "description": "Monetary and other penalties for non-compliance"},
                {"key": "overall_score", "name": "Overall Score", "description": "Combined regulatory compliance score"}
            ]
        }
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"Error comparing countries: {str(e)}")
