
"""Bulk URL processing endpoints."""
from typing import List, Dict, Any
import asyncio
import logging

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, File, UploadFile
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
import csv
import io
import tempfile
import os

from app.db import get_db
from app.db.models import RegulationURL, Regulator, Country
from app.schemas.schemas import RegulationURLCreate, RegulationURLWithRegulator

router = APIRouter(
    prefix="/bulk",
    tags=["bulk processing"],
    responses={404: {"description": "Not found"}},
)

logger = logging.getLogger(__name__)

@router.post("/process-csv", response_model=Dict[str, Any])
async def process_csv_upload(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Process CSV file with URLs to analyze.
    
    Args:
        background_tasks: Background tasks runner
        file: CSV file upload containing URLs
        db: Database session
        
    Returns:
        Dict[str, Any]: Processing status and task ID
    """
    if not file.filename.endswith('.csv'):
        raise HTTPException(status_code=400, detail="File must be a CSV")
        
    # Save the uploaded file to a temporary file
    with tempfile.NamedTemporaryFile(delete=False, suffix='.csv') as temp_csv:
        temp_csv_path = temp_csv.name
        content = await file.read()
        temp_csv.write(content)
    
    # Start background processing
    task_id = f"task_{os.path.basename(temp_csv_path)}"
    background_tasks.add_task(
        process_csv_file_background,
        temp_csv_path,
        task_id
    )
    
    return {
        "message": "CSV processing started",
        "task_id": task_id,
        "status": "processing"
    }


async def process_csv_file_background(csv_path: str, task_id: str) -> None:
    """
    Process CSV file with URLs in the background.
    
    Args:
        csv_path: Path to the temporary CSV file
        task_id: Unique task identifier
    """
    results = {
        "task_id": task_id,
        "status": "processing",
        "total_urls": 0,
        "processed_urls": 0,
        "successful": 0,
        "failed": 0,
        "errors": []
    }
    
    try:
        # Import here to avoid circular imports
        from scripts.import_urls import process_url
        
        # Get a database session
        db_generator = get_db()
        db = next(db_generator)
        
        with open(csv_path, 'r') as csvfile:
            # Count total URLs
            reader = csv.reader(csvfile)
            total_urls = sum(1 for _ in reader) - 1  # Subtract header
            results["total_urls"] = total_urls
            
            # Reset file position
            csvfile.seek(0)
            
            # Process URLs
            reader = csv.reader(csvfile)
            next(reader)  # Skip header
            
            for row in reader:
                if not row:
                    continue
                    
                url = row[0].strip()
                results["processed_urls"] += 1
                
                try:
                    process_url(url, db)
                    results["successful"] += 1
                except Exception as e:
                    results["failed"] += 1
                    results["errors"].append(f"Error processing {url}: {str(e)}")
                    logger.error(f"Error processing {url}: {e}")
        
        results["status"] = "completed"
    except Exception as e:
        results["status"] = "failed"
        results["errors"].append(f"Processing error: {str(e)}")
        logger.error(f"CSV processing error: {e}")
    finally:
        # Clean up temporary file
        try:
            os.unlink(csv_path)
        except Exception as e:
            logger.error(f"Error removing temporary file: {e}")
            
        # Store results in a file or database for later retrieval
        with open(f"temp_{task_id}_results.json", "w") as f:
            import json
            json.dump(results, f)


@router.get("/task/{task_id}", response_model=Dict[str, Any])
async def get_task_status(task_id: str) -> Dict[str, Any]:
    """
    Get the status of a background processing task.
    
    Args:
        task_id: The task identifier
        
    Returns:
        Dict[str, Any]: Task status and progress information
    """
    result_file = f"temp_{task_id}_results.json"
    
    if not os.path.exists(result_file):
        return {
            "task_id": task_id,
            "status": "processing",
            "message": "Task is still processing or not found"
        }
        
    with open(result_file, "r") as f:
        import json
        return json.load(f)
