
"""Analytics API endpoints for regulatory data visualization."""
from typing import Dict, Any, List

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy import func

from app.db import get_db
from app.db.models import RegulationURL, Regulator, Country

router = APIRouter(
    prefix="/analytics",
    tags=["analytics"],
    responses={404: {"description": "Not found"}},
)

@router.get("/dashboard", response_model=Dict[str, Any])
def get_dashboard_data(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    Get analytics data for the dashboard.
    
    Returns:
        Dict[str, Any]: Analytics data including confidence distribution,
                        top countries, top regulators, and categories.
    """
    # Get total URLs count
    total_urls = db.query(RegulationURL).count()
    
    # Get confidence distribution
    high_confidence = db.query(RegulationURL).filter(
        RegulationURL.confidence_level >= 0.7
    ).count()
    
    medium_confidence = db.query(RegulationURL).filter(
        RegulationURL.confidence_level >= 0.4,
        RegulationURL.confidence_level < 0.7
    ).count()
    
    low_confidence = db.query(RegulationURL).filter(
        RegulationURL.confidence_level < 0.4
    ).count()
    
    # Get top countries
    countries = db.query(
        Country.name, 
        func.count(RegulationURL.id).label('count')
    ).join(
        Regulator, 
        Country.id == Regulator.country_id
    ).join(
        RegulationURL, 
        Regulator.id == RegulationURL.regulator_id
    ).group_by(
        Country.name
    ).order_by(
        func.count(RegulationURL.id).desc()
    ).limit(10).all()
    
    # Get top regulators
    regulators = db.query(
        Regulator.name, 
        func.count(RegulationURL.id).label('count')
    ).join(
        RegulationURL, 
        Regulator.id == RegulationURL.regulator_id
    ).group_by(
        Regulator.name
    ).order_by(
        func.count(RegulationURL.id).desc()
    ).limit(10).all()
    
    # Get URL categories distribution
    categories = db.query(
        RegulationURL.category,
        func.count(RegulationURL.id).label('count')
    ).group_by(
        RegulationURL.category
    ).all()
    
    return {
        "total_urls": total_urls,
        "confidence_distribution": {
            "high": high_confidence,
            "medium": medium_confidence,
            "low": low_confidence
        },
        "top_countries": [{"name": name, "count": count} for name, count in countries],
        "top_regulators": [{"name": name, "count": count} for name, count in regulators],
        "categories": {category: count for category, count in categories if category}
    }
