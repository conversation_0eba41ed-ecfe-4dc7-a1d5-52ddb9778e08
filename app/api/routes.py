
from fastapi import APIRouter, Request, Depends, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from app.db import get_db
import os

router = APIRouter()

templates = Jinja2Templates(directory="templates")

@router.get("/data-enrichment", response_class=HTMLResponse)
async def data_enrichment_page(request: Request, job_id: str = None):
    """Data enrichment review page."""
    return templates.TemplateResponse(
        "data_enrichment.html",
        {"request": request, "job_id": job_id}
    )


@router.get("/document-summarizer", response_class=HTMLResponse)
async def document_summarizer_page(request: Request):
    """Render the AI document summarizer page."""
    return templates.TemplateResponse("ai_document_summary.html", {"request": request})
