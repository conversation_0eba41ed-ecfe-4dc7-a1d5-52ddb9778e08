
"""
Compliance API endpoints for regulatory compliance data.
"""
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.db import get_db
from app.db.models import Country, Regulator, RegulationURL
from app.utils.regulatory_analysis import (
    calculate_compliance_scores,
    identify_critical_gaps,
    generate_recommendations,
    calculate_regional_averages
)

router = APIRouter()

@router.get("/summary", response_model=Dict[str, Any])
async def get_compliance_summary(
    db: Session = Depends(get_db)
):
    """
    Get a summary of compliance data including overall score, regional scores,
    critical gaps, recommendations, and recent regulatory changes.
    """
    # Load regulatory data
    try:
        # This would normally load from a database or external source
        # For now we'll use mock data
        countries_data = [
            {"country": "US", "code": "US", "data_protection_score": 85, "breach_notification_score": 80, 
             "penalties_score": 70, "overall_compliance": 78, "region": "Americas"},
            {"country": "UK", "code": "UK", "data_protection_score": 90, "breach_notification_score": 85, 
             "penalties_score": 80, "overall_compliance": 85, "region": "Europe"},
            {"country": "DE", "code": "DE", "data_protection_score": 92, "breach_notification_score": 88, 
             "penalties_score": 85, "overall_compliance": 88, "region": "Europe"},
            {"country": "JP", "code": "JP", "data_protection_score": 75, "breach_notification_score": 70, 
             "penalties_score": 65, "overall_compliance": 70, "region": "Asia"},
            {"country": "CA", "code": "CA", "data_protection_score": 83, "breach_notification_score": 78, 
             "penalties_score": 72, "overall_compliance": 77, "region": "Americas"},
        ]
        
        # Calculate weighted compliance scores
        compliance_data = calculate_compliance_scores(countries_data)
        
        # Identify critical gaps
        gaps = identify_critical_gaps(countries_data, threshold=80)
        
        # Generate recommendations
        recommendations = generate_recommendations(countries_data, gaps)
        
        # Calculate regional averages
        regional_data = calculate_regional_averages(countries_data)
        
        # Recent regulatory changes (mock data)
        recent_changes = [
            {
                "country": "US", 
                "date": "2023-06-15", 
                "description": "Updated breach notification requirements for financial institutions"
            },
            {
                "country": "EU", 
                "date": "2023-05-22", 
                "description": "New data protection guidelines for cloud service providers"
            },
            {
                "country": "JP", 
                "date": "2023-04-10", 
                "description": "Revised security requirements for personal data"
            }
        ]
        
        # Format critical gaps for display
        formatted_gaps = []
        for country_code in gaps.get("countries_with_gaps", []):
            country_info = next((c for c in countries_data if c["code"] == country_code), None)
            if country_info:
                for gap_category in gaps.get("gap_categories", {}).get(country_code, []):
                    severity = "HIGH" if gap_category == "data_protection_score" else "MEDIUM"
                    formatted_gaps.append({
                        "country": country_info["country"],
                        "description": f"Insufficient {gap_category.replace('_score', '').replace('_', ' ')}",
                        "severity": severity
                    })
        
        # Format recommendations for display
        formatted_recommendations = []
        for country_code, recs in recommendations.get("country_recommendations", {}).items():
            country_info = next((c for c in countries_data if c["code"] == country_code), None)
            if country_info and recs:
                formatted_recommendations.append({
                    "title": f"Improve {country_info['country']} Compliance",
                    "description": recs[0] if recs else "No specific recommendations"
                })
        
        # Format regional data for chart
        regional_scores = []
        for _, row in regional_data.iterrows():
            regional_scores.append({
                "region": row["region"],
                "score": row["avg_overall_compliance"]
            })
        
        # Calculate overall compliance score (average of all countries)
        overall_score = sum(c["overall_compliance"] for c in countries_data) / len(countries_data)
        
        return {
            "overall_score": overall_score,
            "regional_scores": regional_scores,
            "critical_gaps": formatted_gaps,
            "recommendations": formatted_recommendations,
            "recent_changes": recent_changes
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating compliance summary: {str(e)}")

@router.get("/countries", response_model=List[Dict[str, Any]])
async def get_country_compliance(
    country_code: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """
    Get compliance data for specific countries or all countries.
    """
    try:
        # Mock data - in a real application, this would be retrieved from a database
        countries_data = [
            {"country": "US", "code": "US", "data_protection_score": 85, "breach_notification_score": 80, 
             "penalties_score": 70, "overall_compliance": 78, "region": "Americas"},
            {"country": "UK", "code": "UK", "data_protection_score": 90, "breach_notification_score": 85, 
             "penalties_score": 80, "overall_compliance": 85, "region": "Europe"},
            {"country": "DE", "code": "DE", "data_protection_score": 92, "breach_notification_score": 88, 
             "penalties_score": 85, "overall_compliance": 88, "region": "Europe"},
            {"country": "JP", "code": "JP", "data_protection_score": 75, "breach_notification_score": 70, 
             "penalties_score": 65, "overall_compliance": 70, "region": "Asia"},
            {"country": "CA", "code": "CA", "data_protection_score": 83, "breach_notification_score": 78, 
             "penalties_score": 72, "overall_compliance": 77, "region": "Americas"},
        ]
        
        if country_code:
            filtered_data = [c for c in countries_data if c["code"] == country_code.upper()]
            if not filtered_data:
                raise HTTPException(status_code=404, detail=f"Country {country_code} not found")
            return filtered_data
        
        return countries_data
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving country compliance data: {str(e)}")
