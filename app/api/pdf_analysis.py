
"""PDF document analysis API for regulatory compliance."""
from typing import Dict, Any, List, Optional
import tempfile
import logging
import os
import uuid

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, BackgroundTasks
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

from app.db import get_db
from app.utils.pdf_analyzer import analyze_pdf_document

router = APIRouter(
    prefix="/pdf",
    tags=["pdf analysis"],
    responses={404: {"description": "Not found"}},
)

logger = logging.getLogger(__name__)

# Store analysis results in memory (in a real app, would use a database)
analysis_results = {}

@router.post("/analyze", response_model=Dict[str, Any])
async def analyze_pdf(
    background_tasks: BackgroundTasks,
    document: UploadFile = File(...),
    country: str = Form(None),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Analyze a PDF document for regulatory content.
    
    Args:
        background_tasks: FastAPI background tasks
        document: PDF document to analyze
        country: Country code for localized analysis (optional)
        db: Database session
        
    Returns:
        Dict[str, Any]: Job ID and status for tracking the analysis
    """
    # Validate file type
    if not document.content_type.lower() in ["application/pdf", "binary/octet-stream"]:
        raise HTTPException(
            status_code=400,
            detail="Invalid file type. Only PDF files are accepted."
        )
    
    # Generate a unique job ID
    job_id = str(uuid.uuid4())
    
    # Save uploaded file to a temporary location
    suffix = os.path.splitext(document.filename)[1].lower()
    with tempfile.NamedTemporaryFile(suffix=suffix, delete=False) as tmp:
        tmp_path = tmp.name
        content = await document.read()
        tmp.write(content)
    
    # Initialize the job status
    analysis_results[job_id] = {
        "status": "processing",
        "filename": document.filename,
        "country": country
    }
    
    # Run analysis in background
    background_tasks.add_task(run_pdf_analysis, job_id, tmp_path)
    
    return {
        "job_id": job_id,
        "status": "processing",
        "message": f"Analysis of {document.filename} started. Check /pdf/status/{job_id} for results."
    }

def run_pdf_analysis(job_id: str, file_path: str, cleanup: bool = True) -> None:
    """
    Run the PDF analysis in the background.
    
    Args:
        job_id: The unique job identifier
        file_path: Path to the temporary PDF file
        cleanup: Whether to delete the temporary file after analysis
    """
    try:
        # Analyze the PDF document
        analysis_result = analyze_pdf_document(file_path)
        
        # Update the results
        analysis_results[job_id].update({
            "status": "completed",
            "result": analysis_result
        })
        
    except Exception as e:
        # Log the error
        logger.error(f"Error analyzing PDF: {str(e)}")
        
        # Update job status to failed
        analysis_results[job_id].update({
            "status": "failed",
            "error": str(e)
        })
    
    finally:
        # Clean up the temporary file if requested
        if cleanup and os.path.exists(file_path):
            try:
                os.unlink(file_path)
            except Exception as e:
                logger.error(f"Error deleting temporary file: {str(e)}")

@router.post("/analyze", response_model=Dict[str, Any])
async def analyze_pdf(
    background_tasks: BackgroundTasks,
    document: UploadFile = File(...),
    country: Optional[str] = Form(None),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Analyze a PDF document for regulatory compliance.
    
    Args:
        background_tasks: Background tasks runner
        document: The PDF file to analyze
        country: Optional country code to focus analysis
        db: Database session
        
    Returns:
        Dict[str, Any]: Analysis job ID and status
    """
    # Validate file type
    if not document.filename.lower().endswith('.pdf'):
        raise HTTPException(
            status_code=400, 
            detail="Invalid file type. Only PDF files are accepted."
        )
    
    # Save uploaded file to temp location
    with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as temp_file:
        temp_path = temp_file.name
        content = await document.read()
        temp_file.write(content)
    
    # Generate a job ID
    job_id = str(uuid.uuid4())
    
    # Store initial status
    analysis_results[job_id] = {
        "status": "processing",
        "filename": document.filename,
        "country": country
    }
    
    # Run analysis in background
    background_tasks.add_task(
        run_pdf_analysis, 
        job_id=job_id, 
        file_path=temp_path,
        cleanup=True
    )
    
    # Return job ID for status checking
    return {
        "job_id": job_id,
        "status": "processing",
        "message": "PDF analysis started. Use the /pdf/status/{job_id} endpoint to check results."
    }

@router.get("/status/{job_id}", response_model=Dict[str, Any])
async def get_analysis_status(job_id: str) -> Dict[str, Any]:
    """
    Get the status of a PDF analysis job.
    
    Args:
        job_id: The job ID returned by the analyze endpoint
        
    Returns:
        Dict[str, Any]: Analysis status and results if complete
    """
    if job_id not in analysis_results:
        raise HTTPException(
            status_code=404,
            detail=f"Analysis job {job_id} not found"
        )
    
    return analysis_results[job_id]

def run_pdf_analysis(job_id: str, file_path: str, cleanup: bool = True):
    """
    Run PDF analysis in the background.
    
    Args:
        job_id: The job ID
        file_path: Path to the PDF file
        cleanup: Whether to delete the file after analysis
    """
    try:
        # Run analysis
        result = analyze_pdf_document(file_path)
        
        # Update result
        analysis_results[job_id].update({
            "status": "completed",
            "result": result
        })
        
    except Exception as e:
        logger.error(f"Error analyzing PDF: {str(e)}")
        analysis_results[job_id].update({
            "status": "failed",
            "error": str(e)
        })
    
    finally:
        # Clean up temporary file
        if cleanup and os.path.exists(file_path):
            try:
                os.unlink(file_path)
            except Exception as e:
                logger.error(f"Error deleting temporary file: {str(e)}")
