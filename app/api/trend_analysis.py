
"""
API for regulatory trend analysis.
"""
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field
from datetime import datetime, timedelta
import random

from app.db import get_db
from app.db.models import RegulationURL, RegulatoryChange

router = APIRouter()

class TrendPeriod(BaseModel):
    """Model for trend analysis period."""
    start_date: datetime
    end_date: datetime
    regions: Optional[List[str]] = None
    categories: Optional[List[str]] = None

class RegulatoryTrend(BaseModel):
    """Model for regulatory trends."""
    category: str
    region: str
    count: int
    change_rate: float  # percentage change from previous period
    severity_avg: float

@router.post("/analyze", response_model=Dict[str, Any])
async def analyze_trends(
    period: TrendPeriod,
    db: Session = Depends(get_db)
):
    """
    Analyze regulatory trends over a specified period.
    """
    try:
        # Validate date range
        if period.end_date <= period.start_date:
            raise HTTPException(status_code=400, detail="End date must be after start date")
            
        # Calculate previous period of same length for comparison
        period_length = (period.end_date - period.start_date).days
        prev_end_date = period.start_date
        prev_start_date = prev_end_date - timedelta(days=period_length)
        
        # Mock data - in a real application, this would query the database
        categories = ["Data Protection", "Privacy", "Cybersecurity", "Financial", "Healthcare"]
        regions = ["EU", "US", "APAC", "LATAM", "MEA"]
        
        # Filter categories and regions if specified
        if period.categories:
            categories = [c for c in categories if c in period.categories]
        if period.regions:
            regions = [r for r in regions if r in period.regions]
            
        # Generate mock trend data
        trends = []
        for category in categories:
            for region in regions:
                # Generate random counts for current and previous periods
                current_count = random.randint(5, 30)
                prev_count = random.randint(3, 25)
                
                # Calculate change rate
                if prev_count > 0:
                    change_rate = ((current_count - prev_count) / prev_count) * 100
                else:
                    change_rate = 100  # New category/region
                    
                trends.append({
                    "category": category,
                    "region": region,
                    "count": current_count,
                    "change_rate": round(change_rate, 2),
                    "severity_avg": round(random.uniform(3.0, 9.0), 2)
                })
        
        # Sort trends by count (descending)
        trends.sort(key=lambda x: x["count"], reverse=True)
        
        # Calculate overall stats
        total_regulations = sum(t["count"] for t in trends)
        avg_change_rate = sum(t["change_rate"] for t in trends) / len(trends) if trends else 0
        
        # Find fastest growing categories and regions
        category_growth = {}
        region_growth = {}
        
        for trend in trends:
            cat = trend["category"]
            reg = trend["region"]
            
            if cat not in category_growth or trend["change_rate"] > category_growth[cat]:
                category_growth[cat] = trend["change_rate"]
                
            if reg not in region_growth or trend["change_rate"] > region_growth[reg]:
                region_growth[reg] = trend["change_rate"]
        
        fastest_growing_category = max(category_growth.items(), key=lambda x: x[1]) if category_growth else None
        fastest_growing_region = max(region_growth.items(), key=lambda x: x[1]) if region_growth else None
        
        return {
            "period": {
                "start_date": period.start_date,
                "end_date": period.end_date,
                "days": period_length
            },
            "previous_period": {
                "start_date": prev_start_date,
                "end_date": prev_end_date
            },
            "overall_stats": {
                "total_regulations": total_regulations,
                "avg_change_rate": round(avg_change_rate, 2)
            },
            "fastest_growing": {
                "category": fastest_growing_category[0] if fastest_growing_category else None,
                "category_change_rate": round(fastest_growing_category[1], 2) if fastest_growing_category else 0,
                "region": fastest_growing_region[0] if fastest_growing_region else None,
                "region_change_rate": round(fastest_growing_region[1], 2) if fastest_growing_region else 0
            },
            "trends": trends
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error analyzing regulatory trends: {str(e)}")

@router.get("/hot-topics", response_model=Dict[str, Any])
async def get_hot_topics(
    days: Optional[int] = Query(90, ge=30, le=365),
    db: Session = Depends(get_db)
):
    """
    Get emerging regulatory hot topics based on recent changes.
    """
    try:
        # Mock data - in a real application, this would analyze recent regulatory changes
        hot_topics = [
            {
                "topic": "AI Regulation",
                "mentions": 47,
                "growth_rate": 215.5,  # percentage growth
                "regions": ["EU", "US", "UK"],
                "sample_regulations": [
                    "EU AI Act",
                    "US Executive Order on AI",
                    "UK AI Safety Framework"
                ]
            },
            {
                "topic": "Data Sovereignty",
                "mentions": 32,
                "growth_rate": 143.2,
                "regions": ["EU", "APAC", "MEA"],
                "sample_regulations": [
                    "EU Data Act",
                    "India Data Protection Bill",
                    "Saudi Data Protection Law"
                ]
            },
            {
                "topic": "Cloud Security",
                "mentions": 28,
                "growth_rate": 85.7,
                "regions": ["US", "EU", "APAC"],
                "sample_regulations": [
                    "US CIRCIA",
                    "EU NIS2 Directive",
                    "Singapore CIIP Act"
                ]
            },
            {
                "topic": "Children's Privacy",
                "mentions": 21,
                "growth_rate": 75.0,
                "regions": ["US", "EU", "UK", "AU"],
                "sample_regulations": [
                    "US COPPA Update",
                    "UK Age Appropriate Design Code",
                    "EU DSA"
                ]
            },
            {
                "topic": "Digital Identity",
                "mentions": 19,
                "growth_rate": 58.3,
                "regions": ["EU", "APAC", "CA"],
                "sample_regulations": [
                    "EU Digital Identity Framework",
                    "Singapore Digital ID Act",
                    "Canada Digital Charter"
                ]
            }
        ]
        
        # Sort by growth rate
        hot_topics.sort(key=lambda x: x["growth_rate"], reverse=True)
        
        return {
            "period_days": days,
            "analysis_date": datetime.now(),
            "hot_topics": hot_topics
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving hot topics: {str(e)}")
