
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from pydantic import BaseModel
from sqlalchemy.orm import Session
from app.db import get_db
from app.db.models.country import Country
from app.db.models.regulator import Regulator
from app.db.models.regulation_url import RegulationURL
from app.db.models.regulatory_source import RegulatorySource
import json
import uuid
import os
from pathlib import Path
import logging

router = APIRouter(prefix="/api/v1/data-enrichment", tags=["data_enrichment"])

# Models
class FieldChange(BaseModel):
    field: str
    current_value: Any
    suggested_value: Any
    confidence: float

class EnrichmentSuggestion(BaseModel):
    id: int
    record_type: str
    name: str
    original_data: Dict[str, Any]
    enriched_data: Dict[str, Any]
    field_changes: List[FieldChange]
    overall_confidence: float

class EnrichmentSuggestionResponse(BaseModel):
    record_type: str
    suggestions: List[EnrichmentSuggestion]

class ApprovedChange(BaseModel):
    id: int
    record_type: str

class ApplyChangesRequest(BaseModel):
    approved_changes: List[ApprovedChange]

class ApplyChangesResponse(BaseModel):
    success: bool
    applied_count: int
    record_type: str
    errors: List[str]

# Temporary storage for enrichment jobs
ENRICHMENT_CACHE_DIR = Path("./cache/enrichment")
os.makedirs(ENRICHMENT_CACHE_DIR, exist_ok=True)

@router.get("/suggestions/{job_id}", response_model=EnrichmentSuggestionResponse)
async def get_enrichment_suggestions(
    job_id: str,
    db: Session = Depends(get_db)
):
    """Get enrichment suggestions for a specific job."""
    job_file = ENRICHMENT_CACHE_DIR / f"{job_id}.json"
    
    if not job_file.exists():
        raise HTTPException(status_code=404, detail="Enrichment job not found")
    
    try:
        with open(job_file, 'r') as f:
            suggestions_data = json.load(f)
        
        return suggestions_data
    except Exception as e:
        logging.error(f"Error loading enrichment suggestions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error loading enrichment data: {str(e)}")

@router.post("/generate", response_model=Dict[str, str])
async def generate_enrichment_suggestions(
    record_type: str = Query(..., description="Type of records to enrich (country, regulator, regulation)"),
    min_confidence: float = Query(0.3, description="Minimum confidence threshold for suggestions"),
    background_tasks: BackgroundTasks = None,
    db: Session = Depends(get_db)
):
    """Generate enrichment suggestions for a specific record type."""
    job_id = str(uuid.uuid4())
    
    # Add the task to background processing
    background_tasks.add_task(
        process_enrichment_suggestions,
        job_id=job_id,
        record_type=record_type,
        min_confidence=min_confidence,
        db=db
    )
    
    return {"job_id": job_id, "status": "processing"}

@router.post("/apply/{job_id}", response_model=ApplyChangesResponse)
async def apply_enrichment(
    job_id: str,
    request: ApplyChangesRequest,
    db: Session = Depends(get_db)
):
    """Apply approved enrichment changes."""
    job_file = ENRICHMENT_CACHE_DIR / f"{job_id}.json"
    
    if not job_file.exists():
        raise HTTPException(status_code=404, detail="Enrichment job not found")
    
    try:
        # Load the enrichment data
        with open(job_file, 'r') as f:
            enrichment_data = json.load(f)
        
        record_type = enrichment_data["record_type"]
        suggestions = {s["id"]: s for s in enrichment_data["suggestions"]}
        
        # Process approved changes
        applied_count = 0
        errors = []
        
        for change in request.approved_changes:
            if change.id not in suggestions:
                errors.append(f"Record with ID {change.id} not found in enrichment data")
                continue
            
            suggestion = suggestions[change.id]
            
            try:
                # Apply the changes based on record type
                if record_type == "country":
                    apply_country_changes(db, change.id, suggestion["enriched_data"])
                elif record_type == "regulator":
                    apply_regulator_changes(db, change.id, suggestion["enriched_data"])
                elif record_type == "regulation":
                    apply_regulation_changes(db, change.id, suggestion["enriched_data"])
                elif record_type == "regulatory_source":
                    apply_source_changes(db, change.id, suggestion["enriched_data"])
                else:
                    errors.append(f"Unsupported record type: {record_type}")
                    continue
                
                applied_count += 1
            except Exception as e:
                errors.append(f"Error applying changes to {record_type} {change.id}: {str(e)}")
        
        db.commit()
        
        # Mark the job as completed by appending result to the file
        with open(job_file, 'w') as f:
            enrichment_data["applied"] = {
                "count": applied_count,
                "errors": errors
            }
            json.dump(enrichment_data, f)
        
        return {
            "success": True,
            "applied_count": applied_count,
            "record_type": record_type,
            "errors": errors
        }
        
    except Exception as e:
        db.rollback()
        logging.error(f"Error applying enrichment changes: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error applying changes: {str(e)}")

# Background processing function
def process_enrichment_suggestions(
    job_id: str,
    record_type: str,
    min_confidence: float,
    db: Session
):
    """Process and generate enrichment suggestions in the background."""
    try:
        suggestions = []
        
        if record_type == "country":
            suggestions = generate_country_enrichment(db, min_confidence)
        elif record_type == "regulator":
            suggestions = generate_regulator_enrichment(db, min_confidence)
        elif record_type == "regulation":
            suggestions = generate_regulation_enrichment(db, min_confidence)
        elif record_type == "regulatory_source":
            suggestions = generate_source_enrichment(db, min_confidence)
        
        # Save the result to a file
        result = {
            "record_type": record_type,
            "suggestions": suggestions
        }
        
        with open(ENRICHMENT_CACHE_DIR / f"{job_id}.json", 'w') as f:
            json.dump(result, f)
            
    except Exception as e:
        logging.error(f"Error generating enrichment suggestions: {str(e)}")
        
        # Save error to file
        with open(ENRICHMENT_CACHE_DIR / f"{job_id}.json", 'w') as f:
            json.dump({
                "record_type": record_type,
                "error": str(e),
                "suggestions": []
            }, f)

# Enrichment generation functions
def generate_country_enrichment(db: Session, min_confidence: float) -> List[EnrichmentSuggestion]:
    """Generate enrichment suggestions for country records."""
    countries = db.query(Country).all()
    suggestions = []
    
    for country in countries:
        # Skip countries that are already well-populated
        if country.name and country.code and country.region:
            continue
            
        original_data = {
            "name": country.name,
            "code": country.code,
            "region": country.region,
            "capital": getattr(country, "capital", None),
            "population": getattr(country, "population", None)
        }
        
        # Simulate enriched data (in a real system, this would query external APIs or ML models)
        enriched_data = dict(original_data)
        field_changes = []
        
        # Generate some sample enrichments
        if not country.code and country.name:
            from app.utils.document_classifier import get_country_code_from_name
            code, confidence = get_country_code_from_name(country.name)
            if code and confidence > min_confidence:
                enriched_data["code"] = code
                field_changes.append(FieldChange(
                    field="code",
                    current_value=country.code,
                    suggested_value=code,
                    confidence=confidence
                ))
        
        if not country.region and country.name:
            from app.utils.document_classifier import get_country_region
            region, confidence = get_country_region(country.name)
            if region and confidence > min_confidence:
                enriched_data["region"] = region
                field_changes.append(FieldChange(
                    field="region",
                    current_value=country.region,
                    suggested_value=region,
                    confidence=confidence
                ))
        
        # Only add suggestions if there are actual changes
        if field_changes:
            # Calculate overall confidence as average of individual confidences
            overall_confidence = sum(fc.confidence for fc in field_changes) / len(field_changes)
            
            suggestions.append(EnrichmentSuggestion(
                id=country.id,
                record_type="country",
                name=country.name or f"Country #{country.id}",
                original_data=original_data,
                enriched_data=enriched_data,
                field_changes=field_changes,
                overall_confidence=overall_confidence
            ))
    
    return suggestions

def generate_regulator_enrichment(db: Session, min_confidence: float) -> List[EnrichmentSuggestion]:
    """Generate enrichment suggestions for regulator records."""
    regulators = db.query(Regulator).all()
    suggestions = []
    
    for regulator in regulators:
        original_data = {
            "name": regulator.name,
            "country_id": getattr(regulator, "country_id", None),
            "website": getattr(regulator, "website", None),
            "description": getattr(regulator, "description", None)
        }
        
        # Simulate enriched data
        enriched_data = dict(original_data)
        field_changes = []
        
        # Generate some sample enrichments
        if not regulator.website and regulator.name:
            from app.utils.document_classifier import get_regulator_website
            website, confidence = get_regulator_website(regulator.name)
            if website and confidence > min_confidence:
                enriched_data["website"] = website
                field_changes.append(FieldChange(
                    field="website",
                    current_value=getattr(regulator, "website", None),
                    suggested_value=website,
                    confidence=confidence
                ))
        
        if not regulator.country_id and regulator.name:
            from app.utils.document_classifier import get_regulator_country
            country_id, confidence = get_regulator_country(regulator.name, db)
            if country_id and confidence > min_confidence:
                enriched_data["country_id"] = country_id
                field_changes.append(FieldChange(
                    field="country_id",
                    current_value=getattr(regulator, "country_id", None),
                    suggested_value=country_id,
                    confidence=confidence
                ))
        
        # Only add suggestions if there are actual changes
        if field_changes:
            overall_confidence = sum(fc.confidence for fc in field_changes) / len(field_changes)
            
            suggestions.append(EnrichmentSuggestion(
                id=regulator.id,
                record_type="regulator",
                name=regulator.name or f"Regulator #{regulator.id}",
                original_data=original_data,
                enriched_data=enriched_data,
                field_changes=field_changes,
                overall_confidence=overall_confidence
            ))
    
    return suggestions

def generate_regulation_enrichment(db: Session, min_confidence: float) -> List[EnrichmentSuggestion]:
    """Generate enrichment suggestions for regulation records."""
    regulations = db.query(RegulationURL).all()
    suggestions = []
    
    for regulation in regulations:
        original_data = {
            "url": regulation.url,
            "country_id": regulation.country_id,
            "regulator_id": regulation.regulator_id,
            "source_id": regulation.source_id,
            "title": getattr(regulation, "title", None),
            "published_date": getattr(regulation, "published_date", None) 
        }
        
        # Simulate enriched data
        enriched_data = dict(original_data)
        field_changes = []
        
        # Generate some sample enrichments based on URL content
        if regulation.url and not regulation.title:
            from app.utils.document_classifier import extract_title_from_url
            title, confidence = extract_title_from_url(regulation.url)
            if title and confidence > min_confidence:
                enriched_data["title"] = title
                field_changes.append(FieldChange(
                    field="title",
                    current_value=getattr(regulation, "title", None),
                    suggested_value=title,
                    confidence=confidence
                ))
        
        if regulation.url and not regulation.country_id:
            from app.utils.document_classifier import extract_country_from_url
            country_id, confidence = extract_country_from_url(regulation.url, db)
            if country_id and confidence > min_confidence:
                enriched_data["country_id"] = country_id
                field_changes.append(FieldChange(
                    field="country_id",
                    current_value=regulation.country_id,
                    suggested_value=country_id,
                    confidence=confidence
                ))
        
        # Only add suggestions if there are actual changes
        if field_changes:
            overall_confidence = sum(fc.confidence for fc in field_changes) / len(field_changes)
            
            # Get title or URL for display
            name = getattr(regulation, "title", None) or regulation.url
            if len(name) > 60:
                name = name[:57] + "..."
            
            suggestions.append(EnrichmentSuggestion(
                id=regulation.id,
                record_type="regulation",
                name=name or f"Regulation #{regulation.id}",
                original_data=original_data,
                enriched_data=enriched_data,
                field_changes=field_changes,
                overall_confidence=overall_confidence
            ))
    
    return suggestions

def generate_source_enrichment(db: Session, min_confidence: float) -> List[EnrichmentSuggestion]:
    """Generate enrichment suggestions for regulatory source records."""
    sources = db.query(RegulatorySource).all()
    suggestions = []
    
    for source in sources:
        original_data = {
            "name": source.name,
            "url": getattr(source, "url", None),
            "description": getattr(source, "description", None),
            "reliability": getattr(source, "reliability", None)
        }
        
        # Simulate enriched data
        enriched_data = dict(original_data)
        field_changes = []
        
        # Generate some sample enrichments
        if source.name and not getattr(source, "reliability", None):
            from app.utils.document_classifier import assess_source_reliability
            reliability, confidence = assess_source_reliability(source.name)
            if reliability is not None and confidence > min_confidence:
                enriched_data["reliability"] = reliability
                field_changes.append(FieldChange(
                    field="reliability",
                    current_value=getattr(source, "reliability", None),
                    suggested_value=reliability,
                    confidence=confidence
                ))
        
        # Only add suggestions if there are actual changes
        if field_changes:
            overall_confidence = sum(fc.confidence for fc in field_changes) / len(field_changes)
            
            suggestions.append(EnrichmentSuggestion(
                id=source.id,
                record_type="regulatory_source",
                name=source.name or f"Source #{source.id}",
                original_data=original_data,
                enriched_data=enriched_data,
                field_changes=field_changes,
                overall_confidence=overall_confidence
            ))
    
    return suggestions

# Change application functions
def apply_country_changes(db: Session, country_id: int, enriched_data: Dict[str, Any]):
    """Apply changes to a country record."""
    country = db.query(Country).filter(Country.id == country_id).first()
    if not country:
        raise ValueError(f"Country with ID {country_id} not found")
    
    # Apply changes from enriched data
    if "code" in enriched_data:
        country.code = enriched_data["code"]
    
    if "region" in enriched_data:
        country.region = enriched_data["region"]
    
    if "capital" in enriched_data:
        country.capital = enriched_data["capital"]
    
    if "population" in enriched_data:
        country.population = enriched_data["population"]

def apply_regulator_changes(db: Session, regulator_id: int, enriched_data: Dict[str, Any]):
    """Apply changes to a regulator record."""
    regulator = db.query(Regulator).filter(Regulator.id == regulator_id).first()
    if not regulator:
        raise ValueError(f"Regulator with ID {regulator_id} not found")
    
    # Apply changes from enriched data
    if "website" in enriched_data:
        regulator.website = enriched_data["website"]
    
    if "country_id" in enriched_data:
        regulator.country_id = enriched_data["country_id"]
    
    if "description" in enriched_data:
        regulator.description = enriched_data["description"]

def apply_regulation_changes(db: Session, regulation_id: int, enriched_data: Dict[str, Any]):
    """Apply changes to a regulation record."""
    regulation = db.query(RegulationURL).filter(RegulationURL.id == regulation_id).first()
    if not regulation:
        raise ValueError(f"Regulation with ID {regulation_id} not found")
    
    # Apply changes from enriched data
    if "title" in enriched_data:
        regulation.title = enriched_data["title"]
    
    if "country_id" in enriched_data:
        regulation.country_id = enriched_data["country_id"]
    
    if "regulator_id" in enriched_data:
        regulation.regulator_id = enriched_data["regulator_id"]
    
    if "source_id" in enriched_data:
        regulation.source_id = enriched_data["source_id"]
    
    if "published_date" in enriched_data:
        regulation.published_date = enriched_data["published_date"]

def apply_source_changes(db: Session, source_id: int, enriched_data: Dict[str, Any]):
    """Apply changes to a regulatory source record."""
    source = db.query(RegulatorySource).filter(RegulatorySource.id == source_id).first()
    if not source:
        raise ValueError(f"Regulatory source with ID {source_id} not found")
    
    # Apply changes from enriched data
    if "url" in enriched_data:
        source.url = enriched_data["url"]
    
    if "description" in enriched_data:
        source.description = enriched_data["description"]
    
    if "reliability" in enriched_data:
        source.reliability = enriched_data["reliability"]
