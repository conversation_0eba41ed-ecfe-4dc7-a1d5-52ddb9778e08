"""
Base client for regulatory data sources.
Provides common functionality for all data source clients.
"""
import logging
import time
from typing import Dict, List, Optional, Any, Union
import requests
from datetime import datetime, timedelta
from pydantic import BaseModel

logger = logging.getLogger(__name__)

class SourceConfig(BaseModel):
    """Configuration for a data source."""
    name: str
    base_url: str
    api_key: Optional[str] = None
    rate_limit: float = 1.0  # Requests per second
    timeout: int = 30
    retry_count: int = 3
    retry_delay: int = 5
    headers: Optional[Dict[str, str]] = None

class DataSourceClient:
    """Base client for regulatory data sources."""
    
    def __init__(self, config: SourceConfig):
        """
        Initialize the data source client.
        
        Args:
            config: Configuration for the data source
        """
        self.config = config
        self.session = requests.Session()
        
        # Set default headers
        default_headers = {
            'User-Agent': 'RegulationGuru Research Bot (<EMAIL>)'
        }
        
        # Merge with custom headers if provided
        if config.headers:
            default_headers.update(config.headers)
            
        self.session.headers.update(default_headers)
        
        # Add API key to headers if provided
        if config.api_key:
            self.session.headers.update({'X-API-Key': config.api_key})
            
        self.last_request_time = 0
        
    def _respect_rate_limit(self):
        """Respect the rate limit by waiting if necessary."""
        if self.config.rate_limit <= 0:
            return
            
        current_time = time.time()
        elapsed = current_time - self.last_request_time
        
        if elapsed < (1.0 / self.config.rate_limit):
            sleep_time = (1.0 / self.config.rate_limit) - elapsed
            time.sleep(sleep_time)
            
        self.last_request_time = time.time()
        
    def _make_request(
        self, 
        endpoint: str, 
        method: str = 'GET', 
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        json_data: Optional[Dict[str, Any]] = None
    ) -> Optional[Union[Dict[str, Any], List[Any], str]]:
        """
        Make a request to the data source API.
        
        Args:
            endpoint: API endpoint
            method: HTTP method
            params: Query parameters
            data: Form data
            json_data: JSON data
            
        Returns:
            Response data or None if the request failed
        """
        url = f"{self.config.base_url.rstrip('/')}/{endpoint.lstrip('/')}"
        
        for attempt in range(self.config.retry_count + 1):
            try:
                self._respect_rate_limit()
                
                response = self.session.request(
                    method=method,
                    url=url,
                    params=params,
                    data=data,
                    json=json_data,
                    timeout=self.config.timeout
                )
                
                response.raise_for_status()
                
                # Try to parse as JSON first
                try:
                    return response.json()
                except ValueError:
                    # If not JSON, return text
                    return response.text
                    
            except requests.exceptions.RequestException as e:
                logger.warning(f"Request to {url} failed (attempt {attempt + 1}/{self.config.retry_count + 1}): {str(e)}")
                
                if attempt < self.config.retry_count:
                    time.sleep(self.config.retry_delay)
                else:
                    logger.error(f"Request to {url} failed after {self.config.retry_count + 1} attempts")
                    return None
                    
        return None
        
    def get_source_info(self) -> Dict[str, Any]:
        """
        Get information about the data source.
        
        Returns:
            Dictionary with source information
        """
        return {
            "name": self.config.name,
            "base_url": self.config.base_url,
            "has_api_key": bool(self.config.api_key)
        }
        
    def test_connection(self) -> bool:
        """
        Test the connection to the data source.
        
        Returns:
            True if the connection is successful, False otherwise
        """
        raise NotImplementedError("Subclasses must implement test_connection()")
        
    def fetch_data(self, **kwargs) -> List[Dict[str, Any]]:
        """
        Fetch data from the data source.
        
        Args:
            **kwargs: Additional arguments for the data source
            
        Returns:
            List of data items
        """
        raise NotImplementedError("Subclasses must implement fetch_data()")
