"""
FINRA Rules API client for accessing FINRA regulatory rules and notices.
"""
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import re
from bs4 import BeautifulSoup

from app.api.data_sources.base_client import DataSourceClient, SourceConfig

logger = logging.getLogger(__name__)

class FINRARulesClient(DataSourceClient):
    """Client for the FINRA Rules API and web scraping."""
    
    def __init__(self):
        """Initialize the FINRA Rules client."""
        config = SourceConfig(
            name="FINRA Rules",
            base_url="https://www.finra.org/",
            rate_limit=0.2  # Be conservative with public APIs
        )
        
        super().__init__(config)
        
    def test_connection(self) -> bool:
        """
        Test the connection to the FINRA website.
        
        Returns:
            True if the connection is successful, False otherwise
        """
        try:
            # Try to access the rules page
            response = self._make_request("rules-guidance/rulebooks/finra-rules")
            return response is not None and isinstance(response, str)
        except Exception as e:
            logger.error(f"Error testing connection to FINRA website: {str(e)}")
            return False
            
    def fetch_rules(
        self,
        rule_number: Optional[str] = None,
        category: Optional[str] = None,
        keyword: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Fetch FINRA rules.
        
        Args:
            rule_number: Filter by rule number
            category: Filter by category
            keyword: Filter by keyword
            limit: Maximum number of rules to fetch
            
        Returns:
            List of rules
        """
        # FINRA doesn't have a public API, so we'll scrape the website
        endpoint = "rules-guidance/rulebooks/finra-rules"
        
        if rule_number:
            endpoint = f"{endpoint}/{rule_number}"
            
        response = self._make_request(endpoint)
        
        if not response or not isinstance(response, str):
            return []
            
        # Parse the HTML to extract rule information
        rules = []
        
        try:
            soup = BeautifulSoup(response, 'html.parser')
            
            # Find the rule list or rule details
            rule_elements = soup.select('.finra-rule-block')
            
            for rule_element in rule_elements[:limit]:
                # Extract rule number
                rule_num_elem = rule_element.select_one('.rule-number')
                rule_num = rule_num_elem.get_text(strip=True) if rule_num_elem else 'Unknown'
                
                # Extract rule title
                rule_title_elem = rule_element.select_one('.rule-title')
                rule_title = rule_title_elem.get_text(strip=True) if rule_title_elem else 'Unknown'
                
                # Extract rule text
                rule_text_elem = rule_element.select_one('.rule-text')
                rule_text = rule_text_elem.get_text(strip=True) if rule_text_elem else ''
                
                # Extract rule URL
                rule_url = f"https://www.finra.org/rules-guidance/rulebooks/finra-rules/{rule_num.replace('.', '')}"
                
                rule = {
                    'rule_number': rule_num,
                    'title': rule_title,
                    'text': rule_text,
                    'url': rule_url,
                    'source': 'FINRA Rules'
                }
                
                # Apply keyword filter if provided
                if keyword and keyword.lower() not in rule_title.lower() and keyword.lower() not in rule_text.lower():
                    continue
                    
                rules.append(rule)
                
                if len(rules) >= limit:
                    break
        except Exception as e:
            logger.error(f"Error parsing FINRA rules: {str(e)}")
            
        return rules
        
    def fetch_regulatory_notices(
        self,
        year: Optional[int] = None,
        keyword: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Fetch FINRA regulatory notices.
        
        Args:
            year: Filter by year
            keyword: Filter by keyword
            limit: Maximum number of notices to fetch
            
        Returns:
            List of regulatory notices
        """
        endpoint = "rules-guidance/notices"
        
        params = {}
        if year:
            params['filter_year'] = year
            
        if keyword:
            params['filter_keyword'] = keyword
            
        response = self._make_request(endpoint, params=params)
        
        if not response or not isinstance(response, str):
            return []
            
        # Parse the HTML to extract notice information
        notices = []
        
        try:
            soup = BeautifulSoup(response, 'html.parser')
            
            # Find the notice list
            notice_elements = soup.select('.notice-item')
            
            for notice_element in notice_elements[:limit]:
                # Extract notice number
                notice_num_elem = notice_element.select_one('.notice-number')
                notice_num = notice_num_elem.get_text(strip=True) if notice_num_elem else 'Unknown'
                
                # Extract notice title
                notice_title_elem = notice_element.select_one('.notice-title')
                notice_title = notice_title_elem.get_text(strip=True) if notice_title_elem else 'Unknown'
                
                # Extract notice date
                notice_date_elem = notice_element.select_one('.notice-date')
                notice_date_str = notice_date_elem.get_text(strip=True) if notice_date_elem else None
                notice_date = None
                
                if notice_date_str:
                    try:
                        notice_date = datetime.strptime(notice_date_str, '%m/%d/%Y').isoformat()
                    except ValueError:
                        pass
                
                # Extract notice URL
                notice_url_elem = notice_element.select_one('a')
                notice_url = notice_url_elem.get('href') if notice_url_elem else None
                
                if notice_url and not notice_url.startswith('http'):
                    notice_url = f"https://www.finra.org{notice_url}"
                
                notice = {
                    'notice_number': notice_num,
                    'title': notice_title,
                    'date': notice_date,
                    'url': notice_url,
                    'source': 'FINRA Regulatory Notices'
                }
                
                notices.append(notice)
        except Exception as e:
            logger.error(f"Error parsing FINRA regulatory notices: {str(e)}")
            
        return notices
        
    def fetch_data(self, data_type: str = 'rules', **kwargs) -> List[Dict[str, Any]]:
        """
        Fetch data from FINRA.
        
        Args:
            data_type: Type of data to fetch ('rules' or 'notices')
            **kwargs: Additional arguments for the specific data type
            
        Returns:
            List of data items
        """
        if data_type == 'notices':
            return self.fetch_regulatory_notices(**kwargs)
        else:
            return self.fetch_rules(**kwargs)
