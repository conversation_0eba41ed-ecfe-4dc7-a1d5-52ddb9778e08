"""
USAspending.gov API client for accessing federal spending data.
"""
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from app.api.data_sources.base_client import DataSourceClient, SourceConfig

logger = logging.getLogger(__name__)

class USASpendingClient(DataSourceClient):
    """Client for the USAspending.gov API."""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the USAspending.gov API client.
        
        Args:
            api_key: API key for the USAspending.gov API (not required for public endpoints)
        """
        config = SourceConfig(
            name="USAspending.gov",
            base_url="https://api.usaspending.gov/",
            api_key=api_key,
            rate_limit=0.5  # Be conservative with public APIs
        )
        
        super().__init__(config)
        
    def test_connection(self) -> bool:
        """
        Test the connection to the USAspending.gov API.
        
        Returns:
            True if the connection is successful, False otherwise
        """
        try:
            # Try to access a simple endpoint
            response = self._make_request("v2/references/toptier_agencies/")
            return response is not None and isinstance(response, dict)
        except Exception as e:
            logger.error(f"Error testing connection to USAspending.gov API: {str(e)}")
            return False
            
    def fetch_federal_accounts(
        self,
        agency_identifier: Optional[str] = None,
        fiscal_year: Optional[int] = None,
        limit: int = 100,
        page: int = 1
    ) -> List[Dict[str, Any]]:
        """
        Fetch federal accounts from the USAspending.gov API.
        
        Args:
            agency_identifier: Filter by agency identifier
            fiscal_year: Filter by fiscal year
            limit: Maximum number of accounts to fetch
            page: Page number for pagination
            
        Returns:
            List of federal accounts
        """
        endpoint = "v2/federal_accounts/"
        
        params = {
            "limit": min(limit, 100),
            "page": page
        }
        
        if agency_identifier:
            params["agency_identifier"] = agency_identifier
            
        if fiscal_year:
            params["fiscal_year"] = fiscal_year
            
        response = self._make_request(endpoint, params=params)
        
        if not response or not isinstance(response, dict):
            return []
            
        # Extract the federal accounts from the response
        accounts = []
        
        try:
            results = response.get("results", [])
            
            for result in results:
                account = {
                    "id": result.get("account_id"),
                    "number": result.get("account_number"),
                    "name": result.get("account_name"),
                    "agency_identifier": result.get("agency_identifier"),
                    "agency_name": result.get("agency_name"),
                    "budgetary_resources": result.get("budgetary_resources"),
                    "managing_agency": result.get("managing_agency"),
                    "source": "USAspending.gov"
                }
                
                accounts.append(account)
        except Exception as e:
            logger.error(f"Error parsing USAspending.gov API response: {str(e)}")
            
        return accounts
        
    def fetch_awards(
        self,
        award_type: Optional[List[str]] = None,
        agency_ids: Optional[List[str]] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        award_amounts: Optional[List[Dict[str, Any]]] = None,
        recipient_locations: Optional[List[Dict[str, Any]]] = None,
        limit: int = 100,
        page: int = 1
    ) -> List[Dict[str, Any]]:
        """
        Fetch awards from the USAspending.gov API.
        
        Args:
            award_type: Filter by award type
            agency_ids: Filter by agency IDs
            start_date: Start date for awards
            end_date: End date for awards
            award_amounts: Filter by award amounts
            recipient_locations: Filter by recipient locations
            limit: Maximum number of awards to fetch
            page: Page number for pagination
            
        Returns:
            List of awards
        """
        endpoint = "v2/search/spending_by_award/"
        
        # Build the request payload
        payload = {
            "limit": min(limit, 100),
            "page": page,
            "fields": [
                "Award ID",
                "Recipient Name",
                "Awarding Agency",
                "Award Amount",
                "Award Type",
                "Description",
                "Recipient Location",
                "Place of Performance",
                "Award Date"
            ]
        }
        
        filters = {}
        
        if award_type:
            filters["award_type_codes"] = award_type
            
        if agency_ids:
            filters["agencies"] = [{"type": "awarding", "tier": "toptier", "name": agency_id} for agency_id in agency_ids]
            
        if start_date or end_date:
            time_period = {}
            
            if start_date:
                time_period["start_date"] = start_date.strftime('%Y-%m-%d')
                
            if end_date:
                time_period["end_date"] = end_date.strftime('%Y-%m-%d')
                
            filters["time_period"] = [time_period]
            
        if award_amounts:
            filters["award_amounts"] = award_amounts
            
        if recipient_locations:
            filters["recipient_locations"] = recipient_locations
            
        if filters:
            payload["filters"] = filters
            
        # Make the request
        response = self._make_request(endpoint, method='POST', json_data=payload)
        
        if not response or not isinstance(response, dict):
            return []
            
        # Extract the awards from the response
        awards = []
        
        try:
            results = response.get("results", [])
            
            for result in results:
                award = {
                    "id": result.get("Award ID"),
                    "recipient_name": result.get("Recipient Name"),
                    "awarding_agency": result.get("Awarding Agency"),
                    "award_amount": result.get("Award Amount"),
                    "award_type": result.get("Award Type"),
                    "description": result.get("Description"),
                    "recipient_location": result.get("Recipient Location"),
                    "place_of_performance": result.get("Place of Performance"),
                    "award_date": result.get("Award Date"),
                    "source": "USAspending.gov"
                }
                
                awards.append(award)
        except Exception as e:
            logger.error(f"Error parsing USAspending.gov API response: {str(e)}")
            
        return awards
        
    def fetch_data(self, data_type: str = 'awards', **kwargs) -> List[Dict[str, Any]]:
        """
        Fetch data from the USAspending.gov API.
        
        Args:
            data_type: Type of data to fetch ('awards' or 'federal_accounts')
            **kwargs: Additional arguments for the specific data type
            
        Returns:
            List of data items
        """
        if data_type == 'federal_accounts':
            return self.fetch_federal_accounts(**kwargs)
        else:
            return self.fetch_awards(**kwargs)
            
    def get_agencies(self) -> List[Dict[str, Any]]:
        """
        Get the list of available agencies.
        
        Returns:
            List of agencies
        """
        response = self._make_request("v2/references/toptier_agencies/")
        
        if not response or not isinstance(response, dict):
            return []
            
        agencies = []
        
        try:
            results = response.get("results", [])
            
            for result in results:
                agency = {
                    "agency_id": result.get("agency_id"),
                    "toptier_code": result.get("toptier_code"),
                    "name": result.get("name"),
                    "abbreviation": result.get("abbreviation"),
                    "agency_slug": result.get("agency_slug"),
                    "budget_authority_amount": result.get("budget_authority_amount"),
                    "current_total_budget_authority_amount": result.get("current_total_budget_authority_amount"),
                    "obligated_amount": result.get("obligated_amount"),
                    "outlay_amount": result.get("outlay_amount")
                }
                
                agencies.append(agency)
        except Exception as e:
            logger.error(f"Error parsing USAspending.gov API response: {str(e)}")
            
        return agencies
