"""
Manager for public regulatory data sources.
Provides a unified interface for accessing all public data sources.
"""
import logging
from typing import Dict, List, Optional, Any, Type
from datetime import datetime

from app.api.data_sources.base_client import DataSourceClient
from app.api.data_sources.public.sec_edgar import SECEdgarClient
from app.api.data_sources.public.cfpb_complaints import CFPBComplaintsClient
from app.api.data_sources.public.finra_rules import FINRARulesClient
from app.api.data_sources.public.federal_reserve import FederalReserveClient
from app.api.data_sources.public.usa_spending import USASpendingClient

logger = logging.getLogger(__name__)

class PublicDataSourceManager:
    """Manager for public regulatory data sources."""
    
    def __init__(self):
        """Initialize the public data source manager."""
        self.sources: Dict[str, Type[DataSourceClient]] = {
            'sec_edgar': SECEdgarClient,
            'cfpb_complaints': CFPBComplaintsClient,
            'finra_rules': FINRARulesClient,
            'federal_reserve': FederalReserveClient,
            'usa_spending': USASpendingClient
        }
        
        self.source_instances: Dict[str, DataSourceClient] = {}
        
    def get_available_sources(self) -> List[str]:
        """
        Get the list of available public data sources.
        
        Returns:
            List of source names
        """
        return list(self.sources.keys())
        
    def get_source_client(self, source_name: str, **kwargs) -> Optional[DataSourceClient]:
        """
        Get a client for a specific data source.
        
        Args:
            source_name: Name of the data source
            **kwargs: Additional arguments for the data source client
            
        Returns:
            Data source client or None if the source is not available
        """
        if source_name not in self.sources:
            logger.error(f"Data source '{source_name}' not found")
            return None
            
        # Check if we already have an instance
        if source_name in self.source_instances:
            return self.source_instances[source_name]
            
        # Create a new instance
        try:
            client = self.sources[source_name](**kwargs)
            self.source_instances[source_name] = client
            return client
        except Exception as e:
            logger.error(f"Error creating client for data source '{source_name}': {str(e)}")
            return None
            
    def test_all_connections(self) -> Dict[str, bool]:
        """
        Test connections to all available data sources.
        
        Returns:
            Dictionary mapping source names to connection status
        """
        results = {}
        
        for source_name in self.sources:
            client = self.get_source_client(source_name)
            
            if client:
                results[source_name] = client.test_connection()
            else:
                results[source_name] = False
                
        return results
        
    def fetch_data_from_all_sources(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit_per_source: int = 10
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        Fetch data from all available sources.
        
        Args:
            start_date: Start date for data
            end_date: End date for data
            limit_per_source: Maximum number of items to fetch per source
            
        Returns:
            Dictionary mapping source names to lists of data items
        """
        results = {}
        
        for source_name in self.sources:
            client = self.get_source_client(source_name)
            
            if not client:
                results[source_name] = []
                continue
                
            try:
                # Fetch data with source-specific parameters
                if source_name == 'sec_edgar':
                    data = client.fetch_data(start_date=start_date, end_date=end_date, limit=limit_per_source)
                elif source_name == 'cfpb_complaints':
                    data = client.fetch_data(start_date=start_date, end_date=end_date, limit=limit_per_source)
                elif source_name == 'finra_rules':
                    data = client.fetch_data(limit=limit_per_source)
                elif source_name == 'federal_reserve':
                    data = client.fetch_data(start_date=start_date, end_date=end_date, limit=limit_per_source)
                elif source_name == 'usa_spending':
                    data = client.fetch_data(start_date=start_date, end_date=end_date, limit=limit_per_source)
                else:
                    data = []
                    
                results[source_name] = data
            except Exception as e:
                logger.error(f"Error fetching data from source '{source_name}': {str(e)}")
                results[source_name] = []
                
        return results
