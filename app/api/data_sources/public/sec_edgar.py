"""
SEC EDGAR API client for accessing SEC filings and regulatory data.
"""
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import re

from app.api.data_sources.base_client import DataSourceClient, SourceConfig

logger = logging.getLogger(__name__)

class SECEdgarClient(DataSourceClient):
    """Client for the SEC EDGAR API."""
    
    def __init__(self, api_key: Optional[str] = None, email: Optional[str] = None):
        """
        Initialize the SEC EDGAR API client.
        
        Args:
            api_key: API key for the SEC EDGAR API (not required for public endpoints)
            email: Email address for identification (recommended by SEC)
        """
        headers = {}
        if email:
            headers['User-Agent'] = f'RegulationGuru Research Bot ({email})'
            
        config = SourceConfig(
            name="SEC EDGAR",
            base_url="https://www.sec.gov/",
            api_key=api_key,
            headers=headers,
            rate_limit=0.1  # SEC recommends no more than 10 requests per second
        )
        
        super().__init__(config)
        
    def test_connection(self) -> bool:
        """
        Test the connection to the SEC EDGAR API.
        
        Returns:
            True if the connection is successful, False otherwise
        """
        try:
            # Try to access a simple endpoint
            response = self._make_request("edgar/daily-index/")
            return response is not None
        except Exception as e:
            logger.error(f"Error testing connection to SEC EDGAR API: {str(e)}")
            return False
            
    def fetch_company_filings(
        self, 
        cik: Optional[str] = None,
        form_type: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Fetch company filings from the SEC EDGAR API.
        
        Args:
            cik: Central Index Key (CIK) of the company
            form_type: Type of form to fetch (e.g., '10-K', '10-Q')
            start_date: Start date for filings
            end_date: End date for filings
            limit: Maximum number of filings to fetch
            
        Returns:
            List of filings
        """
        params = {}
        
        if cik:
            # Ensure CIK is properly formatted with leading zeros
            cik = cik.strip().lstrip('0')
            cik = cik.zfill(10)
            params['CIK'] = cik
            
        if form_type:
            params['type'] = form_type
            
        if start_date:
            params['dateb'] = start_date.strftime('%Y%m%d')
            
        if end_date:
            params['datea'] = end_date.strftime('%Y%m%d')
            
        params['count'] = min(limit, 100)  # SEC limits to 100 per request
        
        endpoint = "cgi-bin/browse-edgar"
        response = self._make_request(endpoint, params=params)
        
        if not response:
            return []
            
        # Parse the HTML response to extract filing information
        # This is a simplified implementation - in a real system, you would use
        # a proper HTML parser like BeautifulSoup
        filings = []
        
        # For now, return a placeholder
        return filings
        
    def fetch_regulatory_data(
        self,
        category: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Fetch regulatory data from the SEC EDGAR API.
        
        Args:
            category: Category of regulatory data to fetch
            start_date: Start date for regulatory data
            end_date: End date for regulatory data
            limit: Maximum number of items to fetch
            
        Returns:
            List of regulatory data items
        """
        # For regulatory data, we'll use the SEC's full-text search API
        params = {
            'q': 'regulation rule',
            'category': 'custom',
            'from': 0,
            'size': min(limit, 100)
        }
        
        if category:
            params['q'] += f' {category}'
            
        if start_date:
            params['date_from'] = start_date.strftime('%Y-%m-%d')
            
        if end_date:
            params['date_to'] = end_date.strftime('%Y-%m-%d')
            
        endpoint = "edgar/search/api/v1/query"
        response = self._make_request(endpoint, params=params)
        
        if not response or not isinstance(response, dict):
            return []
            
        # Extract the regulatory data from the response
        items = []
        
        try:
            for hit in response.get('hits', {}).get('hits', []):
                source = hit.get('_source', {})
                
                item = {
                    'id': hit.get('_id'),
                    'title': source.get('display_title', 'Unknown'),
                    'url': f"https://www.sec.gov{source.get('url', '')}",
                    'filing_date': source.get('filing_date'),
                    'category': source.get('category'),
                    'document_type': source.get('document_type'),
                    'file_type': source.get('file_type'),
                    'source': 'SEC EDGAR'
                }
                
                items.append(item)
        except Exception as e:
            logger.error(f"Error parsing SEC EDGAR API response: {str(e)}")
            
        return items
        
    def fetch_data(self, **kwargs) -> List[Dict[str, Any]]:
        """
        Fetch data from the SEC EDGAR API.
        
        Args:
            **kwargs: Additional arguments for the SEC EDGAR API
            
        Returns:
            List of data items
        """
        # By default, fetch regulatory data
        return self.fetch_regulatory_data(**kwargs)
        
    def fetch_xbrl_data(self, accession_number: str) -> Optional[Dict[str, Any]]:
        """
        Fetch XBRL data for a specific filing.
        
        Args:
            accession_number: Accession number of the filing
            
        Returns:
            XBRL data or None if not available
        """
        # Format the accession number
        accession_number = accession_number.replace('-', '')
        
        endpoint = f"Archives/edgar/data/{accession_number}/{accession_number}-index.html"
        response = self._make_request(endpoint)
        
        if not response:
            return None
            
        # Parse the HTML to find the XBRL data
        # This is a simplified implementation
        return None
