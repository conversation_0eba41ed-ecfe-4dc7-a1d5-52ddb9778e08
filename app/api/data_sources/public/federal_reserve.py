"""
Federal Reserve Data API client for accessing economic and financial data.
"""
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from app.api.data_sources.base_client import DataSourceClient, SourceConfig

logger = logging.getLogger(__name__)

class FederalReserveClient(DataSourceClient):
    """Client for the Federal Reserve Data API."""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the Federal Reserve Data API client.
        
        Args:
            api_key: API key for the Federal Reserve API (not required for public endpoints)
        """
        config = SourceConfig(
            name="Federal Reserve",
            base_url="https://api.federalreserve.gov/",
            api_key=api_key,
            rate_limit=0.5  # Be conservative with public APIs
        )
        
        super().__init__(config)
        
    def test_connection(self) -> bool:
        """
        Test the connection to the Federal Reserve API.
        
        Returns:
            True if the connection is successful, False otherwise
        """
        try:
            # Try to access a simple endpoint
            response = self._make_request("v1/series/categories")
            return response is not None and isinstance(response, dict)
        except Exception as e:
            logger.error(f"Error testing connection to Federal Reserve API: {str(e)}")
            return False
            
    def fetch_regulations(
        self,
        category: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Fetch regulations from the Federal Reserve.
        
        Args:
            category: Filter by category
            start_date: Start date for regulations
            end_date: End date for regulations
            limit: Maximum number of regulations to fetch
            
        Returns:
            List of regulations
        """
        # The Federal Reserve doesn't have a direct API for regulations,
        # so we'll use their press releases API and filter for regulatory content
        endpoint = "v1/press-releases"
        
        params = {
            "limit": min(limit, 100),
            "content_type": "regulation"
        }
        
        if category:
            params["category"] = category
            
        if start_date:
            params["from_date"] = start_date.strftime('%Y-%m-%d')
            
        if end_date:
            params["to_date"] = end_date.strftime('%Y-%m-%d')
            
        response = self._make_request(endpoint, params=params)
        
        if not response or not isinstance(response, dict):
            return []
            
        # Extract the regulations from the response
        regulations = []
        
        try:
            items = response.get("items", [])
            
            for item in items:
                regulation = {
                    "id": item.get("id"),
                    "title": item.get("title"),
                    "url": item.get("url"),
                    "date": item.get("published_date"),
                    "category": item.get("category"),
                    "content": item.get("content"),
                    "source": "Federal Reserve"
                }
                
                regulations.append(regulation)
        except Exception as e:
            logger.error(f"Error parsing Federal Reserve API response: {str(e)}")
            
        return regulations
        
    def fetch_economic_data(
        self,
        series_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        frequency: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Fetch economic data from the Federal Reserve.
        
        Args:
            series_id: ID of the data series to fetch
            start_date: Start date for data
            end_date: End date for data
            frequency: Frequency of data (e.g., 'daily', 'monthly', 'annual')
            limit: Maximum number of data points to fetch
            
        Returns:
            List of data points
        """
        endpoint = f"v1/series/data/{series_id}"
        
        params = {
            "limit": min(limit, 100)
        }
        
        if start_date:
            params["from_date"] = start_date.strftime('%Y-%m-%d')
            
        if end_date:
            params["to_date"] = end_date.strftime('%Y-%m-%d')
            
        if frequency:
            params["frequency"] = frequency
            
        response = self._make_request(endpoint, params=params)
        
        if not response or not isinstance(response, dict):
            return []
            
        # Extract the data points from the response
        data_points = []
        
        try:
            observations = response.get("observations", [])
            
            for observation in observations:
                data_point = {
                    "date": observation.get("date"),
                    "value": observation.get("value"),
                    "series_id": series_id,
                    "source": "Federal Reserve Economic Data"
                }
                
                data_points.append(data_point)
        except Exception as e:
            logger.error(f"Error parsing Federal Reserve API response: {str(e)}")
            
        return data_points
        
    def fetch_data(self, data_type: str = 'regulations', **kwargs) -> List[Dict[str, Any]]:
        """
        Fetch data from the Federal Reserve.
        
        Args:
            data_type: Type of data to fetch ('regulations' or 'economic')
            **kwargs: Additional arguments for the specific data type
            
        Returns:
            List of data items
        """
        if data_type == 'economic':
            if 'series_id' not in kwargs:
                logger.error("series_id is required for economic data")
                return []
                
            return self.fetch_economic_data(**kwargs)
        else:
            return self.fetch_regulations(**kwargs)
            
    def get_series_categories(self) -> List[Dict[str, Any]]:
        """
        Get the list of available data series categories.
        
        Returns:
            List of categories
        """
        response = self._make_request("v1/series/categories")
        
        if not response or not isinstance(response, dict):
            return []
            
        categories = []
        
        try:
            for category in response.get("categories", []):
                categories.append({
                    "id": category.get("id"),
                    "name": category.get("name"),
                    "parent_id": category.get("parent_id")
                })
        except Exception as e:
            logger.error(f"Error parsing Federal Reserve API response: {str(e)}")
            
        return categories
        
    def get_series_by_category(self, category_id: str) -> List[Dict[str, Any]]:
        """
        Get the list of available data series in a category.
        
        Args:
            category_id: ID of the category
            
        Returns:
            List of data series
        """
        response = self._make_request(f"v1/series/categories/{category_id}/series")
        
        if not response or not isinstance(response, dict):
            return []
            
        series_list = []
        
        try:
            for series in response.get("series", []):
                series_list.append({
                    "id": series.get("id"),
                    "title": series.get("title"),
                    "frequency": series.get("frequency"),
                    "units": series.get("units"),
                    "seasonal_adjustment": series.get("seasonal_adjustment")
                })
        except Exception as e:
            logger.error(f"Error parsing Federal Reserve API response: {str(e)}")
            
        return series_list
