"""
CFPB Consumer Complaint Database API client.
Provides access to consumer complaints about financial products and services.
"""
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from app.api.data_sources.base_client import DataSourceClient, SourceConfig

logger = logging.getLogger(__name__)

class CFPBComplaintsClient(DataSourceClient):
    """Client for the CFPB Consumer Complaint Database API."""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the CFPB Consumer Complaint Database API client.
        
        Args:
            api_key: API key for the CFPB API (not required for public endpoints)
        """
        config = SourceConfig(
            name="CFPB Consumer Complaints",
            base_url="https://www.consumerfinance.gov/data-research/consumer-complaints/search/api/v1/",
            api_key=api_key,
            rate_limit=0.2  # Be conservative with public APIs
        )
        
        super().__init__(config)
        
    def test_connection(self) -> bool:
        """
        Test the connection to the CFPB API.
        
        Returns:
            True if the connection is successful, False otherwise
        """
        try:
            # Try to access a simple endpoint with minimal data
            response = self._make_request("views", params={"size": 1})
            return response is not None and isinstance(response, dict)
        except Exception as e:
            logger.error(f"Error testing connection to CFPB API: {str(e)}")
            return False
            
    def fetch_complaints(
        self,
        product: Optional[str] = None,
        company: Optional[str] = None,
        issue: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        state: Optional[str] = None,
        consumer_disputed: Optional[bool] = None,
        company_response: Optional[str] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        Fetch complaints from the CFPB Consumer Complaint Database.
        
        Args:
            product: Filter by product (e.g., 'Mortgage', 'Credit card')
            company: Filter by company name
            issue: Filter by issue
            start_date: Start date for complaints
            end_date: End date for complaints
            state: Filter by state (two-letter code)
            consumer_disputed: Filter by whether the consumer disputed the response
            company_response: Filter by company response
            limit: Maximum number of complaints to fetch
            offset: Offset for pagination
            
        Returns:
            List of complaints
        """
        params = {
            "size": min(limit, 100),  # Limit to 100 per request
            "frm": offset
        }
        
        # Build the search query
        search_terms = []
        
        if product:
            search_terms.append(f"product:{product}")
            
        if company:
            search_terms.append(f"company:{company}")
            
        if issue:
            search_terms.append(f"issue:{issue}")
            
        if start_date:
            search_terms.append(f"date_received_min:{start_date.strftime('%Y-%m-%d')}")
            
        if end_date:
            search_terms.append(f"date_received_max:{end_date.strftime('%Y-%m-%d')}")
            
        if state:
            search_terms.append(f"state:{state}")
            
        if consumer_disputed is not None:
            search_terms.append(f"consumer_disputed:{str(consumer_disputed).lower()}")
            
        if company_response:
            search_terms.append(f"company_response:{company_response}")
            
        if search_terms:
            params["search_term"] = " AND ".join(search_terms)
            
        # Make the request
        response = self._make_request("_search", params=params)
        
        if not response or not isinstance(response, dict):
            return []
            
        # Extract the complaints from the response
        complaints = []
        
        try:
            hits = response.get("hits", {}).get("hits", [])
            
            for hit in hits:
                source = hit.get("_source", {})
                
                complaint = {
                    "id": hit.get("_id"),
                    "date_received": source.get("date_received"),
                    "product": source.get("product"),
                    "sub_product": source.get("sub_product"),
                    "issue": source.get("issue"),
                    "sub_issue": source.get("sub_issue"),
                    "company": source.get("company"),
                    "state": source.get("state"),
                    "zip_code": source.get("zip_code"),
                    "consumer_complaint_narrative": source.get("consumer_complaint_narrative"),
                    "company_response": source.get("company_response"),
                    "timely": source.get("timely"),
                    "consumer_disputed": source.get("consumer_disputed"),
                    "complaint_id": source.get("complaint_id"),
                    "source": "CFPB Consumer Complaints"
                }
                
                complaints.append(complaint)
        except Exception as e:
            logger.error(f"Error parsing CFPB API response: {str(e)}")
            
        return complaints
        
    def fetch_data(self, **kwargs) -> List[Dict[str, Any]]:
        """
        Fetch data from the CFPB Consumer Complaint Database.
        
        Args:
            **kwargs: Additional arguments for the CFPB API
            
        Returns:
            List of data items
        """
        return self.fetch_complaints(**kwargs)
        
    def get_products(self) -> List[str]:
        """
        Get the list of available products.
        
        Returns:
            List of product names
        """
        response = self._make_request("_suggest", params={"size": 100, "field": "product"})
        
        if not response or not isinstance(response, dict):
            return []
            
        products = []
        
        try:
            for suggestion in response.get("suggestions", []):
                products.append(suggestion.get("text"))
        except Exception as e:
            logger.error(f"Error parsing CFPB API response: {str(e)}")
            
        return products
        
    def get_issues(self) -> List[str]:
        """
        Get the list of available issues.
        
        Returns:
            List of issue names
        """
        response = self._make_request("_suggest", params={"size": 100, "field": "issue"})
        
        if not response or not isinstance(response, dict):
            return []
            
        issues = []
        
        try:
            for suggestion in response.get("suggestions", []):
                issues.append(suggestion.get("text"))
        except Exception as e:
            logger.error(f"Error parsing CFPB API response: {str(e)}")
            
        return issues
