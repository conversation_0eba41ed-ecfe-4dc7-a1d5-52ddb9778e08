"""
API router for data sources.
Provides endpoints for managing and querying data sources.
"""
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks, Path
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.db import get_db
from app.db.models import RegulatorySource, RegulationURL, Regulator, Country
from app.db.models.external_api_source import ExternalAPISource, ExternalAPISyncLog
from app.api.data_sources.public.manager import PublicDataSourceManager

router = APIRouter()
logger = logging.getLogger(__name__)

# Initialize the public data source manager
public_source_manager = PublicDataSourceManager()


class ExternalAPISourceCreate(BaseModel):
    """Schema for creating an external API source."""
    name: str = Field(..., description="Name of the external API source")
    source_type: str = Field(..., description="Type of the source (sec_edgar, cfpb_complaints, etc.)")
    base_url: str = Field(..., description="Base URL of the API")
    api_key: Optional[str] = Field(None, description="API key for authentication")
    description: Optional[str] = Field(None, description="Description of the source")
    config: Optional[Dict[str, Any]] = Field(None, description="Additional configuration")
    is_active: bool = Field(True, description="Whether the source is active")
    sync_frequency: int = Field(24, description="Sync frequency in hours")


class ExternalAPISourceResponse(BaseModel):
    """Schema for external API source responses."""
    id: int = Field(..., description="Unique identifier for the source")
    name: str = Field(..., description="Name of the external API source")
    source_type: str = Field(..., description="Type of the source")
    base_url: str = Field(..., description="Base URL of the API")
    description: Optional[str] = Field(None, description="Description of the source")
    is_active: bool = Field(..., description="Whether the source is active")
    last_sync: Optional[datetime] = Field(None, description="Timestamp of the last synchronization")
    sync_frequency: int = Field(..., description="Sync frequency in hours")
    created_at: datetime = Field(..., description="Timestamp when the record was created")
    changed_on: datetime = Field(..., description="Timestamp when the record was last updated")
    
    class Config:
        from_attributes = True


class ExternalAPISyncLogResponse(BaseModel):
    """Schema for external API sync log responses."""
    id: int = Field(..., description="Unique identifier for the log")
    source_id: int = Field(..., description="ID of the external API source")
    status: str = Field(..., description="Status of the synchronization")
    items_processed: int = Field(..., description="Number of items processed")
    items_created: int = Field(..., description="Number of items created")
    items_updated: int = Field(..., description="Number of items updated")
    items_failed: int = Field(..., description="Number of items failed")
    error_message: Optional[str] = Field(None, description="Error message if any")
    duration_seconds: Optional[float] = Field(None, description="Duration of the synchronization in seconds")
    created_at: datetime = Field(..., description="Timestamp when the record was created")
    
    class Config:
        from_attributes = True


class PublicSourceInfo(BaseModel):
    """Schema for public data source information."""
    name: str = Field(..., description="Name of the public data source")
    description: str = Field(..., description="Description of the public data source")
    base_url: str = Field(..., description="Base URL of the public data source")
    requires_api_key: bool = Field(..., description="Whether the source requires an API key")
    data_types: List[str] = Field(..., description="Types of data available from the source")


@router.get("/public/available", response_model=List[PublicSourceInfo])
async def list_available_public_sources():
    """
    List all available public data sources.
    
    Returns:
        List of public data source information
    """
    sources = []
    
    # SEC EDGAR
    sources.append(PublicSourceInfo(
        name="sec_edgar",
        description="SEC EDGAR database of corporate filings and regulatory data",
        base_url="https://www.sec.gov/",
        requires_api_key=False,
        data_types=["regulatory_data", "company_filings", "xbrl_data"]
    ))
    
    # CFPB Complaints
    sources.append(PublicSourceInfo(
        name="cfpb_complaints",
        description="CFPB Consumer Complaint Database",
        base_url="https://www.consumerfinance.gov/data-research/consumer-complaints/",
        requires_api_key=False,
        data_types=["consumer_complaints"]
    ))
    
    # FINRA Rules
    sources.append(PublicSourceInfo(
        name="finra_rules",
        description="FINRA Rules and Regulatory Notices",
        base_url="https://www.finra.org/",
        requires_api_key=False,
        data_types=["rules", "regulatory_notices"]
    ))
    
    # Federal Reserve
    sources.append(PublicSourceInfo(
        name="federal_reserve",
        description="Federal Reserve Data and Regulations",
        base_url="https://www.federalreserve.gov/",
        requires_api_key=False,
        data_types=["regulations", "economic_data"]
    ))
    
    # USAspending.gov
    sources.append(PublicSourceInfo(
        name="usa_spending",
        description="USAspending.gov Federal Spending Data",
        base_url="https://www.usaspending.gov/",
        requires_api_key=False,
        data_types=["federal_accounts", "awards"]
    ))
    
    return sources


@router.get("/public/test-connections", response_model=Dict[str, bool])
async def test_public_source_connections():
    """
    Test connections to all available public data sources.
    
    Returns:
        Dictionary mapping source names to connection status
    """
    return public_source_manager.test_all_connections()


@router.get("/public/{source_name}/data", response_model=List[Dict[str, Any]])
async def fetch_public_source_data(
    source_name: str = Path(..., description="Name of the public data source"),
    data_type: Optional[str] = Query(None, description="Type of data to fetch"),
    start_date: Optional[datetime] = Query(None, description="Start date for data"),
    end_date: Optional[datetime] = Query(None, description="End date for data"),
    limit: int = Query(10, description="Maximum number of items to fetch")
):
    """
    Fetch data from a public data source.
    
    Args:
        source_name: Name of the public data source
        data_type: Type of data to fetch
        start_date: Start date for data
        end_date: End date for data
        limit: Maximum number of items to fetch
        
    Returns:
        List of data items
    """
    client = public_source_manager.get_source_client(source_name)
    
    if not client:
        raise HTTPException(status_code=404, detail=f"Public data source '{source_name}' not found")
        
    try:
        kwargs = {
            "limit": limit
        }
        
        if start_date:
            kwargs["start_date"] = start_date
            
        if end_date:
            kwargs["end_date"] = end_date
            
        if data_type:
            kwargs["data_type"] = data_type
            
        return client.fetch_data(**kwargs)
    except Exception as e:
        logger.error(f"Error fetching data from public source '{source_name}': {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching data: {str(e)}")


@router.post("/external", response_model=ExternalAPISourceResponse)
async def create_external_api_source(
    source: ExternalAPISourceCreate,
    db: Session = Depends(get_db)
):
    """
    Create a new external API source.
    
    Args:
        source: External API source data
        db: Database session
        
    Returns:
        Created external API source
    """
    # Check if the source type is valid
    if source.source_type not in public_source_manager.get_available_sources():
        raise HTTPException(
            status_code=400, 
            detail=f"Invalid source type. Available types: {', '.join(public_source_manager.get_available_sources())}"
        )
        
    # Check if a source with the same name already exists
    existing_source = db.query(ExternalAPISource).filter(
        ExternalAPISource.name == source.name
    ).first()
    
    if existing_source:
        raise HTTPException(
            status_code=400,
            detail=f"External API source with name '{source.name}' already exists"
        )
        
    # Create the new source
    new_source = ExternalAPISource(
        name=source.name,
        source_type=source.source_type,
        base_url=source.base_url,
        api_key=source.api_key,
        description=source.description,
        config=source.config,
        is_active=source.is_active,
        sync_frequency=source.sync_frequency
    )
    
    db.add(new_source)
    db.commit()
    db.refresh(new_source)
    
    return new_source


@router.get("/external", response_model=List[ExternalAPISourceResponse])
async def list_external_api_sources(
    skip: int = Query(0, description="Number of items to skip"),
    limit: int = Query(100, description="Maximum number of items to return"),
    source_type: Optional[str] = Query(None, description="Filter by source type"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    db: Session = Depends(get_db)
):
    """
    List external API sources.
    
    Args:
        skip: Number of items to skip
        limit: Maximum number of items to return
        source_type: Filter by source type
        is_active: Filter by active status
        db: Database session
        
    Returns:
        List of external API sources
    """
    query = db.query(ExternalAPISource)
    
    if source_type:
        query = query.filter(ExternalAPISource.source_type == source_type)
        
    if is_active is not None:
        query = query.filter(ExternalAPISource.is_active == is_active)
        
    return query.offset(skip).limit(limit).all()


@router.get("/external/{source_id}", response_model=ExternalAPISourceResponse)
async def get_external_api_source(
    source_id: int = Path(..., description="ID of the external API source"),
    db: Session = Depends(get_db)
):
    """
    Get an external API source by ID.
    
    Args:
        source_id: ID of the external API source
        db: Database session
        
    Returns:
        External API source
    """
    source = db.query(ExternalAPISource).filter(
        ExternalAPISource.id == source_id
    ).first()
    
    if not source:
        raise HTTPException(
            status_code=404,
            detail=f"External API source with ID {source_id} not found"
        )
        
    return source


@router.put("/external/{source_id}", response_model=ExternalAPISourceResponse)
async def update_external_api_source(
    source_id: int = Path(..., description="ID of the external API source"),
    source_update: ExternalAPISourceCreate = None,
    db: Session = Depends(get_db)
):
    """
    Update an external API source.
    
    Args:
        source_id: ID of the external API source
        source_update: Updated external API source data
        db: Database session
        
    Returns:
        Updated external API source
    """
    source = db.query(ExternalAPISource).filter(
        ExternalAPISource.id == source_id
    ).first()
    
    if not source:
        raise HTTPException(
            status_code=404,
            detail=f"External API source with ID {source_id} not found"
        )
        
    # Check if the source type is valid
    if source_update.source_type not in public_source_manager.get_available_sources():
        raise HTTPException(
            status_code=400, 
            detail=f"Invalid source type. Available types: {', '.join(public_source_manager.get_available_sources())}"
        )
        
    # Update the source
    source.name = source_update.name
    source.source_type = source_update.source_type
    source.base_url = source_update.base_url
    source.api_key = source_update.api_key
    source.description = source_update.description
    source.config = source_update.config
    source.is_active = source_update.is_active
    source.sync_frequency = source_update.sync_frequency
    
    db.commit()
    db.refresh(source)
    
    return source


@router.delete("/external/{source_id}", response_model=Dict[str, Any])
async def delete_external_api_source(
    source_id: int = Path(..., description="ID of the external API source"),
    db: Session = Depends(get_db)
):
    """
    Delete an external API source.
    
    Args:
        source_id: ID of the external API source
        db: Database session
        
    Returns:
        Deletion status
    """
    source = db.query(ExternalAPISource).filter(
        ExternalAPISource.id == source_id
    ).first()
    
    if not source:
        raise HTTPException(
            status_code=404,
            detail=f"External API source with ID {source_id} not found"
        )
        
    # Delete the source
    db.delete(source)
    db.commit()
    
    return {"status": "success", "message": f"External API source with ID {source_id} deleted"}


@router.post("/external/{source_id}/sync", response_model=Dict[str, Any])
async def sync_external_api_source(
    source_id: int = Path(..., description="ID of the external API source"),
    background_tasks: BackgroundTasks = None,
    force: bool = Query(False, description="Force synchronization even if not due"),
    db: Session = Depends(get_db)
):
    """
    Synchronize data from an external API source.
    
    Args:
        source_id: ID of the external API source
        background_tasks: Background tasks
        force: Force synchronization even if not due
        db: Database session
        
    Returns:
        Synchronization status
    """
    source = db.query(ExternalAPISource).filter(
        ExternalAPISource.id == source_id
    ).first()
    
    if not source:
        raise HTTPException(
            status_code=404,
            detail=f"External API source with ID {source_id} not found"
        )
        
    # Check if the source is active
    if not source.is_active:
        raise HTTPException(
            status_code=400,
            detail=f"External API source with ID {source_id} is not active"
        )
        
    # Check if synchronization is due
    now = datetime.utcnow()
    sync_due = True
    
    if not force and source.last_sync:
        next_sync = source.last_sync + timedelta(hours=source.sync_frequency)
        if next_sync > now:
            sync_due = False
            
    if not sync_due:
        next_sync = source.last_sync + timedelta(hours=source.sync_frequency)
        return {
            "source_id": source.id,
            "name": source.name,
            "status": "skipped",
            "message": f"Synchronization not due until {next_sync}",
            "last_sync": source.last_sync,
            "next_sync": next_sync
        }
        
    # Start synchronization in background
    background_tasks.add_task(
        sync_external_api_source_task,
        source_id=source_id,
        db_factory=get_db
    )
    
    return {
        "source_id": source.id,
        "name": source.name,
        "status": "started",
        "message": "Synchronization started in background"
    }


@router.get("/external/{source_id}/logs", response_model=List[ExternalAPISyncLogResponse])
async def get_external_api_source_logs(
    source_id: int = Path(..., description="ID of the external API source"),
    skip: int = Query(0, description="Number of items to skip"),
    limit: int = Query(100, description="Maximum number of items to return"),
    db: Session = Depends(get_db)
):
    """
    Get synchronization logs for an external API source.
    
    Args:
        source_id: ID of the external API source
        skip: Number of items to skip
        limit: Maximum number of items to return
        db: Database session
        
    Returns:
        List of synchronization logs
    """
    source = db.query(ExternalAPISource).filter(
        ExternalAPISource.id == source_id
    ).first()
    
    if not source:
        raise HTTPException(
            status_code=404,
            detail=f"External API source with ID {source_id} not found"
        )
        
    logs = db.query(ExternalAPISyncLog).filter(
        ExternalAPISyncLog.source_id == source_id
    ).order_by(
        ExternalAPISyncLog.created_at.desc()
    ).offset(skip).limit(limit).all()
    
    return logs


async def sync_external_api_source_task(source_id: int, db_factory):
    """
    Background task to synchronize data from an external API source.
    
    Args:
        source_id: ID of the external API source
        db_factory: Function to get database session
    """
    # Create a new db session for this background task
    db = next(db_factory())
    
    try:
        # Find the source
        source = db.query(ExternalAPISource).filter(
            ExternalAPISource.id == source_id
        ).first()
        
        if not source:
            logger.error(f"External API source with ID {source_id} not found")
            return
            
        logger.info(f"Starting synchronization from {source.name} (ID: {source.id})")
        
        # Create a sync log
        sync_log = ExternalAPISyncLog(
            source_id=source.id,
            status="in_progress",
            items_processed=0,
            items_created=0,
            items_updated=0,
            items_failed=0
        )
        
        db.add(sync_log)
        db.commit()
        db.refresh(sync_log)
        
        start_time = datetime.utcnow()
        
        # Update source with sync time
        source.last_sync = start_time
        source.last_error = None
        db.commit()
        
        # Get the client for the source type
        client = public_source_manager.get_source_client(
            source.source_type,
            api_key=source.api_key
        )
        
        if not client:
            raise Exception(f"Failed to create client for source type '{source.source_type}'")
            
        # Fetch data from the source
        data = client.fetch_data()
        
        # Process the data
        items_processed = 0
        items_created = 0
        items_updated = 0
        items_failed = 0
        
        for item in data:
            items_processed += 1
            
            try:
                # Check if the URL already exists
                url = item.get("url")
                
                if not url:
                    logger.warning(f"Item from {source.name} has no URL, skipping")
                    items_failed += 1
                    continue
                    
                existing_url = db.query(RegulationURL).filter(
                    RegulationURL.url == url
                ).first()
                
                if existing_url:
                    # Update existing URL
                    if "title" in item and item["title"]:
                        existing_url.title = item["title"]
                        
                    if "publication_date" in item and item["publication_date"]:
                        existing_url.publication_date = item["publication_date"]
                        
                    existing_url.external_api_source_id = source.id
                    
                    items_updated += 1
                else:
                    # Create new URL
                    new_url = RegulationURL(
                        url=url,
                        title=item.get("title", "Unknown"),
                        external_api_source_id=source.id,
                        publication_date=item.get("publication_date"),
                        confidence_level=0.8,  # Default confidence
                        status="new"
                    )
                    
                    db.add(new_url)
                    items_created += 1
            except Exception as e:
                logger.error(f"Error processing item from {source.name}: {str(e)}")
                items_failed += 1
                
        db.commit()
        
        # Update the sync log
        end_time = datetime.utcnow()
        duration_seconds = (end_time - start_time).total_seconds()
        
        sync_log.status = "success"
        sync_log.items_processed = items_processed
        sync_log.items_created = items_created
        sync_log.items_updated = items_updated
        sync_log.items_failed = items_failed
        sync_log.duration_seconds = duration_seconds
        
        db.commit()
        
        logger.info(f"Synchronization from {source.name} completed: {items_created} created, {items_updated} updated, {items_failed} failed")
    except Exception as e:
        logger.error(f"Error synchronizing from {source.name}: {str(e)}")
        
        # Update the source with error
        source.last_error = str(e)
        db.commit()
        
        # Update the sync log
        if sync_log:
            sync_log.status = "error"
            sync_log.error_message = str(e)
            
            if start_time:
                end_time = datetime.utcnow()
                sync_log.duration_seconds = (end_time - start_time).total_seconds()
                
            db.commit()
