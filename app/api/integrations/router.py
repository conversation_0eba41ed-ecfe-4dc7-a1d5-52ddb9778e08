
"""
Main router for integration APIs using SuperGlu.
"""
from fastapi import APIRouter, Depends, Query
from typing import Dict, Any, List

from app.api.integrations.superglu import router as superglu_router

router = APIRouter()

# Include the SuperGlu router
router.include_router(
    superglu_router,
    prefix="/superglu",
    tags=["superglu_integration"]
)

@router.get("/", tags=["integrations"])
async def list_available_integrations():
    """List all available integration options."""
    return {
        "available_integrations": [
            {
                "name": "SuperGlu",
                "endpoint": "/superglu",
                "description": "Integration with external governance/risk tools via SuperGlu",
                "status": "active"
            }
        ]
    }
