
"""
API for regulatory impact assessment.
"""
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Body, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field
import json

from app.db import get_db
from app.db.models import RegulationURL
from app.db.models import RegulatoryChange, ChangeImpact
from app.db.models import ChangeImpact

router = APIRouter()

class RiskFactorModel(BaseModel):
    """Model for risk factors in impact assessment."""
    name: str
    weight: float
    score: float

class BusinessProcessModel(BaseModel):
    """Model for business processes in impact assessment."""
    name: str
    importance: float  # 0-1 scale

class ImpactAssessmentInput(BaseModel):
    """Input model for impact assessment."""
    regulation_url_id: int
    business_processes: List[BusinessProcessModel]
    company_size: str = Field(..., description="small, medium, large, enterprise")
    industry: str
    geographical_regions: List[str]
    data_types_processed: List[str]
    existing_compliance_level: float = Field(..., ge=0, le=10, description="Scale of 0-10")
    
class ImpactAssessmentResult(BaseModel):
    """Result model for impact assessment."""
    overall_impact_score: float
    impact_category: str
    risk_factors: List[RiskFactorModel]
    affected_processes: List[Dict[str, Any]]
    estimated_implementation_cost: Dict[str, Any]
    estimated_implementation_time: Dict[str, Any]
    recommendations: List[str]

@router.post("/assess", response_model=ImpactAssessmentResult)
async def assess_regulatory_impact(
    assessment_input: ImpactAssessmentInput,
    db: Session = Depends(get_db)
):
    """
    Perform a regulatory impact assessment based on input parameters.
    """
    try:
        # Retrieve regulation information
        regulation = db.query(RegulationURL).filter(RegulationURL.id == assessment_input.regulation_url_id).first()
        if not regulation:
            raise HTTPException(status_code=404, detail="Regulation not found")
        
        # Calculate impact scores for each risk factor
        risk_factors = calculate_risk_factors(
            assessment_input.company_size,
            assessment_input.industry,
            assessment_input.geographical_regions,
            assessment_input.data_types_processed,
            assessment_input.existing_compliance_level,
            regulation
        )
        
        # Calculate overall impact score (weighted average of risk factors)
        overall_score = sum(rf.weight * rf.score for rf in risk_factors) / sum(rf.weight for rf in risk_factors)
        
        # Determine impact category
        impact_category = determine_impact_category(overall_score)
        
        # Determine affected business processes
        affected_processes = analyze_affected_processes(assessment_input.business_processes, regulation)
        
        # Estimate implementation costs and time
        implementation_costs = estimate_implementation_costs(
            assessment_input.company_size,
            impact_category,
            affected_processes,
            assessment_input.existing_compliance_level
        )
        
        implementation_time = estimate_implementation_time(
            assessment_input.company_size,
            impact_category,
            affected_processes,
            assessment_input.existing_compliance_level
        )
        
        # Generate recommendations
        recommendations = generate_impact_recommendations(
            impact_category,
            risk_factors,
            affected_processes,
            assessment_input.existing_compliance_level,
            regulation
        )
        
        return ImpactAssessmentResult(
            overall_impact_score=round(overall_score, 2),
            impact_category=impact_category,
            risk_factors=risk_factors,
            affected_processes=affected_processes,
            estimated_implementation_cost=implementation_costs,
            estimated_implementation_time=implementation_time,
            recommendations=recommendations
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error performing impact assessment: {str(e)}")

def calculate_risk_factors(
    company_size: str,
    industry: str,
    geographical_regions: List[str],
    data_types: List[str],
    existing_compliance: float,
    regulation: RegulationURL
) -> List[RiskFactorModel]:
    """Calculate risk factors based on input parameters."""
    # This would use more sophisticated logic in a real application
    
    risk_factors = []
    
    # Data sensitivity risk
    sensitive_data_types = ["health", "financial", "biometric", "children"]
    sensitivity_score = 0
    for data_type in data_types:
        if data_type.lower() in sensitive_data_types:
            sensitivity_score += 2
        else:
            sensitivity_score += 0.5
    sensitivity_score = min(10, sensitivity_score)
    risk_factors.append(RiskFactorModel(name="Data Sensitivity", weight=0.25, score=sensitivity_score))
    
    # Industry risk
    high_risk_industries = ["healthcare", "finance", "insurance", "education"]
    medium_risk_industries = ["retail", "technology", "telecommunications"]
    if industry.lower() in high_risk_industries:
        industry_score = 8
    elif industry.lower() in medium_risk_industries:
        industry_score = 5
    else:
        industry_score = 3
    risk_factors.append(RiskFactorModel(name="Industry Risk", weight=0.2, score=industry_score))
    
    # Company size risk
    size_scores = {
        "small": 3,
        "medium": 5,
        "large": 7,
        "enterprise": 9
    }
    risk_factors.append(RiskFactorModel(name="Organizational Complexity", weight=0.15, 
                                        score=size_scores.get(company_size.lower(), 5)))
    
    # Geographical complexity
    geo_score = min(len(geographical_regions) * 2, 10)
    risk_factors.append(RiskFactorModel(name="Geographical Complexity", weight=0.15, score=geo_score))
    
    # Existing compliance gap
    compliance_gap = 10 - existing_compliance
    risk_factors.append(RiskFactorModel(name="Compliance Gap", weight=0.25, score=compliance_gap))
    
    return risk_factors

def determine_impact_category(score: float) -> str:
    """Determine impact category based on overall score."""
    if score >= 8:
        return "Critical"
    elif score >= 6:
        return "High"
    elif score >= 4:
        return "Medium"
    else:
        return "Low"

def analyze_affected_processes(
    business_processes: List[BusinessProcessModel],
    regulation: RegulationURL
) -> List[Dict[str, Any]]:
    """Analyze which business processes are affected by the regulation."""
    # This would be more sophisticated in a real application
    
    affected = []
    
    # Example logic - in a real application, this would analyze the regulation content
    for process in business_processes:
        # Mock impact calculation
        if "data" in process.name.lower() or "customer" in process.name.lower():
            impact_level = "high"
            impact_score = 8
        elif "marketing" in process.name.lower() or "sales" in process.name.lower():
            impact_level = "medium"
            impact_score = 5
        else:
            impact_level = "low"
            impact_score = 3
            
        affected.append({
            "process_name": process.name,
            "importance": process.importance,
            "impact_level": impact_level,
            "impact_score": impact_score,
            "weighted_impact": round(process.importance * impact_score, 2),
            "required_changes": [
                "Update privacy notices",
                "Implement consent mechanisms",
                "Review data retention periods"
            ]
        })
    
    # Sort by weighted impact (highest first)
    affected.sort(key=lambda x: x["weighted_impact"], reverse=True)
    
    return affected

def estimate_implementation_costs(
    company_size: str,
    impact_category: str,
    affected_processes: List[Dict[str, Any]],
    existing_compliance: float
) -> Dict[str, Any]:
    """Estimate implementation costs based on assessment factors."""
    # Base costs by company size (example values)
    base_costs = {
        "small": {"low": 5000, "medium": 15000, "high": 40000, "critical": 75000},
        "medium": {"low": 15000, "medium": 50000, "high": 120000, "critical": 250000},
        "large": {"low": 50000, "medium": 150000, "high": 400000, "critical": 750000},
        "enterprise": {"low": 100000, "medium": 300000, "high": 750000, "critical": 1500000}
    }
    
    # Get base cost
    base = base_costs.get(company_size.lower(), base_costs["medium"]).get(impact_category.lower(), 50000)
    
    # Adjust for existing compliance level (higher compliance = lower cost)
    compliance_factor = 1 - (existing_compliance / 15)  # Reduction factor
    adjusted_base = base * compliance_factor
    
    # Calculate process-specific costs
    process_costs = []
    for process in affected_processes:
        process_cost = (process["weighted_impact"] / 10) * base * 0.2  # 20% of base per process, weighted by impact
        process_costs.append({
            "process": process["process_name"],
            "estimated_cost": round(process_cost, 2)
        })
    
    # Calculate total cost
    total_cost = adjusted_base + sum(pc["estimated_cost"] for pc in process_costs)
    
    # Define cost ranges
    low_range = total_cost * 0.8
    high_range = total_cost * 1.2
    
    return {
        "currency": "USD",
        "base_cost": round(base, 2),
        "adjusted_base_cost": round(adjusted_base, 2),
        "process_specific_costs": process_costs,
        "total_estimated_cost": round(total_cost, 2),
        "cost_range": {
            "low": round(low_range, 2),
            "high": round(high_range, 2)
        },
        "cost_breakdown": {
            "technology": round(total_cost * 0.4, 2),  # 40% technology
            "personnel": round(total_cost * 0.35, 2),  # 35% personnel
            "training": round(total_cost * 0.15, 2),   # 15% training
            "other": round(total_cost * 0.1, 2)        # 10% other
        }
    }

def estimate_implementation_time(
    company_size: str,
    impact_category: str,
    affected_processes: List[Dict[str, Any]],
    existing_compliance: float
) -> Dict[str, Any]:
    """Estimate implementation time based on assessment factors."""
    # Base times by company size (in months)
    base_times = {
        "small": {"low": 1, "medium": 3, "high": 6, "critical": 9},
        "medium": {"low": 2, "medium": 4, "high": 8, "critical": 12},
        "large": {"low": 3, "medium": 6, "high": 12, "critical": 18},
        "enterprise": {"low": 4, "medium": 8, "high": 16, "critical": 24}
    }
    
    # Get base time
    base = base_times.get(company_size.lower(), base_times["medium"]).get(impact_category.lower(), 6)
    
    # Adjust for existing compliance level (higher compliance = lower time)
    compliance_factor = 1 - (existing_compliance / 15)
    adjusted_base = base * compliance_factor
    
    # Calculate process-specific times
    process_times = []
    for process in affected_processes:
        process_time = (process["weighted_impact"] / 10) * base * 0.3  # 30% of base per process, weighted by impact
        process_times.append({
            "process": process["process_name"],
            "estimated_time_months": round(process_time, 1)
        })
    
    # Calculate implementation phases
    assessment_phase = adjusted_base * 0.2
    planning_phase = adjusted_base * 0.3
    implementation_phase = adjusted_base * 0.4
    testing_phase = adjusted_base * 0.1
    
    # Calculate total time (not just a sum of phases due to parallelization)
    total_time = adjusted_base * 0.8  # Assuming 20% efficiency from parallelization
    
    # Define time ranges
    low_range = total_time * 0.8
    high_range = total_time * 1.3
    
    return {
        "total_estimated_time_months": round(total_time, 1),
        "time_range_months": {
            "optimistic": round(low_range, 1),
            "pessimistic": round(high_range, 1)
        },
        "implementation_phases": {
            "assessment": round(assessment_phase, 1),
            "planning": round(planning_phase, 1),
            "implementation": round(implementation_phase, 1),
            "testing_and_validation": round(testing_phase, 1)
        },
        "process_specific_times": process_times
    }

def generate_impact_recommendations(
    impact_category: str,
    risk_factors: List[RiskFactorModel],
    affected_processes: List[Dict[str, Any]],
    existing_compliance: float,
    regulation: RegulationURL
) -> List[str]:
    """Generate recommendations based on impact assessment."""
    recommendations = []
    
    # Add category-specific recommendations
    if impact_category == "Critical":
        recommendations.append("Form a dedicated compliance task force with executive sponsorship")
        recommendations.append("Consider engaging specialized legal counsel for implementation support")
        recommendations.append("Develop a comprehensive compliance roadmap with regular board-level reporting")
    elif impact_category == "High":
        recommendations.append("Establish a cross-functional compliance team to implement changes")
        recommendations.append("Develop a detailed project plan with clear milestones and accountability")
        recommendations.append("Consider targeted external expertise for complex implementation areas")
    elif impact_category == "Medium":
        recommendations.append("Assign a compliance coordinator to oversee implementation activities")
        recommendations.append("Develop a phased implementation approach focusing on highest-risk areas first")
    else:  # Low
        recommendations.append("Integrate compliance requirements into existing processes and reviews")
        recommendations.append("Establish monitoring procedures to track ongoing compliance")
    
    # Add recommendations based on high-scoring risk factors
    high_risk_factors = [rf for rf in risk_factors if rf.score >= 7]
    for factor in high_risk_factors:
        if factor.name == "Data Sensitivity":
            recommendations.append("Conduct a data mapping exercise to identify and classify all sensitive data")
            recommendations.append("Implement enhanced security controls for sensitive data categories")
        elif factor.name == "Industry Risk":
            recommendations.append("Review industry-specific regulatory guidance and common compliance approaches")
            recommendations.append("Engage with industry associations for compliance best practices")
        elif factor.name == "Organizational Complexity":
            recommendations.append("Develop clear roles and responsibilities across departments and regions")
            recommendations.append("Establish a centralized compliance documentation repository")
        elif factor.name == "Geographical Complexity":
            recommendations.append("Analyze jurisdiction-specific requirements and implementation differences")
        elif factor.name == "Compliance Gap":
            recommendations.append("Prioritize closing critical compliance gaps before addressing lower-risk areas")
    
    # Add recommendations for highest-impact business processes
    high_impact_processes = [p for p in affected_processes if p["impact_level"] == "high"][:2]
    for process in high_impact_processes:
        recommendations.append(f"Develop a detailed remediation plan for the {process['process_name']} process")
    
    # Add general recommendations
    if existing_compliance < 5:
        recommendations.append("Invest in compliance training for all relevant personnel")
        recommendations.append("Document current practices as a baseline for improvement")
    
    recommendations.append("Establish ongoing compliance monitoring and regular reassessment procedures")
    
    return recommendations
