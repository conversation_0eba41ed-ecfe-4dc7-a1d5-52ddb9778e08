
"""Document analysis API for regulatory compliance."""
from typing import Dict, Any, List, Optional
import tempfile
import logging
import os

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

from app.db import get_db

router = APIRouter(
    prefix="/documents",
    tags=["document analysis"],
    responses={404: {"description": "Not found"}},
)

logger = logging.getLogger(__name__)

@router.post("/analyze", response_model=Dict[str, Any])
async def analyze_document(
    document: UploadFile = File(...),
    document_type: str = Form(...),  # pdf, docx, txt
    country: Optional[str] = Form(None),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Analyze a document for regulatory compliance.
    
    Args:
        document: The document file to analyze
        document_type: Type of document (pdf, docx, txt)
        country: Optional country code to focus analysis
        db: Database session
        
    Returns:
        Dict[str, Any]: Analysis results including entities, keywords, and compliance issues
    """
    # Validate document type
    valid_types = ["pdf", "docx", "txt"]
    if document_type not in valid_types:
        raise HTTPException(
            status_code=400, 
            detail=f"Invalid document type. Must be one of: {', '.join(valid_types)}"
        )
    
    # Save uploaded file to temp location
    with tempfile.NamedTemporaryFile(delete=False, suffix=f".{document_type}") as temp_file:
        temp_path = temp_file.name
        content = await document.read()
        temp_file.write(content)
    
    try:
        # Extract text from document based on type
        document_text = extract_text_from_document(temp_path, document_type)
        
        # Analyze the document text
        analysis_results = analyze_document_text(document_text, country)
        
        # Return analysis results
        return {
            "filename": document.filename,
            "document_type": document_type,
            "file_size_kb": len(content) / 1024,
            "analysis": analysis_results
        }
    
    except Exception as e:
        logger.error(f"Error analyzing document: {e}")
        raise HTTPException(status_code=500, detail=f"Document analysis error: {str(e)}")
    
    finally:
        # Clean up temporary file
        try:
            os.unlink(temp_path)
        except Exception as e:
            logger.error(f"Error removing temporary file: {e}")


def extract_text_from_document(file_path: str, document_type: str) -> str:
    """
    Extract text from a document file.
    
    Args:
        file_path: Path to the document file
        document_type: Type of document
        
    Returns:
        str: Extracted text
    """
    try:
        if document_type == "pdf":
            # Install PyPDF2 if it's not already installed
            try:
                import PyPDF2
            except ImportError:
                import subprocess
                subprocess.check_call(["pip", "install", "PyPDF2"])
                import PyPDF2
            
            text = ""
            with open(file_path, "rb") as pdf_file:
                pdf_reader = PyPDF2.PdfReader(pdf_file)
                for page_num in range(len(pdf_reader.pages)):
                    text += pdf_reader.pages[page_num].extract_text()
            return text
            
        elif document_type == "docx":
            # Install python-docx if it's not already installed
            try:
                import docx
            except ImportError:
                import subprocess
                subprocess.check_call(["pip", "install", "python-docx"])
                import docx
            
            doc = docx.Document(file_path)
            return "\n".join([paragraph.text for paragraph in doc.paragraphs])
            
        elif document_type == "txt":
            with open(file_path, "r", encoding="utf-8") as txt_file:
                return txt_file.read()
        
        else:
            raise ValueError(f"Unsupported document type: {document_type}")
    
    except Exception as e:
        logger.error(f"Error extracting text from {document_type} file: {e}")
        raise


def analyze_document_text(text: str, country: Optional[str] = None) -> Dict[str, Any]:
    """
    Analyze extracted text for regulatory compliance.
    
    Args:
        text: Document text to analyze
        country: Optional country code to focus analysis
        
    Returns:
        Dict[str, Any]: Analysis results
    """
    # Placeholder for actual NLP analysis
    # In a real implementation, this would use NLP libraries
    
    # Basic keyword analysis
    privacy_keywords = ["privacy", "personal data", "data protection", "gdpr", "consent", "processing"]
    security_keywords = ["security", "encryption", "confidential", "breach", "cybersecurity"]
    financial_keywords = ["financial", "payment", "transaction", "banking", "credit", "debit"]
    
    privacy_count = sum(1 for keyword in privacy_keywords if keyword.lower() in text.lower())
    security_count = sum(1 for keyword in security_keywords if keyword.lower() in text.lower())
    financial_count = sum(1 for keyword in financial_keywords if keyword.lower() in text.lower())
    
    # Categorize document
    categories = []
    if privacy_count > 2:
        categories.append("Privacy")
    if security_count > 2:
        categories.append("Security")
    if financial_count > 2:
        categories.append("Financial")
    
    # Word count
    word_count = len(text.split())
    
    # Readability score (very basic)
    sentences = text.split('.')
    sentence_count = len(sentences)
    average_sentence_length = word_count / max(1, sentence_count)
    
    return {
        "word_count": word_count,
        "sentence_count": sentence_count,
        "average_sentence_length": round(average_sentence_length, 1),
        "categories": categories,
        "keyword_counts": {
            "privacy": privacy_count,
            "security": security_count,
            "financial": financial_count
        },
        "regulatory_relevance": {
            "overall_score": min(100, (privacy_count + security_count + financial_count) * 10),
            "categorization": categories[0] if categories else "General",
            "country_specific": [] if not country else [
                {"country": country, "relevance": "High" if any(cat in categories for cat in ["Privacy", "Security"]) else "Medium"}
            ]
        }
    }
"""API endpoints for document analysis and AI-powered summarization."""
from fastapi import APIRouter, UploadFile, File, HTTPException, Depends
from fastapi.responses import JSONResponse
import tempfile
import os
from typing import List, Dict, Any, Optional
import logging
from app.utils.pdf_analyzer import extract_text_from_pdf, analyze_regulatory_text
from app.utils.ai_summarizer import AISummarizer
from app.db.get_db import get_db
from sqlalchemy.orm import Session
from app.db.models import Document, DocumentSummary

router = APIRouter()
summarizer = AISummarizer()

@router.post("/analyze")
async def analyze_document(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """
    Analyze a document using built-in regulatory parser.
    
    Args:
        file: Uploaded file
        db: Database session
        
    Returns:
        Analysis results
    """
    if not file.filename.lower().endswith('.pdf'):
        raise HTTPException(status_code=400, detail="Only PDF files are supported")
    
    # Save the uploaded file temporarily
    with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as temp_file:
        temp_file.write(await file.read())
        temp_path = temp_file.name
    
    try:
        # Extract text from the PDF
        text = extract_text_from_pdf(temp_path)
        
        # Analyze the text
        analysis_result = analyze_regulatory_text(text)
        
        # Store the document in the database
        doc = Document(
            filename=file.filename,
            content_type=file.content_type,
            content_length=os.path.getsize(temp_path),
            confidence_score=analysis_result["confidence_score"]
        )
        db.add(doc)
        db.commit()
        db.refresh(doc)
        
        # Add document ID to result
        analysis_result["document_id"] = doc.id
        analysis_result["filename"] = file.filename
        
        return analysis_result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error analyzing document: {str(e)}")
    finally:
        # Clean up the temporary file
        os.unlink(temp_path)

@router.post("/summarize")
async def summarize_document(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """
    Generate an AI-powered summary of a document.
    
    Args:
        file: Uploaded file
        db: Database session
        
    Returns:
        Document summary with key requirements, deadlines, and penalties
    """
    if not file.filename.lower().endswith('.pdf'):
        raise HTTPException(status_code=400, detail="Only PDF files are supported")
    
    # Check if OpenAI API key is configured
    if not summarizer.api_key:
        raise HTTPException(
            status_code=501, 
            detail="AI summarization not available. OpenAI API key not configured."
        )
    
    # Save the uploaded file temporarily
    with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as temp_file:
        temp_file.write(await file.read())
        temp_path = temp_file.name
    
    try:
        # Generate AI summary
        summary_data = summarizer.summarize_pdf_document(temp_path)
        
        # Store the document and summary in the database
        doc = Document(
            filename=file.filename,
            content_type=file.content_type,
            content_length=os.path.getsize(temp_path)
        )
        db.add(doc)
        db.commit()
        db.refresh(doc)
        
        # Store the summary
        doc_summary = DocumentSummary(
            document_id=doc.id,
            summary_text=summary_data["summary"],
            key_requirements=summary_data.get("key_requirements", []),
            deadlines=summary_data.get("deadlines", []),
            penalties=summary_data.get("penalties", [])
        )
        db.add(doc_summary)
        db.commit()
        
        # Add document ID to result
        summary_data["document_id"] = doc.id
        summary_data["filename"] = file.filename
        
        return summary_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error summarizing document: {str(e)}")
    finally:
        # Clean up the temporary file
        os.unlink(temp_path)

@router.get("/summaries")
async def get_summaries(
    limit: int = 10,
    skip: int = 0,
    db: Session = Depends(get_db)
):
    """
    Get a list of document summaries.
    
    Args:
        limit: Maximum number of summaries to retrieve
        skip: Number of summaries to skip
        db: Database session
        
    Returns:
        List of document summaries
    """
    summaries = db.query(DocumentSummary).join(Document).offset(skip).limit(limit).all()
    
    result = []
    for summary in summaries:
        result.append({
            "id": summary.id,
            "document_id": summary.document_id,
            "filename": summary.document.filename,
            "summary": summary.summary_text,
            "key_requirements": summary.key_requirements,
            "deadlines": summary.deadlines,
            "penalties": summary.penalties,
            "created_at": summary.created_at
        })
    
    return result

@router.get("/summary/{summary_id}")
async def get_summary(
    summary_id: int,
    db: Session = Depends(get_db)
):
    """
    Get a specific document summary.
    
    Args:
        summary_id: ID of the summary
        db: Database session
        
    Returns:
        Document summary details
    """
    summary = db.query(DocumentSummary).filter(DocumentSummary.id == summary_id).first()
    
    if not summary:
        raise HTTPException(status_code=404, detail="Summary not found")
    
    return {
        "id": summary.id,
        "document_id": summary.document_id,
        "filename": summary.document.filename,
        "summary": summary.summary_text,
        "key_requirements": summary.key_requirements,
        "deadlines": summary.deadlines,
        "penalties": summary.penalties,
        "created_at": summary.created_at
    }
