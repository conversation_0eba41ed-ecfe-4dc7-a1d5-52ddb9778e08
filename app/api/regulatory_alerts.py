
"""
API for regulatory alert notifications.
"""
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Path, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field
from datetime import datetime, timedelta
import logging

from app.db import get_db
from app.db.models import RegulationURL, Regulator, RegulatoryChange, Country

router = APIRouter()

class AlertThreshold(BaseModel):
    """Model for alert thresholds."""
    importance: int = Field(..., ge=1, le=10, description="Importance level 1-10")
    categories: List[str] = Field(None, description="Regulation categories to monitor")
    countries: List[str] = Field(None, description="Country codes to monitor")
    lookback_days: int = Field(30, ge=1, le=365, description="Days to look back")

class Alert(BaseModel):
    """Model for regulatory alerts."""
    id: int
    title: str
    description: str
    country_code: str
    importance: int
    publication_date: datetime
    categories: List[str]
    url: Optional[str] = None

class QuarterlyDigest(BaseModel):
    """Model for quarterly regulatory digest."""
    quarter: str
    year: int
    total_changes: int
    summary: str
    top_categories: List[Dict[str, Any]]
    significant_changes: List[Dict[str, Any]]
    regional_breakdown: Dict[str, int]

@router.post("/configure", response_model=Dict[str, Any])
def configure_alerts(
    config: AlertThreshold,
    db: Session = Depends(get_db)
):
    """
    Configure alert thresholds for regulatory monitoring.
    
    Args:
        config: Alert configuration parameters
        db: Database session
        
    Returns:
        Dict containing configuration status and settings
    """
    # In a production system, this would save to user preferences
    # For now, we'll return the configuration as if saved
    return {
        "status": "success",
        "configuration": {
            "importance": config.importance,
            "categories": config.categories,
            "countries": config.countries,
            "lookback_days": config.lookback_days
        }
    }

@router.get("/recent", response_model=List[Alert])
def get_recent_alerts(
    importance: Optional[int] = Query(None, ge=1, le=10, description="Minimum importance level"),
    countries: Optional[str] = Query(None, description="Comma-separated list of country codes"),
    categories: Optional[str] = Query(None, description="Comma-separated list of categories"),
    days: Optional[int] = Query(30, ge=1, le=365, description="Days to look back"),
    db: Session = Depends(get_db)
):
    """
    Get recent regulatory alerts based on configured filters.
    
    Args:
        importance: Minimum importance level filter
        countries: Country codes to filter by
        categories: Categories to filter by
        days: Number of days to look back
        db: Database session
        
    Returns:
        List of matching regulatory alerts
    """
    try:
        # Calculate the cutoff date
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # Build the base query
        query = db.query(RegulatoryChange).join(
            RegulationURL, RegulatoryChange.regulation_url_id == RegulationURL.id
        ).join(
            Regulator, RegulationURL.regulator_id == Regulator.id
        ).join(
            Country, Regulator.country_id == Country.id
        ).filter(
            RegulatoryChange.change_date >= cutoff_date
        )
        
        # Apply filters
        if importance:
            query = query.filter(RegulatoryChange.importance >= importance)
            
        if countries:
            country_list = [code.strip() for code in countries.split(",")]
            query = query.filter(Country.code.in_(country_list))
            
        if categories:
            category_list = [cat.strip() for cat in categories.split(",")]
            # This assumes categories are stored in a way that can be filtered
            # You might need to adjust based on your actual data model
            query = query.filter(RegulationURL.category.in_(category_list))
            
        # Execute query and convert to response model
        changes = query.order_by(RegulatoryChange.change_date.desc()).all()
        
        # Convert to response format
        alerts = []
        for change in changes:
            url = change.regulation_url
            alerts.append(Alert(
                id=change.id,
                title=change.title,
                description=change.description,
                country_code=url.regulator.country.code,
                importance=change.importance,
                publication_date=change.change_date,
                categories=[url.category] if url.category else [],
                url=url.url
            ))
            
        return alerts
        
    except Exception as e:
        logging.error(f"Error in get_recent_alerts: {str(e)}")
        # Return empty list instead of raising exception for better UI resilience
        return []

@router.get("/{alert_id}", response_model=Alert)
def get_alert_details(
    alert_id: int = Path(..., description="ID of the alert to retrieve"),
    db: Session = Depends(get_db)
):
    """
    Get detailed information about a specific regulatory alert.
    
    Args:
        alert_id: ID of the alert to retrieve
        db: Database session
        
    Returns:
        Detailed alert information
    """
    try:
        change = db.query(RegulatoryChange).filter(RegulatoryChange.id == alert_id).first()
        
        if not change:
            raise HTTPException(status_code=404, detail=f"Alert with ID {alert_id} not found")
            
        url = change.regulation_url
        
        return Alert(
            id=change.id,
            title=change.title,
            description=change.description,
            country_code=url.regulator.country.code,
            importance=change.importance,
            publication_date=change.change_date,
            categories=[url.category] if url.category else [],
            url=url.url
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error in get_alert_details: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving alert details: {str(e)}")

@router.get("/digest/quarterly", response_model=QuarterlyDigest)
def get_quarterly_digest(
    year: Optional[int] = Query(None, description="Year for the digest (defaults to current)"),
    quarter: Optional[int] = Query(None, ge=1, le=4, description="Quarter (1-4, defaults to last complete)"),
    db: Session = Depends(get_db)
):
    """
    Get a quarterly digest of regulatory changes.
    
    Args:
        year: Year for the digest
        quarter: Quarter number (1-4)
        db: Database session
        
    Returns:
        Quarterly digest of regulatory changes
    """
    try:
        now = datetime.utcnow()
        
        # Set defaults if not provided
        if not year:
            year = now.year
            # If we're in Q1 and quarter not specified, default to Q4 of previous year
            if now.month <= 3 and not quarter:
                year -= 1
                quarter = 4
                
        if not quarter:
            # Calculate the last complete quarter
            current_quarter = (now.month - 1) // 3 + 1
            quarter = current_quarter - 1 if current_quarter > 1 else 4
            if current_quarter == 1:
                year -= 1
                
        # Calculate start and end dates for the quarter
        start_month = (quarter - 1) * 3 + 1
        end_month = quarter * 3
        
        start_date = datetime(year, start_month, 1)
        if end_month == 12:
            end_date = datetime(year + 1, 1, 1)
        else:
            end_date = datetime(year, end_month + 1, 1)
        
        end_date = end_date - timedelta(seconds=1)
        
        # Query changes within the quarter
        changes = db.query(RegulatoryChange).filter(
            RegulatoryChange.change_date >= start_date,
            RegulatoryChange.change_date <= end_date
        ).all()
        
        # Calculate statistics
        total_changes = len(changes)
        
        # Extract categories and count occurrences
        categories = {}
        regions = {}
        significant_changes = []
        
        for change in changes:
            # Track categories
            category = change.regulation_url.category
            if category:
                categories[category] = categories.get(category, 0) + 1
                
            # Track regions
            country_code = change.regulation_url.regulator.country.code
            regions[country_code] = regions.get(country_code, 0) + 1
            
            # Track significant changes (importance >= 7)
            if change.importance >= 7:
                significant_changes.append({
                    "id": change.id,
                    "title": change.title,
                    "country": country_code,
                    "importance": change.importance,
                    "date": change.change_date
                })
        
        # Get top categories
        top_categories = [
            {"name": cat, "count": count}
            for cat, count in sorted(categories.items(), key=lambda x: x[1], reverse=True)[:5]
        ]
        
        # Prepare summary
        summary = f"In Q{quarter} {year}, there were {total_changes} regulatory changes across {len(regions)} regions."
        if significant_changes:
            summary += f" {len(significant_changes)} changes were considered highly significant."
        
        return QuarterlyDigest(
            quarter=f"Q{quarter}",
            year=year,
            total_changes=total_changes,
            summary=summary,
            top_categories=top_categories,
            significant_changes=significant_changes[:10],  # Limit to top 10
            regional_breakdown=regions
        )
        
    except Exception as e:
        logging.error(f"Error in get_quarterly_digest: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating quarterly digest: {str(e)}")

@router.get("/notifications/setup", response_model=Dict[str, Any])
def setup_notifications(
    email: Optional[str] = Query(None, description="Email for notifications"),
    frequency: Optional[str] = Query("daily", description="Notification frequency (daily, weekly, monthly, quarterly)"),
    channels: Optional[str] = Query("email", description="Notification channels (comma-separated: email, sms, webhook)"),
    db: Session = Depends(get_db)
):
    """
    Configure notification preferences for regulatory updates.
    
    Args:
        email: Email address for notifications
        frequency: Notification frequency
        channels: Notification channels
        db: Database session
        
    Returns:
        Notification configuration status
    """
    # In a production system, this would save to user preferences
    # For now, we'll return the configuration as if saved
    channel_list = [c.strip() for c in channels.split(",")] if channels else ["email"]
    
    return {
        "status": "success",
        "notification_settings": {
            "email": email,
            "frequency": frequency,
            "channels": channel_list,
            "next_notification": (datetime.utcnow() + timedelta(days=1)).strftime("%Y-%m-%d")
        }
    }
