
"""
API endpoints for regulatory document relationship mapping.
"""
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Query, Depends, UploadFile, File
import os
import tempfile
import json
import logging
from app.utils.regulatory_parser import RegulatoryParser
from app.utils.relationship_mapper import RelationshipMapper

router = APIRouter()
parser = RegulatoryParser()
mapper = RelationshipMapper()

@router.post("/api/documents/analyze", response_model=Dict[str, Any])
async def analyze_document(file: UploadFile = File(...)):
    """
    Analyze a single regulatory document.
    
    Args:
        file: Uploaded document file
        
    Returns:
        Dict containing analysis results
    """
    # Save the uploaded file temporarily
    with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
        content = await file.read()
        temp_file.write(content)
        temp_path = temp_file.name
    
    try:
        # Analyze the document
        result = parser.parse_document(temp_path)
        result["file_name"] = file.filename
        
        return result
    except Exception as e:
        logging.error(f"Error analyzing document: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error analyzing document: {str(e)}")
    finally:
        # Clean up the temporary file
        os.unlink(temp_path)

@router.post("/api/documents/map-relationships", response_model=Dict[str, Any])
async def map_relationships(documents: List[Dict[str, Any]]):
    """
    Map relationships between multiple regulatory documents.
    
    Args:
        documents: List of document analysis results
        
    Returns:
        Dict containing relationship mapping data
    """
    try:
        # Map relationships
        relationship_data = mapper.map_document_relationships(documents)
        return relationship_data
    except Exception as e:
        logging.error(f"Error mapping relationships: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error mapping relationships: {str(e)}")

@router.post("/api/documents/batch-analyze", response_model=Dict[str, Any])
async def batch_analyze(files: List[UploadFile] = File(...)):
    """
    Analyze multiple documents and map relationships between them.
    
    Args:
        files: List of uploaded document files
        
    Returns:
        Dict containing analysis results and relationship mapping
    """
    temp_files = []
    try:
        # Save all uploaded files temporarily
        for file in files:
            with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
                content = await file.read()
                temp_file.write(content)
                temp_files.append((temp_file.name, file.filename))
        
        # Analyze each document
        analysis_results = []
        for temp_path, filename in temp_files:
            try:
                result = parser.parse_document(temp_path)
                result["file_name"] = filename
                analysis_results.append(result)
            except Exception as e:
                logging.error(f"Error analyzing {filename}: {str(e)}")
                # Continue with other files even if one fails
        
        # Map relationships if we have at least 2 documents
        relationship_data = {}
        if len(analysis_results) >= 2:
            relationship_data = mapper.map_document_relationships(analysis_results)
        
        return {
            "document_count": len(analysis_results),
            "documents": analysis_results,
            "relationships": relationship_data
        }
    except Exception as e:
        logging.error(f"Error in batch analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error in batch analysis: {str(e)}")
    finally:
        # Clean up all temporary files
        for temp_path, _ in temp_files:
            try:
                os.unlink(temp_path)
            except:
                pass

@router.get("/api/frameworks/controls", response_model=Dict[str, Any])
async def get_control_frameworks(framework: Optional[str] = Query(None)):
    """
    Get available control frameworks and their controls.
    
    Args:
        framework: Optional framework name to filter (NIST, ISO, ISF)
        
    Returns:
        Dict containing control frameworks data
    """
    frameworks = {
        "NIST": mapper._load_nist_controls(),
        "ISO": mapper._load_iso_controls(),
        "ISF": mapper._load_isf_controls()
    }
    
    if framework and framework.upper() in frameworks:
        return {framework.upper(): frameworks[framework.upper()]}
    
    return frameworks
