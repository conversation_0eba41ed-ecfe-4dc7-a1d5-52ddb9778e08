"""
API endpoints for regulations CSV operations.

This module provides REST API endpoints for importing, exporting, and managing
regulations CSV data with soft-delete support and comprehensive validation.
"""

import os
import tempfile
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Query, BackgroundTasks
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session

from app.db import get_db
from app.db.models.regulations_csv import RegulationCSVRecord, RegulationCSVImportLog, RegulationCSVExportLog
from app.schemas.regulations_csv import (
    RegulationCSVRecord as RegulationCSVRecordSchema,
    RegulationCSVRecordCreate,
    RegulationCSVRecordUpdate,
    RegulationCSVImportResult,
    RegulationCSVExportRequest,
    RegulationCSVBatch
)
from app.utils.regulations_csv_utils import RegulationCSVProcessor

router = APIRouter(prefix="/api/v1/regulations-csv", tags=["Regulations CSV"])


@router.get("/records", response_model=List[RegulationCSVRecordSchema])
def get_csv_records(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    country_code: Optional[str] = Query(None, description="Filter by country code"),
    document_type: Optional[str] = Query(None, description="Filter by document type"),
    legal_status: Optional[str] = Query(None, description="Filter by legal status"),
    include_deleted: bool = Query(False, description="Include soft-deleted records"),
    db: Session = Depends(get_db)
):
    """
    Get regulations CSV records with filtering and pagination.
    
    Supports soft-delete filtering and comprehensive search options.
    """
    query = db.query(RegulationCSVRecord)
    
    # Apply soft-delete filter
    if not include_deleted:
        query = query.filter(RegulationCSVRecord.is_deleted == False)
    
    # Apply filters
    if country_code:
        query = query.filter(RegulationCSVRecord.country_code == country_code)
    if document_type:
        query = query.filter(RegulationCSVRecord.document_type == document_type)
    if legal_status:
        query = query.filter(RegulationCSVRecord.legal_status == legal_status)
    
    # Apply pagination
    records = query.offset(skip).limit(limit).all()
    
    return records


@router.get("/records/{record_id}", response_model=RegulationCSVRecordSchema)
def get_csv_record(
    record_id: UUID,
    include_deleted: bool = Query(False, description="Include soft-deleted records"),
    db: Session = Depends(get_db)
):
    """Get a specific regulations CSV record by ID."""
    query = db.query(RegulationCSVRecord).filter(RegulationCSVRecord.id == record_id)
    
    if not include_deleted:
        query = query.filter(RegulationCSVRecord.is_deleted == False)
    
    record = query.first()
    if not record:
        raise HTTPException(status_code=404, detail="Record not found")
    
    return record


@router.post("/records", response_model=RegulationCSVRecordSchema)
def create_csv_record(
    record: RegulationCSVRecordCreate,
    db: Session = Depends(get_db)
):
    """Create a new regulations CSV record."""
    # Check for duplicates
    existing = db.query(RegulationCSVRecord).filter(
        RegulationCSVRecord.country_code == record.country_code,
        RegulationCSVRecord.document_title == record.document_title,
        RegulationCSVRecord.is_deleted == False
    ).first()
    
    if existing:
        raise HTTPException(
            status_code=400, 
            detail="Record with same country code and document title already exists"
        )
    
    # Create new record
    db_record = RegulationCSVRecord(**record.dict())
    db.add(db_record)
    db.commit()
    db.refresh(db_record)
    
    return db_record


@router.put("/records/{record_id}", response_model=RegulationCSVRecordSchema)
def update_csv_record(
    record_id: UUID,
    record_update: RegulationCSVRecordUpdate,
    db: Session = Depends(get_db)
):
    """Update a regulations CSV record."""
    db_record = db.query(RegulationCSVRecord).filter(
        RegulationCSVRecord.id == record_id,
        RegulationCSVRecord.is_deleted == False
    ).first()
    
    if not db_record:
        raise HTTPException(status_code=404, detail="Record not found")
    
    # Update fields
    update_data = record_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_record, field, value)
    
    db.commit()
    db.refresh(db_record)
    
    return db_record


@router.delete("/records/{record_id}")
def soft_delete_csv_record(
    record_id: UUID,
    user_id: Optional[UUID] = Query(None, description="ID of user performing deletion"),
    db: Session = Depends(get_db)
):
    """Soft delete a regulations CSV record."""
    db_record = db.query(RegulationCSVRecord).filter(
        RegulationCSVRecord.id == record_id,
        RegulationCSVRecord.is_deleted == False
    ).first()
    
    if not db_record:
        raise HTTPException(status_code=404, detail="Record not found")
    
    # Perform soft delete
    db_record.soft_delete()
    if user_id:
        db_record.deleted_by_id = user_id
    
    db.commit()
    
    return {"message": "Record soft deleted successfully"}


@router.post("/records/{record_id}/restore")
def restore_csv_record(
    record_id: UUID,
    db: Session = Depends(get_db)
):
    """Restore a soft-deleted regulations CSV record."""
    db_record = db.query(RegulationCSVRecord).filter(
        RegulationCSVRecord.id == record_id,
        RegulationCSVRecord.is_deleted == True
    ).first()
    
    if not db_record:
        raise HTTPException(status_code=404, detail="Deleted record not found")
    
    # Restore record
    db_record.is_deleted = False
    db_record.deleted_at = None
    db_record.deleted_by_id = None
    
    db.commit()
    db.refresh(db_record)
    
    return {"message": "Record restored successfully"}


@router.post("/import", response_model=RegulationCSVImportResult)
async def import_csv_file(
    file: UploadFile = File(..., description="CSV file to import"),
    batch_id: Optional[str] = Query(None, description="Optional batch identifier"),
    user_id: Optional[UUID] = Query(None, description="User ID for tracking"),
    db: Session = Depends(get_db)
):
    """
    Import regulations from uploaded CSV file.
    
    Supports batch processing with comprehensive error handling and validation.
    """
    if not file.filename.endswith('.csv'):
        raise HTTPException(status_code=400, detail="File must be a CSV file")
    
    # Save uploaded file temporarily
    with tempfile.NamedTemporaryFile(delete=False, suffix='.csv') as temp_file:
        content = await file.read()
        temp_file.write(content)
        temp_file_path = temp_file.name
    
    try:
        # Process the CSV file
        processor = RegulationCSVProcessor(db)
        result = processor.import_csv_file(
            file_path=temp_file_path,
            batch_id=batch_id,
            user_id=str(user_id) if user_id else None
        )
        
        return result
        
    finally:
        # Clean up temporary file
        os.unlink(temp_file_path)


@router.post("/export")
async def export_csv_file(
    export_request: RegulationCSVExportRequest,
    background_tasks: BackgroundTasks,
    user_id: Optional[UUID] = Query(None, description="User ID for tracking"),
    db: Session = Depends(get_db)
):
    """
    Export regulations to CSV file.
    
    Supports filtering and background processing for large exports.
    """
    # Generate temporary file path
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.csv')
    temp_file_path = temp_file.name
    temp_file.close()
    
    try:
        # Process the export
        processor = RegulationCSVProcessor(db)
        export_id = processor.export_csv_file(
            file_path=temp_file_path,
            export_request=export_request,
            user_id=str(user_id) if user_id else None
        )
        
        # Return file for download
        return FileResponse(
            path=temp_file_path,
            filename=f"regulations_export_{export_id}.csv",
            media_type="text/csv"
        )
        
    except Exception as e:
        # Clean up on error
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
        raise HTTPException(status_code=500, detail=f"Export failed: {str(e)}")


@router.get("/import-logs", response_model=List[dict])
def get_import_logs(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: Optional[str] = Query(None, description="Filter by import status"),
    db: Session = Depends(get_db)
):
    """Get import operation logs."""
    query = db.query(RegulationCSVImportLog)
    
    if status:
        query = query.filter(RegulationCSVImportLog.import_status == status)
    
    logs = query.order_by(RegulationCSVImportLog.created_at.desc()).offset(skip).limit(limit).all()
    
    return [
        {
            "id": log.id,
            "batch_id": log.batch_id,
            "file_name": log.file_name,
            "total_records": log.total_records,
            "successful_imports": log.successful_imports,
            "failed_imports": log.failed_imports,
            "updated_records": log.updated_records,
            "import_status": log.import_status,
            "processing_time": log.processing_time,
            "created_at": log.created_at
        }
        for log in logs
    ]


@router.get("/export-logs", response_model=List[dict])
def get_export_logs(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: Optional[str] = Query(None, description="Filter by export status"),
    db: Session = Depends(get_db)
):
    """Get export operation logs."""
    query = db.query(RegulationCSVExportLog)
    
    if status:
        query = query.filter(RegulationCSVExportLog.export_status == status)
    
    logs = query.order_by(RegulationCSVExportLog.created_at.desc()).offset(skip).limit(limit).all()
    
    return [
        {
            "id": log.id,
            "export_id": log.export_id,
            "file_name": log.file_name,
            "total_records_exported": log.total_records_exported,
            "export_status": log.export_status,
            "processing_time": log.processing_time,
            "created_at": log.created_at
        }
        for log in logs
    ]


@router.get("/stats")
def get_csv_stats(
    include_deleted: bool = Query(False, description="Include soft-deleted records in stats"),
    db: Session = Depends(get_db)
):
    """Get statistics about regulations CSV data."""
    query = db.query(RegulationCSVRecord)
    
    if not include_deleted:
        query = query.filter(RegulationCSVRecord.is_deleted == False)
    
    total_records = query.count()
    
    # Get country distribution
    country_stats = db.query(
        RegulationCSVRecord.country_code,
        db.func.count(RegulationCSVRecord.id).label('count')
    ).filter(
        RegulationCSVRecord.is_deleted == False if not include_deleted else True
    ).group_by(RegulationCSVRecord.country_code).all()
    
    # Get document type distribution
    type_stats = db.query(
        RegulationCSVRecord.document_type,
        db.func.count(RegulationCSVRecord.id).label('count')
    ).filter(
        RegulationCSVRecord.is_deleted == False if not include_deleted else True
    ).group_by(RegulationCSVRecord.document_type).all()
    
    return {
        "total_records": total_records,
        "country_distribution": {stat.country_code: stat.count for stat in country_stats},
        "document_type_distribution": {stat.document_type: stat.count for stat in type_stats},
        "include_deleted": include_deleted
    }
