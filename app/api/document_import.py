"""API for document import and database mapping."""

import os
import uuid
import tempfile
import logging
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, Depends, File, UploadFile, Form, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session

from app.db import get_db
from app.db.models import Country
from app.db.models import Regulator
from app.db.models import RegulationURL
from app.utils.document_classifier import process_pdf_for_mapping

router = APIRouter(prefix="/document-import", tags=["Document Import"])
logger = logging.getLogger(__name__)

# Store for processing jobs
import_jobs = {}

@router.post("/analyze", response_model=Dict[str, Any])
async def analyze_document(
    background_tasks: BackgroundTasks,
    document: UploadFile = File(...),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Analyze a document for potential database mappings.

    Args:
        background_tasks: Background tasks runner
        document: The document file to analyze
        db: Database session

    Returns:
        Dict[str, Any]: Analysis job ID and status
    """
    try:
        # Validate file type (only PDF for now)
        if not document.filename.lower().endswith('.pdf'):
            raise HTTPException(
                status_code=400, 
                detail="Invalid file type. Only PDF files are accepted."
            )

        # Save uploaded file to temp location
        with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as temp_file:
            temp_path = temp_file.name
            content = await document.read()
            temp_file.write(content)

        # Generate a job ID
        job_id = str(uuid.uuid4())

        # Store initial status
        import_jobs[job_id] = {
            "status": "processing",
            "filename": document.filename
        }

        # Run analysis in background
        background_tasks.add_task(
            run_analysis, 
            job_id=job_id, 
            file_path=temp_path,
            cleanup=True,
            db=db
        )

        # Return job ID for status checking
        return {
            "job_id": job_id,
            "status": "processing",
            "message": "Document analysis started"
        }
    except Exception as e:
        logger.error(f"Error in analyze_document: {e}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {e}")


@router.get("/status/{job_id}", response_model=Dict[str, Any])
async def check_status(
    job_id: str
) -> Dict[str, Any]:
    """
    Check the status of a document analysis job.

    Args:
        job_id: The job ID to check

    Returns:
        Dict[str, Any]: Current job status
    """
    try:
        if job_id not in import_jobs:
            raise HTTPException(
                status_code=404,
                detail="Job not found"
            )

        return import_jobs[job_id]
    except Exception as e:
        logger.error(f"Error in check_status: {e}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {e}")


@router.post("/apply-mapping/{job_id}", response_model=Dict[str, Any])
async def apply_mapping(
    job_id: str,
    mapping_updates: Dict[str, Any],
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Apply user-approved mappings to the database.

    Args:
        job_id: The job ID for the document analysis
        mapping_updates: User-selected entity mappings to apply
        db: Database session

    Returns:
        Dict[str, Any]: Result of the mapping operation
    """
    try:
        if job_id not in import_jobs or import_jobs[job_id]["status"] != "completed":
            raise HTTPException(
                status_code=400,
                detail="Analysis job not found or not completed"
            )

        result = {
            "updated": {},
            "created": {},
            "errors": []
        }

        # Process country updates
        if "countries" in mapping_updates:
            updated_countries = []
            created_countries = []

            for country_data in mapping_updates["countries"]:
                try:
                    if country_data.get("existing", False) and country_data.get("id"):
                        # Update existing country
                        country = db.query(Country).filter(Country.id == country_data["id"]).first()
                        if country:
                            # Update fields that are provided and not None
                            for field, value in country_data.items():
                                if field not in ["id", "existing"] and value is not None:
                                    setattr(country, field, value)
                            db.commit()
                            updated_countries.append({"id": country.id, "name": country.name})
                    else:
                        # Create new country
                        new_country = Country(
                            name=country_data["name"],
                            code=country_data.get("code", ""),
                            region=country_data.get("region", "")
                        )
                        db.add(new_country)
                        db.commit()
                        db.refresh(new_country)
                        created_countries.append({"id": new_country.id, "name": new_country.name})
                except Exception as e:
                    result["errors"].append(f"Error processing country {country_data.get('name')}: {str(e)}")
                    db.rollback()

            if updated_countries:
                result["updated"]["countries"] = updated_countries
            if created_countries:
                result["created"]["countries"] = created_countries

        # Process regulation updates
        if "regulations" in mapping_updates:
            updated_regulations = []
            created_regulations = []

            for regulation_data in mapping_updates["regulations"]:
                try:
                    if regulation_data.get("existing", False) and regulation_data.get("id"):
                        # Update existing regulation
                        regulation = db.query(RegulationURL).filter(RegulationURL.id == regulation_data["id"]).first()
                        if regulation:
                            # Update fields that are provided and not None
                            for field, value in regulation_data.items():
                                if field not in ["id", "existing"] and value is not None:
                                    setattr(regulation, field, value)
                            db.commit()
                            updated_regulations.append({"id": regulation.id, "title": regulation.title})
                    else:
                        # Create new regulation
                        new_regulation = RegulationURL(
                            title=regulation_data["title"],
                            country_id=regulation_data.get("country_id"),
                            url=regulation_data.get("url", ""),
                            description=regulation_data.get("description", "")
                        )
                        db.add(new_regulation)
                        db.commit()
                        db.refresh(new_regulation)
                        created_regulations.append({"id": new_regulation.id, "title": new_regulation.title})
                except Exception as e:
                    result["errors"].append(f"Error processing regulation {regulation_data.get('title')}: {str(e)}")
                    db.rollback()

            if updated_regulations:
                result["updated"]["regulations"] = updated_regulations
            if created_regulations:
                result["created"]["regulations"] = created_regulations

        # Process regulator updates
        if "regulators" in mapping_updates:
            updated_regulators = []
            created_regulators = []

            for regulator_data in mapping_updates["regulators"]:
                try:
                    if regulator_data.get("existing", False) and regulator_data.get("id"):
                        # Update existing regulator
                        regulator = db.query(Regulator).filter(Regulator.id == regulator_data["id"]).first()
                        if regulator:
                            # Update fields that are provided and not None
                            for field, value in regulator_data.items():
                                if field not in ["id", "existing"] and value is not None:
                                    setattr(regulator, field, value)
                            db.commit()
                            updated_regulators.append({"id": regulator.id, "name": regulator.name})
                    else:
                        # Create new regulator
                        new_regulator = Regulator(
                            name=regulator_data["name"],
                            country_id=regulator_data.get("country_id"),
                            website=regulator_data.get("website", ""),
                            description=regulator_data.get("description", "")
                        )
                        db.add(new_regulator)
                        db.commit()
                        db.refresh(new_regulator)
                        created_regulators.append({"id": new_regulator.id, "name": new_regulator.name})
                except Exception as e:
                    result["errors"].append(f"Error processing regulator {regulator_data.get('name')}: {str(e)}")
                    db.rollback()

            if updated_regulators:
                result["updated"]["regulators"] = updated_regulators
            if created_regulators:
                result["created"]["regulators"] = created_regulators

        return result
    except Exception as e:
        logger.error(f"Error in apply_mapping: {e}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {e}")


def run_analysis(
    job_id: str, 
    file_path: str, 
    cleanup: bool = True,
    db: Session = None
) -> None:
    """
    Background task to analyze a document.

    Args:
        job_id: The job ID
        file_path: Path to the document file
        cleanup: Whether to delete the temp file after processing
        db: Database session
    """
    try:
        # Process the document
        results = process_pdf_for_mapping(file_path, db)

        # Store results
        import_jobs[job_id] = {
            "status": "completed",
            "result": results
        }
    except Exception as e:
        logger.error(f"Error during document analysis: {str(e)}")
        import_jobs[job_id] = {
            "status": "failed",
            "error": str(e)
        }
    finally:
        # Clean up temp file
        if cleanup and os.path.exists(file_path):
            os.remove(file_path)