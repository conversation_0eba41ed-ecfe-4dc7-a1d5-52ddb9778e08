
"""
API for scheduled regulatory digests (weekly, monthly, quarterly).
"""
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Request
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field
from datetime import datetime, timedelta
import logging
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse

from app.db import get_db
from app.db.models import RegulationURL, Regulator, RegulatoryChange, Country
from app.i18n import get_translator

router = APIRouter()
templates = Jinja2Templates(directory="templates")

class DigestPeriod(BaseModel):
    """Model for digest period."""
    start_date: datetime
    end_date: datetime
    label: str
    previous_period: Optional[Dict[str, Any]] = None

class DigestItem(BaseModel):
    """Model for a digest item."""
    id: int
    title: str
    description: str
    url: Optional[str] = None
    country_code: str
    importance: int
    change_date: datetime
    change_type: str

class DigestSummary(BaseModel):
    """Model for digest summary statistics."""
    total_changes: int
    by_importance: Dict[str, int]
    by_category: Dict[str, int]
    by_country: Dict[str, int]
    top_regulators: List[Dict[str, Any]]

class Digest(BaseModel):
    """Model for a complete digest."""
    period: DigestPeriod
    summary: DigestSummary
    changes: List[DigestItem]
    highlights: List[DigestItem]

@router.get("/quarterly", response_model=Digest)
def get_quarterly_digest(
    year: Optional[int] = Query(None, description="Year for the digest (defaults to current)"),
    quarter: Optional[int] = Query(None, ge=1, le=4, description="Quarter (1-4, defaults to last complete)"),
    db: Session = Depends(get_db)
):
    """
    Get a quarterly digest of regulatory changes.
    
    Args:
        year: Year for the digest
        quarter: Quarter number (1-4)
        db: Database session
        
    Returns:
        Quarterly digest of regulatory changes
    """
    try:
        # Calculate quarters
        period = _calculate_quarter_period(year, quarter)
        
        # Get changes for this period
        changes_query = db.query(RegulatoryChange).join(
            RegulationURL, RegulatoryChange.regulation_url_id == RegulationURL.id
        ).join(
            Regulator, RegulationURL.regulator_id == Regulator.id
        ).join(
            Country, Regulator.country_id == Country.id
        ).filter(
            RegulatoryChange.change_date >= period["start_date"],
            RegulatoryChange.change_date <= period["end_date"]
        ).order_by(RegulatoryChange.importance.desc(), RegulatoryChange.change_date.desc())
        
        changes = changes_query.all()
        
        # Calculate summary statistics
        summary = _calculate_digest_summary(changes, db)
        
        # Convert changes to DigestItems
        digest_items = []
        highlights = []
        
        for change in changes:
            item = DigestItem(
                id=change.id,
                title=change.title,
                description=change.description,
                url=change.regulation_url.url,
                country_code=change.regulation_url.regulator.country.code,
                importance=change.importance,
                change_date=change.change_date,
                change_type=change.change_type.value if hasattr(change, "change_type") else "update"
            )
            
            digest_items.append(item)
            
            # Add to highlights if importance >= 7
            if change.importance >= 7:
                highlights.append(item)
                
        return Digest(
            period=DigestPeriod(
                start_date=period["start_date"],
                end_date=period["end_date"],
                label=period["label"],
                previous_period={
                    "start_date": period["prev_start_date"],
                    "end_date": period["prev_end_date"],
                    "label": period["prev_label"]
                }
            ),
            summary=summary,
            changes=digest_items,
            highlights=highlights[:10]  # Limit to top 10
        )
        
    except Exception as e:
        logging.error(f"Error in get_quarterly_digest: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating quarterly digest: {str(e)}")

@router.get("/quarterly/html", response_class=HTMLResponse)
def get_quarterly_digest_html(
    request: Request,
    year: Optional[int] = Query(None, description="Year for the digest (defaults to current)"),
    quarter: Optional[int] = Query(None, ge=1, le=4, description="Quarter (1-4, defaults to last complete)"),
    db: Session = Depends(get_db),
    translator=Depends(get_translator)
):
    """
    Get a quarterly digest of regulatory changes in HTML format.
    
    Args:
        request: FastAPI request
        year: Year for the digest
        quarter: Quarter number (1-4)
        db: Database session
        translator: Translation function
        
    Returns:
        HTML response with quarterly digest
    """
    try:
        # Get the digest data
        digest = get_quarterly_digest(year, quarter, db)
        
        # Return the rendered template
        return templates.TemplateResponse(
            "quarterly_digest.html", 
            {
                "request": request, 
                "digest": digest, 
                "translator": translator,
                "title": f"Regulatory Digest {digest.period.label}"
            }
        )
        
    except Exception as e:
        logging.error(f"Error in get_quarterly_digest_html: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating quarterly digest HTML: {str(e)}")

@router.get("/monthly", response_model=Digest)
def get_monthly_digest(
    year: Optional[int] = Query(None, description="Year for the digest (defaults to current)"),
    month: Optional[int] = Query(None, ge=1, le=12, description="Month (1-12, defaults to last complete)"),
    db: Session = Depends(get_db)
):
    """
    Get a monthly digest of regulatory changes.
    
    Args:
        year: Year for the digest
        month: Month number (1-12)
        db: Database session
        
    Returns:
        Monthly digest of regulatory changes
    """
    try:
        now = datetime.utcnow()
        
        # Set defaults if not provided
        if not year:
            year = now.year
            
        if not month:
            # Use previous month
            if now.month == 1:
                year -= 1
                month = 12
            else:
                month = now.month - 1
                
        # Calculate start and end dates
        if month == 12:
            start_date = datetime(year, month, 1)
            end_date = datetime(year + 1, 1, 1) - timedelta(seconds=1)
            prev_month = 11
            prev_year = year
        else:
            start_date = datetime(year, month, 1)
            end_date = datetime(year, month + 1, 1) - timedelta(seconds=1)
            prev_month = month - 1
            prev_year = year
            
        if prev_month == 0:
            prev_month = 12
            prev_year -= 1
            
        prev_start_date = datetime(prev_year, prev_month, 1)
        if prev_month == 12:
            prev_end_date = datetime(prev_year + 1, 1, 1) - timedelta(seconds=1)
        else:
            prev_end_date = datetime(prev_year, prev_month + 1, 1) - timedelta(seconds=1)
        
        # Get month name
        month_name = datetime(year, month, 1).strftime("%B")
        prev_month_name = datetime(prev_year, prev_month, 1).strftime("%B")
        
        period = {
            "start_date": start_date,
            "end_date": end_date,
            "label": f"{month_name} {year}",
            "prev_start_date": prev_start_date,
            "prev_end_date": prev_end_date,
            "prev_label": f"{prev_month_name} {prev_year}"
        }
        
        # Implement similar to quarterly but for monthly period
        changes_query = db.query(RegulatoryChange).join(
            RegulationURL, RegulatoryChange.regulation_url_id == RegulationURL.id
        ).join(
            Regulator, RegulationURL.regulator_id == Regulator.id
        ).join(
            Country, Regulator.country_id == Country.id
        ).filter(
            RegulatoryChange.change_date >= period["start_date"],
            RegulatoryChange.change_date <= period["end_date"]
        ).order_by(RegulatoryChange.importance.desc(), RegulatoryChange.change_date.desc())
        
        changes = changes_query.all()
        
        # Calculate summary statistics
        summary = _calculate_digest_summary(changes, db)
        
        # Convert changes to DigestItems
        digest_items = []
        highlights = []
        
        for change in changes:
            item = DigestItem(
                id=change.id,
                title=change.title,
                description=change.description,
                url=change.regulation_url.url,
                country_code=change.regulation_url.regulator.country.code,
                importance=change.importance,
                change_date=change.change_date,
                change_type=change.change_type.value if hasattr(change, "change_type") else "update"
            )
            
            digest_items.append(item)
            
            # Add to highlights if importance >= 7
            if change.importance >= 7:
                highlights.append(item)
                
        return Digest(
            period=DigestPeriod(
                start_date=period["start_date"],
                end_date=period["end_date"],
                label=period["label"],
                previous_period={
                    "start_date": period["prev_start_date"],
                    "end_date": period["prev_end_date"],
                    "label": period["prev_label"]
                }
            ),
            summary=summary,
            changes=digest_items,
            highlights=highlights[:10]  # Limit to top 10
        )
        
    except Exception as e:
        logging.error(f"Error in get_monthly_digest: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating monthly digest: {str(e)}")

def _calculate_quarter_period(year=None, quarter=None):
    """
    Calculate quarter period dates.
    
    Args:
        year: Year for the digest
        quarter: Quarter number (1-4)
        
    Returns:
        Dict with period dates and labels
    """
    now = datetime.utcnow()
    
    # Set defaults if not provided
    if not year:
        year = now.year
        # If we're in Q1 and quarter not specified, default to Q4 of previous year
        if now.month <= 3 and not quarter:
            year -= 1
            quarter = 4
            
    if not quarter:
        # Calculate the last complete quarter
        current_quarter = (now.month - 1) // 3 + 1
        quarter = current_quarter - 1 if current_quarter > 1 else 4
        if current_quarter == 1:
            year -= 1
            
    # Calculate start and end dates for the quarter
    start_month = (quarter - 1) * 3 + 1
    end_month = quarter * 3
    
    start_date = datetime(year, start_month, 1)
    if end_month == 12:
        end_date = datetime(year + 1, 1, 1) - timedelta(seconds=1)
    else:
        end_date = datetime(year, end_month + 1, 1) - timedelta(seconds=1)
    
    # Calculate previous quarter
    prev_quarter = quarter - 1 if quarter > 1 else 4
    prev_year = year if quarter > 1 else year - 1
    
    prev_start_month = (prev_quarter - 1) * 3 + 1
    prev_end_month = prev_quarter * 3
    
    prev_start_date = datetime(prev_year, prev_start_month, 1)
    if prev_end_month == 12:
        prev_end_date = datetime(prev_year + 1, 1, 1) - timedelta(seconds=1)
    else:
        prev_end_date = datetime(prev_year, prev_end_month + 1, 1) - timedelta(seconds=1)
    
    return {
        "start_date": start_date,
        "end_date": end_date,
        "label": f"Q{quarter} {year}",
        "prev_start_date": prev_start_date,
        "prev_end_date": prev_end_date,
        "prev_label": f"Q{prev_quarter} {prev_year}"
    }

def _calculate_digest_summary(changes, db):
    """
    Calculate summary statistics for a digest.
    
    Args:
        changes: List of RegulatoryChange objects
        db: Database session
        
    Returns:
        DigestSummary object
    """
    importance_counts = {
        "Critical (9-10)": 0,
        "High (7-8)": 0,
        "Medium (4-6)": 0,
        "Low (1-3)": 0
    }
    
    categories = {}
    countries = {}
    regulators = {}
    
    for change in changes:
        # Count by importance
        importance = change.importance
        if importance >= 9:
            importance_counts["Critical (9-10)"] += 1
        elif importance >= 7:
            importance_counts["High (7-8)"] += 1
        elif importance >= 4:
            importance_counts["Medium (4-6)"] += 1
        else:
            importance_counts["Low (1-3)"] += 1
            
        # Count by category
        category = change.regulation_url.category
        if category:
            categories[category] = categories.get(category, 0) + 1
            
        # Count by country
        country_code = change.regulation_url.regulator.country.code
        countries[country_code] = countries.get(country_code, 0) + 1
        
        # Count by regulator
        regulator_id = change.regulation_url.regulator_id
        regulator_name = change.regulation_url.regulator.name
        if regulator_id not in regulators:
            regulators[regulator_id] = {
                "id": regulator_id,
                "name": regulator_name,
                "count": 0
            }
        regulators[regulator_id]["count"] += 1
    
    # Get top regulators
    top_regulators = sorted(regulators.values(), key=lambda x: x["count"], reverse=True)[:5]
    
    return DigestSummary(
        total_changes=len(changes),
        by_importance=importance_counts,
        by_category=categories,
        by_country=countries,
        top_regulators=top_regulators
    )
