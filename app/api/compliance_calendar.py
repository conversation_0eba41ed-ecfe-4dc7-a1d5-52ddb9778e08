
"""
Compliance Calendar API endpoints to manage regulatory deadlines and events.
"""
from datetime import datetime, timedelta
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app.db import get_db
from app.db import models
from app.schemas import schemas

router = APIRouter()

@router.get("/events", response_model=List[schemas.CalendarEvent])
def get_calendar_events(
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    categories: List[str] = Query(None),
    jurisdictions: List[str] = Query(None),
    db: Session = Depends(get_db)
):
    """
    Get regulatory calendar events with optional filtering.
    
    Args:
        start_date: Optional start date for filtering events
        end_date: Optional end date for filtering events
        categories: Optional list of categories to filter by
        jurisdictions: Optional list of jurisdictions to filter by
        db: Database session
        
    Returns:
        List of calendar events matching the criteria
    """
    # Demo implementation - in a real app, this would query the database
    events = generate_demo_events()
    
    # Apply filters
    filtered_events = events
    
    if start_date:
        filtered_events = [e for e in filtered_events if datetime.fromisoformat(e["start"]) >= start_date]
    
    if end_date:
        filtered_events = [e for e in filtered_events if datetime.fromisoformat(e["start"]) <= end_date]
    
    if categories:
        filtered_events = [e for e in filtered_events if e["extendedProps"]["category"] in categories]
        
    if jurisdictions:
        filtered_events = [e for e in filtered_events if e["extendedProps"]["jurisdiction"] in jurisdictions]
    
    return filtered_events

def generate_demo_events():
    """Generate demo calendar events for testing."""
    today = datetime.now()
    
    return [
        {
            "title": "GDPR Annual Assessment",
            "start": (today + timedelta(days=15)).strftime('%Y-%m-%d'),
            "end": (today + timedelta(days=15)).strftime('%Y-%m-%d'),
            "backgroundColor": "#34A853",
            "borderColor": "#34A853",
            "extendedProps": {
                "category": "data",
                "jurisdiction": "European Union",
                "description": "Annual data protection impact assessment required under GDPR Article 35",
                "deadlineType": "Compliance Assessment",
                "action": "Complete DPIA for all high-risk processing activities",
                "regulationId": 101
            }
        },
        {
            "title": "SEC Filing Deadline",
            "start": (today + timedelta(days=30)).strftime('%Y-%m-%d'),
            "end": (today + timedelta(days=30)).strftime('%Y-%m-%d'),
            "backgroundColor": "#4285F4",
            "borderColor": "#4285F4",
            "extendedProps": {
                "category": "finance",
                "jurisdiction": "United States",
                "description": "Quarterly report filing deadline for Q1 2024",
                "deadlineType": "Regulatory Filing",
                "action": "Submit Form 10-Q to SEC",
                "regulationId": 202
            }
        },
        {
            "title": "HIPAA Security Review",
            "start": (today + timedelta(days=10)).strftime('%Y-%m-%d'),
            "end": (today + timedelta(days=12)).strftime('%Y-%m-%d'),
            "backgroundColor": "#EA4335",
            "borderColor": "#EA4335",
            "extendedProps": {
                "category": "healthcare",
                "jurisdiction": "United States",
                "description": "Annual security risk assessment for HIPAA compliance",
                "deadlineType": "Security Audit",
                "action": "Complete security risk assessment and documentation",
                "regulationId": 303
            }
        },
        {
            "title": "Brazil LGPD Implementation",
            "start": (today + timedelta(days=20)).strftime('%Y-%m-%d'),
            "end": (today + timedelta(days=20)).strftime('%Y-%m-%d'),
            "backgroundColor": "#34A853",
            "borderColor": "#34A853",
            "extendedProps": {
                "category": "data",
                "jurisdiction": "Brazil",
                "description": "Implementation deadline for LGPD data subject rights procedures",
                "deadlineType": "Implementation Deadline",
                "action": "Finalize data subject rights management processes",
                "regulationId": 404
            }
        },
        {
            "title": "AML Report Filing",
            "start": (today + timedelta(days=45)).strftime('%Y-%m-%d'),
            "end": (today + timedelta(days=45)).strftime('%Y-%m-%d'),
            "backgroundColor": "#4285F4",
            "borderColor": "#4285F4",
            "extendedProps": {
                "category": "finance",
                "jurisdiction": "United Kingdom",
                "description": "Anti-Money Laundering compliance report due",
                "deadlineType": "Regulatory Filing",
                "action": "Submit AML compliance report to FCA",
                "regulationId": 505
            }
        }
    ]
"""
Compliance calendar API endpoints to manage and retrieve regulatory deadlines and events.
"""
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel

from app.db import get_db
from app.db.models import Country, Regulator, RegulationURL

router = APIRouter()

class CalendarEvent(BaseModel):
    """Model for calendar events."""
    title: str
    start: str
    end: str
    backgroundColor: str
    borderColor: str
    extendedProps: Dict[str, Any]

@router.get("/events", response_model=List[CalendarEvent])
async def get_calendar_events(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    category: Optional[str] = None,
    jurisdiction: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Get regulatory compliance events for the calendar.
    
    Args:
        start_date: Optional filter for events starting after this date
        end_date: Optional filter for events ending before this date
        category: Optional filter by regulation category
        jurisdiction: Optional filter by jurisdiction/country
        
    Returns:
        List of calendar events
    """
    # This would normally query from a database table of regulatory deadlines
    # For now, we'll generate demonstration data
    
    # Define category colors
    category_colors = {
        "finance": "#4285F4",
        "data": "#34A853",
        "healthcare": "#EA4335",
        "general": "#FBBC05"
    }
    
    # Generate demo events
    events = [
        {
            "title": "GDPR Annual Assessment",
            "start": "2024-03-15",
            "end": "2024-03-15",
            "backgroundColor": category_colors["data"],
            "borderColor": category_colors["data"],
            "extendedProps": {
                "category": "data",
                "jurisdiction": "European Union",
                "description": "Annual data protection impact assessment required under GDPR Article 35",
                "deadlineType": "Compliance Assessment",
                "action": "Complete DPIA for all high-risk processing activities",
                "regulationId": 101
            }
        },
        {
            "title": "SEC Filing Deadline",
            "start": "2024-04-01",
            "end": "2024-04-01",
            "backgroundColor": category_colors["finance"],
            "borderColor": category_colors["finance"],
            "extendedProps": {
                "category": "finance",
                "jurisdiction": "United States",
                "description": "Quarterly report filing deadline for Q1 2024",
                "deadlineType": "Regulatory Filing",
                "action": "Submit Form 10-Q to SEC",
                "regulationId": 202
            }
        },
        {
            "title": "HIPAA Security Review",
            "start": "2024-03-10",
            "end": "2024-03-12",
            "backgroundColor": category_colors["healthcare"],
            "borderColor": category_colors["healthcare"],
            "extendedProps": {
                "category": "healthcare",
                "jurisdiction": "United States",
                "description": "Annual security risk assessment for HIPAA compliance",
                "deadlineType": "Security Audit",
                "action": "Complete security risk assessment and documentation",
                "regulationId": 303
            }
        },
        {
            "title": "Brazil LGPD Implementation",
            "start": "2024-04-20",
            "end": "2024-04-20",
            "backgroundColor": category_colors["data"],
            "borderColor": category_colors["data"],
            "extendedProps": {
                "category": "data",
                "jurisdiction": "Brazil",
                "description": "Implementation deadline for LGPD data subject rights procedures",
                "deadlineType": "Implementation Deadline",
                "action": "Finalize data subject rights management processes",
                "regulationId": 404
            }
        },
        {
            "title": "AML Report Filing",
            "start": "2024-05-15",
            "end": "2024-05-15",
            "backgroundColor": category_colors["finance"],
            "borderColor": category_colors["finance"],
            "extendedProps": {
                "category": "finance",
                "jurisdiction": "United Kingdom",
                "description": "Anti-Money Laundering compliance report due",
                "deadlineType": "Regulatory Filing",
                "action": "Submit AML compliance report to FCA",
                "regulationId": 505
            }
        },
        {
            "title": "ESG Disclosure Deadline",
            "start": "2024-03-31",
            "end": "2024-03-31",
            "backgroundColor": category_colors["general"],
            "borderColor": category_colors["general"],
            "extendedProps": {
                "category": "general",
                "jurisdiction": "EU Directive",
                "description": "Environmental, Social, and Governance disclosure requirements",
                "deadlineType": "Disclosure",
                "action": "Publish ESG report with required metrics",
                "regulationId": 606
            }
        },
        {
            "title": "Data Breach Notification Test",
            "start": "2024-04-05",
            "end": "2024-04-05",
            "backgroundColor": category_colors["data"],
            "borderColor": category_colors["data"],
            "extendedProps": {
                "category": "data",
                "jurisdiction": "Global",
                "description": "Quarterly test of data breach notification procedures",
                "deadlineType": "Process Test",
                "action": "Conduct simulation test of breach notification process",
                "regulationId": 707
            }
        },
        {
            "title": "PCI DSS Compliance Audit",
            "start": "2024-05-20",
            "end": "2024-05-22",
            "backgroundColor": category_colors["finance"],
            "borderColor": category_colors["finance"],
            "extendedProps": {
                "category": "finance",
                "jurisdiction": "Global Standard",
                "description": "Annual Payment Card Industry Data Security Standard audit",
                "deadlineType": "Compliance Audit",
                "action": "Complete PCI DSS compliance assessment with QSA",
                "regulationId": 808
            }
        },
        {
            "title": "CCPA Consumer Request Report",
            "start": "2024-03-25",
            "end": "2024-03-25",
            "backgroundColor": category_colors["data"],
            "borderColor": category_colors["data"],
            "extendedProps": {
                "category": "data",
                "jurisdiction": "California, USA",
                "description": "Quarterly report on CCPA consumer requests",
                "deadlineType": "Internal Reporting",
                "action": "Generate and review report on consumer data requests",
                "regulationId": 909
            }
        },
        {
            "title": "Corporate Governance Review",
            "start": "2024-04-10",
            "end": "2024-04-10",
            "backgroundColor": category_colors["general"],
            "borderColor": category_colors["general"],
            "extendedProps": {
                "category": "general",
                "jurisdiction": "Internal",
                "description": "Quarterly review of corporate governance policies",
                "deadlineType": "Internal Review",
                "action": "Review and update governance documentation",
                "regulationId": 1010
            }
        },
        {
            "title": "SOX Compliance Audit",
            "start": "2024-04-25",
            "end": "2024-04-27",
            "backgroundColor": category_colors["finance"],
            "borderColor": category_colors["finance"],
            "extendedProps": {
                "category": "finance",
                "jurisdiction": "United States",
                "description": "Sarbanes-Oxley Act financial controls audit",
                "deadlineType": "Compliance Audit",
                "action": "Review internal controls and financial reporting",
                "regulationId": 1111
            }
        },
        {
            "title": "PIPEDA Privacy Assessment",
            "start": "2024-05-05",
            "end": "2024-05-05",
            "backgroundColor": category_colors["data"],
            "borderColor": category_colors["data"],
            "extendedProps": {
                "category": "data",
                "jurisdiction": "Canada",
                "description": "Assessment of compliance with Personal Information Protection and Electronic Documents Act",
                "deadlineType": "Compliance Assessment",
                "action": "Review privacy policies and data handling practices",
                "regulationId": 1212
            }
        }
    ]
    
    # Apply filters if provided
    filtered_events = events.copy()
    
    if start_date:
        try:
            start = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            filtered_events = [e for e in filtered_events if datetime.fromisoformat(e["start"].replace('Z', '+00:00')) >= start]
        except ValueError:
            # If date parsing fails, ignore this filter
            pass
    
    if end_date:
        try:
            end = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            filtered_events = [e for e in filtered_events if datetime.fromisoformat(e["end"].replace('Z', '+00:00')) <= end]
        except ValueError:
            # If date parsing fails, ignore this filter
            pass
    
    if category:
        filtered_events = [e for e in filtered_events if e["extendedProps"]["category"] == category]
    
    if jurisdiction:
        filtered_events = [e for e in filtered_events if jurisdiction.lower() in e["extendedProps"]["jurisdiction"].lower()]
    
    return filtered_events
