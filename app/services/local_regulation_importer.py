"""
Local Storage Regulation Importer Service.

This service handles importing regulations from local storage with comprehensive
file management, validation, and soft-delete support.
"""

import os
import shutil
import json
import hashlib
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from uuid import uuid4

import pandas as pd
from sqlalchemy.orm import Session

from app.db.models.regulations_csv import RegulationCSVRecord, RegulationCSVImportLog
from app.schemas.regulations_csv import RegulationCSVImportResult
from app.utils.regulations_csv_utils import RegulationCSVProcessor

logger = logging.getLogger(__name__)


class LocalStorageConfig:
    """Configuration for local storage paths."""
    
    def __init__(self, base_path: str = "local_storage"):
        self.base_path = Path(base_path)
        self.imports_path = self.base_path / "imports"
        self.exports_path = self.base_path / "exports"
        self.temp_path = self.base_path / "temp"
        self.processed_path = self.base_path / "processed"
        self.failed_path = self.base_path / "failed"
        self.backups_path = self.base_path / "backups"
        
        # Create directories if they don't exist
        self._ensure_directories()
    
    def _ensure_directories(self):
        """Ensure all required directories exist."""
        for path in [
            self.base_path, self.imports_path, self.exports_path,
            self.temp_path, self.processed_path, self.failed_path, self.backups_path
        ]:
            path.mkdir(parents=True, exist_ok=True)
            logger.debug(f"Ensured directory exists: {path}")


class LocalRegulationImporter:
    """
    Local storage regulation importer with comprehensive file management.
    
    Features:
    - Automatic file discovery and processing
    - File validation and integrity checks
    - Backup and archival of processed files
    - Comprehensive error handling and logging
    - Soft-delete support
    - Batch processing with progress tracking
    """
    
    def __init__(self, db: Session, storage_config: Optional[LocalStorageConfig] = None):
        self.db = db
        self.storage_config = storage_config or LocalStorageConfig()
        self.csv_processor = RegulationCSVProcessor(db)
        
        logger.info(f"LocalRegulationImporter initialized with storage at: {self.storage_config.base_path}")
    
    def discover_import_files(self, file_pattern: str = "*.csv") -> List[Path]:
        """
        Discover CSV files in the imports directory.
        
        Args:
            file_pattern: Glob pattern for file discovery
            
        Returns:
            List of discovered file paths
        """
        import_files = list(self.storage_config.imports_path.glob(file_pattern))
        logger.info(f"Discovered {len(import_files)} files matching pattern '{file_pattern}'")
        
        return sorted(import_files)
    
    def validate_file(self, file_path: Path) -> Tuple[bool, List[str]]:
        """
        Validate a CSV file before processing.
        
        Args:
            file_path: Path to the file to validate
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        
        try:
            # Check file exists and is readable
            if not file_path.exists():
                errors.append(f"File does not exist: {file_path}")
                return False, errors
            
            if not file_path.is_file():
                errors.append(f"Path is not a file: {file_path}")
                return False, errors
            
            # Check file size (not empty, not too large)
            file_size = file_path.stat().st_size
            if file_size == 0:
                errors.append("File is empty")
            elif file_size > 100 * 1024 * 1024:  # 100MB limit
                errors.append(f"File too large: {file_size / (1024*1024):.1f}MB (max 100MB)")
            
            # Try to read as CSV and validate structure
            try:
                df = pd.read_csv(file_path, nrows=1)  # Read just header
                
                # Check for required columns
                required_columns = [
                    'Country_Name', 'Country_Code', 'Document_Title', 
                    'Document_Type', 'Issuing_Authority', 'Legal_Status'
                ]
                
                missing_columns = [col for col in required_columns if col not in df.columns]
                if missing_columns:
                    errors.append(f"Missing required columns: {missing_columns}")
                
                # Check total column count
                if len(df.columns) < 20:
                    errors.append(f"Too few columns: {len(df.columns)} (expected at least 20)")
                
            except pd.errors.EmptyDataError:
                errors.append("CSV file is empty or has no data")
            except pd.errors.ParserError as e:
                errors.append(f"CSV parsing error: {str(e)}")
            except Exception as e:
                errors.append(f"Error reading CSV: {str(e)}")
            
        except Exception as e:
            errors.append(f"File validation error: {str(e)}")
        
        is_valid = len(errors) == 0
        logger.debug(f"File validation for {file_path.name}: {'PASSED' if is_valid else 'FAILED'}")
        
        return is_valid, errors
    
    def calculate_file_hash(self, file_path: Path) -> str:
        """Calculate SHA-256 hash of file for integrity checking."""
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    
    def backup_file(self, file_path: Path, backup_reason: str = "processed") -> Path:
        """
        Create a backup of the file with timestamp and reason.
        
        Args:
            file_path: Path to file to backup
            backup_reason: Reason for backup (processed, failed, etc.)
            
        Returns:
            Path to backup file
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{file_path.stem}_{backup_reason}_{timestamp}{file_path.suffix}"
        backup_path = self.storage_config.backups_path / backup_name
        
        shutil.copy2(file_path, backup_path)
        logger.info(f"Created backup: {backup_path}")
        
        return backup_path
    
    def move_file_to_processed(self, file_path: Path, success: bool = True) -> Path:
        """
        Move file to processed or failed directory based on success.
        
        Args:
            file_path: Path to file to move
            success: Whether processing was successful
            
        Returns:
            New path of moved file
        """
        target_dir = self.storage_config.processed_path if success else self.storage_config.failed_path
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        new_name = f"{file_path.stem}_{timestamp}{file_path.suffix}"
        new_path = target_dir / new_name
        
        shutil.move(str(file_path), str(new_path))
        logger.info(f"Moved file to {'processed' if success else 'failed'}: {new_path}")
        
        return new_path
    
    def import_single_file(
        self, 
        file_path: Path, 
        user_id: Optional[str] = None,
        validate_first: bool = True
    ) -> RegulationCSVImportResult:
        """
        Import a single CSV file with comprehensive processing.
        
        Args:
            file_path: Path to CSV file to import
            user_id: Optional user ID for tracking
            validate_first: Whether to validate file before processing
            
        Returns:
            Import result with statistics and errors
        """
        logger.info(f"Starting import of file: {file_path}")
        
        # Generate batch ID
        batch_id = f"local_import_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid4())[:8]}"
        
        try:
            # Validate file if requested
            if validate_first:
                is_valid, validation_errors = self.validate_file(file_path)
                if not is_valid:
                    logger.error(f"File validation failed: {validation_errors}")
                    
                    # Move to failed directory
                    self.move_file_to_processed(file_path, success=False)
                    
                    return RegulationCSVImportResult(
                        batch_id=batch_id,
                        total_records=0,
                        successful_imports=0,
                        failed_imports=0,
                        updated_records=0,
                        errors=[{"error": "File validation failed", "details": validation_errors}],
                        warnings=[],
                        processing_time=0.0
                    )
            
            # Calculate file hash for integrity
            file_hash = self.calculate_file_hash(file_path)
            
            # Check if this file has been processed before
            existing_import = self.db.query(RegulationCSVImportLog).filter(
                RegulationCSVImportLog.file_hash == file_hash,
                RegulationCSVImportLog.import_status == 'completed'
            ).first()
            
            if existing_import:
                logger.warning(f"File already processed (hash: {file_hash[:8]}...)")
                
                # Move to processed directory
                self.move_file_to_processed(file_path, success=True)
                
                return RegulationCSVImportResult(
                    batch_id=batch_id,
                    total_records=0,
                    successful_imports=0,
                    failed_imports=0,
                    updated_records=0,
                    errors=[],
                    warnings=[{"warning": "File already processed", "previous_batch": existing_import.batch_id}],
                    processing_time=0.0
                )
            
            # Create backup before processing
            backup_path = self.backup_file(file_path, "pre_import")
            
            # Process the file using the CSV processor
            result = self.csv_processor.import_csv_file(
                file_path=str(file_path),
                batch_id=batch_id,
                user_id=user_id
            )
            
            # Determine if import was successful
            import_successful = result.failed_imports == 0
            
            # Move file to appropriate directory
            final_path = self.move_file_to_processed(file_path, success=import_successful)
            
            # Update import log with local storage information
            import_log = self.db.query(RegulationCSVImportLog).filter(
                RegulationCSVImportLog.batch_id == batch_id
            ).first()
            
            if import_log:
                # Add local storage metadata
                if not import_log.error_summary:
                    import_log.error_summary = {}
                
                import_log.error_summary.update({
                    "local_storage": {
                        "original_path": str(file_path),
                        "backup_path": str(backup_path),
                        "final_path": str(final_path),
                        "file_hash": file_hash
                    }
                })
                self.db.commit()
            
            logger.info(f"Import completed for {file_path.name}: {result.successful_imports} successful, {result.failed_imports} failed")
            
            return result
            
        except Exception as e:
            logger.error(f"Import failed for {file_path}: {str(e)}")
            
            # Move to failed directory
            try:
                self.move_file_to_processed(file_path, success=False)
            except Exception as move_error:
                logger.error(f"Failed to move file after error: {move_error}")
            
            # Return error result
            return RegulationCSVImportResult(
                batch_id=batch_id,
                total_records=0,
                successful_imports=0,
                failed_imports=0,
                updated_records=0,
                errors=[{"error": f"Import exception: {str(e)}"}],
                warnings=[],
                processing_time=0.0
            )
    
    def import_all_files(
        self, 
        user_id: Optional[str] = None,
        file_pattern: str = "*.csv"
    ) -> List[RegulationCSVImportResult]:
        """
        Import all CSV files found in the imports directory.
        
        Args:
            user_id: Optional user ID for tracking
            file_pattern: Glob pattern for file discovery
            
        Returns:
            List of import results for each file
        """
        logger.info("Starting batch import of all files")
        
        # Discover files
        files_to_import = self.discover_import_files(file_pattern)
        
        if not files_to_import:
            logger.info("No files found to import")
            return []
        
        results = []
        
        for file_path in files_to_import:
            logger.info(f"Processing file {len(results) + 1}/{len(files_to_import)}: {file_path.name}")
            
            try:
                result = self.import_single_file(file_path, user_id)
                results.append(result)
            except Exception as e:
                logger.error(f"Failed to process {file_path}: {e}")
                
                # Create error result
                error_result = RegulationCSVImportResult(
                    batch_id=f"error_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    total_records=0,
                    successful_imports=0,
                    failed_imports=0,
                    updated_records=0,
                    errors=[{"error": f"Processing exception: {str(e)}", "file": str(file_path)}],
                    warnings=[],
                    processing_time=0.0
                )
                results.append(error_result)
        
        # Log summary
        total_successful = sum(r.successful_imports for r in results)
        total_failed = sum(r.failed_imports for r in results)
        total_updated = sum(r.updated_records for r in results)
        
        logger.info(f"Batch import completed: {len(files_to_import)} files processed")
        logger.info(f"  Total successful: {total_successful}")
        logger.info(f"  Total failed: {total_failed}")
        logger.info(f"  Total updated: {total_updated}")
        
        return results
    
    def cleanup_old_backups(self, days_to_keep: int = 30) -> int:
        """
        Clean up old backup files to save disk space.
        
        Args:
            days_to_keep: Number of days to keep backups
            
        Returns:
            Number of files cleaned up
        """
        cutoff_time = datetime.now().timestamp() - (days_to_keep * 24 * 60 * 60)
        cleaned_count = 0
        
        for backup_file in self.storage_config.backups_path.glob("*"):
            if backup_file.is_file() and backup_file.stat().st_mtime < cutoff_time:
                try:
                    backup_file.unlink()
                    cleaned_count += 1
                    logger.debug(f"Cleaned up old backup: {backup_file}")
                except Exception as e:
                    logger.error(f"Failed to clean up {backup_file}: {e}")
        
        logger.info(f"Cleaned up {cleaned_count} old backup files")
        return cleaned_count
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """Get statistics about local storage usage."""
        stats = {}
        
        for name, path in [
            ("imports", self.storage_config.imports_path),
            ("exports", self.storage_config.exports_path),
            ("processed", self.storage_config.processed_path),
            ("failed", self.storage_config.failed_path),
            ("backups", self.storage_config.backups_path),
            ("temp", self.storage_config.temp_path)
        ]:
            if path.exists():
                files = list(path.glob("*"))
                file_count = len([f for f in files if f.is_file()])
                total_size = sum(f.stat().st_size for f in files if f.is_file())
                
                stats[name] = {
                    "file_count": file_count,
                    "total_size_mb": round(total_size / (1024 * 1024), 2),
                    "path": str(path)
                }
            else:
                stats[name] = {
                    "file_count": 0,
                    "total_size_mb": 0.0,
                    "path": str(path)
                }
        
        return stats
