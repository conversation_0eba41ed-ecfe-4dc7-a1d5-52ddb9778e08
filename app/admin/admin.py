"""
Admin interface configuration using starlette-admin.
With Material Design styling and dark mode support.
"""
from starlette.applications import Starlette
from starlette_admin.contrib.sqla import Admin, ModelView
from starlette.middleware import Middleware
from starlette.middleware.sessions import SessionMiddleware
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.db.database import SQL<PERSON>CHEMY_DATABASE_URL
from app.db.models import Country, Regulator, RegulationURL, Item

# Create engine and session for admin
engine = create_engine(SQLALCHEMY_DATABASE_URL)
session_factory = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create admin application
admin = Admin(
    engine,
    title="RegulationGuru Admin",
    base_url="/ui/manage",
    route_name="admin",
    logo_url="/static/logo.png",  # If you have a logo file
    templates_dir="templates/admin",  # Custom admin templates directory
)

# Define ModelViews with Material Design icons
class CountryView(ModelView):
    def __init__(self, model=Country):
        super().__init__(model)
        self.icon = "public"  # Material icon name
        self.name = "Countries"
        self.label = "Countries"

class RegulatorView(ModelView):
    def __init__(self, model=Regulator):
        super().__init__(model)
        self.icon = "account_balance"  # Material icon name
        self.name = "Regulators"
        self.label = "Regulators"

class RegulationURLView(ModelView):
    def __init__(self, model=RegulationURL):
        super().__init__(model)
        self.icon = "gavel"  # Material icon name
        self.name = "Regulations"
        self.label = "Regulations"

class ItemView(ModelView):
    def __init__(self, model=Item):
        super().__init__(model)
        self.icon = "assignment"  # Material icon name
        self.name = "Items" 
        self.label = "Items"

# Register ModelViews
admin.add_view(CountryView())
admin.add_view(RegulatorView())
admin.add_view(RegulationURLView())
admin.add_view(ItemView())

# Create the WSGI application
middleware = [
    Middleware(SessionMiddleware, secret_key="your-secret-key-here")
]

admin_app = Starlette(
    debug=True,
    middleware=middleware
)

# Mount admin to the application
admin.mount_to(admin_app)

def get_admin_app():
    """
    Get the admin application.

    Returns:
        Starlette: The Starlette admin application
    """
    return admin_app