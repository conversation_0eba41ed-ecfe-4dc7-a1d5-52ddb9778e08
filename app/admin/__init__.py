
"""Admin interface package."""
import logging
import time
logger = logging.getLogger(__name__)

logger.info("Loading admin module...")
start_time = time.time()
try:
    # Import get_admin_app function
    from app.admin.admin import get_admin_app
    logger.info(f"Admin module loaded successfully in {time.time() - start_time:.2f}s")
except Exception as e:
    logger.error(f"Error loading admin module: {str(e)}")
    # Log the full traceback for better debugging
    import traceback
    logger.error(f"Traceback: {traceback.format_exc()}")
    raise

__all__ = ["get_admin_app"]
