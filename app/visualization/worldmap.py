
"""Module for world map functionality using Folium."""
import folium
import io
from typing import Optional, List, Tuple, Dict, Any


def create_world_map(
    center: Tuple[float, float] = (0, 0),
    zoom_start: int = 2,
    markers: Optional[List[Dict[str, Any]]] = None
) -> folium.Map:
    """
    Create a world map with optional markers.
    
    Args:
        center (Tuple[float, float]): The center coordinates (lat, lon)
        zoom_start (int): Initial zoom level
        markers (List[Dict]): List of marker data with format:
                              [{"location": (lat, lon), "popup": "text", "tooltip": "text"}]
    
    Returns:
        folium.Map: A Folium map object
    """
    # Create a map
    m = folium.Map(location=center, zoom_start=zoom_start)
    
    # Add markers if provided
    if markers:
        for marker in markers:
            folium.Marker(
                location=marker["location"],
                popup=marker.get("popup", ""),
                tooltip=marker.get("tooltip", "")
            ).add_to(m)
    
    return m


def get_map_html(map_obj: folium.Map) -> str:
    """
    Convert a Folium map to HTML string.
    
    Args:
        map_obj (folium.Map): The Folium map object
        
    Returns:
        str: HTML string of the map
    """
    # Get the HTML representation
    html_data = map_obj._repr_html_()
    return html_data


def save_map_to_file(map_obj: folium.Map, file_path: str) -> None:
    """
    Save a Folium map to an HTML file.
    
    Args:
        map_obj (folium.Map): The Folium map object
        file_path (str): Path to save the HTML file
    """
    map_obj.save(file_path)
