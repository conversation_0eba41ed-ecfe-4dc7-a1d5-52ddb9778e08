
"""
Module for mapping relationships between regulatory documents and requirements.
"""
from typing import Dict, List, Any, Optional, Set, Tuple
import re
import networkx as nx
import logging
from collections import defaultdict

class RelationshipMapper:
    """
    Maps relationships between regulatory documents and requirements,
    including parent/child relationships and cross-references.
    """
    
    def __init__(self):
        self.graph = nx.DiGraph()
        self.frameworks = {
            "NIST": self._load_nist_controls(),
            "ISO": self._load_iso_controls(),
            "ISF": self._load_isf_controls()
        }
    
    def map_document_relationships(self, documents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Map relationships between a collection of regulatory documents.
        
        Args:
            documents: List of document analysis results
            
        Returns:
            Dict containing relationship mapping data
        """
        # Clear existing graph
        self.graph.clear()
        
        # Add nodes for each document
        for doc in documents:
            doc_id = doc.get("id", str(hash(doc.get("title", "") or doc.get("file_path", ""))))
            self.graph.add_node(doc_id, 
                               type="document", 
                               title=doc.get("title", ""),
                               confidence=doc.get("confidence_score", 0))
            
            # Add nodes for sections and requirements
            self._add_section_nodes(doc_id, doc)
            self._add_requirement_nodes(doc_id, doc)
        
        # Build relationships
        self._build_hierarchical_relationships()
        self._find_cross_references()
        self._map_to_control_frameworks()
        
        # Return the relationship data
        return self._extract_relationship_data()
    
    def _add_section_nodes(self, doc_id: str, doc: Dict[str, Any]) -> None:
        """Add nodes for document sections."""
        sections = doc.get("sections", {})
        
        # Add article nodes
        for i, article in enumerate(sections.get("articles", [])):
            article_id = f"{doc_id}_article_{i}"
            # Extract article number using regex
            article_num_match = re.search(r'Article\s+(\d+[A-Za-z]?)', article)
            article_num = article_num_match.group(1) if article_num_match else str(i+1)
            
            self.graph.add_node(article_id,
                               type="article",
                               document_id=doc_id,
                               number=article_num,
                               text=article)
            # Add edge from document to article
            self.graph.add_edge(doc_id, article_id, type="contains")
        
        # Add section nodes
        for i, section in enumerate(sections.get("sections", [])):
            section_id = f"{doc_id}_section_{i}"
            # Extract section number using regex
            section_num_match = re.search(r'Section\s+(\d+[A-Za-z]?)', section)
            section_num = section_num_match.group(1) if section_num_match else str(i+1)
            
            self.graph.add_node(section_id,
                               type="section",
                               document_id=doc_id,
                               number=section_num,
                               text=section)
            # Add edge from document to section
            self.graph.add_edge(doc_id, section_id, type="contains")
    
    def _add_requirement_nodes(self, doc_id: str, doc: Dict[str, Any]) -> None:
        """Add nodes for document requirements."""
        requirements = doc.get("requirements", [])
        
        for i, req in enumerate(requirements):
            req_id = f"{doc_id}_requirement_{i}"
            self.graph.add_node(req_id,
                              type="requirement",
                              document_id=doc_id,
                              text=req.get("text", ""),
                              priority=req.get("priority", "medium"),
                              subject=req.get("subject", ""),
                              action=req.get("action", ""))
            # Add edge from document to requirement
            self.graph.add_edge(doc_id, req_id, type="requires")
            
            # Try to link requirement to a specific article or section
            self._link_requirement_to_section(doc_id, req_id, req.get("text", ""))
    
    def _link_requirement_to_section(self, doc_id: str, req_id: str, req_text: str) -> None:
        """Link a requirement to its containing section or article."""
        # Get all articles and sections for this document
        articles = [n for n in self.graph.nodes if self.graph.nodes[n].get("type") == "article" 
                  and self.graph.nodes[n].get("document_id") == doc_id]
        sections = [n for n in self.graph.nodes if self.graph.nodes[n].get("type") == "section" 
                  and self.graph.nodes[n].get("document_id") == doc_id]
        
        # Check if requirement text appears in any article or section
        for article_id in articles:
            article_text = self.graph.nodes[article_id].get("text", "")
            if req_text in article_text:
                self.graph.add_edge(article_id, req_id, type="contains")
                return
                
        for section_id in sections:
            section_text = self.graph.nodes[section_id].get("text", "")
            if req_text in section_text:
                self.graph.add_edge(section_id, req_id, type="contains")
                return
    
    def _build_hierarchical_relationships(self) -> None:
        """Build hierarchical (parent/child) relationships between documents."""
        documents = [n for n in self.graph.nodes if self.graph.nodes[n].get("type") == "document"]
        
        for doc_id in documents:
            for other_doc_id in documents:
                if doc_id == other_doc_id:
                    continue
                
                # Check for references in title
                doc_title = self.graph.nodes[doc_id].get("title", "").lower()
                other_title = self.graph.nodes[other_doc_id].get("title", "").lower()
                
                # Check if one document amends or updates another
                amendment_patterns = [
                    r'amend(?:ing|ment|s)?',
                    r'updat(?:ing|e|es)',
                    r'supplement(?:ing|s)?',
                    r'implement(?:ing|s)?'
                ]
                
                for pattern in amendment_patterns:
                    if re.search(pattern, doc_title) and other_title in doc_title:
                        # This document amends/updates the other
                        self.graph.add_edge(other_doc_id, doc_id, type="amended_by")
                    elif re.search(pattern, other_title) and doc_title in other_title:
                        # Other document amends/updates this one
                        self.graph.add_edge(doc_id, other_doc_id, type="amended_by")
                
                # Check for version numbers
                version_pattern = re.compile(r'v(\d+(?:\.\d+)*)')
                doc_version = version_pattern.search(doc_title)
                other_version = version_pattern.search(other_title)
                
                if doc_version and other_version:
                    # Remove version info to compare base titles
                    base_doc_title = version_pattern.sub('', doc_title).strip()
                    base_other_title = version_pattern.sub('', other_title).strip()
                    
                    if base_doc_title == base_other_title:
                        # Same base title, different versions
                        doc_ver = float(doc_version.group(1).replace('.', '0', 1))
                        other_ver = float(other_version.group(1).replace('.', '0', 1))
                        
                        if doc_ver > other_ver:
                            self.graph.add_edge(other_doc_id, doc_id, type="newer_version")
                        else:
                            self.graph.add_edge(doc_id, other_doc_id, type="newer_version")
    
    def _find_cross_references(self) -> None:
        """Find cross-references between requirements."""
        requirements = [n for n in self.graph.nodes if self.graph.nodes[n].get("type") == "requirement"]
        
        # Dictionary of regex patterns to search for references
        reference_patterns = {
            "article": r'(?i)article\s+(\d+[A-Za-z]?)',
            "section": r'(?i)section\s+(\d+[A-Za-z]?)',
            "paragraph": r'(?i)paragraph\s+(\d+[A-Za-z]?)',
            "clause": r'(?i)clause\s+(\d+[A-Za-z]?)',
            "provision": r'(?i)provision\s+(\d+[A-Za-z]?)'
        }
        
        for req_id in requirements:
            req_text = self.graph.nodes[req_id].get("text", "")
            doc_id = self.graph.nodes[req_id].get("document_id", "")
            
            # Look for references to other articles or sections
            for ref_type, pattern in reference_patterns.items():
                for match in re.finditer(pattern, req_text):
                    ref_num = match.group(1)
                    
                    # Find nodes matching the reference
                    if ref_type == "article":
                        ref_nodes = [n for n in self.graph.nodes 
                                   if self.graph.nodes[n].get("type") == "article" 
                                   and self.graph.nodes[n].get("number") == ref_num]
                    elif ref_type == "section":
                        ref_nodes = [n for n in self.graph.nodes 
                                   if self.graph.nodes[n].get("type") == "section" 
                                   and self.graph.nodes[n].get("number") == ref_num]
                    else:
                        # Skip other reference types for now
                        continue
                    
                    # Add cross-reference edges
                    for ref_node in ref_nodes:
                        ref_doc_id = self.graph.nodes[ref_node].get("document_id", "")
                        
                        # Don't add self-references within the same article/section
                        if not self.graph.has_edge(ref_node, req_id, key="contains"):
                            self.graph.add_edge(req_id, ref_node, type="references")
                            
                            # If reference is to a different document, add document cross-reference
                            if ref_doc_id != doc_id:
                                if not self.graph.has_edge(doc_id, ref_doc_id):
                                    self.graph.add_edge(doc_id, ref_doc_id, type="references")
    
    def _map_to_control_frameworks(self) -> None:
        """Map requirements to common control frameworks."""
        requirements = [n for n in self.graph.nodes if self.graph.nodes[n].get("type") == "requirement"]
        
        for req_id in requirements:
            req_text = self.graph.nodes[req_id].get("text", "").lower()
            
            # Map to NIST controls
            for control_id, control_data in self.frameworks["NIST"].items():
                keywords = control_data.get("keywords", [])
                if any(keyword in req_text for keyword in keywords):
                    self.graph.add_edge(req_id, f"NIST_{control_id}", type="maps_to")
            
            # Map to ISO controls
            for control_id, control_data in self.frameworks["ISO"].items():
                keywords = control_data.get("keywords", [])
                if any(keyword in req_text for keyword in keywords):
                    self.graph.add_edge(req_id, f"ISO_{control_id}", type="maps_to")
            
            # Map to ISF controls
            for control_id, control_data in self.frameworks["ISF"].items():
                keywords = control_data.get("keywords", [])
                if any(keyword in req_text for keyword in keywords):
                    self.graph.add_edge(req_id, f"ISF_{control_id}", type="maps_to")
    
    def _extract_relationship_data(self) -> Dict[str, Any]:
        """Extract relationship data from the graph."""
        result = {
            "documents": [],
            "hierarchical_relationships": [],
            "cross_references": [],
            "framework_mappings": defaultdict(list)
        }
        
        # Extract documents
        for node in self.graph.nodes:
            node_data = self.graph.nodes[node]
            if node_data.get("type") == "document":
                result["documents"].append({
                    "id": node,
                    "title": node_data.get("title", ""),
                    "confidence": node_data.get("confidence", 0)
                })
        
        # Extract hierarchical relationships
        for source, target, edge_data in self.graph.edges(data=True):
            if edge_data.get("type") in ["amended_by", "newer_version"]:
                source_type = self.graph.nodes[source].get("type")
                target_type = self.graph.nodes[target].get("type")
                
                if source_type == "document" and target_type == "document":
                    result["hierarchical_relationships"].append({
                        "source_id": source,
                        "source_title": self.graph.nodes[source].get("title", ""),
                        "target_id": target,
                        "target_title": self.graph.nodes[target].get("title", ""),
                        "relationship_type": edge_data.get("type")
                    })
        
        # Extract cross-references
        for source, target, edge_data in self.graph.edges(data=True):
            if edge_data.get("type") == "references":
                source_type = self.graph.nodes[source].get("type")
                target_type = self.graph.nodes[target].get("type")
                
                # Document-to-document cross-references
                if source_type == "document" and target_type == "document":
                    result["cross_references"].append({
                        "source_id": source,
                        "source_title": self.graph.nodes[source].get("title", ""),
                        "target_id": target,
                        "target_title": self.graph.nodes[target].get("title", ""),
                        "reference_type": "document"
                    })
                # Requirement-to-article/section cross-references
                elif source_type == "requirement" and target_type in ["article", "section"]:
                    result["cross_references"].append({
                        "source_id": source,
                        "source_document": self.graph.nodes[source].get("document_id", ""),
                        "source_text": self.graph.nodes[source].get("text", "")[:100] + "...",
                        "target_id": target,
                        "target_document": self.graph.nodes[target].get("document_id", ""),
                        "target_type": target_type,
                        "target_number": self.graph.nodes[target].get("number", ""),
                        "reference_type": "internal"
                    })
        
        # Extract framework mappings
        for source, target, edge_data in self.graph.edges(data=True):
            if edge_data.get("type") == "maps_to" and target.startswith(("NIST_", "ISO_", "ISF_")):
                framework, control_id = target.split("_", 1)
                result["framework_mappings"][framework].append({
                    "requirement_id": source,
                    "requirement_text": self.graph.nodes[source].get("text", "")[:100] + "...",
                    "document_id": self.graph.nodes[source].get("document_id", ""),
                    "control_id": control_id
                })
        
        return result
    
    def _load_nist_controls(self) -> Dict[str, Dict[str, Any]]:
        """Load NIST control mappings."""
        # This would normally load from a file or database
        # For demo purposes, we'll define a few common controls
        return {
            "AC-1": {
                "title": "Access Control Policy and Procedures",
                "keywords": ["access control", "authentication", "authorization", "policy", "procedure"]
            },
            "AC-2": {
                "title": "Account Management",
                "keywords": ["account", "user account", "account management", "privilege"]
            },
            "AU-2": {
                "title": "Audit Events",
                "keywords": ["audit", "logging", "monitoring", "event"]
            },
            "IA-2": {
                "title": "Identification and Authentication",
                "keywords": ["identification", "authentication", "identity", "credential"]
            },
            "SC-8": {
                "title": "Transmission Confidentiality and Integrity",
                "keywords": ["encryption", "confidentiality", "integrity", "transmission", "communication"]
            },
            "SI-4": {
                "title": "Information System Monitoring",
                "keywords": ["monitoring", "detection", "intrusion", "surveillance"]
            }
        }
    
    def _load_iso_controls(self) -> Dict[str, Dict[str, Any]]:
        """Load ISO control mappings."""
        # Basic ISO 27001 controls
        return {
            "A.5.1": {
                "title": "Management direction for information security",
                "keywords": ["policy", "management", "direction", "information security"]
            },
            "A.8.2": {
                "title": "Information classification",
                "keywords": ["classification", "sensitivity", "confidential", "public", "private"]
            },
            "A.9.2": {
                "title": "User access management",
                "keywords": ["access", "user", "privilege", "rights", "authentication"]
            },
            "A.10.1": {
                "title": "Cryptographic controls",
                "keywords": ["cryptographic", "encryption", "key", "certificate", "cryptography"]
            },
            "A.12.4": {
                "title": "Logging and monitoring",
                "keywords": ["log", "audit", "monitor", "event", "activity"]
            },
            "A.18.1": {
                "title": "Compliance with legal and contractual requirements",
                "keywords": ["compliance", "legal", "regulatory", "requirement", "obligation"]
            }
        }
    
    def _load_isf_controls(self) -> Dict[str, Dict[str, Any]]:
        """Load ISF control mappings."""
        # Information Security Forum controls
        return {
            "CF1": {
                "title": "Security Governance",
                "keywords": ["governance", "management", "oversight", "responsibility"]
            },
            "CF2": {
                "title": "Information Risk Assessment",
                "keywords": ["risk", "assessment", "threat", "vulnerability", "impact"]
            },
            "CF3": {
                "title": "Information Security Strategy",
                "keywords": ["strategy", "planning", "objective", "roadmap"]
            },
            "CF4": {
                "title": "Security Architecture",
                "keywords": ["architecture", "design", "pattern", "blueprint"]
            },
            "CF5": {
                "title": "Privacy and Legal",
                "keywords": ["privacy", "legal", "regulation", "compliance", "data protection"]
            }
        }
