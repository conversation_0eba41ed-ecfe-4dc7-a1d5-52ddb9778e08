
"""
URL processor to identify country and regulator information.

This module processes a list of URLs from a text file and attempts to
determine the country and regulatory authority responsible for each URL.
"""
import re
import argparse
import csv
import requests
from typing import Dict, List, Tuple, Optional
from urllib.parse import urlparse
import time
import logging
from app.api import get_all_regulations, REGULATIONS

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# TLD to country mapping (partial list, can be expanded)
TLD_COUNTRY_MAP = {
    'au': 'Australia',
    'ca': 'Canada',
    'ch': 'Switzerland',
    'cn': 'China',
    'de': 'Germany',
    'es': 'Spain',
    'eu': 'European Union',
    'fr': 'France',
    'gov': 'United States',
    'hk': 'Hong Kong',
    'ie': 'Ireland',
    'in': 'India',
    'it': 'Italy',
    'jp': 'Japan',
    'kr': 'South Korea',
    'li': 'Liechtenstein',
    'lu': 'Luxembourg',
    'my': 'Malaysia',
    'nl': 'Netherlands',
    'nz': 'New Zealand',
    'sg': 'Singapore',
    'uk': 'United Kingdom',
    'us': 'United States',
    'za': 'South Africa',
}

# Known regulator domains and their names
REGULATOR_DOMAINS = {
    'mass.gov': 'Massachusetts Office of Consumer Affairs and Business Regulation',
    'ecfr.gov': 'Electronic Code of Federal Regulations (USA)',
    'gov.il': 'Israeli Government',
    'finma.ch': 'Swiss Financial Market Supervisory Authority',
    'cssf.lu': 'Commission de Surveillance du Secteur Financier (Luxembourg)',
    'centralbank.ie': 'Central Bank of Ireland',
    'mas.gov.sg': 'Monetary Authority of Singapore',
    'sec.gov': 'Securities and Exchange Commission (USA)',
    'dfs.ny.gov': 'New York Department of Financial Services',
    'federalregister.gov': 'Federal Register (USA)',
    'ncsc.gov.uk': 'National Cyber Security Centre (UK)',
    'fca.org.uk': 'Financial Conduct Authority (UK)',
    'ecb.europa.eu': 'European Central Bank',
    'gov.uk': 'UK Government',
    'meti.go.jp': 'Ministry of Economy, Trade and Industry (Japan)',
    'fsa.go.jp': 'Financial Services Agency (Japan)',
    'irdai.gov.in': 'Insurance Regulatory and Development Authority of India',
    'luatvietnam.vn': 'Vietnam Legal Database',
    'cyber.gov.au': 'Australian Cyber Security Centre',
    'u.ae': 'United Arab Emirates Government',
    'eba.europa.eu': 'European Banking Authority',
    'bnm.gov.my': 'Bank Negara Malaysia',
    'esma.europa.eu': 'European Securities and Markets Authority',
    'law.go.kr': 'Korea Legislation Research Institute',
    'apra.gov.au': 'Australian Prudential Regulation Authority',
    'legifrance.gouv.fr': 'French Government',
    'legislation.gov.uk': 'UK Legislation',
    'gesetze-im-internet.de': 'German Laws and Regulations',
    'cbirc.gov.cn': 'China Banking and Insurance Regulatory Commission',
    'ivass.it': 'Institute for the Supervision of Insurance (Italy)',
    'eur-lex.europa.eu': 'EUR-Lex (European Union Law)',
    'edpb.europa.eu': 'European Data Protection Board',
    'inicio.inai.org.mx': 'National Institute for Transparency, Access to Information and Personal Data Protection (Mexico)',
    'osfi-bsif.gc.ca': 'Office of the Superintendent of Financial Institutions (Canada)',
    'pcpd.org.hk': 'Privacy Commissioner for Personal Data (Hong Kong)',
    'pdpc.gov.sg': 'Personal Data Protection Commission (Singapore)',
    'priv.gc.ca': 'Office of the Privacy Commissioner of Canada',
    'labuanfsa.gov.my': 'Labuan Financial Services Authority (Malaysia)',
    'eiopa.europa.eu': 'European Insurance and Occupational Pensions Authority',
    'planalto.gov.br': 'Presidency of the Republic of Brazil',
    'gld.gov.hk': 'Government Logistics Department (Hong Kong)',
    'riigiteataja.ee': 'State Gazette of Estonia',
    'kkmm.gov.my': 'Ministry of Communications and Multimedia (Malaysia)',
    'fma-li.li': 'Financial Market Authority Liechtenstein',
    'cnil.fr': 'National Commission on Informatics and Liberty (France)',
    'bma.bm': 'Bermuda Monetary Authority',
    'caa.lu': 'Commissariat aux Assurances (Luxembourg)',
    'nbb.be': 'National Bank of Belgium',
    'bde.es': 'Bank of Spain',
    'bcb.gov.br': 'Central Bank of Brazil',
    'acma.gov.au': 'Australian Communications and Media Authority',
    'sfc.hk': 'Securities and Futures Commission (Hong Kong)',
    'cima.ky': 'Cayman Islands Monetary Authority',
    'bankofengland.co.uk': 'Bank of England',
    'legis.iowa.gov': 'Iowa Legislature',
    'dof.gob.mx': 'Secretariat of Finance and Public Credit (Mexico)',
    'nysenate.gov': 'New York State Senate',
    'naic.org': 'National Association of Insurance Commissioners (USA)',
    'sc.com.my': 'Securities Commission Malaysia',
    'data.oireachtas.ie': 'Houses of the Oireachtas (Ireland)',
    'legislation.govt.nz': 'New Zealand Legislation',
}

# Category to regulator type mapping
CATEGORY_REGULATOR_MAP = {
    'privacy_data_protection': 'Data Protection Authority',
    'healthcare_hipaa': 'Healthcare Regulator',
    'cybersecurity': 'Cybersecurity Authority',
    'financial_regulations': 'Financial Regulator',
    'tax_laws': 'Tax Authority',
    'outsourcing_regulations': 'Financial Services Regulator',
    'cloud_computing': 'Technology Regulator',
    'eu_regulations': 'European Union Regulatory Body'
}


def load_urls_from_file(file_path: str) -> List[str]:
    """
    Load URLs from a text file.
    
    Args:
        file_path (str): Path to the text file containing URLs
        
    Returns:
        List[str]: List of URLs extracted from the file
    """
    try:
        all_urls = []
        with open(file_path, 'r') as file:
            for line in file:
                if line.strip():
                    # Split each line by spaces to get individual URLs
                    line_urls = line.strip().split()
                    all_urls.extend(line_urls)
        
        logging.info(f"Loaded {len(all_urls)} URLs from {file_path}")
        return all_urls
    except Exception as e:
        logging.error(f"Error loading URLs from file: {e}")
        return []


def extract_domain_and_tld(url: str) -> Tuple[str, Optional[str]]:
    """
    Extract domain and TLD from a URL.
    
    Args:
        url (str): URL to analyze
        
    Returns:
        Tuple[str, Optional[str]]: Domain and TLD of the URL
    """
    try:
        # Ensure URL has a protocol
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
            
        parsed_url = urlparse(url)
        domain = parsed_url.netloc or parsed_url.path.split('/')[0]
        
        # Clean the domain by removing www. prefix if present
        if domain.startswith('www.'):
            domain = domain[4:]
            
        # Handle cases where domain might be empty
        if not domain and '/' in parsed_url.path:
            domain = parsed_url.path.split('/')[0]
            
        # Extract TLD (last part of the domain)
        parts = domain.split('.')
        
        # Handle .co.uk type domains
        if len(parts) >= 3 and parts[-2] in ['co', 'gov', 'org', 'ac']:
            tld = '.'.join(parts[-2:])
        else:
            tld = parts[-1] if len(parts) > 1 else None
        
        return domain, tld
    except Exception as e:
        logging.error(f"Error extracting domain from URL {url}: {e}")
        return "", None


def identify_country_from_url(url: str, domain: str, tld: Optional[str]) -> str:
    """
    Identify the country associated with a URL based on domain and TLD.
    
    Args:
        url (str): The URL to analyze
        domain (str): Domain extracted from the URL
        tld (Optional[str]): TLD extracted from the URL
        
    Returns:
        str: Identified country name or "Unknown"
    """
    # Check for country in the domain parts
    domain_parts = domain.split('.')
    
    # First check if the TLD is a known country code
    if tld and tld.lower() in TLD_COUNTRY_MAP:
        return TLD_COUNTRY_MAP[tld.lower()]
    
    # Look for country codes in subdomain parts
    for part in domain_parts:
        if part.lower() in TLD_COUNTRY_MAP:
            return TLD_COUNTRY_MAP[part.lower()]
    
    # Check for country names in the URL path
    country_terms = {
        'australia': 'Australia',
        'canada': 'Canada',
        'china': 'China',
        'germany': 'Germany',
        'deutschland': 'Germany',
        'spain': 'Spain',
        'españa': 'Spain',
        'espana': 'Spain',
        'european': 'European Union',
        'europe': 'European Union',
        'france': 'France',
        'uk': 'United Kingdom',
        'united-kingdom': 'United Kingdom',
        'unitedkingdom': 'United Kingdom',
        'hong-kong': 'Hong Kong',
        'hongkong': 'Hong Kong',
        'ireland': 'Ireland',
        'india': 'India',
        'italy': 'Italy',
        'italia': 'Italy',
        'japan': 'Japan',
        'korea': 'South Korea',
        'singapore': 'Singapore',
        'usa': 'United States',
        'united-states': 'United States',
        'unitedstates': 'United States',
        'america': 'United States',
    }
    
    for term, country in country_terms.items():
        if term in url.lower():
            return country
    
    # If domain ends with gov and no country identified, assume USA
    if domain.endswith('.gov'):
        return 'United States'
    
    return "Unknown"


def identify_regulator_from_url(url: str, domain: str, category: Optional[str] = None) -> str:
    """
    Identify the regulatory authority associated with a URL.
    
    Args:
        url (str): The URL to analyze
        domain (str): Domain extracted from the URL
        category (Optional[str]): Regulation category if known
        
    Returns:
        str: Identified regulator name or "Unknown"
    """
    # First check for exact domain matches in our known regulators
    for regulator_domain, regulator_name in REGULATOR_DOMAINS.items():
        if regulator_domain in domain:
            return regulator_name
    
    # If we know the category, we can provide a generic regulator type
    if category and category in CATEGORY_REGULATOR_MAP:
        return CATEGORY_REGULATOR_MAP[category]
    
    # Check for common regulator terms in the domain
    regulator_terms = {
        'bank': 'Central Bank',
        'centralbank': 'Central Bank',
        'financial': 'Financial Regulatory Authority',
        'finance': 'Financial Regulatory Authority',
        'treasury': 'Treasury Department',
        'regulator': 'Regulatory Authority',
        'commission': 'Regulatory Commission',
        'authority': 'Government Authority',
        'ministry': 'Government Ministry',
        'department': 'Government Department',
        'agency': 'Government Agency',
        'institute': 'Regulatory Institute',
        'protection': 'Protection Authority',
        'privacy': 'Privacy Commission',
        'data': 'Data Protection Authority',
        'security': 'Security Authority',
        'cyber': 'Cybersecurity Authority',
        'tax': 'Tax Authority',
        'revenue': 'Revenue Service',
        'insurance': 'Insurance Regulatory Authority',
        'legislation': 'Legislative Body',
    }
    
    for term, regulator in regulator_terms.items():
        if term in domain.lower():
            return regulator
    
    return "Unknown"


def find_regulation_category(url: str) -> Optional[str]:
    """
    Find the regulation category that the URL belongs to.
    
    Args:
        url (str): The URL to check
        
    Returns:
        Optional[str]: The category name or None if not found
    """
    # Check if the URL exists in our regulations database
    for category, urls in REGULATIONS.items():
        if url in urls:
            return category
    
    # If not found in exact match, check if it's a substring in any URL
    for category, urls in REGULATIONS.items():
        for regulation_url in urls:
            if url in regulation_url or regulation_url in url:
                return category
    
    return None


def process_url(url: str) -> Dict[str, str]:
    """
    Process a URL to identify its country and regulator.
    
    Args:
        url (str): The URL to process
        
    Returns:
        Dict[str, str]: Dictionary with URL, country, and regulator information
    """
    domain, tld = extract_domain_and_tld(url)
    category = find_regulation_category(url)
    country = identify_country_from_url(url, domain, tld)
    regulator = identify_regulator_from_url(url, domain, category)
    
    return {
        'url': url,
        'domain': domain,
        'country': country,
        'regulator': regulator,
        'category': category or "Unknown"
    }


def process_urls_from_file(file_path: str, output_file: str = "regulatory_analysis.csv") -> None:
    """
    Process URLs from a file and save results to a CSV file.
    
    Args:
        file_path (str): Path to the file containing URLs
        output_file (str): Path to save the output CSV file
    """
    urls = load_urls_from_file(file_path)
    if not urls:
        logging.error("No URLs found to process.")
        return
    
    results = []
    total_urls = len(urls)
    
    for i, url in enumerate(urls, 1):
        logging.info(f"Processing URL {i}/{total_urls}: {url}")
        try:
            result = process_url(url)
            results.append(result)
            
            # Add a small delay to avoid excessive processing
            if i < total_urls:
                time.sleep(0.1)
                
        except Exception as e:
            logging.error(f"Error processing URL {url}: {e}")
            results.append({
                'url': url,
                'domain': "Error",
                'country': "Error",
                'regulator': "Error",
                'category': "Error"
            })
    
    # Save results to CSV
    try:
        with open(output_file, 'w', newline='') as csvfile:
            fieldnames = ['url', 'domain', 'country', 'regulator', 'category']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for result in results:
                writer.writerow(result)
        
        logging.info(f"Results saved to {output_file}")
    except Exception as e:
        logging.error(f"Error saving results to CSV: {e}")


def main():
    """Main function to run the URL processor."""
    parser = argparse.ArgumentParser(description='Process URLs to identify country and regulator information.')
    parser.add_argument('file', help='Path to the text file containing URLs')
    parser.add_argument('--output', '-o', default='regulatory_analysis.csv', 
                        help='Path to save the output CSV file (default: regulatory_analysis.csv)')
    
    args = parser.parse_args()
    process_urls_from_file(args.file, args.output)
    

if __name__ == "__main__":
    main()
