
"""PDF document analysis utilities for regulatory compliance."""
import os
import re
import logging
from typing import Dict, Any, List, Optional
import PyPDF2
import spacy
from spacy.matcher import Matcher

# Initialize spaCy
nlp = spacy.load("en_core_web_sm")

# Set up logging
logger = logging.getLogger(__name__)

def extract_text_from_pdf(file_path: str) -> str:
    """
    Extract text from a PDF document.
    
    Args:
        file_path: Path to the PDF file
        
    Returns:
        str: Extracted text content
    """
    text = ""
    try:
        with open(file_path, 'rb') as f:
            pdf_reader = PyPDF2.PdfReader(f)
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text += page.extract_text() + "\n"
        return text
    except Exception as e:
        logger.error(f"Error extracting text from PDF: {str(e)}")
        return ""

def identify_regulatory_sections(text: str) -> Dict[str, List[str]]:
    """
    Identify sections in the text that appear to be regulatory in nature.
    
    Args:
        text: Document text
        
    Returns:
        Dict[str, List[str]]: Dictionary of regulatory section types and their content
    """
    sections = {
        "articles": [],
        "sections": [],
        "requirements": [],
        "penalties": [],
    }
    
    # Article/Section pattern matching
    article_pattern = re.compile(r'(?i)(article|section)\s+(\d+[A-Za-z]?)[.\s:]+(.*?)(?=(?:\n\s*(?:article|section)\s+\d+|$))', re.DOTALL)
    for match in article_pattern.finditer(text):
        section_type = match.group(1).lower()
        section_number = match.group(2)
        section_content = match.group(3).strip()
        
        if section_type == "article":
            sections["articles"].append(f"Article {section_number}: {section_content}")
        else:
            sections["sections"].append(f"Section {section_number}: {section_content}")
    
    # Requirements pattern matching (look for shall, must, required, etc.)
    requirement_patterns = [
        r'(?i)shall\s+\w+',
        r'(?i)must\s+\w+', 
        r'(?i)required\s+to\s+\w+',
        r'(?i)obligation\s+to\s+\w+'
    ]
    
    for pattern in requirement_patterns:
        regex = re.compile(pattern)
        for match in regex.finditer(text):
            # Get the sentence containing the requirement
            start = max(0, text.rfind('.', 0, match.start()) + 1)
            end = text.find('.', match.end())
            if end == -1:
                end = len(text)
            requirement = text[start:end].strip()
            if requirement and len(requirement) < 500:  # Avoid extremely long matches
                sections["requirements"].append(requirement)
    
    # Penalties pattern matching
    penalty_patterns = [
        r'(?i)penalt(y|ies)',
        r'(?i)fine[sd]?\s+(?:of\s+)?(?:up\s+to\s+)?[$€£]?\d+',
        r'(?i)liable\s+(?:to|for)',
        r'(?i)sanctions',
        r'(?i)imprisonment'
    ]
    
    for pattern in penalty_patterns:
        regex = re.compile(pattern)
        for match in regex.finditer(text):
            start = max(0, text.rfind('.', 0, match.start()) + 1)
            end = text.find('.', match.end())
            if end == -1:
                end = len(text)
            penalty = text[start:end].strip()
            if penalty and len(penalty) < 500:  # Avoid extremely long matches
                sections["penalties"].append(penalty)
    
    return sections

def extract_entities(text: str) -> Dict[str, List[str]]:
    """
    Extract entities from the document text using spaCy.
    
    Args:
        text: Document text
        
    Returns:
        Dict[str, List[str]]: Dictionary of entity types and their instances
    """
    entities = {
        "organizations": [],
        "people": [],
        "dates": [],
        "money": [],
        "percent": [],
        "regulatory_terms": []
    }
    
    # Define regulatory terms matcher
    matcher = Matcher(nlp.vocab)
    regulatory_patterns = [
        [{"LOWER": "data"}, {"LOWER": "protection"}],
        [{"LOWER": "privacy"}],
        [{"LOWER": "compliance"}],
        [{"LOWER": "regulation"}],
        [{"LOWER": "gdpr"}],
        [{"LOWER": "ccpa"}],
        [{"LOWER": "hipaa"}],
        [{"LOWER": "personal"}, {"LOWER": "data"}],
        [{"LOWER": "consent"}],
        [{"LOWER": "processor"}],
        [{"LOWER": "controller"}],
        [{"LOWER": "breach"}, {"LOWER": "notification"}],
        [{"LOWER": "data"}, {"LOWER": "subject"}],
        [{"LOWER": "right"}, {"LOWER": "to"}, {"LOWER": "access"}],
        [{"LOWER": "right"}, {"LOWER": "to"}, {"LOWER": "erasure"}],
    ]
    
    for pattern in regulatory_patterns:
        matcher.add("REGULATORY_TERM", [pattern])
    
    # Process text with spaCy
    doc = nlp(text[:100000])  # Limit text size to avoid memory issues
    
    # Extract named entities
    for ent in doc.ents:
        if ent.label_ == "ORG":
            if ent.text not in entities["organizations"]:
                entities["organizations"].append(ent.text)
        elif ent.label_ == "PERSON":
            if ent.text not in entities["people"]:
                entities["people"].append(ent.text)
        elif ent.label_ == "DATE":
            if ent.text not in entities["dates"]:
                entities["dates"].append(ent.text)
        elif ent.label_ == "MONEY":
            if ent.text not in entities["money"]:
                entities["money"].append(ent.text)
        elif ent.label_ == "PERCENT":
            if ent.text not in entities["percent"]:
                entities["percent"].append(ent.text)
    
    # Extract regulatory terms using matcher
    matches = matcher(doc)
    for match_id, start, end in matches:
        span = doc[start:end]
        if span.text not in entities["regulatory_terms"]:
            entities["regulatory_terms"].append(span.text)
    
    return entitieimport os
import re
import logging
from typing import Dict, List, Any, Optional, Set
import PyPDF2
import spacy
from spacy.matcher import Matcher

# Initialize logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Try to load spaCy model, fallback to simpler processing if not available
try:
    nlp = spacy.load("en_core_web_sm")
except (ImportError, OSError):
    logger.warning("spaCy model not available. Using simplified text processing.")
    nlp = None

def extract_text_from_pdf(file_path: str) -> str:
    """
    Extract text content from a PDF file.
    
    Args:
        file_path: Path to the PDF file
        
    Returns:
        str: Extracted text
    """
    if not os.path.exists(file_path):
        logger.error(f"PDF file not found: {file_path}")
        return ""
    
    try:
        text = ""
        with open(file_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            for page in reader.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n\n"
        return text
    except Exception as e:
        logger.error(f"Error extracting text from PDF: {str(e)}")
        return ""

def identify_regulatory_sections(text: str) -> Dict[str, List[str]]:
    """
    Identify regulatory sections in the text.
    
    Args:
        text: Text content
        
    Returns:
        Dict[str, List[str]]: Identified regulatory sections
    """
    sections = {
        "articles": [],
        "sections": [],
        "requirements": [],
        "penalties": []
    }
    
    # Find articles
    article_pattern = r'Article\s+\d+\.?\s+[A-Z][^\.]+\.'
    article_matches = re.findall(article_pattern, text)
    sections["articles"] = article_matches
    
    # Find sections
    section_pattern = r'Section\s+\d+\.?\s+[A-Z][^\.]+\.'
    section_matches = re.findall(section_pattern, text)
    sections["sections"] = section_matches
    
    # Find requirements
    requirement_patterns = [
        r'shall\s+[^\.;]+[\.;]',
        r'must\s+[^\.;]+[\.;]',
        r'required to\s+[^\.;]+[\.;]',
        r'obligation\s+to\s+[^\.;]+[\.;]'
    ]
    
    for pattern in requirement_patterns:
        requirement_matches = re.findall(pattern, text, re.IGNORECASE)
        for match in requirement_matches:
            if len(match) > 10 and match not in sections["requirements"]:
                sections["requirements"].append(match.strip())
    
    # Find penalties
    penalty_patterns = [
        r'fine[^\.;]+[\.;]',
        r'penalty[^\.;]+[\.;]',
        r'imprisonment[^\.;]+[\.;]',
        r'punishable[^\.;]+[\.;]',
        r'liable[^\.;]+[\.;]'
    ]
    
    for pattern in penalty_patterns:
        penalty_matches = re.findall(pattern, text, re.IGNORECASE)
        for match in penalty_matches:
            if len(match) > 10 and match not in sections["penalties"]:
                sections["penalties"].append(match.strip())
    
    return sections

def extract_entities(text: str) -> Dict[str, List[str]]:
    """
    Extract entities from regulatory text.
    
    Args:
        text: Text content
        
    Returns:
        Dict[str, List[str]]: Extracted entities
    """
    entities = {
        "organizations": [],
        "regulatory_terms": [],
        "dates": [],
        "money": [],
        "percent": []
    }
    
    if nlp:
        # Use spaCy for entity extraction
        doc = nlp(text[:100000])  # Limit to avoid memory issues
        
        for ent in doc.ents:
            if ent.label_ == "ORG":
                if ent.text not in entities["organizations"]:
                    entities["organizations"].append(ent.text)
            elif ent.label_ == "DATE":
                if ent.text not in entities["dates"]:
                    entities["dates"].append(ent.text)
            elif ent.label_ == "MONEY":
                if ent.text not in entities["money"]:
                    entities["money"].append(ent.text)
            elif ent.label_ == "PERCENT":
                if ent.text not in entities["percent"]:
                    entities["percent"].append(ent.text)
    
    # Simple pattern matching for regulatory terms
    regulatory_terms = [
        "data protection", "personal data", "privacy", "consent",
        "security measures", "breach notification", "compliance",
        "regulatory authority", "enforcement", "audit", "certification",
        "risk assessment", "impact assessment", "accountability"
    ]
    
    for term in regulatory_terms:
        if re.search(r'\b' + re.escape(term) + r'\b', text, re.IGNORECASE):
            entities["regulatory_terms"].append(term)
    
    # Additional regex patterns for money and percentages
    money_pattern = r'(?:[$€£¥]\s*\d+[,\d]*(?:\.\d+)?)|(?:\d+[,\d]*(?:\.\d+)?\s*(?:dollars|euros|pounds|yen))'
    money_matches = re.findall(money_pattern, text, re.IGNORECASE)
    for match in money_matches:
        if match not in entities["money"]:
            entities["money"].append(match.strip())
    
    percent_pattern = r'\d+(?:\.\d+)?\s*(?:percent|%)'
    percent_matches = re.findall(percent_pattern, text, re.IGNORECASE)
    for match in percent_matches:
        if match not in entities["percent"]:
            entities["percent"].append(match.strip())
    
    # Extract organizations using patterns if spaCy is not available
    if not nlp or not entities["organizations"]:
        org_patterns = [
            r'(?:[A-Z][a-z]*\s+){1,3}(?:Authority|Commission|Agency|Organization|Board|Department|Ministry)',
            r'(?:[A-Z][a-z]*\s+){1,3}(?:Corporation|Inc\.|LLC|Ltd\.)',
            r'(?:the\s+)?(?:[A-Z][a-z]*\s+){1,3}(?:of|for)\s+(?:[A-Z][a-z]*\s+){1,3}'
        ]
        
        for pattern in org_patterns:
            org_matches = re.findall(pattern, text)
            for match in org_matches:
                if match.strip() not in entities["organizations"]:
                    entities["organizations"].append(match.strip())
    
    return entities

def calculate_regulatory_confidence(text: str, sections: Dict[str, List[str]], entities: Dict[str, List[str]]) -> float:
    """
    Calculate confidence score that the document is regulatory in nature.
    
    Args:
        text: Text content
        sections: Extracted regulatory sections
        entities: Extracted entities
        
    Returns:
        float: Confidence score between 0 and 1
    """
    confidence = 0.0
    
    # Regulatory indicators and their weights
    indicators = {
        "has_articles": 0.2,
        "has_sections": 0.1,
        "has_requirements": 0.25,
        "has_penalties": 0.15,
        "has_regulatory_terms": 0.2,
        "has_organizations": 0.1
    }
    
    # Check for articles
    if sections["articles"]:
        confidence += indicators["has_articles"] * min(1.0, len(sections["articles"]) / 5)
    
    # Check for sections
    if sections["sections"]:
        confidence += indicators["has_sections"] * min(1.0, len(sections["sections"]) / 5)
    
    # Check for requirements
    if sections["requirements"]:
        confidence += indicators["has_requirements"] * min(1.0, len(sections["requirements"]) / 10)
    
    # Check for penalties
    if sections["penalties"]:
        confidence += indicators["has_penalties"] * min(1.0, len(sections["penalties"]) / 3)
    
    # Check for regulatory terms
    if entities["regulatory_terms"]:
        confidence += indicators["has_regulatory_terms"] * min(1.0, len(entities["regulatory_terms"]) / 5)
    
    # Check for organizations
    if entities["organizations"]:
        confidence += indicators["has_organizations"] * min(1.0, len(entities["organizations"]) / 3)
    
    # Additional checks for common regulatory phrases
    regulatory_phrases = [
        "compliance", "regulation", "directive", "law", "act",
        "enforcement", "penalties", "authority", "compliance requirements",
        "regulatory framework", "legal obligation", "mandatory"
    ]
    
    phrase_confidence = 0.0
    for phrase in regulatory_phrases:
        if re.search(r'\b' + re.escape(phrase) + r'\b', text, re.IGNORECASE):
            phrase_confidence += 0.02
    
    confidence += min(phrase_confidence, 0.1)  # Cap at 0.1
    
    return min(confidence, 1.0)  # Cap at 1.0

def generate_summary(text: str, sections: Dict[str, List[str]], entities: Dict[str, List[str]]) -> str:
    """
    Generate a summary of the regulatory document.
    
    Args:
        text: Text content
        sections: Extracted regulatory sections
        entities: Extracted entities
        
    Returns:
        str: Document summary
    """
    summary_parts = []
    
    # Add document type
    if sections["articles"] or sections["sections"]:
        summary_parts.append("This document appears to be a regulatory text with")
        
        if sections["articles"]:
            summary_parts.append(f"{len(sections['articles'])} articles")
        
        if sections["sections"]:
            if sections["articles"]:
                summary_parts.append("and")
            summary_parts.append(f"{len(sections['sections'])} sections")
        
        summary_parts.append(".")
    else:
        summary_parts.append("This document contains regulatory content.")
    
    # Add key requirements
    if sections["requirements"]:
        summary_parts.append(f"It specifies {len(sections['requirements'])} requirements,")
        if len(sections["requirements"]) <= 3:
            requirements_summary = "; ".join([r[:100] + "..." if len(r) > 100 else r for r in sections["requirements"]])
            summary_parts.append(f"including: {requirements_summary}.")
        else:
            summary_parts.append(f"including: {sections['requirements'][0][:100]}...")
    
    # Add penalties
    if sections["penalties"]:
        summary_parts.append(f"The document outlines {len(sections['penalties'])} penalties or enforcement measures")
        if len(sections["penalties"]) == 1:
            summary_parts.append(f": {sections['penalties'][0]}")
    
    # Add key entities
    entity_parts = []
    if entities["organizations"]:
        entity_parts.append(f"Organizations mentioned: {', '.join(entities['organizations'][:3])}")
        if len(entities["organizations"]) > 3:
            entity_parts[-1] += ", and others"
    
    if entities["money"]:
        entity_parts.append(f"Monetary values: {', '.join(entities['money'][:3])}")
        if len(entities["money"]) > 3:
            entity_parts[-1] += ", and others"
    
    if entity_parts:
        summary_parts.append(" ".join(entity_parts) + ".")
    
    return " ".join(summary_parts)

def analyze_pdf_document(file_path: str) -> Dict[str, Any]:
    """
    Analyze a PDF document for regulatory compliance information.
    
    Args:
        file_path: Path to the PDF file
        
    Returns:
        Dict[str, Any]: Analysis results
    """
    # Use the enhanced regulatory parser
    from app.utils.regulatory_parser import RegulatoryParser
    
    try:
        parser = RegulatoryParser()
        result = parser.parse_document(file_path)
        return result
    except Exception as e:
        logging.error(f"Error analyzing PDF document: {str(e)}")
        
        # Fallback to original analysis method if enhanced parser fails
        try:
            # Extract text from PDF
            text = extract_text_from_pdf(file_path)
            if not text:
                return {"error": "Failed to extract text from PDF"}
            
            # Analyze the document
            regulatory_sections = identify_regulatory_sections(text)
            entities = extract_entities(text)
            
            # Calculate confidence score for regulatory content
            confidence_score = calculate_regulatory_confidence(text, regulatory_sections, entities)
            
            # Create analysis result
            result = {
                "text_length": len(text),
                "regulatory_sections": regulatory_sections,
                "entities": entities,
                "confidence_score": confidence_score,
                "summary": generate_summary(text, regulatory_sections, entities)
            }
            
            return result
        except Exception as fallback_error:
            logging.error(f"Fallback analysis also failed: {str(fallback_error)}")
            return {"error": f"Failed to analyze document: {str(e)}"}sultult

def calculate_regulatory_confidence(text: str, sections: Dict[str, List[str]], entities: Dict[str, List[str]]) -> float:
    """
    Calculate confidence score that the document contains regulatory content.
    
    Args:
        text: Document text
        sections: Identified regulatory sections
        entities: Extracted entities
        
    Returns:
        float: Confidence score (0-1)
    """
    score = 0.0
    
    # Check for regulatory sections
    if sections["articles"] or sections["sections"]:
        score += 0.3
    
    # Check for requirements
    if sections["requirements"]:
        score += min(0.3, len(sections["requirements"]) * 0.03)
    
    # Check for penalties
    if sections["penalties"]:
        score += min(0.2, len(sections["penalties"]) * 0.05)
    
    # Check for regulatory terms
    if entities["regulatory_terms"]:
        score += min(0.2, len(entities["regulatory_terms"]) * 0.02)
    
    return min(1.0, score)

def generate_summary(text: str, sections: Dict[str, List[str]], entities: Dict[str, List[str]]) -> str:
    """
    Generate a summary of the regulatory document.
    
    Args:
        text: Document text
        sections: Identified regulatory sections
        entities: Extracted entities
        
    Returns:
        str: Document summary
    """
    summary_parts = []
    
    # Add document structure information
    structure = []
    if sections["articles"]:
        structure.append(f"{len(sections['articles'])} articles")
    if sections["sections"]:
        structure.append(f"{len(sections['sections'])} sections")
    
    if structure:
        summary_parts.append(f"Document contains {', '.join(structure)}.")
    
    # Add requirements information
    if sections["requirements"]:
        summary_parts.append(f"Identified {len(sections['requirements'])} potential regulatory requirements.")
    
    # Add penalty information
    if sections["penalties"]:
        summary_parts.append(f"Contains {len(sections['penalties'])} references to penalties or enforcement.")
    
    # Add entity information
    entity_info = []
    if entities["organizations"]:
        entity_info.append(f"{len(entities['organizations'])} organizations")
    if entities["regulatory_terms"]:
        entity_info.append(f"{len(entities['regulatory_terms'])} regulatory terms")
    
    if entity_info:
        summary_parts.append(f"References {', '.join(entity_info)}.")
    
    # Create final summary
    if summary_parts:
        return " ".join(summary_parts)
    else:
        return "No clear regulatory content identified in this document."
"""PDF analyzer utility for regulatory document analysis."""
import re
import logging
import os
from typing import Dict, Any, List

# Try to import PyPDF2 - this is needed for PDF text extraction
try:
    import PyPDF2
except ImportError:
    logging.warning("PyPDF2 not installed. PDF text extraction will not work.")

# Initialize logger
logger = logging.getLogger(__name__)

def extract_text_from_pdf(pdf_path: str) -> str:
    """
    Extract text content from a PDF file.
    
    Args:
        pdf_path: Path to the PDF file
        
    Returns:
        str: Extracted text from the PDF
    """
    if not os.path.exists(pdf_path):
        raise FileNotFoundError(f"PDF file not found: {pdf_path}")
    
    try:
        text = ""
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            for page in reader.pages:
                text += page.extract_text() + "\n"
        return text
    except Exception as e:
        logger.error(f"Error extracting text from PDF: {str(e)}")
        raise Exception(f"Failed to extract text from PDF: {str(e)}")

def identify_regulatory_sections(text: str) -> Dict[str, List[str]]:
    """
    Identify regulatory sections in the document text.
    
    Args:
        text: The text content to analyze
        
    Returns:
        Dict[str, List[str]]: Dictionary of identified sections
    """
    sections = {
        "articles": [],
        "sections": [],
        "requirements": [],
        "penalties": []
    }
    
    # Find articles (e.g., "Article 1. Scope")
    article_matches = re.finditer(r'(?:^|\n)\s*Article\s+(\d+)[.\s]+(.*?)(?=\n|$)', text, re.MULTILINE)
    for match in article_matches:
        sections["articles"].append(match.group(0).strip())
    
    # Find sections (e.g., "Section 3. Penalties")
    section_matches = re.finditer(r'(?:^|\n)\s*Section\s+(\d+)[.\s]+(.*?)(?=\n|$)', text, re.MULTILINE)
    for match in section_matches:
        sections["sections"].append(match.group(0).strip())
    
    # Find requirements (phrases containing words like "shall", "must", "required")
    requirement_patterns = [
        r'[^.;:]*(?:shall|must|required|mandated|obligated)[^.;:]*[.;:]',
        r'[^.;:]*(?:implement|maintain|establish)[^.;:]*(?:measures|controls|procedures)[^.;:]*[.;:]'
    ]
    
    for pattern in requirement_patterns:
        requirement_matches = re.finditer(pattern, text, re.IGNORECASE)
        for match in requirement_matches:
            req = match.group(0).strip()
            if req and req not in sections["requirements"]:
                sections["requirements"].append(req)
    
    # Find penalties (phrases containing words like "penalties", "fines", "violations")
    penalty_patterns = [
        r'[^.;:]*(?:penalt|fine|violat|imprisonment|sanction)[^.;:]*(?:\$|\d+%|(?:\d+\s*(?:year|month|day|hour)))[^.;:]*[.;:]'
    ]
    
    for pattern in penalty_patterns:
        penalty_matches = re.finditer(pattern, text, re.IGNORECASE)
        for match in penalty_matches:
            penalty = match.group(0).strip()
            if penalty and penalty not in sections["penalties"]:
                sections["penalties"].append(penalty)
    
    return sections

def extract_entities(text: str) -> Dict[str, List[str]]:
    """
    Extract relevant entities from document text.
    
    Args:
        text: The text content to analyze
        
    Returns:
        Dict[str, List[str]]: Dictionary of extracted entities
    """
    entities = {
        "organizations": [],
        "regulatory_terms": [],
        "money": [],
        "percent": [],
        "dates": []
    }
    
    # Extract organization names
    org_patterns = [
        r'(?:(?:(?:the|an?)\s+)?[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\s+(?:Authority|Agency|Commission|Board|Organization|Bureau))',
        r'(?:organizations?|entities?|companies?|businesses?|corporations?)'
    ]
    
    for pattern in org_patterns:
        org_matches = re.finditer(pattern, text, re.IGNORECASE)
        for match in org_matches:
            org = match.group(0).strip().lower()
            if org and org not in entities["organizations"]:
                entities["organizations"].append(org)
    
    # Extract regulatory terms
    term_patterns = [
        r'(?:data\s+protection)',
        r'(?:personal\s+data)',
        r'(?:privacy\s+rights?)',
        r'(?:compliance\s+requirements?)',
        r'(?:regulatory\s+framework)',
        r'(?:security\s+measures?)'
    ]
    
    for pattern in term_patterns:
        term_matches = re.finditer(pattern, text, re.IGNORECASE)
        for match in term_matches:
            term = match.group(0).strip().lower()
            if term and term not in entities["regulatory_terms"]:
                entities["regulatory_terms"].append(term)
    
    # Extract monetary amounts
    money_matches = re.finditer(r'(?:(?:\$|€|£)\s*\d+(?:,\d+)*(?:\.\d+)?(?:\s*(?:million|billion|thousand))?)|(?:\d+(?:,\d+)*(?:\.\d+)?\s*(?:USD|EUR|GBP))', text)
    for match in money_matches:
        money = match.group(0).strip()
        if money and money not in entities["money"]:
            entities["money"].append(money)
    
    # Extract percentages
    percent_matches = re.finditer(r'\d+(?:\.\d+)?\s*%', text)
    for match in percent_matches:
        percent = match.group(0).strip()
        if percent and percent not in entities["percent"]:
            entities["percent"].append(percent)
    
    # Extract dates
    date_patterns = [
        r'\d{1,2}\s+(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4}',
        r'(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},\s+\d{4}',
        r'\d{1,2}/\d{1,2}/\d{4}'
    ]
    
    for pattern in date_patterns:
        date_matches = re.finditer(pattern, text, re.IGNORECASE)
        for match in date_matches:
            date = match.group(0).strip()
            if date and date not in entities["dates"]:
                entities["dates"].append(date)
    
    return entities

def analyze_pdf_document(pdf_path: str) -> Dict[str, Any]:
    """
    Analyze a PDF document for regulatory content.
    
    Args:
        pdf_path: Path to the PDF file
        
    Returns:
        Dict[str, Any]: Analysis results
    """
    try:
        # Extract text from PDF
        text = extract_text_from_pdf(pdf_path)
        
        # Identify regulatory sections
        regulatory_sections = identify_regulatory_sections(text)
        
        # Extract entities
        entities = extract_entities(text)
        
        # Calculate confidence score based on identified elements
        confidence_score = calculate_confidence_score(regulatory_sections, entities)
        
        # Generate summary
        summary = generate_summary(regulatory_sections, entities)
        
        # Return complete analysis
        return {
            "regulatory_sections": regulatory_sections,
            "entities": entities,
            "confidence_score": confidence_score,
            "summary": summary,
            "text_length": len(text),
            "document_path": pdf_path
        }
    except Exception as e:
        logger.error(f"Error analyzing PDF document: {str(e)}")
        raise Exception(f"Failed to analyze PDF document: {str(e)}")

def calculate_confidence_score(regulatory_sections: Dict[str, List[str]], entities: Dict[str, List[str]]) -> float:
    """
    Calculate confidence score based on identified elements.
    
    Args:
        regulatory_sections: Dictionary of identified regulatory sections
        entities: Dictionary of extracted entities
        
    Returns:
        float: Confidence score between 0 and 1
    """
    # Initialize base score
    score = 0.5
    
    # Adjust score based on regulatory sections
    if regulatory_sections["articles"] and len(regulatory_sections["articles"]) > 0:
        score += 0.1
    if regulatory_sections["sections"] and len(regulatory_sections["sections"]) > 0:
        score += 0.1
    if regulatory_sections["requirements"] and len(regulatory_sections["requirements"]) > 2:
        score += 0.15
    if regulatory_sections["penalties"] and len(regulatory_sections["penalties"]) > 0:
        score += 0.15
    
    # Adjust score based on entities
    if entities["regulatory_terms"] and len(entities["regulatory_terms"]) > 2:
        score += 0.1
    if entities["money"] and len(entities["money"]) > 0:
        score += 0.05
    if entities["percent"] and len(entities["percent"]) > 0:
        score += 0.05
    
    # Cap score at 1.0
    return min(score, 1.0)

def generate_summary(regulatory_sections: Dict[str, List[str]], entities: Dict[str, List[str]]) -> str:
    """
    Generate a summary of the regulatory document.
    
    Args:
        regulatory_sections: Dictionary of identified regulatory sections
        entities: Dictionary of extracted entities
        
    Returns:
        str: Generated summary
    """
    summary_parts = []
    
    # Add information about articles and sections
    num_articles = len(regulatory_sections["articles"])
    num_sections = len(regulatory_sections["sections"])
    
    if num_articles > 0:
        summary_parts.append(f"The document contains {num_articles} article{'s' if num_articles > 1 else ''}.")
    
    if num_sections > 0:
        summary_parts.append(f"The document contains {num_sections} section{'s' if num_sections > 1 else ''}.")
    
    # Add information about requirements
    num_requirements = len(regulatory_sections["requirements"])
    if num_requirements > 0:
        summary_parts.append(f"There are {num_requirements} requirement{'s' if num_requirements > 1 else ''} identified.")
    
    # Add information about penalties
    num_penalties = len(regulatory_sections["penalties"])
    if num_penalties > 0:
        summary_parts.append(f"The document specifies {num_penalties} penalty/enforcement measure{'s' if num_penalties > 1 else ''}.")
    
    # Add information about key terms
    if entities["regulatory_terms"]:
        key_terms = ", ".join(entities["regulatory_terms"][:3])
        summary_parts.append(f"Key regulatory terms include: {key_terms}.")
    
    # Combine parts into final summary
    if summary_parts:
        return " ".join(summary_parts)
    else:
        return "The document does not appear to contain significant regulatory content."
