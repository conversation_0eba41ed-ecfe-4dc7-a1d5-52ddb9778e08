
"""
Utilities for analyzing regulatory compliance data.
"""
from typing import Dict, List, Any
import pandas as pd
import numpy as np

def load_regulatory_data(file_path: str) -> pd.DataFrame:
    """
    Load regulatory data from a CSV file.
    
    Args:
        file_path: Path to the CSV file
        
    Returns:
        DataFrame containing regulatory data
    """
    try:
        return pd.read_csv(file_path)
    except Exception as e:
        print(f"Error loading regulatory data: {e}")
        # Return empty DataFrame with expected columns
        return pd.DataFrame({
            'country': [],
            'data_protection_score': [],
            'breach_notification_score': [],
            'penalties_score': [],
            'overall_compliance': []
        })

def calculate_compliance_scores(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Calculate weighted compliance scores based on regulatory data.
    
    Args:
        data: List of dictionaries containing country compliance data
        
    Returns:
        List of dictionaries with weighted compliance scores
    """
    # Convert list of dictionaries to DataFrame for easier manipulation
    df = pd.DataFrame(data)
    
    # Define weights for different compliance categories
    weights = {
        'data_protection_score': 0.4,
        'breach_notification_score': 0.3,
        'penalties_score': 0.3
    }
    
    # Calculate weighted score
    df['weighted_compliance_score'] = (
        df['data_protection_score'] * weights['data_protection_score'] +
        df['breach_notification_score'] * weights['breach_notification_score'] +
        df['penalties_score'] * weights['penalties_score']
    )
    
    # Round to nearest integer
    df['weighted_compliance_score'] = df['weighted_compliance_score'].round(1)
    
    # Convert back to list of dictionaries
    return df.to_dict('records')

def identify_critical_gaps(data: List[Dict[str, Any]], threshold: float = 80) -> Dict[str, Any]:
    """
    Identify critical compliance gaps based on a threshold.
    
    Args:
        data: List of dictionaries containing country compliance data
        threshold: Score threshold below which a gap is considered critical
        
    Returns:
        Dictionary containing countries with gaps and gap categories
    """
    # Convert to DataFrame
    df = pd.DataFrame(data)
    
    # Identify countries with gaps
    countries_with_gaps = []
    gap_categories = {}
    
    for _, row in df.iterrows():
        country_gaps = []
        
        # Check each score category
        for category in ['data_protection_score', 'breach_notification_score', 'penalties_score']:
            if row[category] < threshold:
                country_gaps.append(category)
        
        # If there are gaps, add country to the list
        if country_gaps:
            countries_with_gaps.append(row['code'])
            gap_categories[row['code']] = country_gaps
    
    return {
        'countries_with_gaps': countries_with_gaps,
        'gap_categories': gap_categories
    }

def generate_recommendations(data: List[Dict[str, Any]], gaps: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate recommendations based on identified compliance gaps.
    
    Args:
        data: List of dictionaries containing country compliance data
        gaps: Dictionary containing countries with gaps and gap categories
        
    Returns:
        Dictionary containing recommendations
    """
    # Convert to DataFrame
    df = pd.DataFrame(data)
    
    countries_with_gaps = gaps.get('countries_with_gaps', [])
    gap_categories = gaps.get('gap_categories', {})
    
    recommendations = {}
    
    for country_code in countries_with_gaps:
        country_row = df[df['code'] == country_code]
        if country_row.empty:
            continue
            
        country_name = country_row['country'].values[0]
        country_recommendations = []
        
        for category in gap_categories.get(country_code, []):
            if category == 'data_protection_score':
                country_recommendations.append(
                    f"Improve data protection framework in {country_name} to meet international standards."
                )
            elif category == 'breach_notification_score':
                country_recommendations.append(
                    f"Enhance breach notification procedures in {country_name} to ensure timely reporting."
                )
            elif category == 'penalties_score':
                country_recommendations.append(
                    f"Review and update penalty mechanisms in {country_name} to improve enforcement."
                )
        
        recommendations[country_code] = country_recommendations
    
    return {
        'country_recommendations': recommendations
    }

def calculate_regional_averages(data: List[Dict[str, Any]]) -> pd.DataFrame:
    """
    Calculate average compliance scores by region.
    
    Args:
        data: List of dictionaries containing country compliance data with region info
        
    Returns:
        DataFrame with regional averages
    """
    # Convert to DataFrame
    df = pd.DataFrame(data)
    
    # Ensure the region column exists
    if 'region' not in df.columns:
        # Handle the case where region is not provided
        return pd.DataFrame({'region': [], 'avg_overall_compliance': []})
    
    # Calculate regional averages
    regional_avg = df.groupby('region').agg({
        'data_protection_score': 'mean',
        'breach_notification_score': 'mean',
        'penalties_score': 'mean',
        'overall_compliance': 'mean'
    }).reset_index()
    
    # Rename columns
    regional_avg = regional_avg.rename(columns={
        'data_protection_score': 'avg_data_protection_score',
        'breach_notification_score': 'avg_breach_notification_score',
        'penalties_score': 'avg_penalties_score',
        'overall_compliance': 'avg_overall_compliance'
    })
    
    # Round values
    for col in regional_avg.columns:
        if col != 'region':
            regional_avg[col] = regional_avg[col].round(1)
    
    return regional_avg
