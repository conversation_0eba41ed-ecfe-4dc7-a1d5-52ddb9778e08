
"""
Enhanced regulatory document parser with advanced NLP capabilities.
"""
import re
import os
from typing import Dict, List, Any, Tuple, Optional
import spacy
from spacy.language import Language
import PyPDF2
from datetime import datetime, timedelta
import logging

# Initialize spaCy model
try:
    nlp = spacy.load("en_core_web_sm")
except OSError:
    # If model not found, use smaller model or download
    try:
        nlp = spacy.load("en_core_web_md")
    except OSError:
        nlp = None
        logging.warning("spaCy model not available. Some features will be limited.")

class RegulatoryParser:
    """Parser for regulatory documents with NLP capabilities."""
    
    def __init__(self):
        self.nlp = nlp
    
    def parse_document(self, file_path: str) -> Dict[str, Any]:
        """
        Parse a regulatory document and extract structured information.
        
        Args:
            file_path: Path to the document file
            
        Returns:
            Dict containing structured regulatory information
        """
        # Determine file type and extract text
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext == '.pdf':
            text = self.extract_text_from_pdf(file_path)
        elif file_ext in ['.txt', '.md', '.html']:
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read()
        else:
            raise ValueError(f"Unsupported file type: {file_ext}")
            
        # Process the extracted text
        return self.analyze_regulatory_text(text)
    
    def extract_text_from_pdf(self, file_path: str) -> str:
        """
        Extract text content from a PDF file.
        
        Args:
            file_path: Path to the PDF file
            
        Returns:
            Extracted text content
        """
        text = ""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
        except Exception as e:
            logging.error(f"Error extracting text from PDF: {str(e)}")
            raise
            
        return text
    
    def analyze_regulatory_text(self, text: str) -> Dict[str, Any]:
        """
        Analyze regulatory text and extract structured information.
        
        Args:
            text: Regulatory text content
            
        Returns:
            Dict containing structured analysis results
        """
        # Extract sections and entities
        sections = self.identify_regulatory_sections(text)
        entities = self.extract_entities(text)
        requirements = self.extract_requirements(text)
        deadlines = self.extract_deadlines(text)
        penalties = self.extract_penalties(text)
        
        # Calculate confidence score
        confidence = self.calculate_regulatory_confidence(text, sections, entities)
        
        # Generate summary
        summary = self.generate_summary(text, sections, entities, requirements)
        
        # Return structured analysis
        return {
            "text_length": len(text),
            "sections": sections,
            "entities": entities,
            "requirements": requirements,
            "deadlines": deadlines,
            "penalties": penalties,
            "confidence_score": confidence,
            "summary": summary
        }
    
    def identify_regulatory_sections(self, text: str) -> Dict[str, List[str]]:
        """
        Identify sections in the text that appear to be regulatory in nature.
        
        Args:
            text: Document text
            
        Returns:
            Dict of regulatory section types and their content
        """
        sections = {
            "articles": [],
            "sections": [],
            "subsections": [],
            "definitions": [],
            "appendices": []
        }
        
        # Article/Section pattern matching
        article_pattern = re.compile(r'(?i)(article|section)\s+(\d+[A-Za-z]?)[.\s:]+(.*?)(?=(?:\n\s*(?:article|section)\s+\d+|$))', re.DOTALL)
        for match in article_pattern.finditer(text):
            section_type = match.group(1).lower()
            section_number = match.group(2)
            section_content = match.group(3).strip()
            
            if section_type == "article":
                sections["articles"].append(f"Article {section_number}: {section_content}")
            else:
                sections["sections"].append(f"Section {section_number}: {section_content}")
        
        # Look for definitions
        definition_pattern = re.compile(r'(?i)(?:definitions|terms|glossary).*?(?:\n\n|\Z)', re.DOTALL)
        for match in definition_pattern.finditer(text):
            sections["definitions"].append(match.group(0).strip())
        
        # Look for appendices
        appendix_pattern = re.compile(r'(?i)(?:appendix|annex)\s+[A-Z0-9].*?(?:\n\n|\Z)', re.DOTALL)
        for match in appendix_pattern.finditer(text):
            sections["appendices"].append(match.group(0).strip())
            
        return sections
    
    def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """
        Extract entities from regulatory text using NLP.
        
        Args:
            text: Text content
            
        Returns:
            Dict of extracted entities by type
        """
        entities = {
            "organizations": [],
            "regulatory_authorities": [],
            "dates": [],
            "monetary_values": [],
            "percentages": [],
            "regulatory_terms": [],
            "locations": []
        }
        
        if self.nlp:
            # Use spaCy for entity extraction (limit length to avoid memory issues)
            doc = self.nlp(text[:100000])
            
            for ent in doc.ents:
                if ent.label_ == "ORG":
                    clean_text = ent.text.strip()
                    if clean_text and clean_text not in entities["organizations"]:
                        entities["organizations"].append(clean_text)
                        
                        # Check if it might be a regulatory authority
                        auth_keywords = ["authority", "commission", "agency", "department", "bureau", "board", "committee"]
                        if any(keyword in ent.text.lower() for keyword in auth_keywords):
                            if clean_text not in entities["regulatory_authorities"]:
                                entities["regulatory_authorities"].append(clean_text)
                                
                elif ent.label_ == "DATE":
                    if ent.text not in entities["dates"]:
                        entities["dates"].append(ent.text)
                        
                elif ent.label_ == "MONEY":
                    if ent.text not in entities["monetary_values"]:
                        entities["monetary_values"].append(ent.text)
                        
                elif ent.label_ == "PERCENT":
                    if ent.text not in entities["percentages"]:
                        entities["percentages"].append(ent.text)
                        
                elif ent.label_ == "GPE" or ent.label_ == "LOC":
                    if ent.text not in entities["locations"]:
                        entities["locations"].append(ent.text)
        
        # Add regulatory terms through pattern matching
        regulatory_terms = [
            "data protection", "personal data", "privacy", "consent",
            "security measures", "breach notification", "compliance",
            "regulatory authority", "enforcement", "audit", "certification",
            "risk assessment", "impact assessment", "accountability",
            "controller", "processor", "subject rights", "legitimate interest",
            "legal basis", "sensitive data", "special category", "disclosure",
            "retention", "cross-border", "transfer", "adequacy decision",
            "binding corporate rules", "standard contractual clauses"
        ]
        
        for term in regulatory_terms:
            if term.lower() in text.lower() and term not in entities["regulatory_terms"]:
                entities["regulatory_terms"].append(term)
                
        return entities
    
    def extract_requirements(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract requirements from regulatory text.
        
        Args:
            text: Text content
            
        Returns:
            List of requirement dictionaries with text and metadata
        """
        requirements = []
        
        # Requirement patterns (using modal verbs and obligation phrases)
        requirement_patterns = [
            r'(?i)(shall|must|required to|obligated to|have to)\s+\w+[^.;:]*[.;:]',
            r'(?i)(it is (mandatory|obligatory|required|necessary))\s+[^.;:]*[.;:]',
            r'(?i)(organizations?|entities?|companies?|businesses?)\s+(shall|must|are required to|have to)\s+\w+[^.;:]*[.;:]'
        ]
        
        for pattern in requirement_patterns:
            regex = re.compile(pattern)
            for match in regex.finditer(text):
                # Get the full sentence
                sentence = match.group(0).strip()
                
                # Create requirement object
                requirement = {
                    "text": sentence,
                    "modal_verb": self._extract_modal_verb(sentence),
                    "subject": self._extract_subject(sentence),
                    "action": self._extract_action(sentence),
                    "priority": self._determine_priority(sentence)
                }
                
                if requirement not in requirements:
                    requirements.append(requirement)
        
        return requirements
    
    def extract_deadlines(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract deadlines from regulatory text.
        
        Args:
            text: Text content
            
        Returns:
            List of deadline dictionaries with text and metadata
        """
        deadlines = []
        
        # Deadline patterns
        deadline_patterns = [
            r'(?i)within\s+(\d+)\s+(day|week|month|year)s?',
            r'(?i)no later than\s+(\w+\s+\d+,\s+\d{4})',
            r'(?i)by\s+(\w+\s+\d+,\s+\d{4})',
            r'(?i)before\s+(\w+\s+\d+,\s+\d{4})',
            r'(?i)deadline\s+(?:of|is|for)?\s+(\w+\s+\d+,\s+\d{4})',
            r'(?i)due\s+(?:date|by)?\s+(?:of|is|for)?\s+(\w+\s+\d+,\s+\d{4})'
        ]
        
        for pattern in deadline_patterns:
            regex = re.compile(pattern)
            for match in regex.finditer(text):
                # Get the full sentence containing the deadline
                start = max(0, text.rfind('.', 0, match.start()) + 1)
                end = text.find('.', match.end())
                if end == -1:
                    end = min(match.end() + 200, len(text))
                
                sentence = text[start:end].strip()
                
                # Create deadline object
                deadline = {
                    "text": sentence,
                    "deadline_phrase": match.group(0),
                    "time_period": self._extract_time_period(match.group(0))
                }
                
                if deadline not in deadlines:
                    deadlines.append(deadline)
        
        return deadlines
    
    def extract_penalties(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract penalties from regulatory text.
        
        Args:
            text: Text content
            
        Returns:
            List of penalty dictionaries with text and metadata
        """
        penalties = []
        
        # Penalty patterns
        penalty_patterns = [
            r'(?i)(?:penalty|fine|sanction)(?:s)?\s+(?:of|up to)?\s+(?:\$|€|£)?(\d[\d,.]*\s*(?:million|thousand|billion)?\s*(?:dollars|euros|pounds)?)',
            r'(?i)(?:penalty|fine|sanction)(?:s)?\s+(?:of|up to)?\s+(\d+(?:\.\d+)?\s*%)',
            r'(?i)(?:imprisonment|jail|incarceration)\s+(?:for|of)?\s+(?:up to)?\s+(\d+\s+(?:day|week|month|year)s?)',
            r'(?i)(?:class|level)\s+([A-F])\s+(?:felony|misdemeanor)'
        ]
        
        for pattern in penalty_patterns:
            regex = re.compile(pattern)
            for match in regex.finditer(text):
                # Get the full sentence containing the penalty
                start = max(0, text.rfind('.', 0, match.start()) + 1)
                end = text.find('.', match.end())
                if end == -1:
                    end = min(match.end() + 200, len(text))
                
                sentence = text[start:end].strip()
                
                # Create penalty object
                penalty = {
                    "text": sentence,
                    "penalty_phrase": match.group(0),
                    "severity": self._determine_penalty_severity(match.group(0)),
                    "type": self._determine_penalty_type(match.group(0))
                }
                
                if penalty not in penalties:
                    penalties.append(penalty)
        
        return penalties
    
    def calculate_regulatory_confidence(self, text: str, sections: Dict[str, List[str]], entities: Dict[str, List[str]]) -> float:
        """
        Calculate confidence score that the document is regulatory in nature.
        
        Args:
            text: Text content
            sections: Extracted regulatory sections
            entities: Extracted entities
            
        Returns:
            float: Confidence score between 0 and 1
        """
        confidence = 0.0
        
        # Regulatory indicators and their weights
        indicators = {
            "has_articles": 0.15,
            "has_sections": 0.1,
            "has_definitions": 0.05,
            "has_regulatory_terms": 0.2,
            "has_requirements": 0.2,
            "has_penalties": 0.15,
            "has_deadlines": 0.1,
            "has_authorities": 0.05
        }
        
        # Check for articles
        if sections["articles"]:
            confidence += indicators["has_articles"] * min(1.0, len(sections["articles"]) / 5)
        
        # Check for sections
        if sections["sections"]:
            confidence += indicators["has_sections"] * min(1.0, len(sections["sections"]) / 5)
        
        # Check for definitions
        if sections["definitions"]:
            confidence += indicators["has_definitions"]
        
        # Check for regulatory terms
        if entities["regulatory_terms"]:
            confidence += indicators["has_regulatory_terms"] * min(1.0, len(entities["regulatory_terms"]) / 10)
        
        # Check for requirements (using requirement patterns)
        req_patterns = [
            r'(?i)shall\s+\w+',
            r'(?i)must\s+\w+',
            r'(?i)required\s+to\s+\w+'
        ]
        
        req_count = 0
        for pattern in req_patterns:
            req_count += len(re.findall(pattern, text))
        
        if req_count > 0:
            confidence += indicators["has_requirements"] * min(1.0, req_count / 15)
        
        # Check for penalties
        penalty_patterns = [
            r'(?i)fine',
            r'(?i)penalty',
            r'(?i)imprisonment',
            r'(?i)sanction'
        ]
        
        penalty_count = 0
        for pattern in penalty_patterns:
            penalty_count += len(re.findall(pattern, text))
        
        if penalty_count > 0:
            confidence += indicators["has_penalties"] * min(1.0, penalty_count / 5)
        
        # Check for deadlines
        deadline_patterns = [
            r'(?i)within\s+\d+',
            r'(?i)no later than',
            r'(?i)deadline'
        ]
        
        deadline_count = 0
        for pattern in deadline_patterns:
            deadline_count += len(re.findall(pattern, text))
        
        if deadline_count > 0:
            confidence += indicators["has_deadlines"] * min(1.0, deadline_count / 5)
        
        # Check for authorities
        if entities.get("regulatory_authorities", []):
            confidence += indicators["has_authorities"] * min(1.0, len(entities["regulatory_authorities"]) / 3)
        
        return min(1.0, confidence)
    
    def generate_summary(self, text: str, sections: Dict[str, List[str]], entities: Dict[str, List[str]], requirements: List[Dict[str, Any]]) -> str:
        """
        Generate a summary of the regulatory document.
        
        Args:
            text: Text content
            sections: Extracted regulatory sections
            entities: Extracted entities
            requirements: Extracted requirements
            
        Returns:
            str: Generated summary
        """
        summary_parts = []
        
        # Document type
        if len(sections["articles"]) > len(sections["sections"]):
            doc_type = "regulation"
        else:
            doc_type = "regulatory document"
        
        # Count total articles and sections
        total_sections = len(sections["articles"]) + len(sections["sections"])
        
        # Get key entities
        key_orgs = entities.get("organizations", [])[:3]
        key_terms = entities.get("regulatory_terms", [])[:5]
        
        # Create summary intro
        if total_sections > 0:
            summary_parts.append(f"This {doc_type} contains {total_sections} major sections.")
            
            if sections["articles"]:
                summary_parts.append(f"It includes {len(sections['articles'])} articles.")
            
            if sections["sections"]:
                summary_parts.append(f"It contains {len(sections['sections'])} sections.")
        
        # Add requirements info
        if requirements:
            summary_parts.append(f"The document specifies {len(requirements)} distinct requirements.")
        
        # Add entity info
        if key_orgs:
            summary_parts.append(f"Key organizations mentioned include: {', '.join(key_orgs)}.")
        
        if key_terms:
            summary_parts.append(f"Key regulatory concepts include: {', '.join(key_terms)}.")
        
        return " ".join(summary_parts)
    
    def _extract_modal_verb(self, text: str) -> str:
        """Extract modal verb from requirement text."""
        modal_verbs = ["shall", "must", "should", "may", "will", "required to", "have to"]
        for verb in modal_verbs:
            if verb in text.lower():
                return verb
        return ""
    
    def _extract_subject(self, text: str) -> str:
        """Extract subject from requirement text."""
        if self.nlp:
            doc = self.nlp(text)
            for token in doc:
                if token.dep_ == "nsubj":
                    return token.text
        
        # Fallback to simple pattern matching
        subject_patterns = [
            r'(?i)(organizations?|entities?|companies?|businesses?|controllers?|processors?)',
            r'(?i)(data\s+subject|individual|person|citizen)'
        ]
        
        for pattern in subject_patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(0)
                
        return ""
    
    def _extract_action(self, text: str) -> str:
        """Extract action verb from requirement text."""
        # First find modal verb
        modal_verb = self._extract_modal_verb(text)
        if modal_verb:
            # Look for verb after modal
            pattern = f"(?i){re.escape(modal_verb)}\\s+(\\w+)"
            match = re.search(pattern, text)
            if match:
                return match.group(1)
        
        return ""
    
    def _determine_priority(self, text: str) -> str:
        """Determine priority level of requirement."""
        if "shall" in text.lower() or "must" in text.lower() or "required" in text.lower():
            return "high"
        elif "should" in text.lower():
            return "medium"
        elif "may" in text.lower():
            return "low"
        return "medium"
    
    def _extract_time_period(self, text: str) -> Optional[Dict[str, Any]]:
        """Extract time period from deadline text."""
        # Pattern for "within X days/weeks/months/years"
        within_pattern = re.compile(r'(?i)within\s+(\d+)\s+(day|week|month|year)s?')
        within_match = within_pattern.search(text)
        if within_match:
            value = int(within_match.group(1))
            unit = within_match.group(2).lower()
            return {"value": value, "unit": unit, "type": "relative"}
        
        # Pattern for specific dates
        date_pattern = re.compile(r'(?i)(january|february|march|april|may|june|july|august|september|october|november|december)\s+(\d{1,2}),?\s+(\d{4})')
        date_match = date_pattern.search(text)
        if date_match:
            month = date_match.group(1)
            day = date_match.group(2)
            year = date_match.group(3)
            return {"date": f"{month} {day}, {year}", "type": "absolute"}
        
        return None
    
    def _determine_penalty_severity(self, text: str) -> str:
        """Determine severity level of penalty."""
        text_lower = text.lower()
        
        # Check for imprisonment terms
        if "imprisonment" in text_lower or "jail" in text_lower or "incarceration" in text_lower:
            return "severe"
        
        # Check for large monetary amounts
        money_pattern = re.compile(r'(\d+)(?:\.\d+)?\s*(?:million|billion)')
        money_match = money_pattern.search(text_lower)
        if money_match:
            amount = int(money_match.group(1))
            if "billion" in text_lower:
                amount *= 1000
            if amount >= 10:
                return "severe"
            elif amount >= 1:
                return "high"
            else:
                return "medium"
        
        # Check for percentage-based fines
        percent_pattern = re.compile(r'(\d+)(?:\.\d+)?\s*%')
        percent_match = percent_pattern.search(text_lower)
        if percent_match:
            percentage = float(percent_match.group(1))
            if percentage >= 4:
                return "severe"
            elif percentage >= 2:
                return "high"
            else:
                return "medium"
                
        return "low"
    
    def _determine_penalty_type(self, text: str) -> str:
        """Determine type of penalty."""
        text_lower = text.lower()
        
        if "imprisonment" in text_lower or "jail" in text_lower or "incarceration" in text_lower:
            return "criminal"
        elif "fine" in text_lower or "penalty" in text_lower or "$" in text or "€" in text or "£" in text:
            return "monetary"
        elif "injunction" in text_lower or "cease" in text_lower or "desist" in text_lower:
            return "injunctive"
        elif "suspend" in text_lower or "revoke" in text_lower or "terminate" in text_lower:
            return "administrative"
        
        return "other"
