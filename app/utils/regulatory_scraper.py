
"""
Regulatory data scraper utility for collecting information from regulatory websites.
"""
import requests
import logging
from typing import Dict, List, Optional, Any
from bs4 import BeautifulSoup
from datetime import datetime
import re
import time

from app.db.models import RegulatorySource, RegulationURL, Regulator

logger = logging.getLogger(__name__)

class RegulatoryScraper:
    def __init__(self, rate_limit: float = 1.0):
        """
        Initialize the scraper with a rate limit to avoid overwhelming sites.
        
        Args:
            rate_limit: Time in seconds to wait between requests
        """
        self.rate_limit = rate_limit
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'RegulationGuru Research Bot (<EMAIL>)'
        })
        
    def scrape_regulator_site(self, regulator_url: str, max_pages: int = 5) -> List[Dict[str, Any]]:
        """
        Scrape a regulatory site for new regulations and updates.
        
        Args:
            regulator_url: Base URL of the regulator's website
            max_pages: Maximum number of pages to scrape
            
        Returns:
            List of regulation data dictionaries
        """
        logger.info(f"Scraping regulator site: {regulator_url}")
        regulations = []
        
        try:
            # Start with the main page
            current_page = regulator_url
            
            for page_num in range(max_pages):
                logger.info(f"Scraping page {page_num + 1}: {current_page}")
                
                # Respect the rate limit
                time.sleep(self.rate_limit)
                
                # Get the page content
                response = self.session.get(current_page, timeout=30)
                response.raise_for_status()
                
                # Parse the HTML
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Extract regulation links - this will be regulator-specific
                # Here's a generic approach that looks for links in the main content area
                content_area = soup.select_one('main') or soup.select_one('#content') or soup.body
                
                if content_area:
                    # Look for links that might be regulations
                    for link in content_area.select('a[href]'):
                        href = link.get('href')
                        title = link.get_text(strip=True)
                        
                        # Skip if href or title is empty
                        if not href or not title:
                            continue
                            
                        # Make absolute URL if relative
                        if href.startswith('/'):
                            href = regulator_url.rstrip('/') + href
                        
                        # Filter for potential regulation links
                        if self._is_potential_regulation(href, title):
                            # Extract publication date if available
                            date_text = self._find_date_near_element(link)
                            publication_date = self._parse_date(date_text) if date_text else None
                            
                            regulations.append({
                                'url': href,
                                'title': title,
                                'publication_date': publication_date,
                                'source': regulator_url
                            })
                
                # Find next page link if available
                next_page = self._find_next_page(soup, regulator_url)
                if not next_page or next_page == current_page:
                    break
                    
                current_page = next_page
            
            logger.info(f"Found {len(regulations)} potential regulations")
            return regulations
            
        except Exception as e:
            logger.error(f"Error scraping {regulator_url}: {str(e)}")
            return regulations
    
    def _is_potential_regulation(self, url: str, title: str) -> bool:
        """
        Check if a URL and title might represent a regulation.
        
        Args:
            url: The URL to check
            title: The title text
            
        Returns:
            Boolean indicating if this might be a regulation
        """
        # Keywords that suggest a regulatory document
        regulation_keywords = [
            'regulation', 'directive', 'law', 'act', 'statute', 'rule', 
            'guidance', 'compliance', 'requirement', 'policy', 'standard',
            'legislation'
        ]
        
        # File extensions that might indicate a regulatory document
        doc_extensions = ['.pdf', '.doc', '.docx', '.txt']
        
        # Check URL for document extensions
        has_doc_extension = any(url.lower().endswith(ext) for ext in doc_extensions)
        
        # Check title for regulatory keywords
        title_lower = title.lower()
        has_regulation_keyword = any(keyword in title_lower for keyword in regulation_keywords)
        
        # URL patterns that might indicate regulations
        url_patterns = [
            r'/regulation', r'/directive', r'/guidance', r'/policy',
            r'/[0-9]{4}/[0-9]+', r'/[a-z]+-[0-9]+', r'/[0-9]{4}-[0-9]+'
        ]
        
        matches_url_pattern = any(re.search(pattern, url, re.IGNORECASE) for pattern in url_patterns)
        
        return has_doc_extension or has_regulation_keyword or matches_url_pattern
    
    def _find_date_near_element(self, element) -> Optional[str]:
        """
        Try to find a date near an element.
        
        Args:
            element: The BeautifulSoup element
            
        Returns:
            Date text if found, None otherwise
        """
        # Look at siblings and parents for date-like text
        date_patterns = [
            r'\d{1,2}/\d{1,2}/\d{2,4}',  # MM/DD/YYYY
            r'\d{1,2}-\d{1,2}-\d{2,4}',  # MM-DD-YYYY
            r'\d{4}-\d{1,2}-\d{1,2}',    # YYYY-MM-DD
            r'\d{2}\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{4}',  # 01 January 2023
            r'(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{1,2},?\s+\d{4}'  # January 1, 2023
        ]
        
        # Check parent
        parent = element.parent
        if parent:
            parent_text = parent.get_text()
            for pattern in date_patterns:
                match = re.search(pattern, parent_text)
                if match:
                    return match.group(0)
        
        # Check siblings
        for sibling in element.next_siblings:
            if hasattr(sibling, 'get_text'):
                sibling_text = sibling.get_text()
                for pattern in date_patterns:
                    match = re.search(pattern, sibling_text)
                    if match:
                        return match.group(0)
        
        return None
    
    def _parse_date(self, date_text: str) -> Optional[datetime]:
        """
        Parse a date string into a datetime object.
        
        Args:
            date_text: The date string
            
        Returns:
            Datetime object if parsing succeeds, None otherwise
        """
        formats = [
            '%m/%d/%Y', '%d/%m/%Y', '%Y-%m-%d', 
            '%d-%m-%Y', '%m-%d-%Y',
            '%d %b %Y', '%d %B %Y',
            '%b %d, %Y', '%B %d, %Y',
            '%b %d %Y', '%B %d %Y'
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(date_text, fmt)
            except ValueError:
                continue
        
        return None
    
    def _find_next_page(self, soup: BeautifulSoup, base_url: str) -> Optional[str]:
        """
        Find the URL for the next page if pagination is available.
        
        Args:
            soup: BeautifulSoup object of the current page
            base_url: Base URL for resolving relative links
            
        Returns:
            URL of the next page if found, None otherwise
        """
        # Common patterns for next page links
        next_patterns = [
            ('a:contains("Next")', lambda x: x.get('href')),
            ('a:contains("next")', lambda x: x.get('href')),
            ('a.next', lambda x: x.get('href')),
            ('a.pagination-next', lambda x: x.get('href')),
            ('a[rel="next"]', lambda x: x.get('href')),
            ('a[aria-label="Next"]', lambda x: x.get('href')),
            ('a span.next', lambda x: x.parent.get('href')),
            ('li.next a', lambda x: x.get('href'))
        ]
        
        for selector, extractor in next_patterns:
            try:
                elements = soup.select(selector)
                if elements:
                    href = extractor(elements[0])
                    if href:
                        # Make absolute URL if relative
                        if href.startswith('/'):
                            href = base_url.rstrip('/') + href
                        return href
            except:
                continue
        
        return None

    def extract_regulation_content(self, url: str) -> Optional[Dict[str, Any]]:
        """
        Extract content and metadata from a regulation URL.
        
        Args:
            url: URL of the regulation
            
        Returns:
            Dictionary with regulation content and metadata
        """
        try:
            # Respect the rate limit
            time.sleep(self.rate_limit)
            
            # Get the page content
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            content_type = response.headers.get('Content-Type', '').lower()
            
            # Handle PDF files
            if 'application/pdf' in content_type:
                return self._extract_pdf_content(response.content, url)
            
            # Handle HTML content
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Extract the title
            title = soup.title.string if soup.title else None
            
            # Try to extract the main content
            content = self._extract_main_content(soup)
            
            # Extract publication date
            date_text = self._extract_publication_date(soup)
            publication_date = self._parse_date(date_text) if date_text else None
            
            return {
                'url': url,
                'title': title,
                'content': content,
                'publication_date': publication_date,
                'content_type': 'html'
            }
            
        except Exception as e:
            logger.error(f"Error extracting content from {url}: {str(e)}")
            return None
    
    def _extract_main_content(self, soup: BeautifulSoup) -> str:
        """
        Extract the main content from an HTML page.
        
        Args:
            soup: BeautifulSoup object
            
        Returns:
            Extracted main content as text
        """
        # Try common content containers
        main_selectors = [
            'main', 'article', '#content', '.content', 
            '#main-content', '.main-content', '.article-body'
        ]
        
        for selector in main_selectors:
            content = soup.select_one(selector)
            if content:
                return content.get_text(separator=' ', strip=True)
        
        # If no main content found, use body content excluding headers and footers
        if soup.body:
            # Remove script, style, header, footer, nav tags
            for tag in soup.find_all(['script', 'style', 'header', 'footer', 'nav']):
                tag.extract()
                
            return soup.body.get_text(separator=' ', strip=True)
        
        return soup.get_text(separator=' ', strip=True)
    
    def _extract_publication_date(self, soup: BeautifulSoup) -> Optional[str]:
        """
        Extract publication date from HTML content.
        
        Args:
            soup: BeautifulSoup object
            
        Returns:
            Date string if found, None otherwise
        """
        # Common patterns for publication dates
        date_patterns = [
            r'\b(?:published|issued|effective|release)(?:\s+date)?(?:\s*:\s*|\s+on\s+)([A-Za-z0-9\s,]+\d{4})',
            r'(?:Date|Published|Released|Issued|Effective)(?:\s*:\s*|\s+on\s+)([A-Za-z0-9\s,]+\d{4})',
            r'\b\d{1,2}/\d{1,2}/\d{2,4}\b',
            r'\b\d{4}-\d{1,2}-\d{1,2}\b'
        ]
        
        # Look for date metadata
        meta_date = soup.select_one('meta[name="date"], meta[property="article:published_time"]')
        if meta_date and meta_date.get('content'):
            return meta_date.get('content')
        
        # Check the text
        text = soup.get_text()
        for pattern in date_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(0) if len(match.groups()) == 0 else match.group(1)
        
        return None
    
    def _extract_pdf_content(self, pdf_content: bytes, url: str) -> Dict[str, Any]:
        """
        Extract content from a PDF file.
        
        Args:
            pdf_content: The PDF file content
            url: Source URL
            
        Returns:
            Dictionary with extracted content
        """
        # In a real implementation, you would use a library like PyPDF2 or pdfminer
        # Here we'll just return a placeholder
        return {
            'url': url,
            'title': url.split('/')[-1],
            'content': f"[PDF content from {url}]",
            'content_type': 'pdf'
        }
