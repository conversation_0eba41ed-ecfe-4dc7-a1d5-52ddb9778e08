"""
Utility functions for regulations CSV import/export operations.

This module provides comprehensive utilities for handling the regulations_list.csv
file with proper validation, soft-delete support, and batch processing.
"""

import csv
import hashlib
import logging
import time
from datetime import datetime
from io import String<PERSON>
from typing import List, Dict, Any, Optional, Tuple
from uuid import uuid4

import pandas as pd
from sqlalchemy.orm import Session

from app.db.models.regulations_csv import (
    RegulationCSVRecord, 
    RegulationCSVImportLog, 
    RegulationCSVExportLog
)
from app.schemas.regulations_csv import (
    RegulationCSVRecordCreate,
    RegulationCSVImportResult,
    RegulationCSVExportRequest
)

logger = logging.getLogger(__name__)


class RegulationCSVProcessor:
    """Main processor class for regulations CSV operations."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def import_csv_file(
        self, 
        file_path: str, 
        batch_id: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> RegulationCSVImportResult:
        """
        Import regulations from CSV file with comprehensive error handling.
        
        Args:
            file_path: Path to the CSV file
            batch_id: Optional batch identifier
            user_id: Optional user ID for tracking
            
        Returns:
            Import result with statistics and errors
        """
        if not batch_id:
            batch_id = f"import_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid4())[:8]}"
        
        start_time = time.time()
        errors = []
        warnings = []
        successful_imports = 0
        failed_imports = 0
        updated_records = 0
        
        try:
            # Calculate file hash for tracking
            file_hash = self._calculate_file_hash(file_path)
            file_size = self._get_file_size_mb(file_path)
            
            # Create import log
            import_log = RegulationCSVImportLog(
                batch_id=batch_id,
                file_name=file_path.split('/')[-1],
                file_hash=file_hash,
                file_size=file_size,
                imported_by_id=user_id,
                import_status='processing'
            )
            self.db.add(import_log)
            self.db.commit()
            
            # Read and process CSV
            df = pd.read_csv(file_path)
            total_records = len(df)
            
            logger.info(f"Processing {total_records} records from {file_path}")
            
            for index, row in df.iterrows():
                try:
                    # Convert row to dictionary
                    row_data = row.to_dict()
                    
                    # Check if record already exists (by country_code + document_title)
                    existing_record = self.db.query(RegulationCSVRecord).filter(
                        RegulationCSVRecord.country_code == row_data.get('Country_Code'),
                        RegulationCSVRecord.document_title == row_data.get('Document_Title'),
                        RegulationCSVRecord.is_deleted == False
                    ).first()
                    
                    if existing_record:
                        # Update existing record
                        self._update_record_from_csv(existing_record, row_data, batch_id)
                        updated_records += 1
                        logger.debug(f"Updated existing record: {existing_record.id}")
                    else:
                        # Create new record
                        new_record = RegulationCSVRecord.from_csv_row(row_data, batch_id)
                        self.db.add(new_record)
                        successful_imports += 1
                        logger.debug(f"Created new record for {row_data.get('Country_Code')}: {row_data.get('Document_Title')}")
                    
                    # Commit every 100 records to avoid large transactions
                    if (index + 1) % 100 == 0:
                        self.db.commit()
                        logger.info(f"Processed {index + 1}/{total_records} records")
                
                except Exception as e:
                    failed_imports += 1
                    error_detail = {
                        'row': index + 1,
                        'error': str(e),
                        'data': row_data if 'row_data' in locals() else None
                    }
                    errors.append(error_detail)
                    logger.error(f"Error processing row {index + 1}: {e}")
            
            # Final commit
            self.db.commit()
            
            # Update import log with results
            processing_time = time.time() - start_time
            import_log.total_records = total_records
            import_log.successful_imports = successful_imports
            import_log.failed_imports = failed_imports
            import_log.updated_records = updated_records
            import_log.processing_time = processing_time
            import_log.import_status = 'completed' if failed_imports == 0 else 'completed_with_errors'
            import_log.error_summary = errors if errors else None
            import_log.warnings_summary = warnings if warnings else None
            
            self.db.commit()
            
            logger.info(f"Import completed: {successful_imports} new, {updated_records} updated, {failed_imports} failed")
            
            return RegulationCSVImportResult(
                batch_id=batch_id,
                total_records=total_records,
                successful_imports=successful_imports,
                failed_imports=failed_imports,
                updated_records=updated_records,
                errors=errors,
                warnings=warnings,
                processing_time=processing_time
            )
            
        except Exception as e:
            # Update import log with failure
            if 'import_log' in locals():
                import_log.import_status = 'failed'
                import_log.error_summary = [{'error': str(e)}]
                self.db.commit()
            
            logger.error(f"Import failed: {e}")
            raise
    
    def export_csv_file(
        self, 
        file_path: str,
        export_request: RegulationCSVExportRequest,
        user_id: Optional[str] = None
    ) -> str:
        """
        Export regulations to CSV file with filtering support.
        
        Args:
            file_path: Output file path
            export_request: Export configuration
            user_id: Optional user ID for tracking
            
        Returns:
            Export ID for tracking
        """
        export_id = f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid4())[:8]}"
        start_time = time.time()
        
        try:
            # Create export log
            export_log = RegulationCSVExportLog(
                export_id=export_id,
                file_name=file_path.split('/')[-1],
                filters_applied=export_request.filters,
                include_deleted=export_request.include_deleted,
                exported_by_id=user_id,
                export_status='processing',
                file_path=file_path
            )
            self.db.add(export_log)
            self.db.commit()
            
            # Build query with filters
            query = self.db.query(RegulationCSVRecord)
            
            if not export_request.include_deleted:
                query = query.filter(RegulationCSVRecord.is_deleted == False)
            
            # Apply additional filters if provided
            if export_request.filters:
                for field, value in export_request.filters.items():
                    if hasattr(RegulationCSVRecord, field) and value is not None:
                        query = query.filter(getattr(RegulationCSVRecord, field) == value)
            
            # Execute query and convert to CSV
            records = query.all()
            total_records = len(records)
            
            # Convert to CSV format
            csv_data = []
            for record in records:
                csv_data.append(record.to_dict())
            
            # Write to CSV file
            if csv_data:
                df = pd.DataFrame(csv_data)
                df.to_csv(file_path, index=False)
            else:
                # Create empty CSV with headers
                headers = [
                    'Country_Name', 'Country_Code', 'Document_Title', 'Document_Type',
                    'Issuing_Authority', 'Publication_Date', 'Effective_Date', 'Legal_Status',
                    'Document_URL', 'Language', 'Scope_Application', 'Key_Compliance_Requirements',
                    'Enforcement_Mechanisms', 'Penalties', 'Cross_Border_Elements',
                    'Data_Protection_Provisions', 'Incident_Reporting_Requirements',
                    'Risk_Management_Mandates', 'Third_Party_Requirements', 'Audit_Obligations',
                    'Certification_Requirements', 'Implementation_Timeline',
                    'International_Standards_Alignment', 'Extraterritorial_Reach',
                    'Safe_Harbor_Provisions', 'Industry_Specific_Provisions',
                    'Technology_Specific_Provisions'
                ]
                pd.DataFrame(columns=headers).to_csv(file_path, index=False)
            
            # Update export log
            processing_time = time.time() - start_time
            export_log.total_records_exported = total_records
            export_log.processing_time = processing_time
            export_log.export_status = 'completed'
            
            self.db.commit()
            
            logger.info(f"Export completed: {total_records} records exported to {file_path}")
            return export_id
            
        except Exception as e:
            # Update export log with failure
            if 'export_log' in locals():
                export_log.export_status = 'failed'
                self.db.commit()
            
            logger.error(f"Export failed: {e}")
            raise
    
    def _update_record_from_csv(self, record: RegulationCSVRecord, row_data: dict, batch_id: str):
        """Update existing record with CSV data."""
        record.country_name = row_data.get('Country_Name', record.country_name)
        record.country_code = row_data.get('Country_Code', record.country_code)
        record.document_title = row_data.get('Document_Title', record.document_title)
        record.document_type = row_data.get('Document_Type', record.document_type)
        record.issuing_authority = row_data.get('Issuing_Authority', record.issuing_authority)
        record.publication_date = RegulationCSVRecord._parse_date(row_data.get('Publication_Date')) or record.publication_date
        record.effective_date = RegulationCSVRecord._parse_date(row_data.get('Effective_Date')) or record.effective_date
        record.legal_status = row_data.get('Legal_Status', record.legal_status)
        record.document_url = row_data.get('Document_URL') or record.document_url
        record.language = row_data.get('Language', record.language)
        record.scope_application = row_data.get('Scope_Application', record.scope_application)
        record.key_compliance_requirements = row_data.get('Key_Compliance_Requirements', record.key_compliance_requirements)
        record.enforcement_mechanisms = row_data.get('Enforcement_Mechanisms', record.enforcement_mechanisms)
        record.penalties = row_data.get('Penalties', record.penalties)
        record.cross_border_elements = row_data.get('Cross_Border_Elements', record.cross_border_elements)
        record.extraterritorial_reach = row_data.get('Extraterritorial_Reach', record.extraterritorial_reach)
        record.international_standards_alignment = row_data.get('International_Standards_Alignment', record.international_standards_alignment)
        record.data_protection_provisions = row_data.get('Data_Protection_Provisions', record.data_protection_provisions)
        record.incident_reporting_requirements = row_data.get('Incident_Reporting_Requirements', record.incident_reporting_requirements)
        record.risk_management_mandates = row_data.get('Risk_Management_Mandates', record.risk_management_mandates)
        record.third_party_requirements = row_data.get('Third_Party_Requirements', record.third_party_requirements)
        record.audit_obligations = row_data.get('Audit_Obligations', record.audit_obligations)
        record.certification_requirements = row_data.get('Certification_Requirements', record.certification_requirements)
        record.implementation_timeline = row_data.get('Implementation_Timeline', record.implementation_timeline)
        record.safe_harbor_provisions = row_data.get('Safe_Harbor_Provisions', record.safe_harbor_provisions)
        record.industry_specific_provisions = row_data.get('Industry_Specific_Provisions', record.industry_specific_provisions)
        record.technology_specific_provisions = row_data.get('Technology_Specific_Provisions', record.technology_specific_provisions)
        record.import_batch_id = batch_id
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """Calculate SHA-256 hash of file."""
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    
    def _get_file_size_mb(self, file_path: str) -> float:
        """Get file size in MB."""
        import os
        return os.path.getsize(file_path) / (1024 * 1024)
