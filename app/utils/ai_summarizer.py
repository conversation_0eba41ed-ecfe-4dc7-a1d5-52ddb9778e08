
"""
AI-powered regulatory document summarization utility.
"""
import os
from typing import Dict, List, Optional, Tuple
import logging
import json
import openai
from app.utils.pdf_analyzer import extract_text_from_pdf, identify_regulatory_sections

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AISummarizer:
    """Class for AI-powered regulatory document summarization."""
    
    def __init__(self, openai_api_key: Optional[str] = None):
        """Initialize with optional API key."""
        self.api_key = openai_api_key or os.environ.get("OPENAI_API_KEY")
        if not self.api_key:
            logger.warning("No OpenAI API key provided. AI summarization will not work.")
        else:
            openai.api_key = self.api_key
    
    def generate_document_summary(self, document_text: str, max_length: int = 500) -> Dict[str, any]:
        """
        Generate an AI-powered summary of a regulatory document.
        
        Args:
            document_text: Full text of the document
            max_length: Maximum length of the summary
            
        Returns:
            Dict containing summary, key_requirements, deadlines, and penalties
        """
        if not self.api_key:
            return {
                "summary": "AI summarization unavailable - API key not configured",
                "key_requirements": [],
                "deadlines": [],
                "penalties": []
            }
        
        try:
            # First, identify regulatory sections to provide context
            sections = identify_regulatory_sections(document_text)
            
            # Create a prompt for the AI
            prompt = self._create_summarization_prompt(document_text, sections)
            
            # Call the OpenAI API for summarization
            response = openai.ChatCompletion.create(
                model="gpt-4",  # or another appropriate model
                messages=[
                    {"role": "system", "content": "You are a regulatory compliance expert tasked with summarizing regulatory documents. Extract the key information in a structured format."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.3
            )
            
            # Parse the response
            summary_text = response.choices[0].message.content
            structured_data = self._parse_summary_response(summary_text)
            
            return structured_data
            
        except Exception as e:
            logger.error(f"Error generating AI summary: {str(e)}")
            return {
                "summary": f"Error generating summary: {str(e)}",
                "key_requirements": [],
                "deadlines": [],
                "penalties": []
            }
    
    def summarize_pdf_document(self, pdf_path: str) -> Dict[str, any]:
        """
        Extract text from a PDF and generate an AI summary.
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            Dict containing summary and extracted key elements
        """
        try:
            # Extract text from the PDF
            document_text = extract_text_from_pdf(pdf_path)
            
            # Generate the summary
            summary_data = self.generate_document_summary(document_text)
            
            # Add metadata
            summary_data["document_path"] = pdf_path
            summary_data["document_name"] = os.path.basename(pdf_path)
            
            return summary_data
            
        except Exception as e:
            logger.error(f"Error summarizing PDF: {str(e)}")
            return {
                "summary": f"Error summarizing PDF: {str(e)}",
                "document_path": pdf_path,
                "document_name": os.path.basename(pdf_path),
                "key_requirements": [],
                "deadlines": [],
                "penalties": []
            }
    
    def _create_summarization_prompt(self, document_text: str, sections: Dict[str, List[str]]) -> str:
        """
        Create a prompt for the AI to summarize the document.
        
        Args:
            document_text: Document text
            sections: Identified document sections
            
        Returns:
            str: Formatted prompt
        """
        # If document is too long, use selective excerpts
        if len(document_text) > 8000:
            # Get first 2000 chars
            intro = document_text[:2000]
            
            # Get middle 2000 chars
            middle_start = len(document_text) // 2 - 1000
            middle = document_text[middle_start:middle_start + 2000]
            
            # Get last 2000 chars
            outro = document_text[-2000:]
            
            document_text = f"{intro}\n\n[...]\n\n{middle}\n\n[...]\n\n{outro}"
        
        # Format section information
        section_info = ""
        if sections:
            if sections.get("articles"):
                section_info += f"\nArticles found: {len(sections['articles'])}"
            if sections.get("sections"):
                section_info += f"\nSections found: {len(sections['sections'])}"
            if sections.get("requirements"):
                section_info += f"\nRequirements found: {len(sections['requirements'])}"
            if sections.get("penalties"):
                section_info += f"\nPenalty clauses found: {len(sections['penalties'])}"
        
        prompt = f"""Analyze the following regulatory document and provide:

1. A concise summary (max 3 paragraphs)
2. A list of key requirements (numbered list)
3. A list of any deadlines or important dates
4. A list of any penalties or enforcement mechanisms

Document structure information:{section_info}

Document text:
{document_text}

Format your response as JSON with the following structure:
{{
  "summary": "The concise summary...",
  "key_requirements": ["Requirement 1", "Requirement 2", ...],
  "deadlines": ["Deadline 1: description", "Deadline 2: description", ...],
  "penalties": ["Penalty 1: description", "Penalty 2: description", ...]
}}
"""
        return prompt
    
    def _parse_summary_response(self, response_text: str) -> Dict[str, any]:
        """
        Parse the AI response into a structured format.
        
        Args:
            response_text: Raw response from AI
            
        Returns:
            Structured dictionary with summary elements
        """
        try:
            # Try to extract JSON from the response
            json_start = response_text.find('{')
            json_end = response_text.rfind('}')
            
            if json_start >= 0 and json_end > json_start:
                json_str = response_text[json_start:json_end+1]
                data = json.loads(json_str)
                return data
            
            # If JSON parsing fails, attempt structured extraction
            summary_data = {
                "summary": "",
                "key_requirements": [],
                "deadlines": [],
                "penalties": []
            }
            
            # Simple extraction based on headers
            sections = response_text.split('\n\n')
            current_section = None
            
            for section in sections:
                lower_section = section.lower()
                if "summary" in lower_section and len(section.split('\n')) <= 3:
                    current_section = "summary"
                    continue
                elif "requirement" in lower_section and len(section.split('\n')) <= 3:
                    current_section = "key_requirements"
                    continue
                elif "deadline" in lower_section and len(section.split('\n')) <= 3:
                    current_section = "deadlines"
                    continue
                elif "penalt" in lower_section and len(section.split('\n')) <= 3:
                    current_section = "penalties"
                    continue
                
                if current_section:
                    if current_section == "summary":
                        summary_data["summary"] += section + "\n"
                    else:
                        # Extract list items
                        lines = section.split('\n')
                        for line in lines:
                            line = line.strip()
                            if line and (line.startswith('-') or line.startswith('*') or 
                                        (line[0].isdigit() and '.' in line[:3])):
                                item = line[line.find(' ')+1:].strip()
                                summary_data[current_section].append(item)
            
            # Clean up summary
            summary_data["summary"] = summary_data["summary"].strip()
            
            return summary_data
            
        except Exception as e:
            logger.error(f"Error parsing AI response: {str(e)}")
            return {
                "summary": response_text[:500] + "...",
                "key_requirements": [],
                "deadlines": [],
                "penalties": [],
                "parse_error": str(e)
            }
