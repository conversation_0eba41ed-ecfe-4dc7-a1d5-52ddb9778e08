"""
Middleware for internationalization.
"""
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import RedirectResponse

from app.i18n.translations import get_locale


class I18nMiddleware(BaseHTTPMiddleware):
    """Middleware for setting language cookies based on query parameters."""

    async def dispatch(self, request: Request, call_next):
        """
        Process the request to set language cookies based on query parameters.

        Args:
            request: The FastAPI request
            call_next: The next handler in the middleware chain

        Returns:
            The response from the next handler
        """
        response = await call_next(request)

        # Set the language cookie if lang parameter is provided
        lang_param = request.query_params.get("lang")
        if lang_param and isinstance(response, Response):
            # Set cookie to expire in 30 days
            response.set_cookie(
                key="lang",
                value=lang_param,
                max_age=30 * 24 * 60 * 60,
                httponly=True
            )

        return response