
from fastapi import Request
from fastapi.middleware.base import BaseHTTPMiddleware
from fastapi.responses import HTMLResponse
import re

class SwaggerUIMiddleware(BaseHTTPMiddleware):
    """Middleware to inject custom CSS into Swagger UI."""
    
    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)
        
        # Only process HTML responses for Swagger UI
        if isinstance(response, HTMLResponse) and request.url.path == "/docs":
            content = response.body.decode()
            
            # Insert dark mode CSS link
            if "<head>" in content:
                dark_mode_css = '<link rel="stylesheet" type="text/css" href="/static/css/swagger-ui-dark.css">'
                content = content.replace("<head>", f"<head>\n    {dark_mode_css}")
                
                # Set dark theme on Swagger UI container
                content = re.sub(
                    r'<div id="swagger-ui"></div>',
                    '<div id="swagger-ui" class="swagger-ui"></div>',
                    content
                )
                
                # Update response body
                response.body = content.encode()
                response.headers["Content-Length"] = str(len(response.body))
                
        return response
