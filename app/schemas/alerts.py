
"""
Schemas for the Regulatory Alerts API.
"""
from pydantic import BaseModel, HttpUrl, Field, validator
from typing import List, Optional
from datetime import datetime

class AlertResponse(BaseModel):
    """Schema for alert response data."""
    id: int
    title: str
    url: str
    importance: int = Field(..., ge=1, le=10)
    discovery_date: datetime
    country_code: Optional[str] = None
    regulator_name: Optional[str] = None
    
    class Config:
        orm_mode = True

class SubscriptionRequest(BaseModel):
    """Schema for creating an alert subscription."""
    webhook_url: HttpUrl
    countries: Optional[List[str]] = None
    min_importance: Optional[int] = Field(None, ge=1, le=10)
    description: Optional[str] = None
    
    @validator('countries', each_item=True)
    def validate_country_code(cls, v):
        if len(v) != 2:
            raise ValueError('Country code must be 2 characters (ISO 3166-1 alpha-2)')
        return v.upper()

class SubscriptionResponse(BaseModel):
    """Schema for subscription response data."""
    id: int
    webhook_url: str
    countries: Optional[List[str]] = None
    min_importance: Optional[int] = None
    description: Optional[str] = None
    created_at: datetime
    
    class Config:
        orm_mode = True

class AlertMetadata(BaseModel):
    """Metadata for alert responses."""
    total: int
    skip: int
    limit: int
    filtered_by: Optional[dict] = None

class AlertListResponse(BaseModel):
    """Schema for paginated alert list responses."""
    results: List[AlertResponse]
    metadata: AlertMetadata
