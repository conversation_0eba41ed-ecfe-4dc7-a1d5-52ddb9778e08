
"""Pydantic models for data validation."""
from datetime import datetime
from typing import Optional, List

from pydantic import BaseModel, Field, ConfigDict, HttpUrl


class ItemBase(BaseModel):
    """Base Item schema."""

    name: str = Field(..., description="Name of the item", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="Optional description of the item")


class ItemCreate(ItemBase):
    """Schema for item creation."""
    model_config = ConfigDict(json_schema_extra={
        "example": {
            "name": "Sample Item",
            "description": "This is a sample item description"
        }
    })


class Item(ItemBase):
    """Schema for item responses with timestamps and soft-delete info."""

    id: int = Field(..., description="Unique identifier for the item")
    created_at: datetime = Field(..., description="Timestamp when the item was created")
    changed_on: datetime = Field(..., description="Timestamp when the item was last updated")
    is_deleted: bool = Field(..., description="Whether the item has been soft-deleted")
    deleted_at: Optional[datetime] = Field(None, description="Timestamp when the item was soft-deleted")

    model_config = ConfigDict(from_attributes=True)


# Regulatory schemas

class CountryBase(BaseModel):
    """Base Country schema."""

    name: str = Field(..., description="Name of the country", min_length=1, max_length=100)
    code: Optional[str] = Field(None, description="Country code (ISO)")


class CountryCreate(CountryBase):
    """Schema for country creation."""
    model_config = ConfigDict(json_schema_extra={
        "example": {
            "name": "United States",
            "code": "US"
        }
    })


class CountryUpdate(BaseModel):
    """Schema for country updates, all fields optional."""

    name: Optional[str] = Field(None, description="Name of the country", min_length=1, max_length=100)
    code: Optional[str] = Field(None, description="Country code (ISO)")
    region: Optional[str] = Field(None, description="Region of the country")
    subregion: Optional[str] = Field(None, description="Subregion of the country")
    flag_emoji: Optional[str] = Field(None, description="Flag emoji of the country")

    model_config = ConfigDict(json_schema_extra={
        "example": {
            "name": "United States of America",
            "code": "US",
            "region": "Americas",
            "subregion": "North America",
            "flag_emoji": "🇺🇸"
        }
    })


class Country(CountryBase):
    """Schema for country responses."""

    id: int = Field(..., description="Unique identifier for the country")
    created_at: datetime = Field(..., description="Timestamp when the record was created")
    changed_on: datetime = Field(..., description="Timestamp when the record was last updated")
    region: Optional[str] = Field(None, description="Region of the country")
    subregion: Optional[str] = Field(None, description="Subregion of the country")
    flag_emoji: Optional[str] = Field(None, description="Flag emoji of the country")

    model_config = ConfigDict(from_attributes=True)


class RegulatorBase(BaseModel):
    """Base Regulator schema."""

    name: str = Field(..., description="Name of the regulatory authority", min_length=1, max_length=255)
    description: Optional[str] = Field(None, description="Description of the regulator")
    type: Optional[str] = Field(None, description="Type of regulator")
    website: Optional[str] = Field(None, description="Official website URL")


class RegulatorCreate(RegulatorBase):
    """Schema for regulator creation."""

    country_id: int = Field(..., description="ID of the country this regulator belongs to")

    model_config = ConfigDict(json_schema_extra={
        "example": {
            "name": "Securities and Exchange Commission",
            "description": "US financial markets regulator",
            "type": "Financial Regulator",
            "website": "https://www.sec.gov",
            "country_id": 1
        }
    })


class RegulatorUpdate(BaseModel):
    """Schema for regulator updates, all fields optional."""

    name: Optional[str] = Field(None, description="Name of the regulatory authority", min_length=1, max_length=255)
    description: Optional[str] = Field(None, description="Description of the regulator")
    type: Optional[str] = Field(None, description="Type of regulator")
    website: Optional[str] = Field(None, description="Official website URL")
    country_id: Optional[int] = Field(None, description="ID of the country this regulator belongs to")

    model_config = ConfigDict(json_schema_extra={
        "example": {
            "name": "Securities and Exchange Commission (SEC)",
            "description": "Updated description: US financial markets regulator",
            "type": "Financial Regulator",
            "website": "https://www.sec.gov",
            "country_id": 1
        }
    })


class Regulator(RegulatorBase):
    """Schema for regulator responses."""

    id: int = Field(..., description="Unique identifier for the regulator")
    country_id: int = Field(..., description="ID of the country this regulator belongs to")
    created_at: datetime = Field(..., description="Timestamp when the record was created")
    changed_on: datetime = Field(..., description="Timestamp when the record was last updated")

    model_config = ConfigDict(from_attributes=True)


class RegulatorWithCountry(Regulator):
    """Schema for regulator responses with country information."""

    country: Country = Field(..., description="Country information")


class RegulationURLBase(BaseModel):
    """Base RegulationURL schema."""

    url: str = Field(..., description="The regulation URL")
    domain: Optional[str] = Field(None, description="Domain of the URL")
    normalized_url: Optional[str] = Field(None, description="Normalized URL for comparison")
    category: Optional[str] = Field(None, description="Category of the regulation")


class RegulationURLCreate(RegulationURLBase):
    """Schema for regulation URL creation."""

    regulator_id: Optional[int] = Field(None, description="ID of the associated regulator")
    country_id: Optional[int] = Field(None, description="ID of the associated country")
    source_id: Optional[int] = Field(None, description="ID of the associated regulatory source")
    title: Optional[str] = Field(None, description="Title of the regulation")
    publication_date: Optional[datetime] = Field(None, description="Publication date of the regulation")
    confidence_level: float = Field(0.0, description="Confidence level of regulator association (0.0-1.0)", ge=0.0, le=1.0)

    model_config = ConfigDict(json_schema_extra={
        "example": {
            "url": "https://www.sec.gov/rules/final/2023/33-11151.pdf",
            "domain": "sec.gov",
            "category": "financial_regulations",
            "regulator_id": 1,
            "country_id": 1,
            "title": "SEC Final Rule 33-11151",
            "confidence_level": 0.9
        }
    })


class RegulationURLUpdate(BaseModel):
    """Schema for regulation URL updates, all fields optional."""

    url: Optional[str] = Field(None, description="The regulation URL")
    domain: Optional[str] = Field(None, description="Domain of the URL")
    normalized_url: Optional[str] = Field(None, description="Normalized URL for comparison")
    category: Optional[str] = Field(None, description="Category of the regulation")
    regulator_id: Optional[int] = Field(None, description="ID of the associated regulator")
    country_id: Optional[int] = Field(None, description="ID of the associated country")
    source_id: Optional[int] = Field(None, description="ID of the associated regulatory source")
    title: Optional[str] = Field(None, description="Title of the regulation")
    publication_date: Optional[datetime] = Field(None, description="Publication date of the regulation")
    confidence_level: Optional[float] = Field(None, description="Confidence level of regulator association (0.0-1.0)", ge=0.0, le=1.0)

    model_config = ConfigDict(json_schema_extra={
        "example": {
            "title": "Updated SEC Final Rule 33-11151",
            "category": "financial_regulations",
            "confidence_level": 0.95
        }
    })


class RegulationURL(RegulationURLBase):
    """Schema for regulation URL responses."""

    id: int = Field(..., description="Unique identifier for the regulation URL")
    regulator_id: Optional[int] = Field(None, description="ID of the associated regulator")
    country_id: Optional[int] = Field(None, description="ID of the associated country")
    source_id: Optional[int] = Field(None, description="ID of the associated regulatory source")
    title: Optional[str] = Field(None, description="Title of the regulation")
    publication_date: Optional[datetime] = Field(None, description="Publication date of the regulation")
    confidence_level: float = Field(..., description="Confidence level of regulator association (0.0-1.0)")
    created_at: datetime = Field(..., description="Timestamp when the record was created")
    changed_on: datetime = Field(..., description="Timestamp when the record was last updated")

    model_config = ConfigDict(from_attributes=True)


class RegulationURLWithRegulator(RegulationURL):
    """Schema for regulation URL responses with regulator information."""

    regulator: Optional[RegulatorWithCountry] = Field(None, description="Regulator information")


# Regulation Schemas
class RegulationBase(BaseModel):
    """Base Regulation schema."""

    title: str = Field(..., description="Title of the regulation", min_length=1, max_length=255)
    description: Optional[str] = Field(None, description="Description of the regulation")
    status: Optional[str] = Field(None, description="Status of the regulation (e.g., Draft, Active, Superseded)")
    effective_date: Optional[datetime] = Field(None, description="Date when the regulation becomes effective")
    publication_date: Optional[datetime] = Field(None, description="Date when the regulation was published")
    last_amendment_date: Optional[datetime] = Field(None, description="Date of the last amendment")
    reference_number: Optional[str] = Field(None, description="Reference number of the regulation")


class RegulationCreate(RegulationBase):
    """Schema for regulation creation."""

    regulator_id: Optional[int] = Field(None, description="ID of the associated regulator")
    country_id: Optional[int] = Field(None, description="ID of the associated country")
    category_id: Optional[int] = Field(None, description="ID of the associated category")
    regulation_url_id: Optional[int] = Field(None, description="ID of the associated regulation URL")
    tags: Optional[List[int]] = Field(None, description="List of tag IDs to associate with the regulation")
    industries: Optional[List[int]] = Field(None, description="List of industry IDs to associate with the regulation")

    model_config = ConfigDict(json_schema_extra={
        "example": {
            "title": "General Data Protection Regulation",
            "description": "Regulation on data protection and privacy in the EU",
            "status": "Active",
            "effective_date": "2018-05-25T00:00:00",
            "publication_date": "2016-04-27T00:00:00",
            "reference_number": "2016/679",
            "regulator_id": 1,
            "country_id": 1,
            "category_id": 1,
            "tags": [1, 2],
            "industries": [1, 2, 3]
        }
    })


class RegulationUpdate(BaseModel):
    """Schema for regulation updates, all fields optional."""

    title: Optional[str] = Field(None, description="Title of the regulation", min_length=1, max_length=255)
    description: Optional[str] = Field(None, description="Description of the regulation")
    status: Optional[str] = Field(None, description="Status of the regulation (e.g., Draft, Active, Superseded)")
    effective_date: Optional[datetime] = Field(None, description="Date when the regulation becomes effective")
    publication_date: Optional[datetime] = Field(None, description="Date when the regulation was published")
    last_amendment_date: Optional[datetime] = Field(None, description="Date of the last amendment")
    reference_number: Optional[str] = Field(None, description="Reference number of the regulation")
    regulator_id: Optional[int] = Field(None, description="ID of the associated regulator")
    country_id: Optional[int] = Field(None, description="ID of the associated country")
    category_id: Optional[int] = Field(None, description="ID of the associated category")
    regulation_url_id: Optional[int] = Field(None, description="ID of the associated regulation URL")
    tags: Optional[List[int]] = Field(None, description="List of tag IDs to associate with the regulation")
    industries: Optional[List[int]] = Field(None, description="List of industry IDs to associate with the regulation")

    model_config = ConfigDict(json_schema_extra={
        "example": {
            "title": "Updated General Data Protection Regulation",
            "status": "Amended",
            "last_amendment_date": "2023-01-15T00:00:00",
            "tags": [1, 3, 4]
        }
    })


class RegulationTag(BaseModel):
    """Schema for regulation tag responses."""

    id: int = Field(..., description="Unique identifier for the tag")
    name: str = Field(..., description="Name of the tag")

    model_config = ConfigDict(from_attributes=True)


class RegulationCategory(BaseModel):
    """Schema for regulation category responses."""

    id: int = Field(..., description="Unique identifier for the category")
    name: str = Field(..., description="Name of the category")
    description: Optional[str] = Field(None, description="Description of the category")

    model_config = ConfigDict(from_attributes=True)


class Industry(BaseModel):
    """Schema for industry responses."""

    id: int = Field(..., description="Unique identifier for the industry")
    name: str = Field(..., description="Name of the industry")
    description: Optional[str] = Field(None, description="Description of the industry")

    model_config = ConfigDict(from_attributes=True)


class Regulation(RegulationBase):
    """Schema for regulation responses."""

    id: int = Field(..., description="Unique identifier for the regulation")
    regulator_id: Optional[int] = Field(None, description="ID of the associated regulator")
    country_id: Optional[int] = Field(None, description="ID of the associated country")
    category_id: Optional[int] = Field(None, description="ID of the associated category")
    regulation_url_id: Optional[int] = Field(None, description="ID of the associated regulation URL")
    created_at: datetime = Field(..., description="Timestamp when the record was created")
    changed_on: datetime = Field(..., description="Timestamp when the record was last updated")

    model_config = ConfigDict(from_attributes=True)


class RegulationWithDetails(Regulation):
    """Schema for regulation responses with related information."""

    regulator: Optional[RegulatorWithCountry] = Field(None, description="Regulator information")
    category: Optional[RegulationCategory] = Field(None, description="Category information")
    regulation_url: Optional[RegulationURL] = Field(None, description="Regulation URL information")
    tags: List[RegulationTag] = Field(default=[], description="Associated tags")
    industries: List[Industry] = Field(default=[], description="Associated industries")


# Calendar Event Schemas
class CalendarEventExtendedProps(BaseModel):
    """Extended properties for calendar events."""
    category: str
    jurisdiction: str
    description: str
    deadlineType: str
    action: str
    regulationId: int

class CalendarEvent(BaseModel):
    """Calendar event schema for regulatory deadlines and events."""
    title: str
    start: str
    end: str
    backgroundColor: str
    borderColor: str
    extendedProps: CalendarEventExtendedProps
# Add to the bottom of the existing schemas.py file

class CalendarEventBase(BaseModel):
    """Base model for calendar events."""
    title: str
    start: str  # ISO format date
    end: str  # ISO format date
    category: str
    jurisdiction: str
    description: str
    deadlineType: str
    action: str
    regulationId: int

class CalendarEventCreate(CalendarEventBase):
    """Schema for creating a calendar event."""
    pass

class CalendarEvent(CalendarEventBase):
    """Schema for returning a calendar event."""
    id: int

    class Config:
        orm_mode = True


# Regulation Schemas
class RegulationBase(BaseModel):
    """Base Regulation schema."""

    title: str = Field(..., description="Title of the regulation", min_length=1, max_length=255)
    description: Optional[str] = Field(None, description="Description of the regulation")
    status: Optional[str] = Field(None, description="Status of the regulation (e.g., Draft, Active, Superseded)")
    effective_date: Optional[datetime] = Field(None, description="Date when the regulation becomes effective")
    publication_date: Optional[datetime] = Field(None, description="Date when the regulation was published")
    last_amendment_date: Optional[datetime] = Field(None, description="Date of the last amendment")
    reference_number: Optional[str] = Field(None, description="Reference number of the regulation")


class RegulationCreate(RegulationBase):
    """Schema for regulation creation."""

    regulator_id: Optional[int] = Field(None, description="ID of the associated regulator")
    country_id: Optional[int] = Field(None, description="ID of the associated country")
    category_id: Optional[int] = Field(None, description="ID of the associated category")
    regulation_url_id: Optional[int] = Field(None, description="ID of the associated regulation URL")
    tags: Optional[List[int]] = Field(None, description="List of tag IDs to associate with the regulation")
    industries: Optional[List[int]] = Field(None, description="List of industry IDs to associate with the regulation")

    model_config = ConfigDict(json_schema_extra={
        "example": {
            "title": "General Data Protection Regulation",
            "description": "Regulation on data protection and privacy in the EU",
            "status": "Active",
            "effective_date": "2018-05-25T00:00:00",
            "publication_date": "2016-04-27T00:00:00",
            "reference_number": "2016/679",
            "regulator_id": 1,
            "country_id": 1,
            "category_id": 1,
            "tags": [1, 2],
            "industries": [1, 2, 3]
        }
    })


# Compliance Requirement Schemas
class ComplianceRequirementBase(BaseModel):
    """Base Compliance Requirement schema."""

    text: str = Field(..., description="Text of the compliance requirement", min_length=1)
    section: Optional[str] = Field(None, description="Section or article reference")
    priority: Optional[str] = Field(None, description="Priority level (High, Medium, Low)")
    status: Optional[str] = Field(None, description="Status of compliance (Compliant, Non-Compliant, In Progress, Not Applicable)")
    due_date: Optional[datetime] = Field(None, description="Due date for compliance")
    notes: Optional[str] = Field(None, description="Additional notes or context")


class ComplianceRequirementCreate(ComplianceRequirementBase):
    """Schema for compliance requirement creation."""

    regulation_id: int = Field(..., description="ID of the associated regulation")

    model_config = ConfigDict(json_schema_extra={
        "example": {
            "text": "Implement data protection measures for personal information",
            "section": "Article 32",
            "priority": "High",
            "status": "In Progress",
            "due_date": "2023-12-31T00:00:00",
            "notes": "Need to implement encryption and access controls",
            "regulation_id": 1
        }
    })


class RegulationUpdate(BaseModel):
    """Schema for regulation updates, all fields optional."""

    title: Optional[str] = Field(None, description="Title of the regulation", min_length=1, max_length=255)
    description: Optional[str] = Field(None, description="Description of the regulation")
    status: Optional[str] = Field(None, description="Status of the regulation (e.g., Draft, Active, Superseded)")
    effective_date: Optional[datetime] = Field(None, description="Date when the regulation becomes effective")
    publication_date: Optional[datetime] = Field(None, description="Date when the regulation was published")
    last_amendment_date: Optional[datetime] = Field(None, description="Date of the last amendment")
    reference_number: Optional[str] = Field(None, description="Reference number of the regulation")
    regulator_id: Optional[int] = Field(None, description="ID of the associated regulator")
    country_id: Optional[int] = Field(None, description="ID of the associated country")
    category_id: Optional[int] = Field(None, description="ID of the associated category")
    regulation_url_id: Optional[int] = Field(None, description="ID of the associated regulation URL")
    tags: Optional[List[int]] = Field(None, description="List of tag IDs to associate with the regulation")
    industries: Optional[List[int]] = Field(None, description="List of industry IDs to associate with the regulation")

    model_config = ConfigDict(json_schema_extra={
        "example": {
            "title": "Updated General Data Protection Regulation",
            "status": "Amended",
            "last_amendment_date": "2023-01-15T00:00:00",
            "tags": [1, 3, 4]
        }
    })


class ComplianceRequirementUpdate(BaseModel):
    """Schema for compliance requirement updates, all fields optional."""

    text: Optional[str] = Field(None, description="Text of the compliance requirement", min_length=1)
    section: Optional[str] = Field(None, description="Section or article reference")
    priority: Optional[str] = Field(None, description="Priority level (High, Medium, Low)")
    status: Optional[str] = Field(None, description="Status of compliance (Compliant, Non-Compliant, In Progress, Not Applicable)")
    due_date: Optional[datetime] = Field(None, description="Due date for compliance")
    notes: Optional[str] = Field(None, description="Additional notes or context")
    regulation_id: Optional[int] = Field(None, description="ID of the associated regulation")

    model_config = ConfigDict(json_schema_extra={
        "example": {
            "text": "Updated: Implement data protection measures for personal information",
            "status": "Compliant",
            "notes": "Encryption and access controls implemented on 2023-10-15"
        }
    })


class Regulation(RegulationBase):
    """Schema for regulation responses."""

    id: int = Field(..., description="Unique identifier for the regulation")
    regulator_id: Optional[int] = Field(None, description="ID of the associated regulator")
    country_id: Optional[int] = Field(None, description="ID of the associated country")
    category_id: Optional[int] = Field(None, description="ID of the associated category")
    regulation_url_id: Optional[int] = Field(None, description="ID of the associated regulation URL")
    created_at: datetime = Field(..., description="Timestamp when the record was created")
    changed_on: datetime = Field(..., description="Timestamp when the record was last updated")

    model_config = ConfigDict(from_attributes=True)


class ComplianceRequirement(ComplianceRequirementBase):
    """Schema for compliance requirement responses."""

    id: int = Field(..., description="Unique identifier for the compliance requirement")
    regulation_id: int = Field(..., description="ID of the associated regulation")
    created_at: datetime = Field(..., description="Timestamp when the record was created")
    changed_on: datetime = Field(..., description="Timestamp when the record was last updated")

    model_config = ConfigDict(from_attributes=True)


class RegulationTag(BaseModel):
    """Schema for regulation tag responses."""

    id: int = Field(..., description="Unique identifier for the tag")
    name: str = Field(..., description="Name of the tag")
    created_at: datetime = Field(..., description="Timestamp when the record was created")
    changed_on: datetime = Field(..., description="Timestamp when the record was last updated")

    model_config = ConfigDict(from_attributes=True)


class ComplianceRequirementWithRegulation(ComplianceRequirement):
    """Schema for compliance requirement responses with regulation information."""

    regulation: Optional["Regulation"] = Field(None, description="Regulation information")


# Regulation Document Schemas
class RegulationDocumentBase(BaseModel):
    """Base Regulation Document schema."""

    name: str = Field(..., description="Name of the document", min_length=1, max_length=255)
    description: Optional[str] = Field(None, description="Description of the document")
    document_type: Optional[str] = Field(None, description="Type of document (e.g., PDF, Word, Excel)")
    file_name: Optional[str] = Field(None, description="Original file name")
    file_size: Optional[int] = Field(None, description="File size in bytes")
    file_type: Optional[str] = Field(None, description="MIME type of the file")
    file_url: Optional[str] = Field(None, description="URL to access the file")


class RegulationDocumentCreate(RegulationDocumentBase):
    """Schema for regulation document creation."""

    regulation_id: int = Field(..., description="ID of the associated regulation")

    model_config = ConfigDict(json_schema_extra={
        "example": {
            "name": "GDPR Full Text",
            "description": "Complete text of the General Data Protection Regulation",
            "document_type": "PDF",
            "file_name": "gdpr_full_text.pdf",
            "file_size": 2048576,
            "file_type": "application/pdf",
            "file_url": "https://example.com/documents/gdpr_full_text.pdf",
            "regulation_id": 1
        }
    })


class RegulationDocumentUpdate(BaseModel):
    """Schema for regulation document updates, all fields optional."""

    name: Optional[str] = Field(None, description="Name of the document", min_length=1, max_length=255)
    description: Optional[str] = Field(None, description="Description of the document")
    document_type: Optional[str] = Field(None, description="Type of document (e.g., PDF, Word, Excel)")
    file_name: Optional[str] = Field(None, description="Original file name")
    file_size: Optional[int] = Field(None, description="File size in bytes")
    file_type: Optional[str] = Field(None, description="MIME type of the file")
    file_url: Optional[str] = Field(None, description="URL to access the file")
    regulation_id: Optional[int] = Field(None, description="ID of the associated regulation")

    model_config = ConfigDict(json_schema_extra={
        "example": {
            "name": "Updated: GDPR Full Text",
            "description": "Updated version of the GDPR text with annotations",
            "document_type": "PDF"
        }
    })


class RegulationDocument(RegulationDocumentBase):
    """Schema for regulation document responses."""

    id: int = Field(..., description="Unique identifier for the regulation document")
    regulation_id: int = Field(..., description="ID of the associated regulation")
    created_at: datetime = Field(..., description="Timestamp when the record was created")
    changed_on: datetime = Field(..., description="Timestamp when the record was last updated")

    model_config = ConfigDict(from_attributes=True)


class RegulationDocumentWithRegulation(RegulationDocument):
    """Schema for regulation document responses with regulation information."""

    regulation: Optional["Regulation"] = Field(None, description="Regulation information")


# Regulation Tag Schemas
class RegulationTagBase(BaseModel):
    """Base Regulation Tag schema."""

    name: str = Field(..., description="Name of the tag", min_length=1, max_length=50)
    description: Optional[str] = Field(None, description="Description of the tag")
    color: Optional[str] = Field(None, description="Color code for the tag (e.g., #FF0000)")


class RegulationTagCreate(RegulationTagBase):
    """Schema for regulation tag creation."""

    model_config = ConfigDict(json_schema_extra={
        "example": {
            "name": "Data Protection",
            "description": "Regulations related to data protection and privacy",
            "color": "#1890ff"
        }
    })


class RegulationTagUpdate(BaseModel):
    """Schema for regulation tag updates, all fields optional."""

    name: Optional[str] = Field(None, description="Name of the tag", min_length=1, max_length=50)
    description: Optional[str] = Field(None, description="Description of the tag")
    color: Optional[str] = Field(None, description="Color code for the tag (e.g., #FF0000)")

    model_config = ConfigDict(json_schema_extra={
        "example": {
            "name": "Updated: Data Protection",
            "description": "Updated description for data protection tag",
            "color": "#52c41a"
        }
    })


class RegulationTag(RegulationTagBase):
    """Schema for regulation tag responses."""

    id: int = Field(..., description="Unique identifier for the regulation tag")
    created_at: datetime = Field(..., description="Timestamp when the record was created")
    changed_on: datetime = Field(..., description="Timestamp when the record was last updated")

    model_config = ConfigDict(from_attributes=True)


# Regulation Category Schemas
class RegulationCategoryBase(BaseModel):
    """Base Regulation Category schema."""

    name: str = Field(..., description="Name of the category", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="Description of the category")
    icon: Optional[str] = Field(None, description="Icon identifier for the category (e.g., 'finance', 'health')")


class RegulationCategoryCreate(RegulationCategoryBase):
    """Schema for regulation category creation."""

    model_config = ConfigDict(json_schema_extra={
        "example": {
            "name": "Financial Regulations",
            "description": "Regulations related to financial services and banking",
            "icon": "bank"
        }
    })


class RegulationCategoryUpdate(BaseModel):
    """Schema for regulation category updates, all fields optional."""

    name: Optional[str] = Field(None, description="Name of the category", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="Description of the category")
    icon: Optional[str] = Field(None, description="Icon identifier for the category (e.g., 'finance', 'health')")

    model_config = ConfigDict(json_schema_extra={
        "example": {
            "name": "Updated: Financial Regulations",
            "description": "Updated description for financial regulations category",
            "icon": "dollar"
        }
    })


class RegulationCategory(RegulationCategoryBase):
    """Schema for regulation category responses."""

    id: int = Field(..., description="Unique identifier for the regulation category")
    created_at: datetime = Field(..., description="Timestamp when the record was created")
    changed_on: datetime = Field(..., description="Timestamp when the record was last updated")

    model_config = ConfigDict(from_attributes=True)


class RegulationWithDetails(Regulation):
    """Schema for regulation responses with related information."""

    regulator: Optional[RegulatorWithCountry] = Field(None, description="Regulator information")
    category: Optional[RegulationCategory] = Field(None, description="Category information")
    regulation_url: Optional[RegulationURL] = Field(None, description="Regulation URL information")
    tags: List[RegulationTag] = Field(default=[], description="Associated tags")
    industries: List[Industry] = Field(default=[], description="Associated industries")


# Industry Schemas
class IndustryBase(BaseModel):
    """Base Industry schema."""

    name: str = Field(..., description="Name of the industry", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="Description of the industry")
    sector: Optional[str] = Field(None, description="Sector the industry belongs to (e.g., 'Technology', 'Healthcare')")


class IndustryCreate(IndustryBase):
    """Schema for industry creation."""

    model_config = ConfigDict(json_schema_extra={
        "example": {
            "name": "Banking",
            "description": "Financial institutions that provide banking services",
            "sector": "Financial Services"
        }
    })


class IndustryUpdate(BaseModel):
    """Schema for industry updates, all fields optional."""

    name: Optional[str] = Field(None, description="Name of the industry", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="Description of the industry")
    sector: Optional[str] = Field(None, description="Sector the industry belongs to (e.g., 'Technology', 'Healthcare')")

    model_config = ConfigDict(json_schema_extra={
        "example": {
            "name": "Updated: Banking",
            "description": "Updated description for banking industry",
            "sector": "Financial"
        }
    })


class Industry(IndustryBase):
    """Schema for industry responses."""

    id: int = Field(..., description="Unique identifier for the industry")
    created_at: datetime = Field(..., description="Timestamp when the record was created")
    changed_on: datetime = Field(..., description="Timestamp when the record was last updated")

    model_config = ConfigDict(from_attributes=True)
