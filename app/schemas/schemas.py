
"""Pydantic models for data validation."""
from datetime import datetime
from typing import Optional, List

from pydantic import BaseModel, Field, ConfigDict, HttpUrl


class ItemBase(BaseModel):
    """Base Item schema."""
    
    name: str = Field(..., description="Name of the item", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="Optional description of the item")


class ItemCreate(ItemBase):
    """Schema for item creation."""
    model_config = ConfigDict(json_schema_extra={
        "example": {
            "name": "Sample Item",
            "description": "This is a sample item description"
        }
    })


class Item(ItemBase):
    """Schema for item responses with timestamps and soft-delete info."""
    
    id: int = Field(..., description="Unique identifier for the item")
    created_at: datetime = Field(..., description="Timestamp when the item was created")
    changed_on: datetime = Field(..., description="Timestamp when the item was last updated")
    is_deleted: bool = Field(..., description="Whether the item has been soft-deleted")
    deleted_at: Optional[datetime] = Field(None, description="Timestamp when the item was soft-deleted")
    
    model_config = ConfigDict(from_attributes=True)


# Regulatory schemas

class CountryBase(BaseModel):
    """Base Country schema."""
    
    name: str = Field(..., description="Name of the country", min_length=1, max_length=100)
    code: Optional[str] = Field(None, description="Country code (ISO)")


class CountryCreate(CountryBase):
    """Schema for country creation."""
    model_config = ConfigDict(json_schema_extra={
        "example": {
            "name": "United States",
            "code": "US"
        }
    })


class Country(CountryBase):
    """Schema for country responses."""
    
    id: int = Field(..., description="Unique identifier for the country")
    created_at: datetime = Field(..., description="Timestamp when the record was created")
    changed_on: datetime = Field(..., description="Timestamp when the record was last updated")
    
    model_config = ConfigDict(from_attributes=True)


class RegulatorBase(BaseModel):
    """Base Regulator schema."""
    
    name: str = Field(..., description="Name of the regulatory authority", min_length=1, max_length=255)
    description: Optional[str] = Field(None, description="Description of the regulator")
    type: Optional[str] = Field(None, description="Type of regulator")
    website: Optional[str] = Field(None, description="Official website URL")


class RegulatorCreate(RegulatorBase):
    """Schema for regulator creation."""
    
    country_id: int = Field(..., description="ID of the country this regulator belongs to")
    
    model_config = ConfigDict(json_schema_extra={
        "example": {
            "name": "Securities and Exchange Commission",
            "description": "US financial markets regulator",
            "type": "Financial Regulator",
            "website": "https://www.sec.gov",
            "country_id": 1
        }
    })


class RegulatorUpdate(BaseModel):
    """Schema for regulator updates, all fields optional."""
    
    name: Optional[str] = Field(None, description="Name of the regulatory authority", min_length=1, max_length=255)
    description: Optional[str] = Field(None, description="Description of the regulator")
    type: Optional[str] = Field(None, description="Type of regulator")
    website: Optional[str] = Field(None, description="Official website URL")
    country_id: Optional[int] = Field(None, description="ID of the country this regulator belongs to")
    
    model_config = ConfigDict(json_schema_extra={
        "example": {
            "name": "Securities and Exchange Commission (SEC)",
            "description": "Updated description: US financial markets regulator",
            "type": "Financial Regulator",
            "website": "https://www.sec.gov",
            "country_id": 1
        }
    })


class Regulator(RegulatorBase):
    """Schema for regulator responses."""
    
    id: int = Field(..., description="Unique identifier for the regulator")
    country_id: int = Field(..., description="ID of the country this regulator belongs to")
    created_at: datetime = Field(..., description="Timestamp when the record was created")
    changed_on: datetime = Field(..., description="Timestamp when the record was last updated")
    
    model_config = ConfigDict(from_attributes=True)


class RegulatorWithCountry(Regulator):
    """Schema for regulator responses with country information."""
    
    country: Country = Field(..., description="Country information")


class RegulationURLBase(BaseModel):
    """Base RegulationURL schema."""
    
    url: str = Field(..., description="The regulation URL")
    domain: Optional[str] = Field(None, description="Domain of the URL")
    normalized_url: Optional[str] = Field(None, description="Normalized URL for comparison")
    category: Optional[str] = Field(None, description="Category of the regulation")


class RegulationURLCreate(RegulationURLBase):
    """Schema for regulation URL creation."""
    
    regulator_id: Optional[int] = Field(None, description="ID of the associated regulator")
    confidence_level: float = Field(0.0, description="Confidence level of regulator association (0.0-1.0)", ge=0.0, le=1.0)
    
    model_config = ConfigDict(json_schema_extra={
        "example": {
            "url": "https://www.sec.gov/rules/final/2023/33-11151.pdf",
            "domain": "sec.gov",
            "category": "financial_regulations",
            "regulator_id": 1,
            "confidence_level": 0.9
        }
    })


class RegulationURL(RegulationURLBase):
    """Schema for regulation URL responses."""
    
    id: int = Field(..., description="Unique identifier for the regulation URL")
    regulator_id: Optional[int] = Field(None, description="ID of the associated regulator")
    confidence_level: float = Field(..., description="Confidence level of regulator association (0.0-1.0)")
    created_at: datetime = Field(..., description="Timestamp when the record was created")
    changed_on: datetime = Field(..., description="Timestamp when the record was last updated")
    
    model_config = ConfigDict(from_attributes=True)


class RegulationURLWithRegulator(RegulationURL):
    """Schema for regulation URL responses with regulator information."""
    
    regulator: Optional[RegulatorWithCountry] = Field(None, description="Regulator information")


# Calendar Event Schemas
class CalendarEventExtendedProps(BaseModel):
    """Extended properties for calendar events."""
    category: str
    jurisdiction: str
    description: str
    deadlineType: str
    action: str
    regulationId: int

class CalendarEvent(BaseModel):
    """Calendar event schema for regulatory deadlines and events."""
    title: str
    start: str
    end: str
    backgroundColor: str
    borderColor: str
    extendedProps: CalendarEventExtendedProps
# Add to the bottom of the existing schemas.py file

class CalendarEventBase(BaseModel):
    """Base model for calendar events."""
    title: str
    start: str  # ISO format date
    end: str  # ISO format date
    category: str
    jurisdiction: str
    description: str
    deadlineType: str
    action: str
    regulationId: int

class CalendarEventCreate(CalendarEventBase):
    """Schema for creating a calendar event."""
    pass

class CalendarEvent(CalendarEventBase):
    """Schema for returning a calendar event."""
    id: int
    
    class Config:
        orm_mode = True
