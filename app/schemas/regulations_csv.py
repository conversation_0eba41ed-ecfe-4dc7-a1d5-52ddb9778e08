"""
Schema definitions for regulations_list.csv file structure.

This module provides comprehensive schema definitions, validation rules,
and transformation utilities for the regulations CSV data format with
soft-delete support following the existing codebase patterns.
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any, Union
from enum import Enum
from pydantic import BaseModel, Field, validator, root_validator
from uuid import UUID
import re


class LegalStatus(str, Enum):
    """Legal status enumeration for regulations."""
    BINDING = "Binding"
    NON_BINDING = "Non-binding guidance"
    PENDING = "Pending"
    DRAFT = "Draft"
    SUPERSEDED = "Superseded"


class DocumentType(str, Enum):
    """Document type enumeration for regulations."""
    FEDERAL_LAW = "Federal Law"
    STATE_LAW = "State Law"
    PROVINCIAL_LAW = "Provincial Law"
    LOCAL_LAW = "Local Law"
    NATIONAL_LAW = "National Law"
    EU_REGULATION = "EU Regulation"
    EU_DIRECTIVE = "EU Directive"
    FEDERAL_REGULATION = "Federal Regulation"
    FINANCIAL_REGULATION = "Financial Regulation"
    FINANCIAL_GUIDANCE = "Financial Guidance"
    FINANCIAL_STANDARD = "Financial Standard"
    ADMINISTRATIVE_REGULATION = "Administrative Regulation"
    ADMINISTRATIVE_DIRECTION = "Administrative Direction"
    VOLUNTARY_FRAMEWORK = "Voluntary Framework"
    PENDING_LEGISLATION = "Pending Legislation"
    FINANCIAL_LAW = "Financial Law"
    FINANCIAL_ZONE_LAW = "Financial Zone Law"


class RegulationCSVRecordBase(BaseModel):
    """Base schema for regulation CSV record."""

    # Basic identification fields
    country_name: str = Field(..., description="Full country name")
    country_code: str = Field(..., min_length=2, max_length=3, description="ISO country code")
    document_title: str = Field(..., description="Official title of the regulation")
    document_type: DocumentType = Field(..., description="Type/category of the document")
    issuing_authority: str = Field(..., description="Authority that issued the regulation")

    # Date fields
    publication_date: Optional[date] = Field(None, description="Date when regulation was published")
    effective_date: Optional[date] = Field(None, description="Date when regulation becomes effective")

    # Legal framework
    legal_status: LegalStatus = Field(..., description="Legal binding status")
    document_url: Optional[str] = Field(None, description="URL to the official document")
    language: str = Field(..., description="Primary language(s) of the document")

    # Scope and application
    scope_application: str = Field(..., description="Scope of application and covered entities")
    key_compliance_requirements: str = Field(..., description="Key compliance requirements summary")
    enforcement_mechanisms: str = Field(..., description="Enforcement mechanisms and authorities")
    penalties: str = Field(..., description="Penalty structure and amounts")

    # Cross-border and international aspects
    cross_border_elements: str = Field(..., description="Cross-border applicability and elements")
    extraterritorial_reach: str = Field(..., description="Extraterritorial application scope")
    international_standards_alignment: str = Field(..., description="Alignment with international standards")

    # Data protection and privacy
    data_protection_provisions: str = Field(..., description="Data protection and privacy provisions")
    incident_reporting_requirements: str = Field(..., description="Incident reporting obligations")

    # Risk and compliance management
    risk_management_mandates: str = Field(..., description="Risk management requirements")
    third_party_requirements: str = Field(..., description="Third-party and vendor requirements")
    audit_obligations: str = Field(..., description="Audit and assessment obligations")
    certification_requirements: str = Field(..., description="Certification and accreditation requirements")

    # Implementation and timeline
    implementation_timeline: str = Field(..., description="Implementation timeline and phases")
    safe_harbor_provisions: str = Field(..., description="Safe harbor and compliance protections")

    # Industry and technology focus
    industry_specific_provisions: str = Field(..., description="Industry-specific provisions and requirements")
    technology_specific_provisions: str = Field(..., description="Technology-specific provisions and guidance")

    @validator('country_code')
    def validate_country_code(cls, v):
        """Validate country code format."""
        if not re.match(r'^[A-Z]{2,3}$', v):
            raise ValueError('Country code must be 2-3 uppercase letters')
        return v

    @validator('document_url')
    def validate_url(cls, v):
        """Validate URL format if provided."""
        if v and not re.match(r'^https?://', v):
            raise ValueError('Document URL must start with http:// or https://')
        return v

    @validator('publication_date', 'effective_date', pre=True)
    def parse_date(cls, v):
        """Parse date strings into date objects."""
        if isinstance(v, str) and v.strip():
            try:
                return datetime.strptime(v, '%Y-%m-%d').date()
            except ValueError:
                try:
                    return datetime.strptime(v, '%Y-%m-%d').date()
                except ValueError:
                    return None
        return v

    @root_validator
    def validate_dates(cls, values):
        """Validate date relationships."""
        pub_date = values.get('publication_date')
        eff_date = values.get('effective_date')

        if pub_date and eff_date and pub_date > eff_date:
            raise ValueError('Publication date cannot be after effective date')

        return values


class RegulationCSVRecordCreate(RegulationCSVRecordBase):
    """Schema for creating a new regulation CSV record."""
    pass


class RegulationCSVRecord(RegulationCSVRecordBase):
    """Schema for regulation CSV record with database fields."""
    
    id: UUID = Field(..., description="Unique identifier")
    created_at: datetime = Field(..., description="Creation timestamp")
    changed_on: datetime = Field(..., description="Last update timestamp")
    is_deleted: bool = Field(False, description="Soft delete flag")
    deleted_at: Optional[datetime] = Field(None, description="Deletion timestamp")
    deleted_by_id: Optional[UUID] = Field(None, description="ID of user who deleted the record")
    
    # Additional metadata
    import_batch_id: Optional[str] = Field(None, description="Batch ID for tracking imports")
    data_quality_score: Optional[float] = Field(None, description="Data quality assessment score")
    validation_errors: Optional[List[str]] = Field(None, description="List of validation errors")
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True
        use_enum_values = True
        validate_assignment = True


class RegulationCSVRecordUpdate(BaseModel):
    """Schema for updating regulation CSV records."""
    
    country_name: Optional[str] = None
    country_code: Optional[str] = None
    document_title: Optional[str] = None
    document_type: Optional[DocumentType] = None
    issuing_authority: Optional[str] = None
    publication_date: Optional[date] = None
    effective_date: Optional[date] = None
    legal_status: Optional[LegalStatus] = None
    document_url: Optional[str] = None
    language: Optional[str] = None
    scope_application: Optional[str] = None
    key_compliance_requirements: Optional[str] = None
    enforcement_mechanisms: Optional[str] = None
    penalties: Optional[str] = None
    cross_border_elements: Optional[str] = None
    extraterritorial_reach: Optional[str] = None
    international_standards_alignment: Optional[str] = None
    data_protection_provisions: Optional[str] = None
    incident_reporting_requirements: Optional[str] = None
    risk_management_mandates: Optional[str] = None
    third_party_requirements: Optional[str] = None
    audit_obligations: Optional[str] = None
    certification_requirements: Optional[str] = None
    implementation_timeline: Optional[str] = None
    safe_harbor_provisions: Optional[str] = None
    industry_specific_provisions: Optional[str] = None
    technology_specific_provisions: Optional[str] = None
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True


class RegulationCSVBatch(BaseModel):
    """Schema for batch operations on regulation CSV records."""
    
    batch_id: str = Field(..., description="Unique batch identifier")
    records: List[RegulationCSVRecordCreate] = Field(..., description="List of records to process")
    operation: str = Field(..., description="Batch operation type (import, update, delete)")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional batch metadata")
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True


class RegulationCSVImportResult(BaseModel):
    """Schema for CSV import operation results."""
    
    batch_id: str = Field(..., description="Batch identifier")
    total_records: int = Field(..., description="Total records processed")
    successful_imports: int = Field(..., description="Successfully imported records")
    failed_imports: int = Field(..., description="Failed import records")
    updated_records: int = Field(..., description="Updated existing records")
    errors: List[Dict[str, Any]] = Field(default_factory=list, description="Import errors")
    warnings: List[Dict[str, Any]] = Field(default_factory=list, description="Import warnings")
    processing_time: float = Field(..., description="Processing time in seconds")
    
    class Config:
        """Pydantic configuration."""
        validate_assignment = True


class RegulationCSVExportRequest(BaseModel):
    """Schema for CSV export requests."""
    
    filters: Optional[Dict[str, Any]] = Field(None, description="Export filters")
    include_deleted: bool = Field(False, description="Include soft-deleted records")
    format_options: Optional[Dict[str, Any]] = Field(None, description="Export format options")
    
    class Config:
        """Pydantic configuration."""
        validate_assignment = True
