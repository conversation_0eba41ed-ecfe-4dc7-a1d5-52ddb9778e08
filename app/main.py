"""FastAPI application entry point with proper type hints and docstrings."""
import os
import time
from datetime import datetime
from typing import List, Optional, Dict, Any, Callable

from fastapi import FastAPI, Depends, HTTPException, Path, Query, Request
from fastapi.responses import JSONResponse, HTMLResponse, RedirectResponse
from fastapi.middleware.wsgi import WSGIMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
import os

print("Loading FastAPI app modules...")

# Set up templates directory
templates_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "templates")
templates = Jinja2Templates(directory=templates_dir)
startup_time = time.time()

# Import in a specific order to avoid circular imports
from app.db.database import engine
print(f"  Loaded database engine in {time.time() - startup_time:.2f}s")
module_time = time.time()

from app.db.models import Base
print(f"  Loaded Base in {time.time() - module_time:.2f}s")
module_time = time.time()

from app.db import models
print(f"  Loaded models in {time.time() - module_time:.2f}s")
module_time = time.time()

from app.schemas import schemas
print(f"  Loaded schemas in {time.time() - module_time:.2f}s")
module_time = time.time()

from app.db import get_db
print(f"  Loaded database session in {time.time() - module_time:.2f}s")
module_time = time.time()

from app.i18n import get_translator, get_locale
print(f"  Loaded i18n in {time.time() - module_time:.2f}s")
module_time = time.time()

from app.visualization import worldmap
print(f"  Loaded visualization in {time.time() - module_time:.2f}s")
module_time = time.time()

from app.admin import get_admin_app
print(f"  Loaded admin in {time.time() - module_time:.2f}s")
module_time = time.time()

from app.middleware.i18n_middleware import I18nMiddleware
from app.i18n.translations import get_translations_context

print("Creating database tables...")
table_time = time.time()
# Create database tables
Base.metadata.create_all(bind=engine)
print(f"Database tables created in {time.time() - table_time:.2f}s")

app = FastAPI(
    title="RegulationGuru API",
    description="Comprehensive API for regulatory compliance tracking, analysis, and management",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    openapi_tags=[
        {"name": "compliance", "description": "Endpoints for compliance management and tracking"},
        {"name": "impact", "description": "Impact assessment of regulatory changes"},
        {"name": "alerts", "description": "Regulatory alerts and notifications"},
        {"name": "trends", "description": "Trend analysis for regulatory data"},
        {"name": "digests", "description": "Scheduled regulatory digests"},
        {"name": "data_collection", "description": "Collection of regulatory data"},
        {"name": "document_import", "description": "Import of regulatory documents"},
        {"name": "document_analysis", "description": "Analysis of regulatory documents"},
        {"name": "bulk_processor", "description": "Bulk processing of regulatory data"},
        {"name": "data_enrichment", "description": "Enrichment of regulatory data"},
        {"name": "relationship_mapping", "description": "Mapping relationships between regulatory entities"},
        {"name": "integrations", "description": "Integrations with external systems"},
        {"name": "benchmarks", "description": "Regulatory compliance benchmarks"},
        {"name": "countries", "description": "Country-specific regulatory information"},
        {"name": "regulators", "description": "Information about regulatory authorities"},
        {"name": "regulations", "description": "Regulatory information and URLs"},
        {"name": "items", "description": "Generic item management endpoints"},
        {"name": "data_sources", "description": "Public and external regulatory data sources"}
    ]
)

# Add i18n middleware
app.add_middleware(I18nMiddleware)

# Mount the admin interface under /ui/manage
app.mount("/ui/manage", WSGIMiddleware(get_admin_app()))

# Mount static files if directory exists
if os.path.exists("statics"):
    app.mount("/static", StaticFiles(directory="statics"), name="static")

# Import the SwaggerUI middleware
from app.middleware.swagger_ui import SwaggerUIMiddleware

# Add SwaggerUI dark mode middleware
app.add_middleware(SwaggerUIMiddleware)

@app.middleware("http")
async def locale_middleware(request: Request, call_next):
    """
    Middleware to handle locale selection.

    Args:
        request (Request): The FastAPI request
        call_next: The next middleware or endpoint handler

    Returns:
        Response: The response from the endpoint
    """
    response = await call_next(request)

    # Set the language in a cookie if specified in query params
    lang = request.query_params.get("lang")
    if lang and lang in ["en_US", "en_GB", "es", "de", "af", "zu"]:
        response.set_cookie(key="lang", value=lang, max_age=86400 * 30)  # 30 days

    return response

@app.middleware("http")
async def add_translations_to_context(request: Request, call_next):
    # Make templates context available to all templates
    request.state.translations_context = get_translations_context(request)
    response = await call_next(request)
    return response

# Root route to render the dashboard
@app.get("/", response_class=HTMLResponse)
async def dashboard(request: Request, translator: Callable = Depends(get_translator)):
    """
    Handle root endpoint requests, showing the RegulationGuru dashboard.

    Args:
        request (Request): The FastAPI request
        translator (Callable): Translation function

    Returns:
        HTMLResponse: A formatted HTML page with RegulationGuru dashboard
    """
    # Pass data to the template
    welcome_message = translator("Welcome to RegulationGuru - Your global regulatory compliance platform")

    # Return the rendered template
    context = {
        "request": request,
        "welcome_message": welcome_message,
        "show_api_docs": True,
        **request.state.translations_context
    }
    return templates.TemplateResponse(
        "dashboard.html",
        context
    )

@app.get("/regulatory_dashboard", response_class=HTMLResponse)
async def regulatory_dashboard(request: Request):
    context = {
        "request": request,
        **request.state.translations_context
    }
    return templates.TemplateResponse("regulatory_dashboard.html", context)

# API health check
@app.get("/health", include_in_schema=False)
async def health_check_redirect():
    """Redirect /health to /api/v1/health"""
    return RedirectResponse(url="/api/v1/health")

@app.get("/api/v1/health")
async def health_check(request: Request, db: Session = Depends(get_db)):
    """
    Check the health status of the application and database connection.

    Args:
        request (Request): The FastAPI request
        db (Session): Database session dependency

    Returns:
        dict: Health status information with test coverage metrics
    """
    # Perform a simple database query to check connection
    try:
        from sqlalchemy import text
        db.execute(text("SELECT 1"))
        db_status = "healthy"
    except Exception as e:
        db_status = f"unhealthy: {str(e)}"

    # Log the health check access
    current_time = datetime.utcnow()
    client_host = request.client.host if request.client else "unknown"
    print(f"[{current_time}] Health check accessed by {client_host} - Database status: {db_status}")

    # For a more permanent solution, we could write to a file
    with open("health_check_log.txt", "a") as log_file:
        log_file.write(f"[{current_time}] IP: {client_host} - DB: {db_status}\n")

    # Get coverage data from HTML files if available
    coverage_data = {}
    html_dir = "coverage/html"
    if os.path.exists(html_dir):
        try:
            # Extract coverage for main files and additional files
            main_files = ["main_py.html", "models_py.html", "regulations_py.html", "schemas_py.html", "worldmap_py.html"]
            import re
            pattern = r'<title>Coverage for .+?: (\d+)%</title>'

            for file in main_files:
                file_path = os.path.join(html_dir, file)
                if os.path.exists(file_path):
                    with open(file_path, 'r') as f:
                        content = f.read()
                        match = re.search(pattern, content)
                        if match:
                            module_name = file.replace("_py.html", ".py")
                            coverage_data[module_name] = int(match.group(1))

            # Calculate overall coverage if data is available
            if coverage_data:
                total = sum(coverage_data.values())
                count = len(coverage_data)
                coverage_data["overall"] = round(total / count, 1)
        except Exception as e:
            coverage_data["error"] = str(e)

    return {
        "status": "healthy",
        "timestamp": current_time,
        "database": db_status,
        "test_coverage": coverage_data
    }

# API v1 base path that redirects to the docs
@app.get("/api/v1", response_class=RedirectResponse, include_in_schema=False)
async def api_root():
    """
    API v1 root endpoint, redirects to API documentation.
    """
    return RedirectResponse(url="/api/v1/docs")

# Import all API routers
from app.api import compliance
from app.api import impact_assessment
from app.api import regulatory_alerts
from app.api import trend_analysis
from app.api import scheduled_digests
from app.api import data_collection
from app.api import document_import
from app.api import document_analysis
from app.api import bulk_processor
from app.api import data_enrichment
from app.api.relationship_mapping import router as relationship_mapping_router
from app.api.integrations.router import router as integrations_router
from app.api import benchmarks
from app.api.pdf_analysis import router as pdf_analysis_router
from app.api.alerts import router as alerts_router
from app.api.routes import router as page_routes
from app.api.data_sources.router import router as data_sources_router

# Include all API routers with proper prefixes
app.include_router(compliance.router, prefix="/api/v1/compliance", tags=["compliance"])
app.include_router(impact_assessment.router, prefix="/api/v1/impact", tags=["impact"])
app.include_router(regulatory_alerts.router, prefix="/api/v1/alerts", tags=["alerts"])
app.include_router(trend_analysis.router, prefix="/api/v1/trends", tags=["trends"])
app.include_router(scheduled_digests.router, prefix="/api/v1/digests", tags=["digests"])
app.include_router(data_collection.router, prefix="/api/v1/collect", tags=["data_collection"])
app.include_router(document_import.router, prefix="/api/v1/document_import", tags=["document_import"])
app.include_router(document_analysis.router, prefix="/api/v1/document_analysis", tags=["document_analysis"])
app.include_router(bulk_processor.router, prefix="/api/v1/bulk_processor", tags=["bulk_processor"])
app.include_router(data_enrichment.router, prefix="/api/v1/data_enrichment", tags=["data_enrichment"])
app.include_router(relationship_mapping_router, prefix="/api/v1/relationships", tags=["relationship_mapping"])
app.include_router(integrations_router, prefix="/api/v1/integrations", tags=["integrations"])
app.include_router(benchmarks.router, prefix="/api/v1/benchmarks", tags=["benchmarks"])
app.include_router(pdf_analysis_router, prefix="/api/v1/pdf", tags=["pdf_analysis"])
app.include_router(alerts_router, prefix="/api/v1/alerts", tags=["regulatory_alerts"])
app.include_router(data_sources_router, prefix="/api/v1/data-sources", tags=["data_sources"])

# Country API Endpoints
@app.get("/api/v1/countries/", response_model=List[schemas.Country])
async def get_countries(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """
    Get a list of countries.

    Args:
        skip (int): Number of records to skip
        limit (int): Maximum number of records to return
        db (Session): Database session

    Returns:
        List[schemas.Country]: List of countries
    """
    return db.query(models.Country).offset(skip).limit(limit).all()


@app.get("/api/v1/regulators/", response_model=List[schemas.RegulatorWithCountry])
async def get_regulators(
    skip: int = 0,
    limit: int = 100,
    country_id: Optional[int] = None,
    type: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Get a list of regulators.

    Args:
        skip (int): Number of records to skip
        limit (int): Maximum number of records to return
        country_id (Optional[int]): Filter by country ID
        type (Optional[str]): Filter by regulator type
        db (Session): Database session

    Returns:
        List[schemas.RegulatorWithCountry]: List of regulators with country info
    """
    query = db.query(models.Regulator)

    if country_id:
        query = query.filter(models.Regulator.country_id == country_id)

    if type:
        query = query.filter(models.Regulator.type == type)

    return query.offset(skip).limit(limit).all()


@app.get("/api/v1/regulators/{regulator_id}", response_model=schemas.RegulatorWithCountry)
async def get_regulator(
    regulator_id: int = Path(..., title="The ID of the regulator to retrieve"),
    db: Session = Depends(get_db)
):
    """
    Retrieve regulator information by ID.

    Args:
        regulator_id (int): The ID of the regulator to retrieve
        db (Session): Database session dependency

    Returns:
        schemas.RegulatorWithCountry: The requested regulator

    Raises:
        HTTPException: If the regulator is not found
    """
    db_regulator = db.query(models.Regulator).filter(models.Regulator.id == regulator_id).first()

    if db_regulator is None:
        raise HTTPException(status_code=404, detail="Regulator not found")

    return db_regulator


@app.post("/api/v1/regulators/", response_model=schemas.RegulatorWithCountry, status_code=201)
async def create_regulator(
    regulator: schemas.RegulatorCreate,
    db: Session = Depends(get_db)
):
    """
    Create a new regulator.

    Args:
        regulator (schemas.RegulatorCreate): The regulator data to create
        db (Session): Database session dependency

    Returns:
        schemas.RegulatorWithCountry: The created regulator

    Raises:
        HTTPException: If the country does not exist or regulator already exists
    """
    # Check if country exists
    db_country = db.query(models.Country).filter(models.Country.id == regulator.country_id).first()
    if db_country is None:
        raise HTTPException(status_code=404, detail="Country not found")

    # Check if regulator with the same name already exists for this country
    existing_regulator = db.query(models.Regulator).filter(
        models.Regulator.name == regulator.name,
        models.Regulator.country_id == regulator.country_id
    ).first()

    if existing_regulator:
        raise HTTPException(status_code=400, detail="Regulator with this name already exists for this country")

    # Create new regulator
    db_regulator = models.Regulator(**regulator.model_dump())
    db.add(db_regulator)
    db.commit()
    db.refresh(db_regulator)

    return db_regulator


@app.put("/api/v1/regulators/{regulator_id}", response_model=schemas.RegulatorWithCountry)
async def update_regulator(
    regulator_id: int,
    regulator: schemas.RegulatorUpdate,
    db: Session = Depends(get_db)
):
    """
    Update an existing regulator.

    Args:
        regulator_id (int): The ID of the regulator to update
        regulator (schemas.RegulatorUpdate): The updated regulator data
        db (Session): Database session dependency

    Returns:
        schemas.RegulatorWithCountry: The updated regulator

    Raises:
        HTTPException: If the regulator is not found or validation fails
    """
    db_regulator = db.query(models.Regulator).filter(models.Regulator.id == regulator_id).first()

    if db_regulator is None:
        raise HTTPException(status_code=404, detail="Regulator not found")

    # Check if country exists if it's being updated
    if regulator.country_id is not None:
        db_country = db.query(models.Country).filter(models.Country.id == regulator.country_id).first()
        if db_country is None:
            raise HTTPException(status_code=404, detail="Country not found")

    # Check for name conflicts if name is being updated
    if regulator.name is not None and regulator.name != db_regulator.name:
        country_id = regulator.country_id if regulator.country_id is not None else db_regulator.country_id
        existing_regulator = db.query(models.Regulator).filter(
            models.Regulator.name == regulator.name,
            models.Regulator.country_id == country_id,
            models.Regulator.id != regulator_id
        ).first()

        if existing_regulator:
            raise HTTPException(status_code=400, detail="Regulator with this name already exists for this country")

    # Update regulator
    update_data = regulator.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_regulator, key, value)

    db.commit()
    db.refresh(db_regulator)

    return db_regulator


@app.delete("/api/v1/regulators/{regulator_id}", response_model=schemas.RegulatorWithCountry)
async def delete_regulator(
    regulator_id: int,
    db: Session = Depends(get_db)
):
    """
    Delete a regulator.

    Args:
        regulator_id (int): The ID of the regulator to delete
        db (Session): Database session dependency

    Returns:
        schemas.RegulatorWithCountry: The deleted regulator

    Raises:
        HTTPException: If the regulator is not found
    """
    db_regulator = db.query(models.Regulator).filter(models.Regulator.id == regulator_id).first()

    if db_regulator is None:
        raise HTTPException(status_code=404, detail="Regulator not found")

    # Check if there are any regulation URLs associated with this regulator
    regulation_count = db.query(models.RegulationURL).filter(
        models.RegulationURL.regulator_id == regulator_id
    ).count()

    if regulation_count > 0:
        raise HTTPException(
            status_code=400,
            detail=f"Cannot delete regulator with {regulation_count} associated regulations. Remove the associations first."
        )

    db.delete(db_regulator)
    db.commit()

    return db_regulator


@app.get("/api/v1/regulations/", response_model=List[schemas.RegulationURLWithRegulator])
async def get_regulations(
    skip: int = 0,
    limit: int = 100,
    country_id: Optional[int] = None,
    regulator_id: Optional[int] = None,
    category: Optional[str] = None,
    min_confidence: float = 0.0,
    db: Session = Depends(get_db)
):
    """
    Get a list of regulation URLs.

    Args:
        skip (int): Number of records to skip
        limit (int): Maximum number of records to return
        country_id (Optional[int]): Filter by country ID
        regulator_id (Optional[int]): Filter by regulator ID
        category (Optional[str]): Filter by regulation category
        min_confidence (float): Minimum confidence level (0.0-1.0)
        db (Session): Database session

    Returns:
        List[schemas.RegulationURLWithRegulator]: List of regulation URLs with regulator info
    """
    query = db.query(models.RegulationURL)

    if regulator_id:
        query = query.filter(models.RegulationURL.regulator_id == regulator_id)

    if country_id:
        query = query.join(models.Regulator).filter(models.Regulator.country_id == country_id)

    if category:
        query = query.filter(models.RegulationURL.category == category)

    if min_confidence > 0.0:
        query = query.filter(models.RegulationURL.confidence_level >= min_confidence)

    return query.offset(skip).limit(limit).all()


@app.get("/api/v1/regulations/{regulation_id}", response_model=schemas.RegulationURLWithRegulator)
async def get_regulation(
    regulation_id: int,
    db: Session = Depends(get_db)
):
    """
    Get a specific regulation URL by ID.

    Args:
        regulation_id (int): ID of the regulation URL
        db (Session): Database session

    Returns:
        schemas.RegulationURLWithRegulator: Regulation URL with regulator info

    Raises:
        HTTPException: If regulation URL not found
    """
    regulation = db.query(models.RegulationURL).filter(models.RegulationURL.id == regulation_id).first()

    if not regulation:
        raise HTTPException(status_code=404, detail="Regulation URL not found")

    return regulation

@app.get("/api/v1/worldmap", response_class=HTMLResponse)
async def get_world_map(
    lat: float = Query(0.0, description="Center latitude"),
    lon: float = Query(0.0, description="Center longitude"),
    zoom: int = Query(2, description="Zoom level")
):
    """
    Generate a world map with the specified center and zoom level.

    Args:
        lat (float): Center latitude
        lon (float): Center longitude
        zoom (int): Zoom level

    Returns:
        HTMLResponse: HTML content of the map
    """
    # Create a basic world map
    map_obj = worldmap.create_world_map(
        center=(lat, lon),
        zoom_start=zoom
    )

    # Return the map as HTML
    return worldmap.get_map_html(map_obj)


@app.get("/api/v1/languages")
async def get_languages():
    """
    Get available languages.

    Returns:
        dict: List of supported languages
    """
    return {
        "current_language": get_locale(),
        "available_languages": {
            "en_US": "English (US)",
            "en_GB": "English (UK)",
            "es": "Español",
            "de": "Deutsch",
            "af": "Afrikaans",
            "zu": "isiZulu"
        }
    }

# Item API endpoints
@app.post("/api/v1/items/", response_model=schemas.Item)
async def create_item(item: schemas.ItemCreate, db: Session = Depends(get_db)):
    """
    Create a new item.

    Args:
        item (schemas.ItemCreate): The item data to create
        db (Session): Database session dependency

    Returns:
        models.Item: The created item
    """
    db_item = models.Item(**item.model_dump())
    db.add(db_item)
    db.commit()
    db.refresh(db_item)
    return db_item


@app.get("/api/v1/items/", response_model=List[schemas.Item])
async def read_items(
    skip: int = 0,
    limit: int = 100,
    include_deleted: bool = False,
    db: Session = Depends(get_db)
):
    """
    Retrieve a list of items with pagination.

    Args:
        skip (int): Number of records to skip
        limit (int): Maximum number of records to return
        include_deleted (bool): Whether to include soft-deleted items
        db (Session): Database session dependency

    Returns:
        List[models.Item]: List of items
    """
    query = db.query(models.Item)

    if not include_deleted:
        query = query.filter(models.Item.is_deleted == False)

    return query.offset(skip).limit(limit).all()


@app.get("/api/v1/items/{item_id}", response_model=schemas.Item)
async def read_item(
    item_id: int = Path(..., title="The ID of the item to retrieve"),
    db: Session = Depends(get_db)
):
    """
    Retrieve item information by ID.

    Args:
        item_id (int): The ID of the item to retrieve
        db (Session): Database session dependency

    Returns:
        models.Item: The requested item

    Raises:
        HTTPException: If the item is not found
    """
    db_item = db.query(models.Item).filter(
        models.Item.id == item_id,
        models.Item.is_deleted == False
    ).first()

    if db_item is None:
        raise HTTPException(status_code=404, detail="Item not found")

    return db_item


@app.put("/api/v1/items/{item_id}", response_model=schemas.Item)
async def update_item(
    item_id: int,
    item: schemas.ItemCreate,
    db: Session = Depends(get_db)
):
    """
    Update an existing item.

    Args:
        item_id (int): The ID of the item to update
        item (schemas.ItemCreate): The updated item data
        db (Session): Database session dependency

    Returns:
        models.Item: The updated item

    Raises:
        HTTPException: If the item is not found
    """
    db_item = db.query(models.Item).filter(
        models.Item.id == item_id,
        models.Item.is_deleted == False
    ).first()

    if db_item is None:
        raise HTTPException(status_code=404, detail="Item not found")

    for key, value in item.model_dump().items():
        setattr(db_item, key, value)

    db.commit()
    db.refresh(db_item)
    return db_item


@app.delete("/api/v1/items/{item_id}", response_model=schemas.Item)
async def delete_item(
    item_id: int,
    db: Session = Depends(get_db),
    hard_delete: bool = False
):
    """
    Delete an item (soft delete by default, hard delete if specified).

    Args:
        item_id (int): The ID of the item to delete
        db (Session): Database session dependency
        hard_delete (bool): Whether to permanently delete the item

    Returns:
        models.Item: The deleted item

    Raises:
        HTTPException: If the item is not found
    """
    db_item = db.query(models.Item).filter(
        models.Item.id == item_id,
        models.Item.is_deleted == False
    ).first()

    if db_item is None:
        raise HTTPException(status_code=404, detail="Item not found")

    if hard_delete:
        db.delete(db_item)
    else:
        db_item.soft_delete()

    db.commit()
    return db_item


@app.post("/api/v1/items/{item_id}/restore", response_model=schemas.Item)
async def restore_item(
    item_id: int,
    db: Session = Depends(get_db)
):
    """
    Restore a soft-deleted item.

    Args:
        item_id (int): The ID of the item to restore
        db (Session): Database session dependency

    Returns:
        models.Item: The restored item

    Raises:
        HTTPException: If the item is not found or not deleted
    """
    db_item = db.query(models.Item).filter(
        models.Item.id == item_id,
        models.Item.is_deleted == True
    ).first()

    if db_item is None:
        raise HTTPException(status_code=404, detail="Deleted item not found")

    db_item.is_deleted = False
    db_item.deleted_at = None
    db.commit()
    db.refresh(db_item)
    return db_item

# Include page routes
app.include_router(page_routes)