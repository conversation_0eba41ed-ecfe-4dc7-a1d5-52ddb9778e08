"""FastAPI application entry point with proper type hints and docstrings."""
import os
import time
from datetime import datetime
from typing import List, Optional, Dict, Any, Callable

from fastapi import FastAPI, Depends, HTTPException, Path, Query, Request
from fastapi.responses import JSONResponse, HTMLResponse, RedirectResponse
from fastapi.middleware.wsgi import WSGIMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
import os

print("Loading FastAPI app modules...")

# Set up templates directory
templates_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "templates")
templates = Jinja2Templates(directory=templates_dir)
startup_time = time.time()

# Import in a specific order to avoid circular imports
from app.db.database import engine
print(f"  Loaded database engine in {time.time() - startup_time:.2f}s")
module_time = time.time()

from app.db.models import Base
print(f"  Loaded Base in {time.time() - module_time:.2f}s")
module_time = time.time()

from app.db import models
print(f"  Loaded models in {time.time() - module_time:.2f}s")
module_time = time.time()

# TODO: Temporarily disabled due to forward reference issues
# from app.schemas import schemas
# print(f"  Loaded schemas in {time.time() - module_time:.2f}s")
# module_time = time.time()

from app.db import get_db
print(f"  Loaded database session in {time.time() - module_time:.2f}s")
module_time = time.time()

from app.i18n import get_translator, get_locale
print(f"  Loaded i18n in {time.time() - module_time:.2f}s")
module_time = time.time()

from app.visualization import worldmap
print(f"  Loaded visualization in {time.time() - module_time:.2f}s")
module_time = time.time()

from app.admin import get_admin_app
print(f"  Loaded admin in {time.time() - module_time:.2f}s")
module_time = time.time()

from app.middleware.i18n_middleware import I18nMiddleware
from app.middleware.caching_middleware import CachingMiddleware
from app.i18n.translations import get_translations_context

print("Creating database tables...")
table_time = time.time()
# Create database tables
Base.metadata.create_all(bind=engine)
print(f"Database tables created in {time.time() - table_time:.2f}s")

app = FastAPI(
    title="RegulationGuru API",
    description="Comprehensive API for regulatory compliance tracking, analysis, and management",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    openapi_tags=[
        {"name": "compliance", "description": "Endpoints for compliance management and tracking"},
        {"name": "impact", "description": "Impact assessment of regulatory changes"},
        {"name": "alerts", "description": "Regulatory alerts and notifications"},
        {"name": "trends", "description": "Trend analysis for regulatory data"},
        {"name": "digests", "description": "Scheduled regulatory digests"},
        {"name": "data_collection", "description": "Collection of regulatory data"},
        {"name": "document_import", "description": "Import of regulatory documents"},
        {"name": "document_analysis", "description": "Analysis of regulatory documents"},
        {"name": "bulk_processor", "description": "Bulk processing of regulatory data"},
        {"name": "data_enrichment", "description": "Enrichment of regulatory data"},
        {"name": "relationship_mapping", "description": "Mapping relationships between regulatory entities"},
        {"name": "integrations", "description": "Integrations with external systems"},
        {"name": "benchmarks", "description": "Regulatory compliance benchmarks"},
        {"name": "countries", "description": "Country-specific regulatory information"},
        {"name": "regulators", "description": "Information about regulatory authorities"},
        {"name": "regulations", "description": "Regulatory information and URLs"},
        {"name": "items", "description": "Generic item management endpoints"},
        {"name": "data_sources", "description": "Public and external regulatory data sources"},
        {"name": "calendar", "description": "Compliance calendar and deadline management"},
        {"name": "ai", "description": "AI-powered regulatory information search and analysis"},
        {"name": "compliance_requirements", "description": "Compliance requirements management"},
        {"name": "regulation_documents", "description": "Regulation document management"},
        {"name": "regulation_tags", "description": "Regulation tag management"},

        {"name": "industries", "description": "Industry management"}
    ]
)

# Add i18n middleware
app.add_middleware(I18nMiddleware)

# Add caching middleware
app.add_middleware(
    CachingMiddleware,
    ttl=300,  # 5 minutes cache TTL
    exclude_paths=["/api/v1/health", "/api/v1/auth"],
    exclude_methods=["POST", "PUT", "DELETE", "PATCH"],
    vary_by_headers=["accept", "accept-language"],
    vary_by_query_params=["skip", "limit", "search", "status", "category_id", "industry_id", "tag_id"]
)

# Mount the admin interface under /ui/manage
app.mount("/ui/manage", WSGIMiddleware(get_admin_app()))

# Mount static files if directory exists
if os.path.exists("statics"):
    app.mount("/static", StaticFiles(directory="statics"), name="static")

# Mount Sphinx documentation
docs_html_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "docs", "_build", "html")
if os.path.exists(docs_html_dir):
    app.mount("/docs", StaticFiles(directory=docs_html_dir, html=True), name="docs")
    print(f"  Mounted Sphinx documentation at /docs from {docs_html_dir}")
else:
    print(f"  Warning: Sphinx documentation not found at {docs_html_dir}")
    print("  Run 'cd docs && make html' to build the documentation")

# Import the SwaggerUI middleware
from app.middleware.swagger_ui import SwaggerUIMiddleware

# Add SwaggerUI dark mode middleware
app.add_middleware(SwaggerUIMiddleware)

# Set custom OpenAPI schema
app.openapi = lambda: custom_openapi(app)

@app.middleware("http")
async def locale_middleware(request: Request, call_next):
    """
    Middleware to handle locale selection.

    Args:
        request (Request): The FastAPI request
        call_next: The next middleware or endpoint handler

    Returns:
        Response: The response from the endpoint
    """
    response = await call_next(request)

    # Set the language in a cookie if specified in query params
    lang = request.query_params.get("lang")
    if lang and lang in ["en_US", "en_GB", "es", "de", "af", "zu"]:
        response.set_cookie(key="lang", value=lang, max_age=86400 * 30)  # 30 days

    return response

@app.middleware("http")
async def add_translations_to_context(request: Request, call_next):
    # Make templates context available to all templates
    request.state.translations_context = get_translations_context(request)
    response = await call_next(request)
    return response

# Root route to render the dashboard
@app.get("/", response_class=HTMLResponse)
async def dashboard(request: Request, translator: Callable = Depends(get_translator)):
    """
    Handle root endpoint requests, showing the RegulationGuru dashboard.

    Args:
        request (Request): The FastAPI request
        translator (Callable): Translation function

    Returns:
        HTMLResponse: A formatted HTML page with RegulationGuru dashboard
    """
    # Pass data to the template
    welcome_message = translator("Welcome to RegulationGuru - Your global regulatory compliance platform")

    # Return the rendered template
    context = {
        "request": request,
        "welcome_message": welcome_message,
        "show_api_docs": True,
        **request.state.translations_context
    }
    return templates.TemplateResponse(
        "dashboard.html",
        context
    )

@app.get("/regulatory_dashboard", response_class=HTMLResponse)
async def regulatory_dashboard(request: Request):
    context = {
        "request": request,
        **request.state.translations_context
    }
    return templates.TemplateResponse("regulatory_dashboard.html", context)

@app.get("/enhanced-dashboard", response_class=HTMLResponse)
async def enhanced_dashboard(request: Request):
    """Enhanced dashboard with advanced features"""
    context = {
        "request": request,
        "dashboard_type": "enhanced",
        **request.state.translations_context
    }
    return templates.TemplateResponse("dashboard.html", context)

@app.get("/modern-dashboard", response_class=HTMLResponse)
async def modern_dashboard(request: Request):
    """Modern dashboard with contemporary UI"""
    context = {
        "request": request,
        "dashboard_type": "modern",
        **request.state.translations_context
    }
    return templates.TemplateResponse("dashboard.html", context)

@app.get("/interactive-dashboard", response_class=HTMLResponse)
async def interactive_dashboard(request: Request):
    """Interactive dashboard with dynamic features"""
    context = {
        "request": request,
        "dashboard_type": "interactive",
        **request.state.translations_context
    }
    return templates.TemplateResponse("dashboard.html", context)

@app.get("/regulatory-map", response_class=HTMLResponse)
async def regulatory_map(request: Request):
    """Enhanced Regulatory Map visualization"""
    context = {
        "request": request,
        **request.state.translations_context
    }
    return templates.TemplateResponse("regulatory_map.html", context)

@app.get("/regulatory-analytics", response_class=HTMLResponse)
async def regulatory_analytics(request: Request):
    """Regulatory Analytics Dashboard"""
    context = {
        "request": request,
        **request.state.translations_context
    }
    return templates.TemplateResponse("regulatory_analytics.html", context)

@app.get("/documentation", response_class=RedirectResponse)
async def documentation_redirect():
    """Redirect to Sphinx documentation"""
    return RedirectResponse(url="/docs/")

@app.get("/docs/", response_class=RedirectResponse, include_in_schema=False)
async def docs_index_redirect():
    """Redirect to documentation index"""
    return RedirectResponse(url="/docs/index.html")

# API health check
@app.get("/health", include_in_schema=False)
async def health_check_redirect():
    """Redirect /health to /api/v1/health"""
    return RedirectResponse(url="/api/v1/health")

@app.get("/api/v1/health")
async def health_check(request: Request, db: Session = Depends(get_db)):
    """
    Check the health status of the application and database connection.

    Args:
        request (Request): The FastAPI request
        db (Session): Database session dependency

    Returns:
        dict: Health status information with test coverage metrics
    """
    # Perform a simple database query to check connection
    try:
        from sqlalchemy import text
        db.execute(text("SELECT 1"))
        db_status = "healthy"
    except Exception as e:
        db_status = f"unhealthy: {str(e)}"

    # Log the health check access
    current_time = datetime.utcnow()
    client_host = request.client.host if request.client else "unknown"
    print(f"[{current_time}] Health check accessed by {client_host} - Database status: {db_status}")

    # For a more permanent solution, we could write to a file
    with open("health_check_log.txt", "a") as log_file:
        log_file.write(f"[{current_time}] IP: {client_host} - DB: {db_status}\n")

    # Get coverage data from HTML files if available
    coverage_data = {}
    html_dir = "coverage/html"
    if os.path.exists(html_dir):
        try:
            # Extract coverage for main files and additional files
            main_files = ["main_py.html", "models_py.html", "regulations_py.html", "schemas_py.html", "worldmap_py.html"]
            import re
            pattern = r'<title>Coverage for .+?: (\d+)%</title>'

            for file in main_files:
                file_path = os.path.join(html_dir, file)
                if os.path.exists(file_path):
                    with open(file_path, 'r') as f:
                        content = f.read()
                        match = re.search(pattern, content)
                        if match:
                            module_name = file.replace("_py.html", ".py")
                            coverage_data[module_name] = int(match.group(1))

            # Calculate overall coverage if data is available
            if coverage_data:
                total = sum(coverage_data.values())
                count = len(coverage_data)
                coverage_data["overall"] = round(total / count, 1)
        except Exception as e:
            coverage_data["error"] = str(e)

    return {
        "status": "healthy",
        "timestamp": current_time,
        "database": db_status,
        "test_coverage": coverage_data
    }

# API v1 base path that redirects to the docs
@app.get("/api/v1", response_class=RedirectResponse, include_in_schema=False)
async def api_root():
    """
    API v1 root endpoint, redirects to API documentation.
    """
    return RedirectResponse(url="/api/v1/docs")

# Import all API routers
from app.api import compliance
from app.api import impact_assessment
from app.api import regulatory_alerts
from app.api import trend_analysis
from app.api import scheduled_digests
from app.api import data_collection
from app.api import document_import
from app.api import document_analysis
from app.api import bulk_processor
from app.api import data_enrichment
from app.api.relationship_mapping import router as relationship_mapping_router
from app.api.integrations.router import router as integrations_router
from app.api import benchmarks
from app.api.pdf_analysis import router as pdf_analysis_router
from app.api.alerts.router import router as alerts_router
from app.api.routes import router as page_routes
from app.api.data_sources.router import router as data_sources_router
from app.api.calendar import router as calendar_router
from app.api.ai.router import router as ai_router
from app.api.regulation_management.router import router as regulation_management_router
from app.api.countries.router import router as countries_router
from app.api.regulators.router import router as regulators_router
from app.api.regulation_urls.router import router as regulation_urls_router
from app.api.regulations.router import router as regulations_router
# TODO: Temporarily disabled due to forward reference issue
# from app.api.compliance_requirements.router import router as compliance_requirements_router
from app.api.regulation_documents.router import router as regulation_documents_router
from app.api.regulation_tags.router import router as regulation_tags_router

from app.api.industries.router import router as industries_router
from app.api.regulatory_map import router as regulatory_map_router
from app.api.compliance_monitoring import router as compliance_monitoring_router

# Include all API routers with proper prefixes
app.include_router(compliance.router, prefix="/api/v1/compliance", tags=["compliance"])
app.include_router(impact_assessment.router, prefix="/api/v1/impact", tags=["impact"])
app.include_router(regulatory_alerts.router, prefix="/api/v1/alerts", tags=["alerts"])
app.include_router(trend_analysis.router, prefix="/api/v1/trends", tags=["trends"])
app.include_router(scheduled_digests.router, prefix="/api/v1/digests", tags=["digests"])
app.include_router(data_collection.router, prefix="/api/v1/collect", tags=["data_collection"])
app.include_router(document_import.router, prefix="/api/v1/document_import", tags=["document_import"])
app.include_router(document_analysis.router, prefix="/api/v1/document_analysis", tags=["document_analysis"])
app.include_router(bulk_processor.router, prefix="/api/v1/bulk_processor", tags=["bulk_processor"])
app.include_router(data_enrichment.router, prefix="/api/v1/data_enrichment", tags=["data_enrichment"])
app.include_router(relationship_mapping_router, prefix="/api/v1/relationships", tags=["relationship_mapping"])
app.include_router(integrations_router, prefix="/api/v1/integrations", tags=["integrations"])
app.include_router(benchmarks.router, prefix="/api/v1/benchmarks", tags=["benchmarks"])
app.include_router(pdf_analysis_router, prefix="/api/v1/pdf", tags=["pdf_analysis"])
app.include_router(alerts_router, prefix="/api/v1/alerts", tags=["regulatory_alerts"])
app.include_router(data_sources_router, prefix="/api/v1/data-sources", tags=["data_sources"])
app.include_router(calendar_router, prefix="/api/v1/calendar", tags=["calendar"])
app.include_router(ai_router, prefix="/api/v1/ai", tags=["ai"])
app.include_router(regulation_management_router, prefix="/api/v1/regulations/management", tags=["regulations"])
app.include_router(countries_router, prefix="/api/v1/countries", tags=["countries"])
app.include_router(regulators_router, prefix="/api/v1/regulators", tags=["regulators"])
app.include_router(regulation_urls_router, prefix="/api/v1/regulation-urls", tags=["regulation_urls"])
app.include_router(regulations_router, prefix="/api/v1/regulations", tags=["regulations"])
# app.include_router(compliance_requirements_router, prefix="/api/v1/compliance-requirements", tags=["compliance_requirements"])
app.include_router(regulation_documents_router, prefix="/api/v1/regulation-documents", tags=["regulation_documents"])
app.include_router(regulation_tags_router, prefix="/api/v1/regulation-tags", tags=["regulation_tags"])

app.include_router(industries_router, prefix="/api/v1/industries", tags=["industries"])
app.include_router(regulatory_map_router, tags=["regulatory-map"])
app.include_router(compliance_monitoring_router, tags=["compliance-monitoring"])

# Import and include the calendar analytics router
from app.api.calendar_analytics import router as calendar_analytics_router
app.include_router(calendar_analytics_router, prefix="/api/v1/calendar/analytics", tags=["calendar_analytics"])

# TODO: Governance router temporarily disabled due to import conflicts
# Will be re-enabled after resolving model import structure
# from app.api.governance.router import router as governance_router
# app.include_router(governance_router, prefix="/api/v1/governance", tags=["governance"])

# Include page routes without a prefix
app.include_router(page_routes, tags=["pages"])

# Country API Endpoints are now in app/api/countries/router.py


# Regulator API Endpoints are now in app/api/regulators/router.py


# Regulation URL API Endpoints are now in app/api/regulation_urls/router.py

@app.get("/api/v1/worldmap", response_class=HTMLResponse)
async def get_world_map(
    lat: float = Query(0.0, description="Center latitude"),
    lon: float = Query(0.0, description="Center longitude"),
    zoom: int = Query(2, description="Zoom level")
):
    """
    Generate a world map with the specified center and zoom level.

    Args:
        lat (float): Center latitude
        lon (float): Center longitude
        zoom (int): Zoom level

    Returns:
        HTMLResponse: HTML content of the map
    """
    # Create a basic world map
    map_obj = worldmap.create_world_map(
        center=(lat, lon),
        zoom_start=zoom
    )

    # Return the map as HTML
    return worldmap.get_map_html(map_obj)


@app.get("/api/v1/languages")
async def get_languages():
    """
    Get available languages.

    Returns:
        dict: List of supported languages
    """
    return {
        "current_language": get_locale(),
        "available_languages": {
            "en_US": "English (US)",
            "en_GB": "English (UK)",
            "es": "Español",
            "de": "Deutsch",
            "af": "Afrikaans",
            "zu": "isiZulu"
        }
    }

# TODO: Item API endpoints temporarily disabled due to schema issues
# Will be re-enabled after resolving schema import structure

# Include page routes
app.include_router(page_routes)