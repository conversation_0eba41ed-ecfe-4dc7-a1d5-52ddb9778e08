"""
Comprehensive Testing Configuration for RegulationGuru
Supports multiple testing types: unit, integration, security, behavior, and UI tests
"""
import os
import sys
import subprocess
import json
from datetime import datetime
from pathlib import Path

class TestRunner:
    """Comprehensive test runner for all test types"""
    
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'unit_tests': {},
            'integration_tests': {},
            'security_tests': {},
            'behavior_tests': {},
            'ui_tests': {},
            'coverage': {},
            'summary': {}
        }
        
    def run_unit_tests(self):
        """Run unit tests with coverage"""
        print("🧪 Running Unit Tests...")
        try:
            cmd = [
                'python', '-m', 'pytest', 
                'tests/unit/', 
                'tests/test_*.py',
                '--cov=app',
                '--cov-report=term-missing',
                '--cov-report=html:coverage_html',
                '--cov-report=json:coverage.json',
                '--junit-xml=test-reports/unit-tests.xml',
                '--html=test-reports/unit-tests.html',
                '--self-contained-html',
                '-v'
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            self.results['unit_tests'] = {
                'status': 'passed' if result.returncode == 0 else 'failed',
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
            # Parse coverage if available
            if os.path.exists('coverage.json'):
                with open('coverage.json', 'r') as f:
                    coverage_data = json.load(f)
                    self.results['coverage'] = {
                        'total_coverage': coverage_data.get('totals', {}).get('percent_covered', 0),
                        'files_covered': len(coverage_data.get('files', {}))
                    }
                    
        except subprocess.TimeoutExpired:
            self.results['unit_tests'] = {'status': 'timeout', 'error': 'Tests timed out after 5 minutes'}
        except Exception as e:
            self.results['unit_tests'] = {'status': 'error', 'error': str(e)}
            
    def run_integration_tests(self):
        """Run integration tests"""
        print("🔗 Running Integration Tests...")
        try:
            cmd = [
                'python', '-m', 'pytest', 
                'tests/integration/',
                'tests/test_*integration*.py',
                '--junit-xml=test-reports/integration-tests.xml',
                '--html=test-reports/integration-tests.html',
                '--self-contained-html',
                '-v'
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            
            self.results['integration_tests'] = {
                'status': 'passed' if result.returncode == 0 else 'failed',
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
        except subprocess.TimeoutExpired:
            self.results['integration_tests'] = {'status': 'timeout', 'error': 'Tests timed out after 10 minutes'}
        except Exception as e:
            self.results['integration_tests'] = {'status': 'error', 'error': str(e)}
            
    def run_security_tests(self):
        """Run security tests with bandit and safety"""
        print("🔒 Running Security Tests...")
        
        # Run Bandit for security issues
        try:
            bandit_cmd = ['bandit', '-r', 'app/', '-f', 'json', '-o', 'test-reports/bandit-report.json']
            bandit_result = subprocess.run(bandit_cmd, capture_output=True, text=True, timeout=120)
            
            # Run Safety for dependency vulnerabilities
            safety_cmd = ['safety', 'check', '--json', '--output', 'test-reports/safety-report.json']
            safety_result = subprocess.run(safety_cmd, capture_output=True, text=True, timeout=120)
            
            self.results['security_tests'] = {
                'bandit': {
                    'status': 'passed' if bandit_result.returncode == 0 else 'failed',
                    'returncode': bandit_result.returncode,
                    'stdout': bandit_result.stdout,
                    'stderr': bandit_result.stderr
                },
                'safety': {
                    'status': 'passed' if safety_result.returncode == 0 else 'failed',
                    'returncode': safety_result.returncode,
                    'stdout': safety_result.stdout,
                    'stderr': safety_result.stderr
                }
            }
            
        except subprocess.TimeoutExpired:
            self.results['security_tests'] = {'status': 'timeout', 'error': 'Security tests timed out'}
        except Exception as e:
            self.results['security_tests'] = {'status': 'error', 'error': str(e)}
            
    def run_behavior_tests(self):
        """Run behavior-driven tests with behave"""
        print("🎭 Running Behavior Tests...")
        
        # Check if features directory exists
        if not os.path.exists('features'):
            self.results['behavior_tests'] = {'status': 'skipped', 'reason': 'No features directory found'}
            return
            
        try:
            cmd = ['behave', 'features/', '--format=json', '--outfile=test-reports/behave-report.json']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            self.results['behavior_tests'] = {
                'status': 'passed' if result.returncode == 0 else 'failed',
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
        except subprocess.TimeoutExpired:
            self.results['behavior_tests'] = {'status': 'timeout', 'error': 'Behavior tests timed out'}
        except Exception as e:
            self.results['behavior_tests'] = {'status': 'error', 'error': str(e)}
            
    def run_ui_tests(self):
        """Run UI tests with Playwright"""
        print("🎨 Running UI Tests...")
        try:
            # Run Playwright tests
            cmd = [
                'python', '-m', 'pytest', 
                'tests/e2e/',
                '--browser=chromium',
                '--headed=false',
                '--junit-xml=test-reports/ui-tests.xml',
                '--html=test-reports/ui-tests.html',
                '--self-contained-html',
                '-v'
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            
            self.results['ui_tests'] = {
                'status': 'passed' if result.returncode == 0 else 'failed',
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
        except subprocess.TimeoutExpired:
            self.results['ui_tests'] = {'status': 'timeout', 'error': 'UI tests timed out after 10 minutes'}
        except Exception as e:
            self.results['ui_tests'] = {'status': 'error', 'error': str(e)}
            
    def generate_summary(self):
        """Generate test summary"""
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        
        for test_type, result in self.results.items():
            if test_type in ['unit_tests', 'integration_tests', 'security_tests', 'behavior_tests', 'ui_tests']:
                total_tests += 1
                if isinstance(result, dict):
                    if result.get('status') == 'passed':
                        passed_tests += 1
                    elif result.get('status') in ['failed', 'error', 'timeout']:
                        failed_tests += 1
                        
        self.results['summary'] = {
            'total_test_suites': total_tests,
            'passed_suites': passed_tests,
            'failed_suites': failed_tests,
            'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            'overall_status': 'PASSED' if failed_tests == 0 else 'FAILED'
        }
        
    def save_results(self):
        """Save test results to file"""
        os.makedirs('test-reports', exist_ok=True)
        with open('test-reports/comprehensive-test-results.json', 'w') as f:
            json.dump(self.results, f, indent=2)
            
    def print_summary(self):
        """Print test summary"""
        summary = self.results['summary']
        print("\n" + "="*60)
        print("🎯 COMPREHENSIVE TEST RESULTS SUMMARY")
        print("="*60)
        print(f"📊 Total Test Suites: {summary['total_test_suites']}")
        print(f"✅ Passed Suites: {summary['passed_suites']}")
        print(f"❌ Failed Suites: {summary['failed_suites']}")
        print(f"📈 Success Rate: {summary['success_rate']:.1f}%")
        print(f"🏆 Overall Status: {summary['overall_status']}")
        
        if self.results.get('coverage', {}).get('total_coverage'):
            print(f"📋 Code Coverage: {self.results['coverage']['total_coverage']:.1f}%")
            
        print("\n📝 Detailed Results:")
        for test_type in ['unit_tests', 'integration_tests', 'security_tests', 'behavior_tests', 'ui_tests']:
            result = self.results.get(test_type, {})
            status = result.get('status', 'unknown')
            emoji = "✅" if status == 'passed' else "❌" if status in ['failed', 'error'] else "⏭️"
            print(f"  {emoji} {test_type.replace('_', ' ').title()}: {status.upper()}")
            
        print("="*60)

def main():
    """Main test runner function"""
    print("🚀 Starting Comprehensive Testing Suite for RegulationGuru")
    print("="*60)
    
    # Create test reports directory
    os.makedirs('test-reports', exist_ok=True)
    
    runner = TestRunner()
    
    # Run all test types
    runner.run_unit_tests()
    runner.run_integration_tests()
    runner.run_security_tests()
    runner.run_behavior_tests()
    runner.run_ui_tests()
    
    # Generate summary and save results
    runner.generate_summary()
    runner.save_results()
    runner.print_summary()
    
    # Exit with appropriate code
    sys.exit(0 if runner.results['summary']['overall_status'] == 'PASSED' else 1)

if __name__ == "__main__":
    main()
