"""
Test script for the Gemini API client.
"""
import os
import json
from app.api.ai.gemini_client import GeminiClient

def main():
    """Test the Gemini API client."""
    # Create a client
    client = GeminiClient()
    
    # Check if API key is configured
    if not client.api_key:
        print("Gemini API key not configured. Please set the GEMINI_API_KEY environment variable.")
        return
    
    # Test the search_regulatory_information method
    print("Testing search_regulatory_information method...")
    result = client.search_regulatory_information(
        query="What are the key requirements of GDPR?",
        regulation_name="GDPR",
        jurisdiction="EU"
    )
    
    # Print the result
    print(f"Status: {result.get('status')}")
    if result.get("status") == "error":
        print(f"Error: {result.get('error')}")
        return
    
    # Print structured data if available
    structured_data = result.get("structured_data", {})
    if structured_data:
        print("\nStructured Data:")
        print(f"Answer: {structured_data.get('answer', '')[:100]}...")
        print(f"Citations: {structured_data.get('citations', [])}")
        print(f"Key Considerations: {structured_data.get('key_considerations', [])}")
        print(f"Deadlines: {structured_data.get('deadlines', [])}")
        print(f"Penalties: {structured_data.get('penalties', [])}")
    
    # Print raw text
    print("\nRaw Text:")
    print(result.get("raw_text", "")[:200] + "...")
    
    print("\nTest completed successfully!")

if __name__ == "__main__":
    main()
